@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/0eC6fl06luXEYWpBSJvXCBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/Fl4y0QdOxyyTHEGMXX8kcRJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/-L14Jk06m6pUHB-5mXQQnRJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/I3S1wsgSg9YCurV6PUkTORJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/NYDWBdD4gIq26G5XYbHsFBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/Pru33qjShpZSmG3z6VYwnRJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 300;
    src: local('Roboto Light'), local('Roboto-Light'), url(../plugins/roboto/Hgo13k-tfSpn0qi1SFdUfVtXRa8TVwTICgirnJhmVJw.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/ek4gzZ-GeXAPcSbHtCeQI_esZW2xOQ-xsNqO47m55DA.woff2) format('woff2');
    unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/mErvLBYg_cXG3rLvUsKT_fesZW2xOQ-xsNqO47m55DA.woff2) format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/-2n2p-_Y08sg57CNWQfKNvesZW2xOQ-xsNqO47m55DA.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/u0TOpm082MNkS5K0Q4rhqvesZW2xOQ-xsNqO47m55DA.woff2) format('woff2');
    unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/NdF9MtnOpLzo-noMoG0miPesZW2xOQ-xsNqO47m55DA.woff2) format('woff2');
    unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/Fcx7Wwv8OzT71A3E1XOAjvesZW2xOQ-xsNqO47m55DA.woff2) format('woff2');
    unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    src: local('Roboto'), local('Roboto-Regular'), url(../plugins/roboto/CWB0XYA8bzo0kSThX0UTuA.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/ZLqKeelYbATG60EpZBSDyxJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/oHi30kwQWvpCWqAhzHcCSBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/rGvHdJnr2l75qb0YND9NyBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/mx9Uck6uB63VIKFYnEMXrRJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/mbmhprMH69Zi6eEPBYVFhRJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/oOeFwZNlrTefzLYmlVV1UBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 500;
    src: local('Roboto Medium'), local('Roboto-Medium'), url(../plugins/roboto/RxZJdnzeo3R5zSexge8UUVtXRa8TVwTICgirnJhmVJw.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
/* cyrillic-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/77FXFjRbGzN4aCrSFhlh3hJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0460-052F, U+20B4, U+2DE0-2DFF, U+A640-A69F;
}
/* cyrillic */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/isZ-wbCXNKAbnjo6_TwHThJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/UX6i4JxQDm3fVTc1CPuwqhJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/jSN2CGVDbcVyCnfJfjSdfBJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0370-03FF;
}
/* vietnamese */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/PwZc-YbIL414wB9rB1IAPRJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0102-0103, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/97uahxiqZRoncBaCEI3aWxJtnKITppOI_IvcXXDNrsc.woff2) format('woff2');
    unicode-range: U+0100-024F, U+1E00-1EFF, U+20A0-20AB, U+20AD-20CF, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 700;
    src: local('Roboto Bold'), local('Roboto-Bold'), url(../plugins/roboto/d-6IYplOFocCacKzxwXSOFtXRa8TVwTICgirnJhmVJw.woff2) format('woff2');
    unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2212, U+2215;
}
body, html {
    display: block;
    height: 100%;
    min-height: 100%;
    position: relative;
    text-rendering: optimizelegibility;
    width: 100%;
}
body{
    /*background-image: url("../images/furley_bg.png");*/
    /*background: linear-gradient(#f5f5f5,#fff) no-repeat;*/
    background: #fff;
    min-height: 100%;
}
a, a:focus, a:hover {
    color: #1257b7;
}
/* primary color */
a,
a:hover,
a:focus {
    color: #92000A;
}
body .container .well-primary,
body .container-fluid .well-primary,
body .container .jumbotron-primary,
body .container-fluid .jumbotron-primary {
    background-color: #92000A;
}
.btn:not(.btn-raised).btn-primary,
.input-group-btn .btn:not(.btn-raised).btn-primary {
    color: #92000A;
}
.btn[disabled][disabled]{
    color: rgba(0,0,0,.26);
}
.form-group {
    margin: 5px 0 0 0;
}
.btn.btn-raised.btn-primary,
.input-group-btn .btn.btn-raised.btn-primary,
.btn.btn-fab.btn-primary,
.input-group-btn .btn.btn-fab.btn-primary,
.btn-group-raised .btn.btn-primary,
.btn-group-raised .input-group-btn .btn.btn-primary {
    background-color: #92000A;
    color: rgba(255,255,255, 0.84);
}
.btn-group.open > .dropdown-toggle.btn.btn-primary,
.btn-group-vertical.open > .dropdown-toggle.btn.btn-primary {
    background-color: #92000A;
}
.checkbox input[type=checkbox]:checked + .checkbox-material .check,
label.checkbox-inline input[type=checkbox]:checked + .checkbox-material .check {
    color: #92000A;
    border-color: #92000A;
}
.checkbox input[type=checkbox]:checked + .checkbox-material .check:before,
label.checkbox-inline input[type=checkbox]:checked + .checkbox-material .check:before {
    color: #92000A;
    -webkit-box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
    box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
}
.togglebutton label input[type=checkbox]:checked + .toggle:after {
    background-color: #92000A;
}
.radio .check,
label.radio-inline .check {
    background-color: #92000A;
}
.radio input[type=radio]:checked ~ .check,
label.radio-inline input[type=radio]:checked ~ .check {
    background-color: #92000A;
}
.radio input[type=radio]:checked ~ .circle,
label.radio-inline input[type=radio]:checked ~ .circle {
    border-color: #92000A;
}
.label.label-primary {
    background-color: #92000A;
}
/* new changes*/
.nav-tabs{
    background-color: #f5f5f5 !important;
    color: #555 !important;
}
p.form-control{
    word-break: break-all;
}
button:focus,.btn:focus{
    outline: none !important;
}
.form-control:not([type=search]),
.form-group .form-control:not([type=search]) {
    border: 1px solid #ccc !important;
    border-radius: 3px;
    padding: 7px 8px;
    background-image: none !important;
    float: none;
    -webkit-box-shadow: none;
    box-shadow: none !important;
    transition: border .2s !important;
    background-color: #fff !important;
    margin-bottom: 0px !important;
    margin-top: 0px !important;

}
.form-group.is-focused .form-control {
    outline: none;
    border: 1px solid #920109 !important;
}

.input-group .form-control:not([type=tel]){
    border-right: 0 !important;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}
.dropzone{
    background: #fff !important;
}
.dropzone .form-control:not([type=tel]){
    border-radius: 0;
    border-left: none !important;
    border-top: none !important;
}
.input-group .input-group-addon{
    padding: 6px 4px !important;
    background-color: #eee !important;
    border: 1px solid #ccc !important;
}

.input-group .input-group-addon:nth-child(3){
    border-left: none !important;
}

.pagination-custom {
    background-color: #f5f5f5 !important;
    color: #555 !important;
}
.pagination-custom .input-group .input-group-addon{
    padding: 5px 10px !important;
    background-color: transparent !important;
    border: none !important;
}
.input-group .input-group-addon .btn{
    margin: 0;
    padding: 0;
}

.select2-container .select2-selection--single{
    height: 36px;
    background-color: #fff !important;
}
.select2-container--default .select2-selection--single .select2-selection__rendered{
    line-height: 36px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow{
    top: 5px;
}

.select2-container .select2-selection--multiple {
    min-height: 36px;
    background-color: #fff !important;
}
.select2-container--default .select2-selection--multiple .select2-selection__clear {
    margin-top: 8px;
    margin-right: 6px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    padding: 2px 5px;
}
.select2-container--default .select2-search--inline .select2-search__field {
    line-height: 27px;
    padding-left: 5px;
}
.select2-selection__rendered {
    background: #fff !important;
}
.select2-dropdown--below .select2-search--dropdown {
    background-color: #fff !important;
}
.select2-dropdown--below .select2-search__field {
    background-color: #fff !important;
}
.add-content .select2-container {
    margin-bottom: 0 !important;
}
.select2-results__options {
    background-color: #fff !important;
}
.form-group .select2-container {
    margin-top: 0 !important;
}

.form-group label.control-label {
    margin-top: 12px !important;
    padding-top: 5px !important;
}
/**/
.form-group.is-focused .form-control .material-input:after {
    background-color: #92000A;
}
.form-group.is-focused label,
.form-group.is-focused label.control-label {
    color: #92000A;
}
#navbar .navbar-right{
    padding-top: 3px;
}
.navbar {
    background-color: #92000A;
}
.navbar, .navbar.navbar-default {
    background-color: #92000A;
    color: rgba(255,255,255, 0.84);
}
.dropdown-menu li a:hover {
    background-color: transparent;
    color: #92000A;
}
.alert.alert-primary {
    background-color: #92000A;
    color: rgba(255,255,255, 0.84);
}
.text-primary {
    color: #92000A;
}
.nav-tabs {
    background: #92000A;
}
.panel.panel-primary > .panel-heading {
    background-color: #92000A;
}
.slider.noUi-connect,
.slider.slider-default.noUi-connect {
    background-color: #92000A;
}
.slider.slider-primary.noUi-connect {
    background-color: #92000A;
}
.slider .noUi-connect,
.slider.slider-default .noUi-connect {
    background-color: #92000A;
}
.slider.slider-primary .noUi-connect {
    background-color: #92000A;
}
.slider .noUi-handle,
.slider.slider-default .noUi-handle {
    background-color: #92000A;
    border-color: #92000A;
}
.slider.slider-primary .noUi-handle {
    background-color: #92000A;
}
.btn-group-raised .btn:not(.btn-link).active.btn-primary, .btn-group-raised .btn:not(.btn-link):active.btn-primary, .btn-group-raised .btn:not(.btn-link):focus.btn-primary, .btn-group-raised .btn:not(.btn-link):hover.btn-primary, .btn-group-raised .input-group-btn .btn:not(.btn-link).active.btn-primary, .btn-group-raised .input-group-btn .btn:not(.btn-link):active.btn-primary, .btn-group-raised .input-group-btn .btn:not(.btn-link):focus.btn-primary, .btn-group-raised .input-group-btn .btn:not(.btn-link):hover.btn-primary, .btn.btn-raised:not(.btn-link).active.btn-primary, .btn.btn-raised:not(.btn-link):active.btn-primary, .btn.btn-raised:not(.btn-link):focus.btn-primary, .btn.btn-raised:not(.btn-link):hover.btn-primary, .input-group-btn .btn.btn-raised:not(.btn-link).active.btn-primary, .input-group-btn .btn.btn-raised:not(.btn-link):active.btn-primary, .input-group-btn .btn.btn-raised:not(.btn-link):focus.btn-primary, .input-group-btn .btn.btn-raised:not(.btn-link):hover.btn-primary{
    background-color: #b2000A;
}
/* modal module */
.modal-content{
    margin-top: 100px;
}
/* alert module */
.notif{
    display: none;
}
.notif .dark-back{
    position: fixed;
    z-index: 10000000;
    top: 0;
    left: 0;
    opacity: 0.9;
    background-color: rgba(0,0,0,0.5);
    width: 100%;
    height: 100%;
    overflow-y: auto;
}
.notif .win8-notif-body{
    position: absolute;
    z-index: 100000000;
    top:50%;
    left: 50%;
    margin: 0;
    min-width: 320px;
    max-width: 100%;
    text-align: left;
    padding: 40px;
    font-family: sans-serif;
    transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
}
.notif .win8-notif-body h3{
    font-size: 1.5em;
}
.notif .win8-notifiy-body p{
    font-size: 1em;
}
.notif .win8-notif-button{
    border: solid 2px white;
    padding: 10px;
    min-width: 10%;
    display: block;
    margin-top: 2%;
    margin-left: 2%;
    float: right;
    font-weight: bold;
    margin-bottom: 2%;
}
.notif.black{

    background-color: rgb(39, 37, 37);
    color:white;
}
.notif.black button{
    background-color: rgb(66, 60, 60);
    cursor: pointer;
    color: white;
}
.notif.black .win8-notif-body{
    background-color: rgb(39, 37, 37);
    color:white;
}
.notif.black button:hover{
    background-color: rgb(100, 100, 100);
}

.notif.green{

    background-color: rgb(50, 179, 106);
    color:white;
}
.notif.green button{
    background-color: rgb(50, 179, 106);
    cursor: pointer;
    color: white;
}
.notif.green .win8-notif-body{
    background-color: rgb(50, 179, 106);
    color:white;
}
.notif.green button:hover{
    background-color: rgb(29, 92, 56);
}
.notif.blue{

    background-color:#2672EC;
    color:white;
}
.notif.blue button{
    background-color:#2672EC;
    cursor: pointer;
    color: white;
}
.notif.blue .win8-notif-body{
    background-color: #2672EC;
    color:white;
}

.notif.blue button:hover{
    background-color: #2E8DEF;
}
.notif.red{

    background-color:#AC193D;
    color:white;
}
.notif.red button{
    background-color: #AC193D;
    cursor: pointer;
    color: white;
}
.notif.red .win8-notif-body{
    background-color: #AC193D;
    color:white;
}
.notif.red button:hover{
    background-color:#BF1E4B;
}
.notif.orange{
    background-color: #D24726;
    color:white;
}
.notif.orange button{
    background-color: #D24726;
    cursor: pointer;
    color: white;
}
.notif.orange .win8-notif-body{
    background-color: #D24726;
    color:white;
}

.notif.orange button:hover{
    background-color: #DC572E;
}
/* breadcrumb module */

.breadcrumb{
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
.breadcrumb a,.breadcrumb span{
    font-weight: 500;
    text-overflow: ellipsis;
}

.text-primary {
    color: #1257b7;
}
.header-title.breadcrumb {
    padding: 0;
    background: #f5f5f5;
    list-style: none;
    overflow: hidden;
    border: 1px solid #ccc;
}
.header-title.breadcrumb i{
    top: 3px;
}
.header-title.breadcrumb>li+li:before {
    padding: 0;
    display:none;
}
.header-title.breadcrumb li {
    float: left;
}
.header-title.breadcrumb li.active a,.header-title.breadcrumb li.active span{
    background: #f0f0f0 ;
    color: #a92222;
}
.breadcrumb li.active a:after,.breadcrumb li.active span:after {
    border-left-color:#f0f0f0  !important;
}

.breadcrumb li.completed a,.breadcrumb li.completed span {
    background: #f5f5f5 ;
    color: #a92222;
}
.header-title.breadcrumb li.completed a:after,.header-title.breadcrumb li.completed span:after {
    border-left: 30px solid #f5f5f5;
}



.header-title.breadcrumb li a,.header-title.breadcrumb li span {
    color: #555;
    background-color: #f5f5f5;
    text-decoration: none;
    padding: 10px 0 10px 45px;
    position: relative;
    display: block;
    float: left;
    min-height: 40px;
    transition: .3s ease;
}
.header-title.breadcrumb li a:after,.header-title.breadcrumb li span:after {
    content: " ";
    display: block;
    width: 0;
    height: 0;
    border-top: 50px solid transparent;           /* Go big on the size, and let overflow hide */
    border-bottom: 50px solid transparent;
    border-left: 30px solid #f5f5f5;
    position: absolute;
    top: 50%;
    margin-top: -50px;
    left: 100%;
    z-index: 2;
    transition: .3s ease;
}
.header-title.breadcrumb li a:before,.header-title.breadcrumb li span:before {
    content: " ";
    display: block;
    width: 0;
    height: 0;
    border-top: 50px solid transparent;           /* Go big on the size, and let overflow hide */
    border-bottom: 50px solid transparent;
    border-left: 30px solid #ccc;
    position: absolute;
    top: 50%;
    margin-top: -50px;
    margin-left: 1px;
    left: 100%;
    z-index: 1;
}
.header-title.breadcrumb li:first-child a,.header-title.breadcrumb li:first-child span {
    padding-left: 15px;
}
.header-title.breadcrumb li a:hover {background-color: #f0f0f0; color: #a92222}
.header-title.breadcrumb li a:hover:after {border-left-color: #f0f0f0;}
.header-title.breadcrumb li a{
    color: #92000A;
}
/* form module */
.select2-container.select2-container--focus{
    border: 1px solid #92000A;
}
.form-group label.control-label {
    font-size: 14px;
    color: #444;
    text-align: right;
}
.togglebutton label input[type=checkbox]:checked+.toggle {
    background-color: #03a9f4;
}
.togglebutton label input[type=checkbox]:checked+.toggle:after {
    background-color: #03a9f4;
}
.togglebutton{
    margin-top: 8px;
}
.select2-container--default .select2-selection--single {
    background-color: #f8f8f8;
}
.select2-container--open .select2-dropdown {
    left: -1px;
    top: -1px;
}
.select2-results__options{
    background-color: #f8f8f8;
}

.select2-search--dropdown{
    background-color: #f8f8f8 !important;
}

.select2-search__field{
    background-color: #f8f8f8;
}
.select2-container--default.select2-container--focus .select2-selection--multiple{
    background-color: #f8f8f8 ;
}
.select2-container--default .select2-selection--multiple{
    border-radius: 0 !important;
}
.select2-selection__rendered{
    background-color: #f8f8f8;
}
.select2-selection--multiple{
    background-color: #f8f8f8 !important;
}
.select2-container--default .select2-selection--single{
    border: 0;
}
.select2-container{
    border: 1px solid #aaa ;
}
.select2-container--default.select2-container--focus .select2-selection--multiple{
    border: 0 !important;
}
.select2-container--default .select2-selection--multiple{
    border: 0 !important;
}
.form-group .select2-container{
    margin-top: 7px;
}
.navbar.navbar-primary .dropdown-menu {
    border-radius: 0;
    padding:0 ;
    margin: 0;
}
.navbar.navbar-primary li .dropdown-menu {
    right: 0;
    border-top-width: 0  !important;
}
.container-custom{
    padding: 66px 16px 16px 66px;

}
.angular-app .container-custom.no-navbar-padding{
    padding-top:10px !important;
}
.angular-app .container-custom.no-sidebar-padding{
    padding-left:0px !important;
}
/* content*/
.content{

    position: relative;
}
/* Footer */
.navbar.footer {
    background-color: #F6F6F6;
    color: rgba(0,0,0, 0.84);
}
.well-custom {
    margin-top: 51px
}
.well-custom .breadcrumb{
    margin-bottom: 0;
}
.navbar.footer{
    border-top: 1px solid #ddd;
}
.well-custom{
    margin-bottom: 0;
}
.form-group a{
    outline: none;
}
/* Component 404 */
#block_error{
    min-height: 80vh;
    border: 1px solid #cccccc;
    margin: 10vh auto;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    background: #f7f7f7 url(../images/block.gif) no-repeat 0 51px;
}
#block_error h1,#block_error p{
    color: #92000A;
}
/* file_upload Component   */
magna-file-input input:not(disabled){
    cursor: pointer;
}
.dropzone .label{
    position: absolute;
    top: 0;
    z-index: 1000;
    right: 0;
    padding: 4px;
}
.dropzone {
    min-height: 63px;
    -webkit-border-radius: 3px;
    border-radius: 0;
    padding: 0 4px !important;
    border: 1px solid #aaa !important;
    background: none;
    transition: .5s ease-out;
}
.dropzone.dz-drag-hover {
    background: #00aa00;
    border: 1px solid #f8f8f8;
}
.dropzone.dz-drag-hover  .material-icons{
    color: #fff;
}
.dropzone.dz-drag-hover .btn.btn-fab.btn-fab-mini{
    background: #D2D2D2;
}
.dropzone .dz-preview .dz-error-mark, .dropzone .dz-preview .dz-success-mark {
    margin-left: -15px;
    margin-top: -15px;
}
.dropzone .dz-preview .dz-error-mark svg, .dropzone .dz-preview .dz-success-mark svg {
    width: 32px;
    height: 32px;
}
.dropzone .dz-preview {
    margin: 8px;
    min-height: 80px;
    background: none !important;
}
.dropzone .dz-preview .dz-image{
    width: 80px;
    height: 80px;
}
.dropzone .dz-default.dz-message span {
    display: none;
}
.dropzone .dz-default.dz-message {
    position: relative;
    margin:1px 4px;
    font-weight: 400;
    text-align: center;
    color: #aaa;
    font-size: 17px;
}
.dropzone.dz-drag-hover .dz-default.dz-message{
    color: #fff;
}
.dropzone .label__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    display: inline-block;
    margin: 3px 5px 2px 0;
    padding:0 5px;
    overflow: hidden;
    max-width: 250px;
    font-size: 14px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.dropzone .label__choice .choice__action{
    color: #999;
    display: inline-block;
    font-weight: bold;
    padding: 0 8px;
    cursor: pointer;
    border-right: 1px solid #aaa;
    height: 20px;
    background: #92000A;
    position: relative;
    left: -5px;
}
.dropzone .label__choice .glyphicon{
    position: relative;
    top: 2px;
    color: #fff;
}
.dropzone .dz-preview .dz-details .dz-filename span, .dropzone .dz-preview .dz-details .dz-size span{
    white-space: nowrap;
    font-size: 13px;
}
.dropzone .dz-error-mark svg g g {
    fill: #fd8787;
}
/* sidebar menu */
.sidebar_1.left {
    position: fixed;
    top: 0;
    background: #282828;
    display: inline-block;
    white-space: nowrap;
    width: 50px;
    transition: width .3s ;
    z-index: 1001;
    padding-top: 50px;
    min-height: 100vh;
}

.sidebar_1.left > div{

}
.sidebar_1.left .glyphicon {
    margin:15px;
    width:20px;
}
.sidebar_1 .item {
    min-height:50px;
    overflow:hidden;
    position: relative;
    transition: .2s ease-out ;
}
.sidebar_1 .item:hover{
    color:#aaa;
}
.sidebar_1 .item a{
    color:#fff;
    display: block;
    font-weight: 500;
    transition: .2s ease;
}
.sidebar_1 .item a:hover{
    color:#aaa;
}
.sidebar_1 .item.active a{
    color: #aaa;
}
.sidebar_1 .item a .caret{
    position: absolute;
    right: 8px;
    top: 20px;
}
.sidebar_1 .nav a{
    padding-left: 50px;
    font-weight: 500;
    color: #aaa;
    overflow: hidden;
    width: 250px;
    text-overflow: ellipsis;
}
.sidebar_1 .item.active > a, .sidebar_1 .subitem.active > a{
    background-color: #4e4e4e;
}
.sidebar_1 .nav a:hover,.sidebar_1 .nav a:focus{
    color: #fff;
    background-color: transparent;
}

.sidebar_1 .nav .active > a{
    color: #fff;
    background-color: #353535;
}

/*  pagination*/
.pagination-custom .form-group {
    margin: 4px 0 0 0;
}
.pagination-custom input,.pagination-custom .btn{
    color: #CA2F57 !important;
    text-align: center;
}
.pagination-custom .input-group{
    width: 400px;
    max-width: 100%;
}
/* Table Component */
.datatable{
    background-color:  #ebebeb;
}

.datatable td > .list:not(.overflow-visible) {
    text-overflow: ellipsis;
    max-width: 100%;
    width: 100%;
    display: inline-block;
    white-space: normal;
    position: relative;
    vertical-align: middle;
}
.datatable td.large_td{
    max-width: 100% !important;
    min-width: 220px !important;
}
.datatable td.large_td .list{
    white-space:normal;
}
.datatable .ellipsis_inline{
    display: block !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.datatable thead th a{
    color: #555;
    font-size: 12px !important;
}
.datatable thead th  i{
    font-size: 12px !important;
}
.datatable thead th{
    border-bottom: 2px solid #ddd !important;
    border-top: 2px solid #ddd !important;
    font-weight: bold;
    font-size: 12px;
    vertical-align: bottom;
    line-height: 18px;
    min-width: 128px;
    color: #555 !important;
    padding: 8px;
    text-align: center;
    background-color: #ebebeb;
    white-space: nowrap;
    height: 35px;
}
.datatable td{
    height: auto;
    padding: 4px 8px;
    white-space: nowrap;
    vertical-align: middle !important;
    text-overflow: ellipsis;
    font-size: 12px;
    text-align: center;
}
.datatable .id{
    max-width: 50px;
    min-width: 50px;
}
.datatable td > .list.overflow-visible{
    overflow: visible;
}
.datatable.table-striped tbody > tr:nth-child(even) > td {
    background-color: #f9f9f9;
}
.datatable.table-striped tbody > tr:nth-child(odd) > td {
    background-color: #fff;
}
.datatable.table-striped tbody > tr:nth-child(even):hover > td {
    background-color: #f5f5f5;
}
.datatable.table-striped tbody > tr:nth-child(odd):hover > td {
    background-color: #f5f5f5;
}
.datatable .overflow-visible .label{
    z-index: 1000;
    border-bottom: 1px solid #ddd;
}
.datatable th i.glyphicon{
    padding-left: 4px;
    padding-left: 2px;
    padding-right: 2px;
}
.datatable th a.hide_events{
    color: #c5000A;
}
.datatable th a.hide_events.close_events {
    color: #4c8d50;
}
.datatable th a.close_events i:before {
    content: "\e105";
}
.datatable th .dropdown-menu{
    border-radius: 0;
    margin-left: 3px;
}
.datatable th .dropdown-menu > li > a{
    padding: 5px;
}

.datatable .hideev {
    min-width: 50px !important;
    width: 50px !important;
}
.datatable .btn-group, .btn-group-vertical{
    margin: 0;
}
.datatable .dropdown-menu i.glyphicon{
    font-size: 12px;
    position: relative;
    top: 2px;
    padding-right: 8px;
    color: #555;
}
.datatable .dropdown-menu a{
    font-size: 12px;
}
.datatable i.material-icons{
    font-size: 20px;
    position: relative;
    top: -2px;
    color: #555;
}
.datatable{
    margin-bottom: 0;
}
.datatable a{
    font-weight: 700;
}
.datatable td a:not(.btn):hover{
    text-decoration: underline !important;
}
.datatable th span.sorting::after {
    font-family: 'Glyphicons Halflings';
    -webkit-font-smoothing: antialiased;
    line-height: 6px;
    white-space: pre;
    position: relative;
    vertical-align: middle;
    right: 4px;
    display: inline-block;
    /* float: left; */
    content: "\e253\A\e252";
    font-weight: normal;
    font-size: 11px;
}
.datatable th span.sorting.sorting_desc::after {
    content: "\e252";
    color: #a92222;
}
.datatable th span.sorting.sorting_asc::after {
    content: "\e253";
    color: #a92222;
}
/* New style */
.table-responsive .datatable th:first-of-type{
    min-width: 1px;
    width: 1px;
}
.table-responsive .datatable td:first-of-type{
    min-width: 1px;
    width: 1px;
}
.table-responsive .datatable td .dropdown-menu{
    padding: 0;
}

.table-responsive .datatable .btn-group {
    display: flex;
}
.table-responsive .datatable td .btn-group > .btn {
    border:1px solid rgba(0,0,0,0.1) !important;
    box-shadow: none !important;
    text-transform: capitalize;
    max-width: 120px;
    text-overflow: ellipsis;
    overflow: hidden;
    font-size: 11px;
    padding: 5px 10px;
}
.table-dropdown-visible {
    overflow: visible !important;
}
.table-responsive .datatable td  .dropdown-toggle{
    padding-left: 6px;
    padding-right: 6px;
}
.table-responsive .datatable .dropdown-menu a{
    text-decoration: none !important;
}
.table-responsive .datatable td .dropdown-menu > li > .dropdown-menu-item {
    display: block;
    padding: 8px 10px;
    clear: both;
    font-weight: normal;
    line-height: 18px;
    white-space: nowrap;
    margin: 0;
    border-top: 1px solid #f6f6f6;
}
.table-responsive .datatable td .dropdown-menu > li:hover .dropdown-menu-item,
.table-responsive .datatable td .dropdown-menu > li:focus .dropdown-menu-item{
    background-color: #f5f5f5;
    color: #262625;
}
.table-responsive .datatable td .btn-group > .btn{
    border-radius: 0 ;
}
/* End Datatable */
.required-label:after {
    content:" *";
    color:red;
}
.pager{
    margin: 8px 0;
}
.more{
    border-top: 1px solid #e3e3e3;
    border-bottom: 1px solid #e3e3e3
}


/* Header Classes */
#navbar-form.navbar-form .btn{
    padding: 8px 8px !important;
    margin: 0;
}
#navbar-form.navbar-form .btn i{
    padding-right: 0 !important;
    margin: 0;
}
.dropdown.notification > a{
    padding-top: 12px !important;
    padding-bottom: 8px !important;
}
.notification .block{
    padding: 5px;
}
.notification .block.unread{
    background-color: #f7f7f7;
}
.notification .block .title{
    font-weight: bold;
}
.notification .block:hover{
    opacity: 0.7;
    color: #282828 !important;
}
.notification .block.unread .title{
    font-weight: bold;
    color: #428fdf;
    padding-right: 28px;
}
.notification .block.unread:hover .title{
    color: #282828 !important;
}
.caret-up.open:before
{
    content:'';
    position: absolute;
    bottom: 0;
    right: 23px;
    display: inline-block;
    width:0;
    height:0;
    border:7px solid transparent;
    vertical-align: middle;
    border-bottom-color: #fff;
}
.notification .dropdown-menu li .scroll{
    /*height: auto;
    max-height: 400px;
    overflow-x: hidden;*/
}
.notification .dropdown-menu hr {
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 10px;
    border: 0;
    border-top: 1px solid #eee;
}
.navbar.navbar-primary {
    background-color: #282828;
    color: rgba(255,255,255, 1);
}

.navbar.navbar-primary{

    -webkit-box-shadow: 0 4px 0 #92000A;
    -moz-box-shadow: 0 4px 0 #92000A;
    z-index: 10000;
}
.navbar .active{
    background-color: #92000A;
}

.navbar.navbar-primary .dropdown-menu li > a {
    font-size: 14px;
    padding: 8px 16px;
    font-weight: 500;
}
.navbar.navbar-primary i{
    padding-right:5px;
}
.navbar.navbar-primary .dropdown-menu li > a:hover,
.navbar.navbar-primary .dropdown-menu li > a:focus {
    color: #282828;
    background-color: #eeeeee;
}
.navbar.navbar-primary .dropdown-menu .active > a {
    background-color: #282828;
    color: rgba(255,255,255, 0.84);
}
.navbar-brand .logo {
    display: inline-block;
    font-size: 20px;
    font-weight: 300;
    letter-spacing: 1px;
}
#navbar-top .navbar-nav {
    float: left !important;
    margin:  0  !important;
}
#navbar-top .navbar-nav.navbar-right {
    float: right !important;
}
#navbar-top .navbar-right > li{
    float: left !important;
}
#navbar-top  .navbar-nav .dropdown .dropdown-toggle:after {
    content: ' ';
    font-family: 'Material Icons';
    font-size: 1.5em;
    float: right;
}
#navbar-top .navbar-brand {
    height: 50px;
}
#navbar-top .help .dropdown-menu li > a{
    padding: 4px 8px !important;
    color: #333;
}
#navbar-top .help li a:hover{
    color: #282828;
    background-color: #eeeeee;
}
#navbar-top li.policy-title > a{
    padding-top: 4px;
    padding-bottom: 4px;
    margin-top: 11px;
    min-height: unset;
    transition: .3s ease-in-out;
}
.notification .badge{
    position: absolute;
    top: 1px;
    right: 3px;
    z-index: 1000;
    font-size: 11px;
    border: 3px solid #333;
    width: 27px;
    height: 27px;
    padding: 0;
    line-height: 23px;
    border-radius: 50%
}
.notification .glyphicon.glyphicon-bell{
    margin-top: 3px;
    font-size: 20px;
}
/* Notification pulse */
.notification-eff-pulse {
    position: relative;
    cursor: pointer;
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.9);
    -webkit-animation: pulseNotification 3s;
    -moz-animation: pulseNotification 1.25s;
    -ms-animation: pulseNotification 1.25s;
    animation: pulseNotification 2s infinite;
    /* -webkit-animation-iteration-count: 4; /* Safari 4.0 - 8.0 */
    /* animation-iteration-count: 4; */
}
@-webkit-keyframes pulseNotification {to {box-shadow: 0 0 0 45px rgba(188, 132, 32, 0);opacity: .9}}
@-moz-keyframes pulseNotification {to {box-shadow: 0 0 0 45px rgba(232, 76, 61, 0);opacity: .9}}
@-ms-keyframes pulseNotification {to {box-shadow: 0 0 0 45px rgba(232, 76, 61, 0);opacity: .9}}
@keyframes pulseNotification {to {box-shadow: 0 0 0 20px rgba(232, 76, 61, 0);opacity: .9}}
/* Notification shake */
.notification-eff-shake {
    -webkit-animation: shakeNotification 0.82s cubic-bezier(.36,.07,.19,.97) both;;
    animation: shakeNotification 0.82s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
    -webkit-animation-iteration-count: 2; /* Safari 4.0 - 8.0 */
    animation-iteration-count: 2;
}
@-webkit-keyframes shakeNotification { 10%, 90% {transform: translate3d(-1px, 0, 0);  } 20%, 80% {transform: translate3d(1px, 0, 0);} 30%, 50%, 70% {transform: translate3d(-2px, 0, 0);} 40%, 60% {transform: translate3d(2px, 0, 0);} }
@-moz-keyframes shakeNotification { 10%, 90% {transform: translate3d(-1px, 0, 0);  } 20%, 80% {transform: translate3d(1px, 0, 0);} 30%, 50%, 70% {transform: translate3d(-2px, 0, 0);} 40%, 60% {transform: translate3d(2px, 0, 0);} }
@keyframes shakeNotification { 10%, 90% {transform: translate3d(-1px, 0, 0);  } 20%, 80% {transform: translate3d(1px, 0, 0);} 30%, 50%, 70% {transform: translate3d(-2px, 0, 0);} 40%, 60% {transform: translate3d(2px, 0, 0);} }
/*End Notification shake */
.navbar .is-focused .form-control,.navbar .is-focused   .form-control {
    background-image: -webkit-linear-gradient(#92000A,#92000A),-webkit-linear-gradient(#D2D2D2,#D2D2D2);
    background-image: -o-linear-gradient(#92000A,#92000A),-o-linear-gradient(#D2D2D2,#D2D2D2);
    background-image: gradient(#92000A,#92000A),-o-linear-gradient(#D2D2D2,#D2D2D2);
}
.navbar.navbar-primary .navbar-nav>li>a {
    padding-top: 17px;
    padding-bottom: 13px;
    font-weight: 500;
    min-height: 50px;
}
.navbar.navbar-primary .navbar-nav>li>a.navbar-brand {
    padding-top: 12px;
    padding-bottom: 8px;
}
.navbar.navbar-primary  .form-control{
    height: 23px;
}
.navbar.navbar-primary .navbar-form {
    margin-top: 8px;
}
.navbar.navbar-primary .dropdown-menu .divider{
    margin: 0;
}
.notification .dropdown-menu{
    min-width: 250px !important;
}
.notification .dropdown-menu a:hover{
    color: #000;
}
.notification .dropdown-menu span{
    padding-left: 5px;
    padding-right: 5px;
    font-size: 12px;
    display: block;
}
.notification li  a{
    color: #333;
}
.notification li  a:hover{
    color: #1257b7;
}
.notification li:last-child a{
    padding: 11px 16px 2px !important;
}
.notification li:last-child a:hover{
    background-color: transparent;
    color: #777;
}

/* Loading */
.load-wrap {
    width: 100%;
    height: 100vh;
    text-align: center;
    background-color: rgba(255,255,255,0.4);
    position: fixed;
    z-index: 10000;
    top: 0;
    left: 0;
}


.load-wrap .line {
    display: inline-block;
    width: 15px;
    height: 15px;
    border-radius: 15px;
    background-color: #000;
}

.load-3{
    position:fixed;
    top:50%;
    left:50%;
    transform:translate(-50%,-50%);
    z-index: 100000;
}

.load-3 .line:nth-last-child(1) {animation: loadingC .6s .1s linear infinite;}
.load-3 .line:nth-last-child(2) {animation: loadingC .6s .2s linear infinite;}
.load-3 .line:nth-last-child(3) {animation: loadingC .6s .3s linear infinite;}

@keyframes loadingC {
0 {transform: translate(0,0);}
50% {transform: translate(0,15px);}
100% {transform: translate(0,0);}
}

@keyframes popIn {
    0% {
        -webkit-transform: scale(1, 1);
        transform: scale(1, 1);
    }
    25% {
        -webkit-transform: scale(1.2, 1);
        transform: scale(1.2, 1);
    }
    50% {
        -webkit-transform: scale(1.4, 1);
        transform: scale(1.4, 1);
    }
    100% {
        -webkit-transform: scale(1, 1);
        transform: scale(1, 1);
    }
}



/* login_box */

.login_box *{
    color : #FFF;

}

.login_box div h3 span{
    color : #FFF;
    font-size:18px;
}
.login_box .glyphicon{
    font-size: 18px;
}

.login_box div span {
    font-weight: 200;
}

.login_box h1{
    font-weight: 200;
}

.login_box{
    background: #92000A; /* Old browsers */
    position:absolute;
    top: 50%;
    left: 50%;
    max-width: 400px;
    -moz-transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
    -webkit-transform: translate(-50%,-50%);
    transform: translate(-50%,-50%);
    /* IE9 SVG, needs conditional override of 'filter' to 'none' */
    background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMTAwJSIgeDI9IjEwMCUiIHkyPSIwJSI+CiAgICA8c3RvcCBvZmZzZXQ9IjUlIiBzdG9wLWNvbG9yPSIjZjMyZDI3IiBzdG9wLW9wYWNpdHk9IjEiLz4KICAgIDxzdG9wIG9mZnNldD0iOTklIiBzdG9wLWNvbG9yPSIjZmY2YjQ1IiBzdG9wLW9wYWNpdHk9IjEiLz4KICA8L2xpbmVhckdyYWRpZW50PgogIDxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InVybCgjZ3JhZC11Y2dnLWdlbmVyYXRlZCkiIC8+Cjwvc3ZnPg==);
    background: -moz-linear-gradient(45deg,  #92000A 5%, #cc000A 99%); /* FF3.6+ */
    background: -webkit-gradient(linear, left bottom, right top, color-stop(5%,#92000A), color-stop(99%,#cc000A)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* Opera 11.10+ */
    background: linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#92000A', endColorstr='#cc000A',GradientType=1 ); /* IE6-8 fallback on horizontal gradient */
    -webkit-box-shadow: 0px 0px 8px 0px rgba(50, 50, 50, 0.54);
    -moz-box-shadow:    0px 0px 8px 0px rgba(50, 50, 50, 0.54);
    box-shadow:         0px 0px 8px 0px rgba(50, 50, 50, 0.54);
}
.login_box .image-circle{
    border-radius: 50%;
    width: 175px;
    height: 175px;
    border: 4px solid #FFF;
    margin: 10px;
}



.login_box .login_control{
    background-color:#FFF;
    padding:20px 10px;

}
.login_box .form-control{
    color: #000;
}
.login_box .control {
    color:#000;
    margin:10px;
}
.login_box  .glyphicon {
    color:#ccc;
}
.login_box .is-focused label{
    color:#92000A !important;
}
.login_box .is-focused .form-control{
    background-image: -webkit-gradient(linear,left top,left bottom,from(#92000A),to(#CA2F57)),-webkit-gradient(linear,left top,left bottom,from(#92000A),to(#CA2F57));
    background-image: -webkit-linear-gradient(#92000A,#CA2F57),-webkit-linear-gradient(#92000A,#CA2F57);
    background-image: -o-linear-gradient(#92000A,#CA2F57),-o-linear-gradient(#92000A,#CA2F57);
    background-image: linear-gradient(#92000A,#CA2F57),linear-gradient(#92000A,#CA2F57);
}
.login_box .has-success .label {
    color: #4caf50;
}
.login_box .has-success .form-control {
    border-bottom:1px solid #4caf50;
}
.login_box .has-success .glyphicon {
    color:#4caf50;
}


.login_box .has-error .label {
    color: #ff0000;
}

.login_box .has-error .glyphicon {
    color:#ff0000;
}

.login_box .btn-orange{
    background-color: #92000A !important;
    border-radius: 0px;
    color:#fff !important;
    margin: 5px;
    padding: 5px;
    width: 150px;
    font-size: 20px;
    font-weight: inherit;
    background: center center;
}

.login_box .btn-orange:hover {
    background-color: #C8000A !important;
    border-radius: 0px;
    margin: 5px;
    padding: 5px;
    width: 150px;
    font-size: 20px;
    font-weight: inherit;
    color:#FFF !important;
}

.login_box .line{
    border-bottom : 2px solid #fff;
}


.login_box .outter{
    padding: 0px;
    border: 1px solid rgba(255, 255, 255, 0.29);
    border-radius: 50%;
    width: 200px;
    height: 200px;
}
/* Add content Form */
.add-content .select2-container {
    width: 100% !important;
    margin-bottom: 3px;
}
.add-content .form-group label.control-label{
    margin-top: 7px;
}
.add-content .form-group label.control-label{
    text-align: left;
}
.add-content .form-group .form-control {
    margin-bottom: 2px;
}
.add-content  .row > div > .form-group{
    margin-top: 14px !important;
}

/* filter options */
.filter-options .panel{
    box-shadow: none;
    border: 1px solid #ccc
}
.filter-options .control-label{
    min-height: 32px;
    font-size: 12px !important;
}
.filter-options .togglebutton{
    margin-top: 16px;
}
.title-2{
    margin-top: 100px;
    border-top:1px solid #ccc;
    border-bottom:1px solid #ccc;
    padding: 8px;

}
.filter-options .select2-container {
    width: 100% !important;
}
.filter-options .form-group label.control-label{
    margin-top: 7px;
}
.filter-options .form-group label.control-label{
    text-align: left;
}

.btn{
    text-transform: capitalize !important;
}
.content  .checkbox label,.content .form-group .radio label,.content .form-group label {
    color: #333
}
.form-actions-container{
    border-top: 2px solid #eee;
    margin-top: 48px
}


/* menu toggle */
.panel-default [aria-expanded="true"] .glyphicon-menu-down:before {
    content: "\e260"
}

/*.light_grey {*/
/*    background-color: #f5f5f5 !important;*/
/*    color: #555 !important;*/
/*}*/
.blue{
    color:#428fdf !important;
}
.green{
    color:#adce66;
}

.gray{
    color:#353A4B !important;
}

.text-center{
    text-align: center ;
}
.clear{
    clear: both;
}
.size_12{
    font-size: 12px;
}
.size_15{
    font-size: 15px;
}
.size_20{
    font-size: 20px;
}
.size_25{
    font-size: 25px;
}
.size_40{
    font-size: 40px;
}
.white{
    color:#fff;
}
.black{
    color:#000;
}
.brown{
    color: #655152;
}

.gray_222{
    color:#222;
}
.circle{
    border-radius: 50%;
    padding: 2px;
    border: 2px solid #97a7bc;
}
.circle_2{
    border-radius: 50%;
    border: .1rem solid #ece8e4;
    background-color: #ece8e4;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.width_smaller{
    width: 30%;
}
.width_25{
    width: 25%;
}

.width_50{
    width: 50%;
}

.width_75{
    width: 75%;
}
.width_95{
    width: 95%;
}
.width_100{
    width: 100%;
}
.height_100{
    height: 100%;
}
.margin_center{
    margin: 0 auto;
}

.vertical_middle{
    vertical-align: middle;
}
.table_1{
    display: table;
    margin: 0 auto;
    text-align: center;
}
.table_row{
    display: table-row;
    margin: 0 auto;
    text-align: left;
}
.table_cell{
    display: table-cell;
}
.uppercase{
    text-transform: uppercase;
}


.background_gray{
    background-color: #3d4040;
}
.background_f6f6f6{
    background-color: #f6f6f6;
}
.inline-block{
    display: inline-block !important;
}

.background_red{
    background: #c0a16b !important;
}
.background_green{
    background-color: #72ba26;
}
.background_white{
    background-color: #fff;
}

a{
    text-decoration: none !important;
}
.relative{
    position: relative;
}
.navbar-nav > li.relative{
    float: left;
}
.absolute{
    position: absolute;
}
.bold{
    font-weight: bold !important;
}

.star{
    color: #ff0000;
}
/* progress bar */
.slider-progress {
    width: 100%;
    height: 2px;
    background: #a5a5a5;
}
.slider-progress .progress {
    width: 0;
    height: 2px;
    background: #EA2227;
}
.grayscale-effect{
    -webkit-filter: grayscale(100%);
    filter: grayscale(100%);
    -webkit-transition: .3s ease-in-out;
    transition: .3s ease-in-out;
}
.grayscale-effect:hover {
    -webkit-filter: grayscale(0);
    filter: grayscale(0);
}
.saturate:hover {-webkit-filter: saturate(3); filter: saturate(3);}
.grayscale:hover {-webkit-filter: grayscale(100%); filter: grayscale(100%);}
.contrast:hover {-webkit-filter: contrast(160%); filter: contrast(160%);}
.brightness:hover {-webkit-filter: brightness(0.75); filter: brightness(0.75);}
.blur:hover {-webkit-filter: blur(1px); filter: blur(1px);}
.invert:hover {-webkit-filter: invert(100%); filter: invert(100%);}
.sepia:hover {-webkit-filter: sepia(100%); filter: sepia(100%);}
.huerotate:hover {-webkit-filter: hue-rotate(15deg); filter: hue-rotate(15deg);}
.opacity:hover {-webkit-filter: opacity(70%); filter: opacity(70%);}
.rtl{
    direction: rtl;
}
.ltr{
    direction: ltr;
}
*{
    outline: none
}
.full-height{
    position: relative;
    min-height: 100%;
    overflow: hidden;
}
.full-height-p{
    position: relative;
    min-height: 500px;
    overflow: hidden;
}
.outer {
    display: table;
    height: 100%;
    width: 100%;
}

.middle {
    display: table-cell;
    vertical-align: middle;
}

.inner {
    margin-left: auto;
    margin-right: auto;

}
.tahoma{
    font-family: tahoma , sans-serif;
}
.gess{
    font-family: gess, sans-serif;
}
.min_width_100{
    width: 105%;
}
.border-top{
    border-top: 2px solid #eee;
}
.bg-overlay-light-alfa10:before{background:rgba(255, 255, 255, 0.1);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa20:before{background:rgba(255, 255, 255, 0.2);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa30:before{background:rgba(255, 255, 255, 0.3);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa40:before{background:rgba(255, 255, 255, 0.4);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa50:before{background:rgba(255, 255, 255, 0.5);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa60:before{background:rgba(255, 255, 255, 0.6);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa70:before{background:rgba(255, 255, 255, 0.7);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa80:before{background:rgba(255, 255, 255, 0.8);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-light-alfa90:before{background:rgba(255, 255, 255, 0.9);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa10:before{background:rgba(22, 22, 22, 0.1);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa20:before{background:rgba(22, 22, 22, 0.2);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa30:before{background:rgba(22, 22, 22, 0.3);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa40:before{background:rgba(22, 22, 22, 0.4);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa50:before{background:rgba(22, 22, 22, 0.5);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa60:before{background:rgba(22, 22, 22, 0.6);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa70:before{background:rgba(22, 22, 22, 0.7);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa80:before{background:rgba(22, 22, 22, 0.8);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-alfa90:before{background:rgba(22, 22, 22, 0.9);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.bg-overlay-dark-hover-alfa30:hover:before{background:rgba(22, 22, 22, 0.3);content: " ";height: 100%;left: 0;position: absolute;top: 0;width: 100%;}
.w3-margin{margin:16px!important 0}.w3-margin-0{margin:0!important}.w3-margin-8{margin:8px!important}
.w3-margin-top{margin-top:16px!important}.w3-margin-bottom{margin-bottom:16px!important}
.w3-margin-left{margin-left:16px!important}.w3-margin-right{margin-right:16px!important}
.w3-section{margin-top:16px!important;margin-bottom:16px!important}
.w3-padding-tiny{padding:2px 4px!important}.w3-padding-small{padding:4px 8px!important}
.w3-padding-medium,.w3-padding,.w3-form{padding:8px 8px!important}
.w3-padding-large{padding:12px 24px!important}.w3-padding-xlarge{padding:16px 32px!important}
.w3-padding-xxlarge{padding:24px 48px!important}.w3-padding-jumbo{padding:32px 64px!important}
.w3-padding-4{padding-top:4px!important;padding-bottom:4px!important}
.w3-padding-4-h{padding-left:4px!important;padding-right:4px!important}
.w3-padding-8{padding-top:8px!important;padding-bottom:8px!important}
.w3-padding-8-h{padding-left:8px!important;padding-right:8px!important}
.w3-padding-12{padding-top:12px!important;padding-bottom:12px!important}
.w3-padding-16{padding-top:16px!important;padding-bottom:16px!important}
.w3-padding-16-h{padding-left:16px!important;padding-right:16px!important}
.w3-padding-16-all{padding:16px!important}
.w3-padding-20{padding:0 20px !important}
.w3-padding-24{padding-top:24px!important;padding-bottom:24px!important}
.w3-padding-32{padding-top:32px!important;padding-bottom:32px!important}
.w3-padding-32-h{padding-left:32px!important;padding-right:32px!important}
.w3-padding-32-all{padding:32px!important}
.w3-padding-48{padding-top:48px!important;padding-bottom:48px!important}
.w3-padding-48-h{padding-left:48px!important;padding-right:48px!important}
.w3-padding-64{padding-top:64px!important;padding-bottom:64px!important}
.w3-padding-128{padding-top:100px!important;padding-bottom:100px!important}
.w3-padding-0{padding:0!important}
.w3-padding-top{padding-top:8px!important}.w3-padding-bottom{padding-bottom:8px!important}
.w3-padding-left{padding-left:16px!important}.w3-padding-right{padding-right:16px!important}
/* ------------------  */
/* Start of colors */

.w3-blue,.w3-hover-blue:hover{color:#fff!important;background-color:#428fdf!important;border-color:#428fdf!important; }
.w3-light-blue,.w3-hover-light-blue:hover{color:#000!important;background-color:#87CEEB!important}
.w3-brown,.w3-hover-brown:hover{color:#fff!important;background-color:#795548!important}
.w3-blue-grey,.w3-hover-blue-grey:hover{color:#fff!important;background-color:#607d8b!important}
.w3-green,.w3-hover-green:hover{color:#fff!important;background-color:#4CAF50!important}
.w3-light-green,.w3-hover-light-green:hover{color:#000!important;background-color:#8bc34a!important}
.w3-lime,.w3-hover-lime:hover{color:#000!important;background-color:#cddc39!important}
.w3-orange,.w3-hover-orange:hover{color:#000!important;background-color:#ff9800!important}
.w3-blaze-orange,.w3-hover-blaze-orange:hover{color:#fff!important;background-color:#FF6600 !important}
.w3-red,.w3-hover-red:hover{color:#fff!important;background-color:#92000A !important}
.w3-sand,.w3-hover-sand:hover{color:#000!important;background-color:#fdf5e6!important}
.w3-teal,.w3-hover-teal:hover{color:#fff!important;background-color:#92000A!important}
.w3-yellow,.w3-hover-yellow:hover{color:#fff!important;background-color:#C4AD14!important}
.w3-white,.w3-hover-white:hover{color:#000!important;background-color:#fff!important}
.w3-black,.w3-hover-black:hover{color:#fff!important;background-color:#000!important}
.w3-grey,.w3-hover-grey:hover{color:#fff!important;background-color:#5c5c5c!important}
.w3-light-grey,.w3-hover-light-grey:hover{color:#000!important;background-color:#ebebeb!important}
.w3-dark-grey,.w3-hover-dark-grey:hover{color:#fff!important;background-color:#333!important}
.w3-text-blue,.w3-hover-text-blue:hover{color:#428fdf!important}
.w3-text-light-blue,.w3-hover-text-light-blue:hover{color:#87CEEB!important}
.w3-text-brown,.w3-hover-text-brown:hover{color:#61391c !important}
.w3-text-cyan,.w3-hover-text-cyan:hover{color:#00bcd4!important}
.w3-text-blue-grey,.w3-hover-text-blue-grey:hover{color:#607d8b!important}
.w3-text-green,.w3-hover-text-green:hover{color:#4CAF50!important}
.w3-text-light-green,.w3-hover-text-light-green:hover{color:#8bc34a!important}
.w3-text-indigo,.w3-hover-text-indigo:hover{color:#3f51b5!important}
.w3-text-khaki,.w3-hover-text-khaki:hover{color:#b4aa50!important}
.w3-text-lime,.w3-hover-text-lime:hover{color:#cddc39!important}
.w3-text-orange,.w3-hover-text-orange:hover{color:#ff9800!important}
.w3-text-deep-orange,.w3-hover-text-deep-orange:hover{color:#ff5722!important}
.w3-text-pink,.w3-hover-text-pink:hover{color:#e91e63!important}
.w3-text-purple,.w3-hover-text-purple:hover{color:#9c27b0!important}
.w3-text-deep-purple,.w3-hover-text-deep-purple:hover{color:#673ab7!important}
.w3-text-red,.w3-hover-text-red:hover{color:#92000A !important}
.w3-text-sand,.w3-hover-text-sand:hover{color:#fdf5e6!important}
.w3-text-teal,.w3-hover-text-teal:hover{color:#92000A!important}
.w3-text-yellow,.w3-hover-text-yellow:hover{color:#c4ad14!important}
.w3-text-white,.w3-hover-text-white:hover{color:#fff!important}
.w3-text-black,.w3-hover-text-black:hover{color:#000!important}
.w3-text-grey,.w3-hover-text-grey:hover{color:#757575!important}
.w3-text-light-grey,.w3-hover-text-light-grey:hover{color:#a3a3a3!important}
.w3-text-dark-grey,.w3-hover-text-dark-grey:hover{color:#3a3a3a!important}
.w3-border-blue,.w3-hover-border-blue:hover{border-color:#2196F3!important}
.w3-border-light-blue,.w3-hover-border-light-blue:hover{border-color:#87CEEB!important}
.w3-border-brown,.w3-hover-border-brown:hover{border-color:#795548!important}
.w3-border-blue-grey,.w3-hover-blue-grey:hover{border-color:#607d8b!important}
.w3-border-green,.w3-hover-border-green:hover{border-color:#4CAF50!important}
.w3-border-light-green,.w3-hover-border-light-green:hover{border-color:#8bc34a!important}
.w3-border-indigo,.w3-hover-border-indigo:hover{border-color:#3f51b5!important}
.w3-border-orange,.w3-hover-border-orange:hover{border-color:#ff9800!important}
.w3-border-deep-orange,.w3-hover-border-deep-orange:hover{border-color:#ff5722!important}
.w3-border-deep-purple,.w3-hover-border-deep-purple:hover{border-color:#673ab7!important}
.w3-border-red,.w3-hover-border-red:hover{border-color:#f44336!important}
.w3-border-sand,.w3-hover-border-sand:hover{border-color:#fdf5e6!important}
.w3-border-teal,.w3-hover-border-teal:hover{border-color:#92000A!important}
.w3-border-yellow,.w3-hover-border-yellow:hover{border-color:#ffeb3b!important}
.w3-border-white,.w3-hover-border-white:hover{border-color:#fff!important}
.w3-border-black,.w3-hover-border-black:hover{border-color:#000!important}
.w3-border-grey,.w3-hover-border-grey:hover{border-color:#9e9e9e!important}
.w3-border-light-grey,.w3-hover-border-light-grey:hover{border-color:#a3a3a3 !important}
.w3-border-dark-grey,.w3-hover-border-dark-grey:hover{border-color:#616161!important}


#notification_loading_icon{
    height: 100%;
    display: block;
    position: absolute;
}
#notification_loading_icon .load-3{
    position: absolute;
}
#main_progress_bar{
    position: fixed;
    top: 0;
    z-index: 99999;
    width: 100%;
    height:3px;
    background: none;
}
#main_progress_bar .progress-bar {
    background-color: #FF6600;
    height: 3px;
    -webkit-transition: width 0.6s ease-in;
    -o-transition: width 0.6s ease-in;
    transition: width 0.6s ease-in;
}
.notransition {
    -webkit-transition: none !important;
    -moz-transition: none !important;
    -o-transition: none !important;
    -ms-transition: none !important;
    transition: none !important;
}
.w3-rest{
    overflow: hidden;
}
.sidebar_1 .item a  .caret{
    border-bottom: 4px solid transparent;
    border-top: 4px solid transparent;
    border-left: 4px solid;
}
.sidebar_1 .item a[aria-expanded="true"] .caret{
    border-top: 4px dashed;
    border-right: 4px solid transparent;
    border-left: 4px solid transparent;
    border-bottom: 0;
    right: 10px;
    top: 23px;
}
.form-group p.form-control,.form-group label.form-control{
    height: auto;
    min-height: 38px;
}
.form-group .checkbox label{
    text-align: left !important;
}

#page-container.readOnlyContent  [disabled],
#single-spa-custom-container .readOnlyContent  [disabled],
#page-container.readOnlyContent  [disabled] *,
#single-spa-custom-container.readOnlyContent  [disabled] *,
.readOnlyContent input:not(.read-only-ignored),
.readOnlyContent input:not(.read-only-ignored),
.readOnlyContent input:not(.read-only-ignored),
.readOnlyContent select:not(.read-only-ignored),
.readOnlyContent textarea:not(.read-only-ignored),
.readOnlyContent button:not(.read-only-ignored),
.readOnlyContent .btn:not(.read-only-ignored),
.readOnlyContent a:not(.read-only-ignored),
.readOnlyContent .select2:not(.read-only-ignored) *,
.readOnlyContent .checkbox:not(.read-only-ignored) *,
.readOnlyContent .dropzone:not(.read-only-ignored) {
    pointer-events:none;
}

#page-container.readOnlyContent .read-only-ignored *,
#single-spa-custom-container.readOnlyContent .read-only-ignored *,
#page-container.readOnlyContent .filter-options *,
#single-spa-custom-container.readOnlyContent .filter-options *,
#page-container.readOnlyContent .row.w3-margin-0 > form * ,
#single-spa-custom-container.readOnlyContent .row.w3-margin-0 > form * ,
#page-container.readOnlyContent .row.w3-margin-0 > div > form *,
#single-spa-custom-container.readOnlyContent .row.w3-margin-0 > div > form *
{
    pointer-events:auto ;
}

.readOnlyContent {
    cursor: not-allowed;
}
.navbar-nav .title_name{
    max-width: 200px;
    white-space:nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: middle
}

.nav > li > a{
    padding-right: 10px;
    padding-left: 10px;
}
/* Generic Search */
#modal-search-all .modal-dialog,#modal-policy .modal-dialog{
    width: 90% ;
}
#modal-search-all mark{
    font-weight: bold;
    color: inherit;
    background-color: yellow;
    padding: .2em 0;
}
body .search-nav{
    outline: none;
}
.search-nav.showArrow:before {
    content:'';
    position: absolute;
    bottom: 0;
    right: 24px;
    display: inline-block;
    width:0;
    height:0;
    border:7px solid transparent;
    vertical-align: middle;
    border-bottom-color: #fff;
}
#accordion_search .panel{
    border:0 none;
    box-shadow:none;
    margin-bottom: 20px;
}
#accordion_search .panel-heading{
    padding:0;
    background: transparent;
}
#accordion_search .panel-title a{
    display: block;
    position: relative;
    background:transparent;
    color:#92000A;
    font-size:14px;
    text-transform:uppercase;
    margin-bottom:15px;
    padding:15px 20px;
    border-bottom:1px solid #92000A;
    border-radius: 0 15px 0 15px;
    transition:all 0.10s linear 0s;
    font-weight: 600;
}
#accordion_search .panel-title a.collapsed{
    color:#808080;
    border-bottom:1px solid #d3d3d3;
    margin: 0;
}
#accordion_search .panel-title a i{
    color:#92000A;
    position: absolute;
    top: 14px;
    left:25px;
}
#accordion_search .panel-title a:before, #accordion_search .panel-title a.collapsed:before{
    content:"";
    position: absolute;
    bottom:-15px;
    left:36px;
    border:7px solid transparent;
    border-top:7px solid #92000A;
}
#accordion_search .panel-title a.collapsed:before{
    content:"";
    border: 0 none;
}
#accordion_search .panel-title a.collapsed:hover{
    color: #6a6060;
}
#accordion_search .panel-title a:after, #accordion_search .panel-title a.collapsed:after{
    content: "\e113";
    font-family: 'Glyphicons Halflings';
    color: #92000A;
    font-size: 15px;
    line-height: 20px;
    position: absolute;
    top: 15px;
    right:25px;
}
#accordion_search .panel-title a.collapsed:after{
    content: "\e114";
    color:#808080;
}
#accordion_search .panel-body {
    border-top: 0 none;
    color: #808080;
}
#accordion_search table tr td { padding-left: 15px;padding-right: 15px }
#accordion_search .table {margin-bottom: 0; }
#accordion_search table td, #accordion_search table th {
    border: none;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}
#accordion_search table td a{
    color: #000;
    transition: .3s ease-out;
    display: block;
    padding: 8px 15px !important;
}

#modal-search-all .wrapper-searchAll .padding_0{
    padding: 0 !important;
}
#modal-search-all .wrapper-searchAll .material-icons{
    font-size: 15px;
    position: relative;
    top: -1px;
}
#modal-search-all .buttons-wrapper{  margin-bottom: 20px;  }
#modal-search-all{
    z-index: 10001
}
#modal-search-all .modal-content,#modal-policy .modal-content{
    margin-top: 50px;
    margin-left: 17px;
    margin-right: 1px;
}
#modal-search-all .close,#modal-policy .close{
    position: absolute;
    right: 20px;
    top: 20px
}
#modal-search-all .modal-body,#modal-policy .modal-body{
    max-height: 400px;
    overflow-y: auto;
}
.DS_avatar, .TM_avatar {
    height: 25px;
    width: 25px;
    position: relative;
    border-radius: 12px;
    top: 13px;
    display: block;
}
.DS_avatar {
    background-color: #a92222;
    margin-right: 8px;
    margin-left: -2px;
}
.TM_avatar {
    background-color: #20a92a;
    cursor: pointer;
}
.DS_avatar i, .TM_avatar i {
    position: absolute;
    top: 53%;
    left: 57%;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    font-style: normal;
    color: #fff;
    transform: translate(-50%, -50%);
}

@-webkit-keyframes pulse {
    to {  opacity: 0;  }
}
@keyframes pulse {
    to {  opacity: 0;  }
}
.DS_avatar::before,
.DS_avatar::after,
.TM_avatar::before,
.TM_avatar::after {
    webkit-animation: pulse 2s linear infinite;
    animation: pulse 2s linear infinite;
    border: #fff solid 3px;
    border-radius: 9999px;
    box-sizing: border-box;
    content: ' ';
    height: 120%;
    left: -10%;
    opacity: .3;
    position: absolute;
    top: -10%;
    -webkit-transform: scale(1);
    transform: scale(1);
    width: 120%;
    z-index: 1;
}
.DS_avatar::after {
    -webkit-animation-delay: 1s;
    animation-delay: 1s;
}
.alert-notification-popup {
    box-shadow: 0 0 20px 3px rgba(0,0,0,.22)!important;
    background: #fff !important;
    color: black;
    padding: 1.286em;
    border-radius: 2px;
}
.alert-notification-popup *[data-notify="icon"]{
    font-size: 1.5em;
    color: #a92222;
}
#file_viewer_modal .modal-dialog{
    margin: 10px auto;
    width:80%;
}
#allow_notificatoins_balloon{
    position: absolute;
    top: 101%;
    z-index: 101;
    right: 1px;
    display: none;
}
#allow_notificatoins_balloon .themeBtn {
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    line-height: 0.8;
    padding: 6px 8px 5px 8px;
    text-transform: capitalize;
    letter-spacing: 0.5px;
    border-radius:0;
    min-width: 220px;
    white-space: nowrap;
    border: 1px solid #ddd;
    -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
    margin-left: 16px;
    position: relative;
}
#allow_notificatoins_balloon .themeBtn:before {
    position: absolute;
    top: -12px;
    right: 22px;
    display: inline-block;
    width: 0;
    height: 0;
    border: 7px solid transparent;
    vertical-align: middle;
    content: " ";
    border-bottom-color: #fff;
}
#allow_notificatoins_balloon .themeBtn11:hover {
    -webkit-box-shadow: 0 6px 12px rgba(170, 170, 170, 0.6);
    box-shadow: 0 10px 25px -2px rgba(170, 170, 170, 0.6);
}
#modal-policy .tree-wrapper .pull-left{
    border-right: 1px solid #eee;
}
#modal-policy .tree-wrapper .pull-left > div{
    max-width: 320px;
    overflow-x: auto
}
#modal-policy .tree-wrapper .w3-rest{
    border-left: 1px solid #eee;
    position: relative;
    left: -1px
}
#modal-policy .tree-wrapper .jstree-default .jstree-themeicon {
    background-position: -106px -69px;
}
.intl-tel-input-custom{
    width: 100%;
}
.intl-tel-input-custom .intl-tel-input.separate-dial-code input {
    padding-left: 92px !important;
}
.intl-tel-input-custom .intl-tel-input.separate-dial-code .selected-flag {
    min-width: 83px !important;
}
.intl-tel-input-custom .intl-tel-input.allow-dropdown .flag-container, .intl-tel-input.separate-dial-code .flag-container {
    z-index: 10;
}
.intl-tel-input-custom .intl-tel-input{
    display: block;
}
.intl-tel-input-custom .form-group{
    margin: 0 ;
}
.intl-tel-input-custom .intl-tel-input.allow-dropdown .flag-container, .intl-tel-input.separate-dial-code .flag-container {
    z-index: 10;
}
.advance-search button.dropdown-toggle, .container-tree-search button.dropdown-toggle {
    position: relative;
    width: 100%;
    box-shadow: none;
    margin: 7px 0 0 0;
    border-radius: 0;
    padding: 5px 20px 5px 8px;
    border: 0 solid #aaa;
}
.advance-search button.dropdown-toggle .caret,.container-tree-search button.dropdown-toggle .caret{
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}
.advance-search .dropdown-menu.menu-options, .container-tree-search .dropdown-menu.menu-options {
    border-radius: 0 !important;
    box-shadow: none;
    margin-top: -1px;
    border: 0 solid #aaa;
}
.mr-2{
    margin-right: 2px;
}
.advance-search .search-criteria-title{
    font-weight: 400;
    margin: 36px 0 24px
}
.advance-search .label__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding:0 10px;
    font-size: 14px;
    display: inline-block;
    position: relative;
}
.advance-search .label__choice .choice__action{
    color: #fff;
    text-align: center;
    font-weight: bold;
    padding: 3px 2px;
    cursor: pointer;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    background: #92000A;
    position: absolute;
    left: -10px;
    top: -10px;
    font-size: 11px;
}
.advance-search .label__choice .glyphicon{
    position: relative;
    top: 2px;
    color: #fff;
}
.advance-search .flex-block{
    min-width: 180px;
    text-align: center;
    padding: 8px;
}
.advance-search .flex-block:last-of-type{
    min-width: auto;
    text-align: center;
    padding: 8px;
}
.advance-search .advance-search-flex{
    display: flex;
    align-items: center;
    justify-content: center
}
@media all and (max-width: 1023px){
    .advance-search .advance-search-flex{
        flex-direction: column;
    }
}
form {
    margin-block-end: 1em;
}
.advance-search button.dropdown-toggle, .container-tree-search button.dropdown-toggle {
    position: relative;
    width: 100%;
    box-shadow: none;
    margin: 7px 0 0 0;
    border-radius: 0;
    padding: 5px 20px 5px 8px;
    border: 0 solid #aaa;
}
.advance-search button.dropdown-toggle .caret,.container-tree-search button.dropdown-toggle .caret{
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}
.advance-search .dropdown-menu.menu-options, .container-tree-search .dropdown-menu.menu-options {
    border-radius: 0 !important;
    box-shadow: none;
    margin-top: -1px;
    border: 0 solid #aaa;
}
.mr-2{
    margin-right: 2px
}
.advance-search .search-criteria-title{
    font-weight: 400;
    margin: 36px 0 24px
}
.advance-search .label__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    margin-right: 16px;
    margin-bottom: 16px;
    padding:0 10px;
    font-size: 14px;
    display: inline-block;
    position: relative;
}
.advance-search .label__choice .choice__action{
    color: #fff;
    text-align: center;
    font-weight: bold;
    padding: 3px 2px;
    cursor: pointer;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    background: #92000A;
    position: absolute;
    left: -10px;
    top: -10px;
    font-size: 11px;
}
.advance-search .label__choice .glyphicon{
    position: relative;
    top: 2px;
    color: #fff;
}
.advance-search .flex-block{
    min-width: 180px;
    text-align: center;
    padding: 8px;
}
.advance-search .flex-block:last-of-type{
    min-width: auto;
    text-align: center;
    padding: 8px;
}
.advance-search .advance-search-flex{
    display: flex;
    align-items: center;
    justify-content: center
}
.container-tree-search .panel-icons-container[aria-expanded=true] .glyphicon-minus:before {
    content: "\2212" !important;
}
.container-tree-search .panel-icons-container[aria-expanded=false] .glyphicon-minus:before {
    content: "\002b" !important;
}
@media all and (max-width: 1023px){
    .advance-search .advance-search-flex{
        flex-direction: column;
    }
}
.container-tree-search .fields-group{
    padding-left: 30px;
}
.container-tree-search .fields-group > * {
    float: left;
    margin-right: 8px;
}
.container-tree-search .fields-group > *:nth-of-type(1){
    min-width: 150px;
}
.container-tree-search .rest{
    margin-right: 0;
    display: block;
    overflow: hidden;
    float: none;
}
@media (max-width: 991px) {
    .navbar-nav .title_name {
        max-width: 55px;
    }

    .navbar-header {
        float: none;
    }
    .navbar-left,.navbar-right {
        float: none !important;
    }
    .navbar-toggle {
        display: block;
    }
    .navbar-collapse {
        border-top: 1px solid transparent;
        box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
    }
    .navbar-fixed-top {
        top: 0;
        border-width: 0 0 1px;
    }
    .navbar-collapse.collapse {
        display: none!important;
    }
    .navbar-collapse.collapse.in{
        display:block !important;
    }
}
@media (max-width: 767px) {
    .container-tree-search{
        padding: 4px !important;
        min-width: auto !important;
    }
    .container-tree-search > .row{
        padding: 15px !important;
    }
    .container-tree-search > .row.collapse{
        padding: 4px !important;
    }
    .container-tree-search > .row > .pull-left{
        float: none !important;
        text-align: center;
        margin: 8px auto 0 !important;
        width: auto !important;
    }
    .container-tree-search > .row > .pull-left .btn{
        width: 130px;
    }
    .container-tree-search > .row > .pull-left:first-of-type{
        margin-bottom: 8px !important;
    }
    .container-tree-search > .row > .pull-left .panel-icon{
        border: 1px solid #aaa;
    }
    .container-tree-search .fields-group{
        padding-left: 0;
        text-align: center;
    }
    .container-tree-search .fields-group > * {
        float: none;
        margin-right: 0;
        width: 100%;
        min-width: inherit;
    }
    .login_box{
        background: #92000A; /* Old browsers */
        /* IE9 SVG, needs conditional override of 'filter' to 'none' */
        background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIgdmlld0JveD0iMCAwIDEgMSIgcHJlc2VydmVBc3BlY3RSYXRpbz0ibm9uZSI+CiAgPGxpbmVhckdyYWRpZW50IGlkPSJncmFkLXVjZ2ctZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMTAwJSIgeDI9IjEwMCUiIHkyPSIwJSI+CiAgICA8c3RvcCBvZmZzZXQ9IjUlIiBzdG9wLWNvbG9yPSIjZjMyZDI3IiBzdG9wLW9wYWNpdHk9IjEiLz4KICAgIDxzdG9wIG9mZnNldD0iOTklIiBzdG9wLWNvbG9yPSIjZmY2YjQ1IiBzdG9wLW9wYWNpdHk9IjEiLz4KICA8L2xpbmVhckdyYWRpZW50PgogIDxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9InVybCgjZ3JhZC11Y2dnLWdlbmVyYXRlZCkiIC8+Cjwvc3ZnPg==);
        background: -moz-linear-gradient(45deg,  #92000A 5%, #cc000A 99%); /* FF3.6+ */
        background: -webkit-gradient(linear, left bottom, right top, color-stop(5%,#92000A), color-stop(99%,#cc000A)); /* Chrome,Safari4+ */
        background: -webkit-linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* Chrome10+,Safari5.1+ */
        background: -o-linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* Opera 11.10+ */
        background: -ms-linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* IE10+ */
        background: linear-gradient(45deg,  #92000A 5%,#cc000A 99%); /* W3C */
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#92000A', endColorstr='#cc000A',GradientType=1 ); /* IE6-8 fallback on horizontal gradient */
        width:90%;
        position:absolute;
        top: 50%;
        left: 50%;
        -moz-transform: translate(-50%,-50%);
        -ms-transform: translate(-50%,-50%);
        -webkit-transform: translate(-50%,-50%);
        transform: translate(-50%,-50%);
        -webkit-box-shadow: 0px 0px 8px 0px rgba(50, 50, 50, 0.54);
        -moz-box-shadow:    0px 0px 8px 0px rgba(50, 50, 50, 0.54);
        box-shadow:         0px 0px 8px 0px rgba(50, 50, 50, 0.54);
    }
    #modal-search-all .modal-dialog,#modal-policy .modal-dialog {
        width: 100%;
        padding-right: 24px;
    }
    #navbar-top .navbar-nav .open .dropdown-menu {
        position: absolute;
        min-width: 160px ;
        float: right;
        margin: 0;
        width: inherit;
        background-color: #fff;
        border: 1px solid #ccc;
        -webkit-box-shadow: none;
        box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
        right: 0 !important;
        left: auto;
    }
    #navbar-top .navbar-nav .dropdown.open .dropdown-menu>li>a {
        color: #333;
        font-size: 16px;
    }
    #navbar-top .container-fluid{
        padding: 0;
    }
    .navbar.navbar-primary .dropdown-menu .dropdown-header {
        background-color: #282828;
    }
    #modal-policy .tree-wrapper .pull-left{
        border:  0;
    }
    #modal-policy .tree-wrapper .w3-rest{
        border: 0;
        position: relative;
        left: 0;
        margin-top: 16px;
    }
    #modal-policy .tree-wrapper .pull-left{
        float: none !important;
        margin: 0 auto;
    }
}
@media (max-width: 429px){
    .nav > li > a {
        padding: 10px 8px;
    }
    #allow_notificatoins_balloon .themeBtn:before{
        right: 14px !important;
    }
    .navbar-brand .logo {
        font-size: 16px;
    }
    .caret-up.open:before {
        right: 16px;
    }
    #navbar-top .help .dropdown-menu {
        min-width: 200px !important;
    }
}
@media (max-width: 350px){
    .navbar-brand .logo {
        font-size: 15px;
    }
}
body table {
    font-weight: normal;
}
form {
    margin-block-end: 1em;
}

.loading-icon-inblock {
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-icon-inblock .load-3-inblock {
    margin: 16px;
}
.text-break {
    word-break: break-all;
}
.pointer {
    cursor: pointer;
}
.update-mode {
    background: #ff5140 !important;
    color: white;
}


/* BG Tasks Chart */
.chart {
    font-family: 'Khula', sans-serif;
    font-weight: 300;
    color: white;
    line-height: 1em;
}

@keyframes expand {
    from {
        width: 0%;
    }

    to {
        width: 100%;
    }
}

@media screen and (min-width: 768px) {
    @keyframes expand {
        from {
            width: 0%;
        }

        to {
            width: calc(100%);
        }
    }
}

.chart {
    overflow: hidden;
    width: 0%;
    animation: expand 1.5s ease forwards;
}

.row+.row .chart {
    animation-delay: .2s;
}

.row+.row+.row .chart {
    animation-delay: .4s;
}

.chart-block {
    display: chart-block;
    height: 50px;
    color: #fff;
    font-size: 1em;
    float: left;
    background-color: #334D5C;
    position: relative;
    overflow: hidden;
    opacity: 1;
    transition: opacity, .3s ease;
    cursor: pointer;
}

.chart-block:nth-of-type(2) {
    background-color: #45B29D;
}

.chart-block:nth-of-type(3) {
    background-color: #EFC94C;
}

.chart-block:nth-of-type(4) {
    background-color: #E27A3F;
}

.chart-block:nth-of-type(5) {
    background-color: #DF5A49;
}

.chart-block:nth-of-type(6) {
    background-color: #962D3E;
}


.chart-block:hover {
    opacity: .65;
}

.chart-value {
    display: block;
    line-height: 1em;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%);
}

@media (min-width: 1000px){
    .modal-lg.modal-bg-chart {
        width: 1000px;
    }
}

@media (max-width: 991px) {
    .modal-bg-chart{
        width: 90%!important;
    }
}
/* Nationalities dropdown*/
.navbar-nav #menu-nationalities-dropdown,.navbar-nav #menu-servers-dropdown{
    position: absolute;
    border: 1px solid #f1f1f1;
    bottom: auto;
    top: 46px;
    background: #fff;
    min-width: 225px;
    box-sizing: border-box !important;
    display: block;
    transform: translateY(-200%);
    /*max-height: 424px;*/
    z-index: 1000;
    opacity: 0;
    left: -25px;
    box-shadow: 0 0 4px 1px rgba(0,0,0,.3);
    min-height: 172px;
}
.navbar-nav #menu-servers-dropdown{
    width: 400px;
 }
.navbar-nav #menu-nationalities-dropdown .load-wrap,.navbar-nav #menu-servers-dropdown .load-wrap {
    height: 100%;
}
.navbar-nav #menu-nationalities-dropdown .overflow-hidden,.navbar-nav #menu-servers-dropdown .overflow-hidden {
    overflow: hidden;
}
.navbar-nav #menu-nationalities-dropdown.active:before,.navbar-nav #menu-servers-dropdown.active:before {
    content: '';
    position: absolute;
    top: -14px;
    left: 36px;
    display: inline-block;
    width: 0;
    height: 0;
    border: 7px solid transparent;
    vertical-align: middle;
    border-bottom-color: #fff;
}
.navbar-nav #menu-nationalities-dropdown.active,.navbar-nav #menu-servers-dropdown.active {
    transition: transform 0.25s, -webkit-transform 0.25s, opacity 0.25s  0.1s;
    transform: translateY(0);
    opacity: 1;
}
.navbar-nav #menu-nationalities-dropdown #menu--nationalities,.navbar-nav #menu-servers-dropdown #menu--servers {
    list-style: none;
    -webkit-margin-before: 0;
    margin-block-start: 0;
    -webkit-margin-after: 0;
    margin-block-end: 0;
    margin: 0 !important;
    -webkit-padding-start: 0;
    padding-inline-start: 0;
    padding: 0 !important;
    border-left: none !important;
}
.navbar-nav #menu-nationalities-dropdown .menu-item,.navbar-nav #menu-servers-dropdown .menu-item {
    line-height: 1.28 !important;
    border-left: none !important;
    position: relative;
    padding-left: 10px;
}
.navbar-nav #menu-nationalities-dropdown .menu-item.active-item,.navbar-nav #menu-servers-dropdown .menu-item.active-item {
    background: #f0f0f0;
}
.navbar-nav #menu-nationalities-dropdown .menu-item.active-item:before{
    content: '✔';
    display: block;
    position: absolute;
    top: 7px;
    right: 10px;
    width: 15px;
    height: 100%;
    color: #333;
}
.navbar-nav #menu-nationalities-dropdown .menu-item.active-item:after,.navbar-nav #menu-servers-dropdown .menu-item.active-item:after {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: #92000A !important;
}
.navbar-nav #menu-nationalities-dropdown .menu-item.not-clickable,.navbar-nav #menu-servers-dropdown .menu-item.not-clickable {
    pointer-events: none;
    font-size: 16px;
}
.navbar-nav #menu-nationalities-dropdown .menu-item .menu-link,.navbar-nav #menu-servers-dropdown .menu-item .menu-link {
    display: -webkit-box;
    display: flex;
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    min-height: 40px;
    height: 40px;
    text-decoration: none !important;
    color: #333 !important;
}
.server-progress-container{
    background: #919191;
    border-radius: 5px;
    z-index: 1;
    font-weight: 500;
    font-size: 1.5rem;
}
.server-progress-container .server-value{
    border-radius: 5px;
    height: 100%;
    z-index: -1;
    position: absolute;
}

/* DOWNLOAD THUMBNAIL  */
.thumbnail-image-container {
    position: relative;
    width: 100%;
    border: 1px solid #aaa;
}
.thumbnail-image-container  img{
    max-width: 100%;
}
.thumbnail-image-container:hover .thumbnail-preview-btn {
    opacity: 1;
}
.thumbnail-image-container .thumbnail-preview-btn {
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    z-index: 1;
    font-size: 25px;
    opacity: 0;
    transition: opacity 0.33s ease-in-out;
    -webkit-transition: opacity 0.33s ease-in-out;
    -moz-transition: opacity 0.33s ease-in-out;
    -ms-transition: opacity 0.33s ease-in-out;
    -o-transition: opacity 0.33s ease-in-out;
}
.thumbnail-image-container .thumbnail-download-btn {
    position: absolute;
    z-index: 2;
    bottom: 2px;
    right: 10px;
    font-size: 20px;
    cursor: pointer;
}
/* FILE-INPUT-CROP */
.no-files-dropzone {
    border: 2px dashed #2b2b2b;
    border-radius: 10px;
    text-align: center;
    padding: 2rem;
}
.browse-files {
    color: #55f;
    text-decoration: underline;
    cursor: pointer;
}
.explore-image-wrapper{
    border: 2px dashed #2b2b2b;
    border-radius: 5px;
}
.explore-image {
    max-width: 20rem;
}
.cropper-container {
    min-height: 20rem;
}
.file-crop-icons {
    font-size: 2.3rem;
    padding: 1rem;
    z-index: 26;
    position: absolute;
    right: 0;
    cursor: pointer;
    opacity: 0.3;
}
.file-crop-icons:hover {
    opacity: 1;
}
.buttons-container{
    display: flex;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
}
.progress-message{
    display: flex;
    align-items: center;
}
.message-container{
    margin-top:20px;
}
@media only screen and (min-width: 800px) {
    #menu--nationalities .menu-item,#menu--servers .menu-item {
        margin-bottom: 1px;
    }
    #menu--nationalities .menu-item .menu-link,#menu--servers .menu-item .menu-link {
        border: none;
        min-height: auto;
        height: auto;
        font-size: 14px;
        color: #fff;
        text-decoration: none;
        -webkit-box-align: center;
        align-items: center;
        outline: none;
    }
}
@media only screen and (min-width: 620px) {
    #menu--nationalities,#menu--servers {
        padding: 2px 0;
    }
}


/* new changes*/
.relative{
    position: relative;
}

/**/
.container-box-close {
    clear: both;
}
.user-status-item-title{
    display: flex;
    align-items: center;
    padding: 5px 10px;
    margin-top: -5px;
}
.user-status-item-title .active-status  , .user-status-item .active-status{
  color:green;
}

.user-status-item-title .offline-status ,.user-status-item .offline-status  {
    color:red;
}
.user-status-item-title{
    display: flex;
    align-items: center;
    padding: 5px 10px;
    margin-top: -5px;
}
.user-status-item-title .active-status  , .user-status-item .active-status{
  color:green;
}

.navbar-info.nav{
    display: flex !important;
    flex-direction: row;
    align-items: center;
}
.user-status-item-title .offline-status ,.user-status-item .offline-status  {
    color:red;
}

.user-status-item-title .away-status ,.user-status-item .away-status {
    color:orange;
}
.user-status-item-title .icon{
    font-size:10px;
    margin-right: 5px;
}
.user-status-item-title .caret{
    margin-left: 4px;
}
.user-status-item >  a{
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}
.dropdown-menu li.user-status-item {
    width: 100%;
    font-size: 14px;
    font-weight: 500;
}
.dropdown-menu li.user-status-item > a{
    width: 100%;
    padding: 12px 16px !important;
}

.user-status-item .selected{
    margin-left: 20px;
    font-weight: lighter;

}
.user-status-item .icon{
    margin-right: 10px;
}
.d-flex{
    display: flex !important;
}

.justify-content-center{
    justify-content: center !important;
}
.login_control .control-label{
    text-align: left !important;
}

.change-password-box .form-group{
    display: flex;
    flex-direction: column;
}

.change-password-box .form-group .control-label{
    width: 100% !important;
    margin-bottom: 5px !important;
}

.change-password-box .form-group .form-input {
    width: 100% !important;
}
.align-items-center {
    align-items: center !important;
}
.justify-content-space-around{
    justify-content:space-around ;
}

.angular-app .cc-grid .mat-table.mat-table-hover .mat-row.colored-link.blinking.blinking-red   {
  background-color: #92000A;
  color: #fff;
}
.angular-app .cc-grid .mat-table.mat-table-hover .mat-row.colored-link.blinking.blinking-red:hover  {
  background-color: #e5e5e5;
  color: #000;
}
.blinking {
  -webkit-animation: 1.5s flashing ease infinite;
  -moz-animation: 1.5s flashing ease infinite;
  -ms-animation: 1.5s flashing ease infinite;
  -o-animation: 1.5s flashing ease infinite;
  animation: 1.5s flashing ease infinite;

}
.blinking a {
  color:inherit !important;
}

@keyframes "flashing" {
  50% {
      background-color: #fff;
      color:#000;
  }
}

@-moz-keyframes "flashing" {
  50% {
      background-color: #fff;
      color:#000;
  }
}

@-webkit-keyframes "flashing" {
  50% {
      background-color: #fff;
      color:#000;
  }
}

@-ms-keyframes "flashing" {
  50% {
      background-color: #fff;
      color:#000;
  }
}

@-o-keyframes "flashing" {
  50% {
      background-color: #fff;
      color:#000;
  }
}
.navbar-info.nav{
    display: flex !important;
    flex-direction: row;
    align-items: center;
}


#chatai-assistant-chat-sidenav{
    position: fixed;
    background-color: white;
    right: 0;
    z-index: 9999;
    top: 55px;
    /* height: calc(100dvh - 55px); */
    height: 100%;
    width: 21rem;
    transition: 200ms ease-in-out;
}
#chatai-assistant-chat-sidenav>iframe{
    height: 100%;
    width: 100%;
}

#chatai-assistant-chat-sidenav.open{}
#chatai-assistant-chat-sidenav.closed{
    /* transform: translateX(100%); */
    margin-right: -600px;
}

.angular-app .container-custom.ai-panel-no-sidebar-padding{
    padding-left:15px !important;
}


#refresh_token_modal.modal {
    position: fixed;
    z-index: 9999999999;
}
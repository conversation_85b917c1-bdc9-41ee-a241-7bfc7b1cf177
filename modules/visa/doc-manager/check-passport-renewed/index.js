mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService,$timeout,magnaValidationService) {
    if ($routeParams.taskPageOptions) {
        const taskPageOptions = JSON.parse($routeParams.taskPageOptions);
        $scope.breadcrumbs = taskPageOptions.breadcrumbs;
        $scope.returnPage = taskPageOptions.returnPage;
        $scope.maidName = taskPageOptions.name;
        $scope.breadcrumbs.push({ label: 'Apply for OEC' });
    } else {
        $scope.breadcrumbs = [{ label: MaidccModules.getModule('visa').label }, { label: 'Apply for OEC' }];
    }
    $scope.maidccService = maidccService;
    $scope.todoID = $routeParams.todoID;
    $scope.model = {
        passportStatus:'',
        passportNumber:'',
        expiryDate:'',
        issuanceDate:'',
        countryOfIssue:'',
        passportPlaceAR:'',
        passportPlaceEN:'',
    };
    $scope.todoDetails = {};
    $scope.countriesOptions = {
        placeholder: "Select Country", width: '100%', data: [], ajax: {
            url: __env.PUBLIC + 'picklist/items/countries',
            data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id, tags: item.tags} }) }; }
        }
    }
    $scope.getTodoDetails = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'customToDo/custom-to-do-info/'+$scope.todoID,
            }, function (response) {
                $timeout(function () {
                    magnaHttpService.getImage(`${__env.PUBLIC}download/${response.passportUuid}`, "#passportImagePreview");
                },0);
            }, { needs_loading_icon: true }
        );
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPage)
            $location.path($scope.returnPage);
        else{
            $scope.goToLink('/visa/doc-manager');
        }
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTodoDetails();
    });

    $scope.save = function () {
        var validObj = $scope.model.passportStatus&&$scope.model.passportStatus=='new'?{
            "model.passportStatus": ["required"],
            "model.passportNumber": ["required"],
            "model.expiryDate": ["required"],
            "model.issuanceDate": ["required"],
            "model.countryOfIssue": ["required"],
            "model.passportPlaceAR": ["required"],
            "model.passportPlaceEN": ["required"],
        }:{
            "model.passportStatus": ["required"],
        };
        if (magnaValidationService.validate($scope, validObj)) {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + `customToDo/complete/${$scope.todoID}/DOC_MANAGER_PASSPORT_RENEWED_TODO` ,
                data:{
                    id:$scope.todoID,
                    samePassportWeHave:$scope.model.passportStatus=='same'?true:false,
                    passportId:$scope.model.passportNumber,
                    passportExpiryDate:$scope.model.expiryDate,
                    passportIssueDate:$scope.model.issuanceDate,
                    countryOfIssue:{id:$scope.model.countryOfIssue},
                    passportPlaceArb:$scope.model.passportPlaceAR,
                    passportPlaceEng:$scope.model.passportPlaceEN,
                }
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
                $scope.goToReturnPage();
            }, { needs_loading_icon: true });
        }
    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPage', url);
        $location.path(destinationUrl);
    }

});

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="container-fluid  add-content">
    <form class="form-horizontal" name="new_applicant_form">
        <div class="row  w3-margin-0">
            <div class="col-sm-12 w3-padding-32-h" >
                <h2 class="w3-margin" >Aramex delivered the package to old client for maid ({{todoDetails.housemaid.label}})</h2>
                <div class="row w3-margin-0">
                    <h4 class="text-danger bold">Old client info</h4>
                    <div class="form-group">
                        <label class="control-label col-md-2">Name:</label>
                        <div class="col-md-10">
                            <p class="form-control" >
                                <a href="#!/client/details/{{todoDetails.oldClient.id}}" target="_blank">{{todoDetails.oldClient.name}}</a>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                            <label class="control-label col-md-2">Address:</label>
                            <div class="col-md-10">
                                <p class="form-control" >{{todoDetails.oldClient.fullAddress}}</p>
                            </div>
                    </div>
                </div>
                <hr>
                <div class="row w3-margin-0">
                    <h4 class="text-danger bold">New client info</h4>
                    <div class="form-group">
                        <label class="control-label col-md-2">Name:</label>
                        <div class="col-md-10">
                            <p class="form-control" >
                                <a href="#!/client/details/{{todoDetails.newClient.id}}" target="_blank">{{todoDetails.newClient.name}}</a>
                            </p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label col-md-2">Address:</label>
                        <div class="col-md-10">
                            <p class="form-control" >{{todoDetails.newClient.fullAddress}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-actions-container w3-margin">
            <div class="form-group">
                <div class="col-sm-6 text-right pull-right  w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button ng-if="returnPage? true : false" type="button" class="btn btn-default btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
                        <button type="submit" class="btn btn-default btn-md btn-raised" ng-click="save()">Done</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
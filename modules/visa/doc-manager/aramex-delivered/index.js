mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService) {
    if ($routeParams.taskPageOptions) {
        const taskPageOptions = JSON.parse($routeParams.taskPageOptions);
        $scope.breadcrumbs = taskPageOptions.breadcrumbs;
        $scope.returnPage = taskPageOptions.returnPage;
        $scope.breadcrumbs.push({ label: 'Aramex delivered the package to old client' });
    } else {
        $scope.breadcrumbs = [{ label: MaidccModules.getModule('visa').label }, { label: 'Aramex delivered the package to old client' }];
    }
    $scope.maidccService = maidccService;
    $scope.todoID = $routeParams.todoID;
    $scope.model = {
        title : '',
        description : '',
        housemaid : {},
        notes : '',
        accomplished : '',
        postponedTo : '',
        managerNotes:[]
    };

    $scope.getTodoDetails = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'customToDo/'+$scope.todoID,
                headers: { 'Content-Type': "application/json" }
            }, function (response) {
                $scope.todoDetails = response;
            }, { needs_loading_icon: true }
        );
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPage)
            $location.path($scope.returnPage);
        else{
            $scope.goToLink('/visa/doc-manager');
        }
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTodoDetails();
    });

    $scope.save = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'customToDo/closeToDo/' + $scope.todoID ,
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
                $scope.goToReturnPage();
            }, { needs_loading_icon: true }
        );

    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPage', url);
        $location.path(destinationUrl);
    }

});
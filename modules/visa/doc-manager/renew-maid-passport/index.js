mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile, maidccService) {
  $scope.page_options = $route.current.$$route.page_options;
  if ($routeParams.taskPageOptions) {
    const taskPageOptions = JSON.parse($routeParams.taskPageOptions);
    $scope.breadcrumbs = taskPageOptions.breadcrumbs;
    $scope.returnPage = taskPageOptions.returnPage;
    $scope.breadcrumbs.push({ label: 'Renew Maid Passport' });
  } else {
    $scope.breadcrumbs = [{ label: MaidccModules.getModule('visa').label }, { label: 'Renew Maid Passport' }];
  }
  $scope.taskId = $routeParams.taskId;
  $scope.todoDetails = {};

    //  for task form please view http://teljoy.io:8085/erp-front-end/magnamedia-core/-/wikis/Documentation/Workflow-Components#task-form
  $scope.obj = { attrsVal: {}, attrsValObj: {}, attrsOpt: {}, postedData: {}, actions: { done: {} }, customActions:[{
      label: 'Maid Informed',
      callbackFunc: function(){
        $scope.maidInformed();
      },
      visiblityCond: true
    }] };

  $scope.$on('$viewContentLoaded', function () {
    $scope.getTodoDetails();
  });

  $scope.$on('onDoneTask', function (evt, data) {
      debugger;
      $scope.goToReturnPage();
  });

    $scope.$on('onFinishAllTasks', function (evt, data) {
        debugger;
        $scope.goToReturnPage();
    });


  $scope.getTodoDetails = function () {
    magnaHttpService.HttpWrapper({
      method: "GET",
      url: __env.VISA + 'customToDo/' + $scope.taskId,
      headers: { 'Content-Type': "application/json" }
    }, function (response) {
      $scope.todoDetails = response;
      $scope.passport = response?.attachments?.find(att => att?.tag === 'passport');
      $scope.passportExtension = response?.attachments?.find(att => att?.tag === 'passport_extension');
    }, { needs_loading_icon: true }
    );
  }

  $scope.comment = '';
  $scope.addComment = function () {
    magnaHttpService.HttpWrapper({
      method: "POST",
      url: __env.VISA + 'customToDo/renew-maid-passport/add-note/' + $scope.taskId,
      data: { text: $scope.comment }
    }, function (response) {
      if (!$scope.todoDetails?.notes) $scope.todoDetails['notes'] = [];
      // if the request success
      // push the new comment in the front better than reloading the page
      $scope.todoDetails?.notes?.push({
        text: $scope.comment,
        user: $rootScope.user?.fullName,
        createDate: new Date()
      });
      $scope.comment = '';
      magnaMainService.DialogBox.showSuccessMsg("Comment Added Successfully");
    }, { needs_loading_icon: true });
  }


  $scope.maidInformed = function () {
    magnaHttpService.HttpWrapper({
      method: "POST",
      url: __env.VISA + 'customToDo/renew-maid-passport/inform-maid/' + $scope.taskId,
      headers: { 'Content-Type': "application/json" }
    }, function (response) {
      magnaMainService.DialogBox.showSuccessMsg("Maid Informed Successfully");
      $scope.goToReturnPage();
    }, { needs_loading_icon: true }
    );
  }

  $scope.goToReturnPage = function () {
    if ($scope.returnPage)
      $location.path($scope.returnPage);
    else {
      $location.path('/visa/doc-manager');
    }
  }

}); // end of controller

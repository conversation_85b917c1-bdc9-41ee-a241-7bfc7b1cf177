<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="container-fluid  add-content">
    <form class="form-horizontal" name="new_applicant_form">
        <div class="row  w3-margin-0">
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="row w3-margin-0">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label col-md-2">Maid Name:</label>
                            <div class="col-md-10">
                                <p class="form-control" >
                                    <a href="#!/housemaid/details/{{todoDetails.housemaid.id}}" target="_blank">{{todoDetails.housemaid.name}}</a>
                                </p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-2">Flight Date:</label>
                            <div class="col-md-10">
                                <p class="form-control" > <span class="col-md-3">From:</span> {{getDateTime(todoDetails.flightDateFrom)}}</p><br>
                                <p class="form-control" >  <span class="col-md-3">To:</span> {{getDateTime(todoDetails.flightDateTo)}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group" ng-show="ticket.uuid" >
                            <label class="control-label col-md-2">Copy of Ticket:</label>
                            <div class="col-md-10">
                                <p class="form-control" >
                                    <a href="javascript:void(0)" class="btn btn-primary" ng-click="downloadFile(ticket.uuid)">
                                        <i class="glyphicon glyphicon-download-alt"></i> Download </a>
                                </p>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-md-2">Maid Facebook Username:</label>
                            <div class="col-md-10">
                                <p class="form-control" >{{todoDetails.housemaidFacebookName}}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row w3-margin-0">
                    <h4 class="bold">Maid's Documents:</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <a href="javascript:void(0)" class="btn btn-primary" ng-show="downloads['passport']" ng-click="downloadFile(downloads['passport'])">
                                <i class="glyphicon glyphicon-download-alt"></i> Passport </a>
                            <a href="javascript:void(0)" class="btn btn-primary" ng-click="DownloadSalaryCertificate()" ng-show="!isFilipinoMaid">
                                <i class="glyphicon glyphicon-download-alt"></i> Salary Certificate </a>
                            <a href="javascript:void(0)" class="btn btn-primary" ng-show="downloads['rVisa']" ng-click="downloadFile(downloads['rVisa'])">
                                <i class="glyphicon glyphicon-download-alt"></i> Residency Visa </a>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="row w3-margin-0">
                    <h4 class="bold">Upload Required Document:</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group col-md-8">
                                <label class="control-label col-md-4">Tasheel Contract from eNetwasal:</label>
                                <div class="col-md-8">
                                    <magna-file-input ng-model="enetwasal" tag="enetwasal" ></magna-file-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-actions-container w3-margin">
            <div class="form-group">
                <div class="w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button ng-if="returnPage? true : false" type="button" class="btn btn-success btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
                        <button type="submit" class="btn btn-success btn-md btn-raised" ng-disabled="!enetwasal.id" ng-click="save()">Submit Request to Princess</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
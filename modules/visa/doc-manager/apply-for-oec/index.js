mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService) {
    if ($routeParams.taskPageOptions) {
        const taskPageOptions = JSON.parse($routeParams.taskPageOptions);
        $scope.breadcrumbs = taskPageOptions.breadcrumbs;
        $scope.returnPage = taskPageOptions.returnPage;
        $scope.breadcrumbs.push({ label: 'Apply for OEC' });
    } else {
        $scope.breadcrumbs = [{ label: MaidccModules.getModule('visa').label }, { label: 'Apply for OEC' }];
    }
    $scope.maidccService = maidccService;
    $scope.todoID = $routeParams.todoID;
    $scope.model = {};
    $scope.todoDetails = {};
    $scope.enetwasal = {};
    $scope.isFilipinoMaid = false;
    $scope.downloads = [];
    $scope.ticket = {};

    $scope.getTodoDetails = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'customToDo/'+$scope.todoID,
                headers: { 'Content-Type': "application/json" }
            }, function (response) {
                $scope.todoDetails = response;
                if ($scope.todoDetails.housemaid.nationality.code == 'philippines' || $scope.todoDetails.housemaid.nationality.code == 'filipino') {
                    $scope.isFilipinoMaid = true;
                }
                $scope.getTicketAttachment();
                $scope.getDocuments();
            }, { needs_loading_icon: true }
        );
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPage)
            $location.path($scope.returnPage);
        else{
            $scope.goToLink('/visa/doc-manager');
        }
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTodoDetails();
    });

    $scope.save = function () {
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'customToDo/sendOECRequest' ,
                data:{
                    id:$scope.todoDetails.id,
                    attachments:[{id:$scope.enetwasal.id}]
                }
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
                $scope.goToReturnPage();
            }, { needs_loading_icon: true }
        );

    }
    $scope.getTicketAttachment = function () {
        $.each($scope.todoDetails.attachments,function (index,item) {
            if(item.tag == 'ticket' ){
                $scope.ticket = item;
            }
        })
    }

    $scope.getDocuments =function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'customToDo/documentsOfOEC/'+ $scope.todoDetails.housemaid.id,
            }, function (response) {
                $scope.downloads = response;
            }, { needs_loading_icon: true }
        );
    }

    $scope.getDate = function (datetime) {
        return (datetime) ? datetime.split(' ')[0] : '';
    };
    $scope.getDateTime = function (datetime) {
        return (datetime) ? moment(datetime, 'YYYY-MM-DD hh:mm:ss').format('YYYY-MM-DD hh:mm A') : '';
    }
    
    $scope.downloadFile = function (uuid) {
        magnaHttpService.downloadFile(__env.PUBLIC + "download/" + uuid);
    }
    $scope.DownloadSalaryCertificate = function () {
        var url = __env.STAFFMGMT + 'extradocs/certificateofemployment/' + $scope.todoDetails.housemaid.id;
        url += $scope.todoDetails.designation ? "?designation=" +$scope.todoDetails.designation.id : "";
        magnaHttpService.downloadFile(url);
    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPage', url);
        $location.path(destinationUrl);
    }

});
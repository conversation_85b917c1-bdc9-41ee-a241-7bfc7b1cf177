mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService,$timeout) {
    $scope.breadcrumbs = [{ label: MaidccModules.getModule('visa').label }, { label: 'Prepare folder for zajel' }];
    $scope.maidccService = maidccService;
    $scope.model = {
        selectedInitial:[],
        selectedRenew:[],
        selectedOEC:[],
    }
    $scope.$on('$viewContentLoaded', function () {
        $scope.getDetails();
    });
    $scope.initalDataGrid = {
        columns: [
            {
                label: "#",
                type: "text",
                valueExp: "$index"
            },
            {
                label: "Select",
                type: "html",
                valueExp: function ($data, $index) {
                    var html = `<div class="checkbox">
                                <label>
                                    <input type="checkbox" ng-model="model.selectedInitial['` + $data.id + `']" >
                                    <span class="checkbox-material"></span>
                                </label>
                            </div>`;
                    return $compile(html)($scope);
                }
            },
            {
                label: "Employees",
                type: "text",
                valueExp: "$data.name"
            },
            {
                label: "Nationality",
                type: "text",
                valueExp: "$data.nationality"
            },
        ],
        data: [],
        actions: []
    };
    $scope.renewalDataGrid = {
        columns: [
            {
                label: "#",
                type: "text",
                valueExp: "$index"
            },
            {
                label: "Select",
                type: "html",
                valueExp: function ($data, $index) {
                    var html = `<div class="checkbox">
                                <label>
                                    <input type="checkbox" ng-model="model.selectedRenew['` + $data.id + `']" >
                                    <span class="checkbox-material"></span>
                                </label>
                            </div>`;
                    return $compile(html)($scope);
                }
            },
            {
                label: "Employees",
                type: "text",
                valueExp: "$data.name"
            },
            {
                label: "Nationality",
                type: "text",
                valueExp: "$data.nationality"
            },
        ],
        data: [],
        actions: []
    };
    $scope.oecDataGrid = {
        columns: [
            {
                label: "#",
                type: "text",
                valueExp: "$index"
            },
            {
                label: "Select",
                type: "html",
                valueExp: function ($data, $index) {
                    var html = `<div class="checkbox">
                                <label>
                                    <input type="checkbox" ng-model="model.selectedOEC['` + $data.id + `']" >
                                    <span class="checkbox-material"></span>
                                </label>
                            </div>`;
                    return $compile(html)($scope);
                }
            },
            {
                label: "Employees",
                type: "text",
                valueExp: "$data.name"
            },
            {
                label: "Nationality",
                type: "text",
                valueExp: "$data.nationality"
            },
        ],
        data: [],
        actions: []
    };

    $scope.getDetails = function () {
        magnaHttpService.HttpWrapper({
                url: __env.VISA + 'zajel/prepar-folder-for-zajel',
            }, function (response) {
                let initialArr=[],renewalArr=[],oecArr = [];
                let initialArrMV=[],renewalArrMV=[],oecArrMV = [];
                $.each(response,function (index,item) {
                        if(item.type == 'NEW') {
                            if (item.housemaidType == "MaidVisa")
                                initialArrMV.push(item);
                            else
                                initialArr.push(item);
                        }
                        if(item.type == 'RENEW') {
                            if (item.housemaidType == "MaidVisa")
                                renewalArrMV.push(item);
                            else
                                renewalArr.push(item);
                        }
                        if(item.type == 'MODIFY') {
                            if (item.housemaidType == "MaidVisa")
                                oecArrMV.push(item);
                            else
                                oecArr.push(item);
                        }
                })
                $scope.initalDataGrid.data = initialArrMV.concat(initialArr);
                $scope.renewalDataGrid.data = renewalArrMV.concat(renewalArr);
                $scope.oecDataGrid.data = oecArrMV.concat(oecArr);
                $scope.checkAllInitial();
                $scope.checkAllRenewal();
                $scope.checkAllOEC();
                $timeout(function () {
                    $.material.checkbox();
                });
            }, { needs_loading_icon: true }
        );
    }
    $scope.isSelectAllInitial = false;
    $scope.checkAllInitial = function () {
        $scope.isSelectAllInitial = !$scope.isSelectAllInitial;
        $.each($scope.initalDataGrid.data,function (index,item) {
            $scope.model.selectedInitial[item.id] = $scope.isSelectAllInitial;
        })
    };
    $scope.isSelectAllRenewal = false;
    $scope.checkAllRenewal = function () {
        $scope.isSelectAllRenewal = !$scope.isSelectAllRenewal;
        $.each($scope.renewalDataGrid.data,function (index,item) {
            $scope.model.selectedRenew[item.id] = $scope.isSelectAllRenewal;
        })
    };
    $scope.isSelectAllOEC = false;
    $scope.checkAllOEC = function () {
        $scope.isSelectAllOEC = !$scope.isSelectAllOEC;
        $.each($scope.oecDataGrid.data,function (index,item) {
            $scope.model.selectedOEC[item.id] = $scope.isSelectAllOEC;
        })
    };

    $scope.printZajel = function () {
        magnaMainService.DialogBox.showConfirmDialog('Are you sure you want to print all selected maids?', function () {
            let selected = [];
            $.each($scope.model.selectedInitial,function (index,item) {
                if(item)
                    selected.push({id:index,"type": "NEW"});
            })
            $.each($scope.model.selectedRenew,function (index,item) {
                if(item)
                    selected.push({id:index,"type": "RENEW"});
            })
            $.each($scope.model.selectedOEC,function (index,item) {
                if(item)
                    selected.push({id:index,"type": "MODIFY"});
            })
            magnaHttpService.HttpWrapper({
                    method: 'POST',
                    url: __env.VISA + 'zajel/print-and-complete-tasks',
                    data:selected
                }, function (response) {
                    if(response.uuid)
                        magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + response.uuid);
                }, { needs_loading_icon: true }
            );
        },'Yes','No');
    }

    $scope.printResidenceForm = function () {
        magnaMainService.DialogBox.showConfirmDialog('Are you sure you want to print all selected maids?', function () {
            let selected = [];
            $.each($scope.model.selectedInitial,function (index,item) {
                if(item)
                    selected.push({id:index,"type": "NEW"});
            })
            $.each($scope.model.selectedRenew,function (index,item) {
                if(item)
                    selected.push({id:index,"type": "RENEW"});
            })
            $.each($scope.model.selectedOEC,function (index,item) {
                if(item)
                    selected.push({id:index,"type": "MODIFY"});
            })
            magnaHttpService.HttpWrapper({
                    method: 'POST',
                    url: __env.VISA + 'zajel/print-residence-form',
                    data:selected
                }, function (response) {
                    if(response.uuid)
                        magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + response.uuid);
                }, { needs_loading_icon: true }
            );
        },'Yes','No');
    }

});

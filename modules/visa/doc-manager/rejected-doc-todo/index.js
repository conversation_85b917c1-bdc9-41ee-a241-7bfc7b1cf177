mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $routeParams, $location, magnaMainService, magnaHttpService, magnaValidationService, maidccService, $timeout, $interval) {
    $scope.breadcrumbs = [{label: MaidccModules.getModule('visa').label},
        {label: 'Maid Document Rejected'}
    ];
    $scope.maidccService = maidccService;
    $scope.todoDetails = {};
    $scope.todoID = $routeParams.id;
    $scope.newFileUploadOptions = {};
    $scope.$on('$viewContentLoaded', function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'rejected-document/custom-todo/' + $scope.todoID,
        }, function (response) {
            $scope.todoDetails = response;
            $scope.newFileUploadOptions = {tag:$scope.todoDetails.documentTag}
        }, {needs_loading_icon: true});
    });

    $scope.returnToSourcePage = function () {
        $location.path('/doc-manager');
    };

    $scope.closeTodo = function () {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + `customToDo/complete/${$scope.todoID}/CC_REJECTED_DOCUMENT` ,
                data:{
                    id:$scope.todoID,
                    attachments:[{id:$scope.todoDetails.newFileUploaded.id}]
                }
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Done Successfully');
                $scope.returnToSourcePage();
            }, { needs_loading_icon: true });
    };

});

mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService) {
    if ($routeParams.taskPageOptions) {
        const taskPageOptions = JSON.parse($routeParams.taskPageOptions);
        $scope.breadcrumbs = taskPageOptions.breadcrumbs;
        $scope.returnPage = taskPageOptions.returnPage;
        $scope.breadcrumbs.push({ label: `Maid's Photo Rejected By ICA` });
    } else {
        $scope.breadcrumbs = [{ label: MaidccModules.getModule('visa').label }, { label: `Maid's Photo Rejected By ICA` }];
    }
    $scope.maidccService = maidccService;
    $scope.todoID = $routeParams.todoID;
    $scope.model = {
        photo:{},
        disableSave:true,
        checkingICAStatus:'',
        icaStandardsLink:'',
    };
    $scope.getParameter = function(code){
        var deferred = $.Deferred();
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.PUBLIC + "parameter?code="+code
        }, function (response) {
            if (response.length == 0)
                magnaMainService.DialogBox.showErrorMsg(`Parameter ${code} Not Found`);
            else{
                deferred.resolve(response[0].value);
            }
        });
        return deferred.promise();
    }
    $scope.getTodoDetails = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'customToDo/'+$scope.todoID,
                headers: { 'Content-Type': "application/json" }
            }, function (response) {
                $scope.todoDetails = response;
                magnaHttpService.HttpWrapper({
                    method: "GET",
                    url: __env.VISA + 'ica/latest-unchecked-uploaded-photo/'+$scope.todoDetails.housemaid.id,
                    headers: { 'Content-Type': "application/json" }
                }, function (response) {
                    $scope.model.photo = response??{}
                    }, { needs_loading_icon: true }
                );
            }, { needs_loading_icon: true }
        );
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPage)
            $location.path($scope.returnPage);
        else{
            $scope.goToLink('/visa/doc-manager');
        }
    }
    $scope.$watch('model.photo',function (newVal) {
        if(newVal&&newVal.id)
            $scope.verify();
        else
            $scope.model.checkingICAStatus='';
    })

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTodoDetails();
        $.when($scope.getParameter('ICA_STANDARDS_URL_LINK_PARAM')).done(function (icaStandardsLink) {
            $scope.model.icaStandardsLink = icaStandardsLink;
        })
    });

    $scope.verify = function(){
        $scope.model.checkingICAStatus = 'checking'
        $scope.model.disableSave = true;
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + 'ica/verify/'+$scope.model.photo.id,
            timeout:300000, 
        }, function (response) {
            $scope.model.disableSave = false;
            $scope.model.checkingICAStatus = 'accepted'
        },{ needs_loading_icon: false,ignore_error_messages: true, error_handler:function(response){
                if(response.status==400)
                    $scope.model.checkingICAStatus = 'rejected'
                else{
                    $scope.uploadPhoto.checkingICAStatus = 'error'
                    $scope.icaErrorMSG = response.data.message;
                }
                $scope.model.disableSave = true;
            }
        });
    }

    $scope.save = function () {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + `ica/${$scope.todoDetails.housemaid.id}/accept-photo/${$scope.model.photo.id}`, 
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
            $scope.goToReturnPage();
        }, { needs_loading_icon: true });
    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPage', url);
        $location.path(destinationUrl);
    }

});

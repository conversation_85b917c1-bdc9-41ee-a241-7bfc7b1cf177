mainApp.registerCtrl("page-ctrl", function ($scope, $httpParamSerializer, $rootScope, $location, magnaMainService, $route, magnaHttpService, magnaValidationService, maidccService, $compile, $timeout) {
    $scope.singleGrid = true;
    $scope.$on('$viewContentLoaded', function () {

    });
    $scope.breadcrumbs = [{label: MaidccModules.getModule('staff_mgmt').label}, {label: 'DOC Manager'}];

    $scope.modules = [
        {
            module: 'VISA',
            processTaskFilter: {
                newRequest: ['Repeat Medical', 'Pending medical certificate approval from DHA', 'With PRO for Urgent Visa Stamping'],
                renewRequest: ['Repeat Medical', 'Pending medical certificate approval from DHA'],
                mohreWorkflow: [],
                cancelRequest: ['Awaiting Cancellation Letter From TAWAFOQ'],
                customWorkflow: ['ARAMEX_PACKAGE_DELIVERED_TO_OLD_CLIENT','APPLY_FOR_OEC','ADD_TAWAFUQ_APPOINTMENT','DOC_MANAGER_PASSPORT_RENEWED_TODO','INFORM_AND_FOLLOW_RENEW_MAID_PASSPORT','PHOTO_REJECTED_BY_ICA','CC_REJECTED_DOCUMENT'],
                // cancelRequest: ['Get The Cancellation Paper from Tasheel', 'Get Cancellation Paper Of Used Pre-approval Work Permit', 'Sign the Cancellation Paper', 'Awaiting Cancellation Letter From TAWAFOQ', 'Abscond In AMER',
                //     'Get Sick Cancellation Paper From Tasheel', 'Upload Sick Immigration Cancellation']
            }
        },
        {
            module: 'STAFFMGMT',
            processTaskFilter: {
                maidTermination: [],
            }
        },
        {
            module: 'COMPLAINTS',
            processTaskFilter: {
                maidVisaCancelledWorkflow: []
            }
        }
    ];

    $scope.rowActions = {
        customWorkflow: {
            ADD_TAWAFUQ_APPOINTMENT: [{label: 'Add', code: 'add'}],
        },
    };

    $scope.addAppointmentForComplaint = function (data) {
        $scope.$evalAsync(function () {
            $scope.todoObj = data;
            console.log(data)
            $('#add-appointment-model').modal('show')
        })
    }

    $scope.handleAction = function (dataObj) {
        switch (dataObj.actionCode) {
            case 'add':
                $scope.addAppointmentForComplaint(dataObj.data);
                break;
            default:
                break;
        }

    }


    $scope.tableHeaders = {
        allHeaders: {
            taskLabel: {
                label: 'To-Do',
                type: 'html',
                sortProp: 'taskLabel',
                value: function ($data) {
                    return content = $(`<a href="javascript:void(0)">${$data.taskLabel}</a>`).click(function () {
                        let data = $data;
                        const params = getQueryStringRouteData(data);
                        switch (data.processKey) {
                            case 'maidTermination':
                                window.open(`#!/staff-mgmt/doc-manager/terminate/${data.housemaid}?${$httpParamSerializer(params)}`, '_blank');
                                break;
                            case 'mohreWorkflow':
                                mohre_view_task(data);
                                break;
                            case 'customWorkflow':
                                if (data.taskKey == 'APPLY_FOR_OEC')
                                    window.open(`#!/visa/doc-manager/apply-for-oec/${data.id}?${$httpParamSerializer(params)}`, '_blank');
                                else{
                                    if(data.taskKey == 'ADD_TAWAFUQ_APPOINTMENT')
                                        $scope.addAppointmentForComplaint(data);
                                    else {
                                        if (data.taskKey == 'INFORM_AND_FOLLOW_RENEW_MAID_PASSPORT')
                                            window.open(`#!/visa/doc-manager/renew-maid-passport/INFORM_AND_FOLLOW_RENEW_MAID_PASSPORT/${data.id}?${$httpParamSerializer(params)}`, '_blank');
                                        else{
                                            if(data.taskKey=='DOC_MANAGER_PASSPORT_RENEWED_TODO')
                                                window.open(`#!/visa/doc-manager/check-passport-renewed/${data.id}?${$httpParamSerializer(params)}`, '_blank');
                                            else if(data.taskKey=='PHOTO_REJECTED_BY_ICA')
                                                window.open(`#!/visa/doc-manager/photo-rejected-ica/${data.id}?${$httpParamSerializer(params)}`, '_blank');
                                            else if(data.taskKey=='CC_REJECTED_DOCUMENT')
                                                window.open(`#!/visa/doc-manager/rejected-doc-todo/${data.id}`, '_blank');
                                            else
                                                window.open(`#!/visa/doc-manager/aramex-delivered/${data.id}?${$httpParamSerializer(params)}`, '_blank');
                                        }
                                    }
                                }
                                break;
                            case 'maidVisaCancelledWorkflow':
                                let fromDocManager = data.workPermitConsumed ? '1' : '0';
                                window.open(`#!/staff-mgmt/maids-manager/todo-page/${data.processKey.substring(0, data.processKey.indexOf('Workflow'))}/${data.id}/${data.taskKey}/${fromDocManager}?${$httpParamSerializer(params)}`, '_blank'); // 1 for isDocManager
                                break;
                            default:
                                let taskType = 'new';
                                if (data.processKey.indexOf('renew') != -1) taskType = 'renewal';
                                if (data.processKey.indexOf('cancel') != -1) taskType = 'cancel';
                                let url = `/visa/gpage/${data.id}/${taskType}/${data.taskKey}`

                                window.open(`#!${url}?${$httpParamSerializer(params)}`, '_blank');
                                break;
                        }
                    });
                }
            },
            name: 'Name',
            nationality: 'Nationality',
            type: 'Type',
            details: 'Details',
            pendingSince: 'Pending Since',
            delay: 'Delay',
            waitingTime: 'Waiting Time'

        },
        shownHeaders: ['taskLabel', 'name', 'nationality', 'type', 'details', 'details', 'pendingSince', 'delay']
    };


    // function viewTask(data) {
    //     let taskType = 'new';
    //     if (data.processKey.indexOf('renew') != -1) taskType = 'renewal';
    //     if (data.processKey.indexOf('cancel') != -1) taskType = 'cancel';
    //     let url = `/visa/gpage/${data.id}/${taskType}/${data.taskKey}`

    //     const params = getQueryStringRouteData();
    //     window.open(`#!${url}?${$httpParamSerializer(params)}`, '_blank');
    // }

    function mohre_view_task(data) {
        switch (data.mohreToDoPurpose) {
            case 'Proceed With MOHRE Complaint':
                $scope.goToLink(`/visa/mohre/complaint-form/${data.id}/${data.complaint.id}`);
                break;
            case 'Approve Confirmation SMS':
                $scope.smsReceived(data.id, data["complaint"].id);
                break;
            case 'Add Appointment':
                $scope.showAddAppointmentModal(data);
                break;
            case 'Set first court date':
                $scope.showFirstCourtDateModal(data);
                break;
            case 'Add meeting info':
                $scope.showAddMeetingInfoModal(data);
                break;
            case 'Add Meeting Decision':
                $scope.showAddCourtModal(data);
                break;
            case 'Add Court Date':
                $scope.showAddCourtModal(data);
                break;
            case 'Add Court Decision':
                $scope.showAddMaidDecisionModal(data);
                break;
            case 'Upload BAN Paper':
                $scope.showUploadBanPaperModal(data);
                break;
        }
    }

    // function refreshTask(data) {
    //     const param = { processKey: data.processKey, taskKey: data.taskKey, module: data.module }
    //     $scope.$broadcast("onRefreshTask", param);
    // }

    function getQueryStringRouteData(data) {
        var breadcrumbUrl = `${$location.path()}${$scope.activeProcess ? '?activeProcess=' + $scope.activeProcess : ''}`;
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + breadcrumbUrl;
        return params = {
            taskPageOptions: JSON.stringify({
                returnPage: url,
                name: data.name,
                breadcrumbs: newBreadCrumbs
            })
        };
    }


    /* START MODALS*/

    $scope.currentRow = {};
    $scope.model = {
        appointmentDate: '',
        appointmentTime: '',
        selectedLocation: '',
        locationOptions: {
            placeholder: "Select Location",
            width: '100%',
            data: [{id: 'MOHRE', text: 'MOHRE'}, {id: 'Tawafuq', text: 'Tawafuq'}],
        }
    };

    $scope.smsReceived = function (mohreTodoId, ComplaintId) {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/smsReceived/' + ComplaintId + '/' + mohreTodoId,
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $route.reload();
            }, {needs_loading_icon: true}
        );
    }

    $scope.showAddAppointmentModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.model.selectedLocation = '';
            $scope.model.complaintNumber = $scope.currentRow.complaint.numberofComplaint;
            $scope.model.appointmentDate = '';
            $scope.model.appointmentTime = '';
            magnaMainService.DialogBox.showModal($("#scheduleAppointment_modal"));
        })
    }

    $scope.submitAddAppointment = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/addAppointment/' + $scope.currentRow.complaint.id + '/' + $scope.currentRow.id + '?appointmentDate=' + $scope.model.appointmentDate + ' ' + $scope.model.appointmentTime + '&location=' + $scope.model.selectedLocation + ($scope.currentRow.complaint.mOHREComplaintStatus == 'Appointment Scheduled' ? ('&complaintNumber=' + $scope.model.complaintNumber) : ''),
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $('#scheduleAppointment_modal').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true}
        );
    }
    $scope.firstCourtDate = {
        firstCourtDate: '',
        courtNumber: '',
        housemaidName: '',
        Announcement: '',
    }
    $scope.showFirstCourtDateModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.firstCourtDate.firstCourtDate = '';
            $scope.firstCourtDate.courtNumber = '';
            $scope.firstCourtDate.Announcement = '';
            $scope.firstCourtDate.housemaidName = $scope.currentRow.complaint['hosemaid name'];
            magnaMainService.DialogBox.showModal($("#firstCourtDate_modal"));
        })
    }

    $scope.submitFirstCourtDate = function () {
        var data = {
            id: $scope.currentRow.complaint.id,
            firstCourtDate: $scope.firstCourtDate.firstCourtDate,
            courtNumber: $scope.firstCourtDate.courtNumber,
        };
        if ($scope.firstCourtDate.Announcement) {
            data.attachments = [{id: $scope.firstCourtDate.Announcement.id}];
        }
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'mohreComplaint/addFirstCourtSessionDate/' + $scope.currentRow.id,
                data: data
            }, function (response) {
                $('#firstCourtDate_modal').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true}
        );
    }

    $scope.courtInfo = {
        decisionOptions: {
            placeholder: "Select Decision",
            width: '100%',
            data: [
                {id: 'POSTPONDED', text: 'Postponded'},
                {id: 'PENDING_COURT', text: 'Pending court decision'},
                {id: 'COURT_CASE_WON', text: 'Court case won'},
                {id: 'COURT_CASE_LOST', text: 'Court case lost'}
            ],
        },
        selectedDecision: '',
        nextCourtDate: '',
        notes: '',
        whatdocumentsdidyouprepareforcourt: '',
        whatisthelawyerstrategytowinthecase: '',
        couldwehaveimprovedourdocuments: '',
    }

    $scope.showAddCourtModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.courtInfo.selectedDecision = '';
            $scope.courtInfo.nextCourtDate = '';
            $scope.courtInfo.notes = '';
            $scope.courtInfo.whatdocumentsdidyouprepareforcourt = '';
            $scope.courtInfo.whatisthelawyerstrategytowinthecase = '';
            $scope.courtInfo.couldwehaveimprovedourdocuments = '';
            magnaMainService.DialogBox.showModal($("#AddCourtInfo_modal"));
        })
    }
    $scope.submitAddCourtInfo = function () {
        var data = {
            resultOfCourt: $scope.courtInfo.notes,
            documentsPrepared: $scope.courtInfo.whatdocumentsdidyouprepareforcourt,
            lawyerStrategy: $scope.courtInfo.whatisthelawyerstrategytowinthecase,
            improvedDocuments: $scope.courtInfo.couldwehaveimprovedourdocuments,
            id: $scope.currentRow.id
        };
        if ($scope.courtInfo.selectedDecision == 'POSTPONDED') {
            data.nextCourtDate = $scope.courtInfo.nextCourtDate + " 00:00:00";
        }
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'mohreComplaint/addCourtDecisionForMaid/' + $scope.currentRow.complaint.id + '?courtStatus=' + $scope.courtInfo.selectedDecision,
                data: data
            }, function (response) {
                $('#AddCourtInfo_modal').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true}
        );
    }

    $scope.CourtDecision = {
        decisionOptions: {
            placeholder: "Select Decision",
            width: '100%',
            data: [{id: 'true', text: 'Case won'}, {id: 'false', text: 'Case lost'}],
        },
        selectedDecision: '',
    }

    $scope.showAddCourtDecisionModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.CourtDecision.selectedDecision = '';
            magnaMainService.DialogBox.showModal($("#addCourtDecision_modal"));
        })
    }
    $scope.submitAddCourtDecision = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/addCourtDecisionForMaid/' + $scope.currentRow.complaint.id + '/' + $scope.currentRow.id + '?decision=' + $scope.CourtDecision.selectedDecision,
            }, function (response) {
                $('#addCourtDecision_modal').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true}
        );
    }
    $scope.meetingInfo = {
        meetingStatus: '',
        notes: '',
        whatDidTheLegalAdvisorSay: '',
        whatDocumentsShouldWeImproveToWinTheCase: '',
        whyIsTheComplaintRaised: '',
    }
    $scope.MaidDecision = {
        decisionOptions: {
            placeholder: "Select Decision",
            width: '100%',
            data: [{id: 'Abscond HM', text: 'Abscond HM'}, {
                id: 'Company won complaint',
                text: 'Company won complaint'
            }, {id: 'Maid will appeal in court', text: 'Maid will appeal in court'}, {
                id: 'Complaint Cancelled',
                text: 'Complaint Cancelled'
            }],
        },
        cancellationReasonsOptions: {
            placeholder: "Select Cancellation Reason",
            data: [],
            ajax: {
                url: __env.PUBLIC + '/picklist/items/Mohre_Complaint_Cancellation_Reasons?page=0&size=50',
                data: function (params) {
                    return {
                        search: params.term ? params.term : ""
                    }
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.label,
                                id: item.id,
                            }
                        })
                    };
                }
            },
            width: "100%"
        },
        selectedDecision: '',
        selectedCancellationReasons: '',
    };

    $scope.showAddMeetingInfoModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.MaidDecision.selectedDecision = '';
            $scope.MaidDecision.selectedCancellationReasons = '';
            $scope.meetingInfo.meetingStatus = 'decision taken';
            $scope.meetingInfo.notes = '';
            $scope.meetingInfo.whatDidTheLegalAdvisorSay = '';
            $scope.meetingInfo.whatDocumentsShouldWeImproveToWinTheCase = '';
            $scope.meetingInfo.whyIsTheComplaintRaised = $scope.currentRow.complaint.reasonOfTermination ? $scope.currentRow.complaint.reasonOfTermination : '';
            $scope.currentModal = "AddMeetingInfo_modal";
            magnaMainService.DialogBox.showModal($("#AddMeetingInfo_modal"));
        })
    }

    $scope.addMeetingDeferred = $.Deferred();
    $scope.addDecisionDeferred = $.Deferred();
    $scope.submitAddMeetingInfo = function () {
        $scope.addMeetingDeferred = $.Deferred();
        $scope.addDecisionDeferred = $.Deferred();
        if ($scope.meetingInfo.meetingStatus == 'decision taken') {
            $scope.AddMaidDecision(function (response) {
                $scope.addDecisionDeferred.resolve(response);
            });
        } else {
            $scope.addDecisionDeferred.resolve({});
        }
        var metting = {
            id: $scope.currentRow.complaint.meeting,
            "meetingStatus": $scope.meetingInfo.meetingStatus,
            "notes": $scope.meetingInfo.notes,
            "whatDidTheLegalAdvisorSay": $scope.meetingInfo.whatDidTheLegalAdvisorSay,
            "whatDocumentsShouldWeImproveToWinTheCase": $scope.meetingInfo.whatDocumentsShouldWeImproveToWinTheCase,
            "whyIsTheComplaintRaised": $scope.meetingInfo.whyIsTheComplaintRaised
        };

        $.when($scope.addDecisionDeferred.promise()).done(function () {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'meeting/addMeetingInfo/' + $scope.currentRow.id,
                data: metting
            }, function (response) {
                $scope.addMeetingDeferred.resolve(response);
            }, {needs_loading_icon: true});
        });
        $.when($scope.addMeetingDeferred.promise(), $scope.addDecisionDeferred.promise()).done(function () {
            $('#AddMeetingInfo_modal').modal('hide');
            $route.reload();
        });
    }

    $scope.showAddMaidDecisionModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.MaidDecision.selectedDecision = '';
            $scope.MaidDecision.selectedCancellationReasons = '';
            $scope.MaidDecision.housemaidName = $scope.currentRow.complaint['hosemaid name'];
            $scope.currentModal = "addDecisionForMaid_modal";
            magnaMainService.DialogBox.showModal($("#addDecisionForMaid_modal"));
        })
    }

    $scope.submitAddMaidDecision = function (callback) {
        $scope.AddMaidDecision(function (response) {
            $('#addDecisionForMaid_modal').modal('hide');
            $route.reload();
        });
    }
    $scope.AddMaidDecision = function (callback) {
        var decision = {
            id: $scope.currentRow.complaint.id,
            mohreComplaintDecisions: $scope.MaidDecision.selectedDecision,
        };
        if ($scope.MaidDecision.selectedDecision == 'Complaint Cancelled') {
            decision.cancellationReasons = {id: $scope.MaidDecision.selectedCancellationReasons};
        }
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + 'mohreComplaint/addDecisionForMaid/' + $scope.currentRow.id,
            data: decision
        }, function (response) {
            callback(response);
        }, {needs_loading_icon: true});
    }
    $scope.sendEmailToPro = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'mohreComplaint/sendEmailtoPRO',
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("Email Sent Successfully");
            $route.reload();
        }, {needs_loading_icon: true});
    }

    // BAN PAPER

    $scope.banPaperFile = {};

    $scope.showUploadBanPaperModal = function (row) {
        $scope.$apply(function () {
            $scope.currentRow = row;
            $scope.banPaperFile = {};
            magnaMainService.DialogBox.showModal($("#uploadBanPaper_modal"));
        })
    }

    $scope.submitBanPaper = function () {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + 'mohreComplaint/uploadBanPaper/' + $scope.currentRow.id,
            data: {
                id: $scope.currentRow.complaint.id,
                attachments: [{id: $scope.banPaperFile.id}]
            }
        }, function (response) {
            $('#uploadBanPaper_modal').modal('hide');
            $route.reload();
        }, {needs_loading_icon: true});

    }

    $scope.goToLink = function (destinationUrl) {
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPageUrl', url);
        $scope.$apply(function () {
            $location.path(destinationUrl);
        })
    }

    $scope.refresh = function () {
        $route.reload();
    };


    $scope.appointment = {
        appointmentDate: '',
        appointmentTime: ''
    }

    $scope.addTawafuq = function () {
        $scope.tawafuqObj = {
            appointmentDate: '',
            complaintNumber: '',
            requestedBy: '',
            housemaid: '',
            housemaidObj: '',
            appointmentSmsReceived: '',
            appointmentTime: '',
            terminationMode: '',
            terminationReason: '',
        }
        $('#add-tawafuq-model').modal('show')
    }

    $scope.addPossibleComplaint = function () {
        $('#add-possible-complaint-model').modal('show')
    }

    $scope.tawafuqObj = {
        appointmentDate: '',
        complaintNumber: '',
        requestedBy: '',
        housemaid: '',
        housemaidObj: '',
        appointmentSmsReceived: '',
        appointmentTime: '',
        terminationMode: '',
        terminationReason: '',
    }
    $scope.terminationModeOptions = {
        placeholder: 'Select Termination Type',
        width: '100%',
        data: [{id:'RESIGNATION',text:'Resigned'},{id:'FIRED',text:'Terminated'},{id:'NON_RENEWAL',text:'Non Renewal'}],
    };
    $scope.terminationReasonOptions = {
        placeholder: 'Select Reason',
        width: '100%',
        data: [],
        ajax:{
            url: __env.PUBLIC + '/picklist/items/housemaid_termination_reasons?page=0&size=50',
            data: function (params) {
                return {
                    search: params.term ? params.term : ""
                };
            },
            processResults: function (data) {
                return {
                    results: $.map(data, function (item) {
                        let tagExist = false;
                        $.each(item.tags,function (index,tag) {
                            if(tag.label == $scope.tawafuqObj.terminationMode){
                                tagExist = true;
                            }
                            if($scope.tawafuqObj.terminationMode=='RESIGNATION'&&tag.label=='QUIT'){
                                tagExist = true;
                            }
                        })
                        if(tagExist){
                            return {
                                text: item.label,
                                id: item.id,
                            };
                        }
                    })
                };
            }
        }
    };

    $scope.$watch('tawafuqObj.terminationMode',function (newVal) {
        $scope.tawafuqObj.terminationReason = '';
    })
    $scope.possibleComplaintObj = {
        housemaid: '',
        maidDemand: '',
        notes: '',
    }

    $scope.housemaidOptions = {
        placeholder: "Select Housemaid",
        width: "100%",
        data: [],
        ajax: {
            url: function (params) {
                return __env.VISA + 'tawafuqComplaint/housemaids';
            },
            data: function (params) {
                return {
                    search: params.term ? params.term : "",
                    page: 0,
                    size: 30,
                }
            },
            processResults: function (data) {
                return {
                    results: $.map(data.content, function (item) {
                        return {
                            text: item.name,
                            id: item.id,
                            modeOfTermination: item.modeOfTermination,
                        };
                    })
                };
            }

        }

    }

    $scope.requestedByOptions = {
        data: [
            {id: 'Maid', text: 'Maid'},
            {id: 'Company', text: 'Company'},
        ],
        placeholder: 'Select Complainer',
        width: '100%'
    }

    $scope.submitAddTawafuq = function () {
        let validation = {
            "tawafuqObj.housemaid": ["required"],
            "tawafuqObj.requestedBy": ["required"],
            "tawafuqObj.appointmentSmsReceived": ["required"],
        }
        if ($scope.tawafuqObj.requestedBy === 'Maid') {
            validation['tawafuqObj.appointmentDate'] = ["required"]
            validation['tawafuqObj.complaintNumber'] = ["required"]
            validation['tawafuqObj.appointmentTime'] = ["required"]
        }
        if (!$scope.tawafuqObj.housemaidObj.modeOfTermination) {
            validation['tawafuqObj.terminationMode'] = ["required"]
            validation['tawafuqObj.terminationReason'] = ["required"]
        }
        if (magnaValidationService.validate($scope, validation)) {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'tawafuqComplaint/addTawafuqComplaint',
                data: {
                    housemaid: {id: +$scope.tawafuqObj.housemaid},
                    requestedBy: $scope.tawafuqObj.requestedBy,
                    appointmentSmsReceived: $scope.tawafuqObj.appointmentSmsReceived === 'yes',
                },
                params: {
                    mode:$scope.tawafuqObj.terminationMode,
                    reason:$scope.tawafuqObj.terminationReason,
                    appointmentDate: $scope.tawafuqObj.appointmentDate + " " + $scope.tawafuqObj.appointmentTime,
                    complaintNumber: $scope.tawafuqObj.complaintNumber,
                }
            }, function (response) {
                $('#add-tawafuq-model').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true});
        }
    }

    $scope.submitAddPossibleComplaint = function () {
        let validation = {
            "possibleComplaintObj.housemaid": ["required"],
            "possibleComplaintObj.maidDemand": ["required"],
        }
        if (magnaValidationService.validate($scope, validation)) {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'tawafuqComplaint/addPossibleComplaint',
                data: {
                    housemaid: {id: +$scope.possibleComplaintObj.housemaid},
                    maidDemand: $scope.possibleComplaintObj.maidDemand,
                    notes: $scope.possibleComplaintObj.notes,
                }
            }, function (response) {
                $('#add-possible-complaint-model').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true});
        }
    }

    $scope.submitAddAppointmentTwafuq = function () {
        console.log($scope.todoObj)
        let validation = {
            "appointment.appointmentDate": ["required"],
            "appointment.appointmentTime": ["required"],
        }
        if (magnaValidationService.validate($scope, validation)) {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'tawafuqComplaint/addAppointment/' + $scope.todoObj.mohreComplaint.id,
                params: {
                    appointmentDate: $scope.appointment.appointmentDate + " " + $scope.appointment.appointmentTime,
                    // complaintNumber: $scope.tawafuqObj.complaintNumber,
                }
            }, function (response) {
                $('#add-tawafuq-model').modal('hide');
                $route.reload();
            }, {needs_loading_icon: true});
        }
    }

    /** Passports with us starts **/
    $scope.showPassportsModal = function () {
        $scope.getSectionsData();
        magnaMainService.DialogBox.showModal($('#passportsWithUs'));
    };
    $scope.getSectionsData = function(){
        $.each($scope.passportSections,function (index,item) {
            item.currentPage = 0;
            item.getData();
        })
    };
    $scope.passportCols = [{
        label: "Select",
        type: "html",
        valueExp: function ($data) {
                var html = `<div class="checkbox">
                                    <label>
                                        <input type="checkbox" ng-model="model.selectedArr[${$data.id}]" >
                                        <span class="checkbox-material"></span>
                                    </label>
                                </div>`;
                return $compile(html)($scope);
            }
        },
        {
            label: "Maid Name",
            type: "text",
            valueExp: function ($data) {
                return $data.name;
            }
        }];
    $scope.passportSections = [
        {
            title:'For Medical',
            code:'FOR_MEDICAL',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_MEDICAL');
                    sec.currentPage = pageNo;
                    $scope.getSectionData(sec);
                }
            }
        },
        {
            title:'For Biometrics',
            code:'FOR_BIOMETRICS',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_BIOMETRICS');
                    sec.currentPage = pageNo;
                    $scope.getSectionData(sec);
                }
            }
        },
        {
            title:'For Visa Stamping',
            code:'FOR_ZAJEL',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_ZAJEL');
                    sec.currentPage = pageNo
                    $scope.getSectionData(sec)
                }
            }
        },
        {
            title:'For Delivery',
            code:'FOR_DELIVERY',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_DELIVERY');
                    sec.currentPage = pageNo
                    $scope.getSectionData(sec);
                }
            }
        },
        {
            title:'Others',
            code:'OTHERS',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('OTHERS');
                    sec.currentPage = pageNo
                    $scope.getSectionData(sec);
                }
            }
        },
    ];

    $scope.model = {
        searchPassports:'',
        selectedArr:[],
        receivePassportHousemaid:''
    };

    $scope.getSelectedCount = function(){
        let selected = $.map($scope.model.selectedArr,function (item,index) {
            if(item)
                return index
        })
        return selected.length;
    };
    $scope.getSectionByCode = function(code){
        let sec = {};
        $.each($scope.passportSections,function (index,item){
            if(item.code == code)
                sec = item;
        })
        return sec;
    };
    $scope.getSectionData = function(section){
        magnaHttpService.HttpWrapper({
                url: __env.VISA +  'passport-location/list-passport-with-us-maids',
                params:{
                    section:section.code,
                    housemaidName:$scope.model.searchPassports,
                    page:section.currentPage,
                    size:__env.DATAGRID_PAGE_SIZE,
                }
            }, function (response) {
                section.grid.data = response.content;
                section.gridPagination.paginationInfo = response;
                $timeout(function () {
                    $.material.checkbox();
                });
            }, { needs_loading_icon: true }
        );
    };

    $scope.searchPassports = function(){
        $.each($scope.passportSections,function (index,item) {
            item.getData();
        })
    };

    $scope.ReceivePassportHousemaidOptions = {
        placeholder: "Select Housemaid",
        width: '100%',
        ajax: {
            url: __env.VISA +  'passport-location/list-not-passport-with-us-maids',
            data: function (params) {
                return {
                    housemaidName: params.term ? params.term : "",
                }
            },
            processResults: function (data) {
                return {
                    results: $.map(data.content, function (item) {
                        return {
                            text: item.name,
                            id: item.id,
                        }
                    })
                };
            }
        }
    };

    $scope.handAPassport = function(){
        magnaMainService.DialogBox.showWarningMsg(`Are you sure you want to hand this passport?`, () => {
            let selected = $.map($scope.model.selectedArr,function (item,index) {
                if(item)
                    return index
            })
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA +  'passport-location/change-passport-location',
                params:{
                    passportLocation:'with_maid',
                },
                data:selected
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Done Successfully')
                $scope.model.selectedArr = [];
                $scope.getSectionsData();
            }, { needs_loading_icon: true });
        });
    }

    $scope.showReceiveAPassport = function(){
        $scope.model.receivePassportHousemaid = '';
        magnaMainService.DialogBox.showModal($('#receiveAPassportModal'));
    }

    $scope.receiveAPassport = function(){
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA +  'passport-location/change-passport-location',
            params:{
                passportLocation:'with_us',
            },
            data:[$scope.model.receivePassportHousemaid]
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Done Successfully')
            $('#receiveAPassportModal').modal('hide');
            $scope.searchPassports();
        }, { needs_loading_icon: true });

    }

    /** Passports with us ends **/

});

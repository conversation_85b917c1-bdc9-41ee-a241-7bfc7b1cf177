mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, maidccService, magnaValidationService,
                                            magnaMainService, magnaHttpService, __env, $window, $compile, $routeParams, $location,$filter) {
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    var breadcrumbs = magnaMainService.RouteData.getStoredData('breadcrumb');
    if (!breadcrumbs)
        breadcrumbs = [
            {
                label: MaidccModules.getModule('visa').label
            }
        ];
    $scope.breadcrumbs = breadcrumbs;
    $scope.breadcrumbs.push({ label: 'Edit Message' });
    $scope.model = {
        id: $routeParams.messageId ? $routeParams.messageId : 0,
        selectedVariables:[],
        remindEvery:0,
        maxReminders:0,
        messageText:'',
        createWorkOrder:false,
        createWorkOrderAtLastReminder:false,
        templateOrder:0
    };
    $scope.model.variablesOptions = {
        multiple: true,
        width: '100%',
        placeholder: "Select Variables an Links",
        data: []
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getSmsDetails($scope.model.id);
    });
    $scope.eventFired = 0;

    $scope.$watch("model.selectedVariables",function (newVal, oldVal){
        $scope.eventFired = $scope.eventFired +1;
        if($scope.eventFired > 2) {
            if (newVal.length || oldVal.length) {
                if (newVal.length > oldVal.length)
                    $scope.model.messageText += ' ' + newVal[newVal.length - 1] + ' ';
                else {
                    /*$.each(oldVal,function (index,item){
                        console.log(item);
                        var exist = false;
                        $.each(newVal,function (i,item1){
                            if(item==item1){
                                exist = true;
                            }
                        })
                        if(!exist){
                            $scope.model.messageText = $scope.model.messageText.replace(item,'');
                        }
                    })*/
                }
            }
        }
    })

    $scope.getSmsDetails = function (id) {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + "messageTemplate/"+id,
        }, function (response) {
            $scope.model.remindEvery = response.reminder;
            $scope.model.maxReminders = response.maxReminders;
            $scope.model.messageText = response.smsText;
            $scope.model.createWorkOrder = response.createWorkOrder;
            $scope.model.createWorkOrderAtLastReminder = response.createWorkOrderAtLastReminder;
            $scope.model.templateOrder = response.templateOrder;
            $scope.getVariablesAndLinks(response.variablesAndLinks);
        }, { needs_loading_icon: true });
    }

    $scope.getVariablesAndLinks = function (selectedVal) {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + "messageTemplate/getVariablesAndLinks/",
        }, function (response) {
            $scope.model.variablesOptions.data = $.map(response, function (item) {
                return {
                    text: item,
                    id: item,
                }
            });
            $scope.model.selectedVariables = selectedVal?selectedVal:[];
        }, { needs_loading_icon: true });
    }

    $scope.save = function () {
        var validate_obj = {
            "model.selectedVariables": ["required"],
            "model.remindEvery": ["number"],
            "model.maxReminders": ["number"],
        };
        if(magnaValidationService.validate($scope, validate_obj)) {
            $scope.saveOperator();
        }
    }
    $scope.saveOperator = function () {
        var data = {
            id:$scope.model.id,
            smsText:$scope.model.messageText,
            reminder:$scope.model.remindEvery,
            maxReminders:$scope.model.maxReminders,
            variablesAndLinks:$scope.model.selectedVariables,
            createWorkOrder:$scope.model.createWorkOrder,
            createWorkOrderAtLastReminder:$scope.model.maxReminders>0?$scope.model.createWorkOrderAtLastReminder:false,
            templateOrder:$scope.model.templateOrder,
        }
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + "messageTemplate/update",
            data: data,
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("SMS Updated Successfully");
            $scope.goToReturnPage();
        }, { needs_loading_icon: true });
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPageUrl)
            $location.path($scope.returnPageUrl);
    }



});

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="container-fluid  add-content">
    <form class="form-horizontal" name="new_applicant_form">
        <div class="row  w3-margin-0">
            <div class="col-sm-8 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-2 required-label">Text Message:</label>
                    <div class="col-md-10">
                        <textarea rows="3" style="width: 100%" ng-model="model.messageText" ng-class="model.messageText.length>500?'text-danger':''"></textarea>
                        {{model.messageText.length}}/500
                    </div>
                </div>
            </div>
            <div class="col-sm-4 w3-padding-32-h" >
                <div class="form-group" >
                    <div class="col-md-12">
                        <magna-select-input options="model.variablesOptions" ng-model="model.selectedVariables" ></magna-select-input>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-2">Remind Every:</label>
                    <div class="col-md-8">
                        <magna-input-amount ng-model="model.remindEvery" ></magna-input-amount>
                    </div>
                    Hours
                </div>
            </div>
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-2">Max Number of Reminders:</label>
                    <div class="col-md-8">
                        <magna-input-amount ng-model="model.maxReminders" ></magna-input-amount>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-2">Order Number:</label>
                    <div class="col-md-8">
                        <magna-input-amount ng-model="model.templateOrder" ></magna-input-amount>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group" ng-show="!model.maxReminders > 0" >
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" ng-model="model.createWorkOrder" >
                            <span class="checkbox-material"></span> Create a work order in MaidVisa Follow-up Screen
                        </label>
                    </div>
                </div>
                <div class="form-group" ng-show="model.maxReminders > 0" >
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" ng-model="model.createWorkOrder" >
                            <span class="checkbox-material"></span> Create a work order in MaidVisa Follow-up Screen With Each Reminder
                        </label>
                    </div>
                </div>
                <div class="form-group" ng-show="model.maxReminders > 0" >
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" ng-model="model.createWorkOrderAtLastReminder" >
                            <span class="checkbox-material"></span> Create a work order in MaidVisa Follow-up Screen At Last Reminder Only
                        </label>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-actions-container">
            <div class="form-group">
                <div class="col-sm-6 text-right pull-right  w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button ng-if="returnPageUrl? true : false" type="button" class="btn btn-default  btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
                        <button type="submit" class="btn btn-default btn-md btn-raised" ng-click="save()">Update</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

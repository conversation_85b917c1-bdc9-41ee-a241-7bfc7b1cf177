mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, maidccService, magnaValidationService,
                                            magnaMainService, magnaHttpService, __env, $window, $compile, $routeParams, $location,$filter) {

    $scope.breadcrumbs = [
        { label: MaidccModules.getModule('visa').label }, { label: 'Visa documents setup' }
    ];

    $scope.search = {};

    $scope.getTableData = function (page) {
        let params = {
            page,
            size: __env.DATAGRID_PAGE_SIZE,
        };
        if($scope.search.documentTag) params['documentTag'] = $scope.search.documentTag;
        if($scope.search.nationality) params['nationality'] = $scope.search.nationality;
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'required-document-config/all',
                params,
            }, function (response) {
                $scope.mainDataGrid.data = response.content;
                $scope.mainDataGridPagination.paginationInfo = response;
            }, {needs_loading_icon: true}
        );
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: "Document",
                type: "text",
                valueExp: "$data['documentName']",
            },
            {
                label: "Nationality",
                type: "text",
                valueExp: function ($data) {
                    let col = '';
                    if($data.nationalities && $data.nationalities.length) {
                        col = $data.nationalities?.reduce((acc, curr) => (acc + curr.label + ' /'), '');
                        col = col.slice(0, -1);
                    }
                    return col;
                },
            }, {
                label: "Gender",
                type: "text",
                valueExp: "$data['gender']",
            }, {
                label: "Document need",
                type: "text",
                valueExp: function ($data) {
                    return $data.required ? 'Required' : 'On demand';
                },
            }
        ],
        data: [],
        actions: [
            {
                label: 'Edit',
                callbackFunc: function ($data) {
                    $scope.$apply(function () { $location.path('/visa/documents-setup/' + $data['id']); });
                },
                visiblityCond: 'true',
                htmlAttributes: { class: 'btn-success' }
            }, {
                label: 'Delete',
                callbackFunc: function ($data) {
                    $scope.id = $data['id'];
                    magnaMainService.DialogBox.showWarningMsg('Are you sure you want to delete the document ' + $data.documentName + ' ?', function () {
                        $scope.deleteOperation();
                    });
                },
                visiblityCond: 'true',
                htmlAttributes: { class: 'btn-danger' }
            }
        ]
    };

    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    };

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
        $scope.getDocumentTypesOptions();
    });

    $scope.deleteOperation = function() {
        magnaHttpService.HttpWrapper({
                method: "DELETE",
                url: __env.VISA + 'required-document-config/delete/' + $scope.id,
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Deleted Successfully ');
                $scope.getTableData(0);
            }, {needs_loading_icon: true}
        );
    };

    $scope.addNewSetup = function() {
         $location.path('/visa/documents-setup/0');
    };

    $scope.documentsOptions = {
        placeholder: "Select Document", width: '100%', data: []
    };

    $scope.getDocumentTypesOptions = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'required-document-config/document-tags/get?non-configured-documents-only=false',
        }, function (response) {
            $scope.documentsOptions.data = response?.map(_ => ({id: _.tag, text: _.label}));
        }, {needs_loading_icon: true});
    };

    $scope.nationalitiesOptions = {
        placeholder: "Select nationality", width: '100%', data: [],
         ajax: {
            url: __env.PUBLIC + 'picklist/items/nationalities?page=0&size=100',
            data: function (params) { return { search: params.term ? params.term : "" } },
            processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.code, code:item.code } }) }; }
        }
    };

});

<style>
    th { background-color: #d3d3f3; }

    /* Fix for Material Design checkboxes */
    .checkbox input[type="checkbox"] {
        opacity: 0; /* Keep opacity 0 to maintain Material Design styling */
        position: absolute;
        margin: 0;
        z-index: 1; /* Increase z-index to make it clickable */
        width: 20px;
        height: 20px;
        overflow: visible;
        left: 0;
        pointer-events: auto !important; /* Ensure clicks are registered */
    }

    /* Ensure the checkbox-material span is properly displayed */
    .checkbox .checkbox-material {
        vertical-align: middle;
        position: relative;
        top: 3px;
        display: inline-block;
    }

    /* Make the check visible when checked */
    .checkbox .checkbox-material .check {
        position: relative;
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 2px solid #555555; /* Light black border by default */
        border-radius: 2px;
        overflow: hidden;
        z-index: 0;
    }

    /* Style for checked checkboxes */
    .checkbox input[type="checkbox"]:checked + .checkbox-material .check {
        border-color: #ff0000; /* Red border when checked */
    }

    .checkbox input[type="checkbox"]:checked + .checkbox-material .check:before {
        color: #ff0000; /* Red checkmark */
        box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
    }

    /* Fix for checkboxes inside ng-if */
    [ng-if] .checkbox input[type="checkbox"]:checked + .checkbox-material .check:before {
        color: #ff0000; /* Red checkmark */
        box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
    }

    /* Special styling for enhanced checkboxes */
    .enhanced-checkbox input[type="checkbox"] {
        opacity: 0;
        position: absolute;
        z-index: 2;
        width: 20px;
        height: 20px;
        margin: 0;
        cursor: pointer;
    }

    .enhanced-checkbox .checkbox-material .check {
        border: 2px solid #555555; /* Light black border by default */
    }

    .enhanced-checkbox input[type="checkbox"]:checked + .checkbox-material .check {
        border-color: #ff0000; /* Red border when checked */
    }

    .enhanced-checkbox input[type="checkbox"]:checked + .checkbox-material .check:before {
        color: #ff0000; /* Red checkmark */
        box-shadow: 0 0 0 10px, 10px -10px 0 10px, 32px 0 0 20px, 0px 32px 0 20px, -5px 5px 0 10px, 20px -12px 0 11px;
    }
</style>

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>

<div class="panel-group">
    <form>
        <div class="panel panel-default light_grey">
            <div class="panel-heading">
                <div class="row w3-margin-0">
                    <div class="col-sm-9">
                        <div class="bold">
                            <i class="glyphicon glyphicon-info-sign"></i> {{id > 0?'Edit':'Add new'}} document setup
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-body">
                <fieldset>
                    <div class="row  w3-margin-0">
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label required-label col-md-3">Document:</label>
                                <div class="col-md-6">
                                    <div class="input-group">
                                        <magna-select-input ng-model="model.documentTag" ng-model-obj="model.documentTagItem" options="documentsOptions"></magna-select-input>
                                        <span class="input-group-btn">
                                        </span>
                                    </div>
                            </div>
                            </div>
                        </div>
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label required-label col-md-3">Document Name:</label>
                                <div class="col-md-6">
                                    <input class="form-control" ng-model="model.documentName"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label col-md-3">Nationality:</label>
                                <div class="col-md-6">
                                    <magna-select-input ng-model="model.nationalities" options="nationalitiesOptions"></magna-select-input>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label col-md-3 required-label">Gender:</label>
                                <div class="col-md-6">
                                    <magna-select-input ng-model="model.gender" options="GenderOptions"></magna-select-input>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label col-md-3">Age:</label>
                                <label class="control-label col-md-1">From:</label>
                                <div class="col-md-1">
                                    <input type="number" class="form-control" ng-model="model.minAge" ng-change="ageChanges()"/>
                                </div>
                                <label class="control-label col-md-1">To:</label>
                                <div class="col-md-1">
                                    <input type="number" class="form-control" ng-model="model.maxAge" ng-change="ageChanges()"/>
                                </div>
                                <div class="col-md-2">
                                    <div class="checkbox">
                                        <label>
                                            <input name="inactive" ng-model="model.allAges" type="checkbox" ng-change="allAgesChanges()">
                                            <span class="checkbox-material"></span> All
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label col-md-3">Job:</label>
                                <div class="col-md-6">
                                    <magna-select-input ng-model="model.jobs" options="jobsOptions"></magna-select-input>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label col-md-3">Maid Type:</label>
                                <div class="col-md-6">
                                    <magna-select-input ng-model="model.maidTypes" options="maidTypeOptions"></magna-select-input>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label col-md-3"></label>
                                <div class="col-md-9">
                                    <div class="row">
                                        <div class="form-group" ng-if="imageEnhance">
                                        <div class="col-md-4">
                                            <div class="checkbox enhanced-checkbox">
                                                <label>
                                                    <input name="enhanceDocument" ng-model="model.enhanceDocument" type="checkbox">
                                                    <span class="checkbox-material"><span class="check"></span></span> Enhance the document
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="checkbox enhanced-checkbox">
                                                <label>
                                                    <input name="aiEnhancer" ng-model="model.aiEnhancer" type="checkbox">
                                                    <span class="checkbox-material"><span class="check"></span></span> AI Enhancer
                                                </label>
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-sm-12 w3-padding-32-h">
                            <div class="form-group">
                                <label class="control-label required-label col-md-3">Document need:</label>
                                <div class="col-md-2">
                                    <label style="padding-top: 10px">
                                        <input type="radio" ng-model="model.required" value="true">
                                        Required
                                    </label>
                                </div>
                                <div class="col-md-2">
                                    <label style="padding-top: 10px">
                                        <input type="radio" ng-model="model.required" value="false">
                                        On demand
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row w3-margin-0">
                        <div class="col-sm-9 col-sm-offset-2 w3-padding-32">
                            <h4 class="bold">Rejection reasons:</h4>
                        </div>
                        <div class="col-sm-6 col-sm-offset-3">
                            <table class="table table-striped table-bordered table-hover">
                                <thead class="thead-light">
                                    <tr>
                                        <th scope="col">Rejection reason</th>
                                        <th>Instructions</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="reason in model.rejectionReasons track by $index">
                                        <td>{{reason.reason.name}}</td>
                                        <td>{{reason.instructions}}</td>
                                        <td>
                                            <button class="btn" ng-click="editReason($index)"><i class="glyphicon glyphicon-edit"></i></button>
                                            <button class="btn" ng-click="deleteReason($index)"><i class="glyphicon glyphicon-trash text-danger"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <magna-select-input options="rejectionReasonOptions" ng-model="tempReasonId" ng-model-obj="tempReason.reason"></magna-select-input>
                                        </td>
                                        <td>
                                            <input class="form-control" ng-model="tempReason.instructions" />
                                        </td>
                                        <td>
                                            <button ng-if="!tempReason.editing" ng-disabled="!(tempReason.reason&&tempReason.instructions)" class="btn" ng-click="addReason()"><i class="glyphicon glyphicon-plus text-success"></i></button>
                                            <button ng-if="tempReason.editing" ng-disabled="!(tempReason.reason&&tempReason.instructions)" class="btn" ng-click="saveReason()"><i class="glyphicon glyphicon-floppy-disk text-success"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>


                    <div class="row w3-margin-0">
                        <div class="col-sm-9 col-sm-offset-2 w3-padding-32">
                            <h4 class="bold">Examples:</h4>
                        </div>
                        <div class="col-sm-6 col-sm-offset-3">
                            <table class="table table-striped table-bordered table-hover">
                                <thead class="thead-light">
                                <tr>
                                    <th scope="col">Example</th>
                                    <th>Description</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                    <tr ng-repeat="sample in model.documentSamples track by $index">
                                        <td><a ng-click="downloadSample(sample.attachment.uuid)">{{sample.attachment.name}}</a></td>
                                        <td>{{sample.description}}</td>
                                        <td>
                                            <button class="btn" ng-click="editSample($index)"><i class="glyphicon glyphicon-edit"></i></button>
                                            <button class="btn" ng-click="deleteSample($index)"><i class="glyphicon glyphicon-trash text-danger"></i></button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <magna-file-input ng-model="tempSample.attachment" ></magna-file-input>
                                        </td>
                                        <td>
                                            <input ng-model="tempSample.description" class="form-control"/>
                                        </td>
                                        <td>
                                            <button ng-if="!tempSample.editing" ng-disabled="!(tempSample.attachment.id||tempSample.description)" class="btn" ng-click="addSample()"><i class="glyphicon glyphicon-plus text-success"></i></button>
                                            <button ng-if="tempSample.editing" ng-disabled="!(tempSample.attachment.id||tempSample.description)" class="btn" ng-click="saveSample()"><i class="glyphicon glyphicon-floppy-disk text-success"></i></button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <hr/>


                    <div class="row">
                        <div class="col-sm-12 w3-padding-32">
                            <button class="btn btn-raised btn-success pull-right" ng-click="save()">Save</button>
                            <button class="btn btn-raised btn-default pull-right" ng-click="cancel()">Cancel</button>
                        </div>
                    </div>
                </fieldset>

            </div>
        </div>
    </form>
</div>

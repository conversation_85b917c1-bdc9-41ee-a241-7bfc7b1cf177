mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, maidccService, magnaValidationService,
                                            magnaMainService, magnaHttpService, __env, $window, $compile, $routeParams, $location,$filter) {
    $scope.id = +$routeParams.id;
    $scope.model = {
        rejectionReasons: [],
        documentSamples: [],
        gender: 'All',
    };
    $scope.breadcrumbs = [
        { label: MaidccModules.getModule('visa').label }, { label: 'Visa documents setup', link: '#!/visa/documents-setup' },
        { label: ($scope.id > 0? 'Edit' : 'Add') + ' Document Setup'}
    ];

    $scope.documentsTags = []

    $scope.$on('$viewContentLoaded', function () {
        $scope.getMaidsTypesOptions();
        if($scope.id > 0) $scope.getDetails();
        $scope.getDocumentTypesOptions();
    });

    // Function to handle document tag changes
    $scope.handleDocumentTagChange = function(newValue) {
        // Get the selected document item from the options
        if (newValue && $scope.documentsTags.length) {
            const selectedDoc = $scope.documentsTags.find(item => item.tag === newValue);
            if (selectedDoc) {
                $scope.imageEnhance = selectedDoc.couldEnhance;
            }
        }
    };

    $scope.$watch('model.documentTag', function(newValue, oldValue) {
        if (newValue ) {
            $scope.handleDocumentTagChange(newValue, oldValue);
        }
    });

    $scope.getDetails = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'required-document-config/' + $scope.id,
        }, function (response) {
            $scope.model = response;
            $scope.model.required = '' + $scope.model.required;
            if(!$scope.documentsOptions.data?.length) $scope.documentsOptions.data = [{id: response?.documentTagItem?.code, text: response?.documentTagItem?.name}];
            else $scope.documentsOptions.data.push({id: response.documentTagItem?.code, text: response.documentTagItem?.name});
            $scope.model.documentTag = response?.documentTagItem.code;

            $scope.nationalitiesOptions.data = $scope.model.nationalities?.map(_ => ({id: _.id, text: _.label, code: _.code}));
            $scope.model.nationalities = $scope.model.nationalities?.map(_ => _.id);
            $scope.jobsOptions.data = $scope.model.jobs?.map(_ => ({id: _.id, text: _.label, code: _.code}));
            $scope.model.jobs = $scope.model.jobs?.map(_ => _.id);
            if(!$scope.model.minAge && !$scope.model.maxAge) $scope.model.allAges = true;
            $scope.model.rejectionReasons = $scope.model.rejectionReasons?.map(_ =>
                ({ instructions: _.instructions,
                    reason: {id: "" + _.reason.id, code: _.reason.code, name: _.reason.label, text: _.reason.label}}));
            $scope.model.gender = response.gender?response.gender:'All'
            $scope.model.enhanceDocument = response.enableEnhanceDocument;
            $scope.model.aiEnhancer = response.enableEnhanceDocument;

        }, {needs_loading_icon: true});
    };

    $scope.documentsOptions = {
        placeholder: "Select Document", width: '100%', data: []
    };

    $scope.getDocumentTypesOptions = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'required-document-config/document-tags/get?non-configured-documents-only=false',
        }, function (response) {
            $scope.documentsTags = response;

            const selectedDoc = response.find(item => item.tag === $scope.model.documentTag);
            if (selectedDoc) {
                $scope.imageEnhance = selectedDoc.couldEnhance;
            }

            $scope.documentsOptions.data = response?.map(_ => ({id: _.tag, text: _.label}));
            if($scope.model && $scope.model.documentTag) $scope.documentsOptions.data.push({id: $scope.model.documentTag, text: $scope.model.documentTag});
        }, {needs_loading_icon: true});
    };

    $scope.getMaidsTypesOptions = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'required-document-config/maid-types/get',
        }, function (response) {
            $scope.maidTypeOptions.data = response?.map(_ => ({id: _.value, text: _.label}));
        }, {needs_loading_icon: true});
    };

    $scope.nationalitiesOptions = {
        placeholder: "Select Nationality", width: '100%', multiple: true, data: [],
         ajax: {
            url: __env.PUBLIC + 'picklist/items/nationalities?page=0&size=100',
            data: function (params) { return { search: params.term ? params.term : "" } },
            processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id, code:item.code } }) }; }
        }
    };

    $scope.GenderOptions = {
            placeholder: "Select Gender", width: '100%', data: [{ id: 'All', text: 'All'},{ id: 'Female', text: 'Female'}, { id: 'Male', text: 'Male'}]
    };

    $scope.jobsOptions = {
        placeholder: "Select Job", width: '100%', multiple: true, data: [],
         ajax: {
            url: __env.PUBLIC + 'picklist/items/worker_types?page=0&size=100',
            data: function (params) { return { search: params.term ? params.term : "" } },
            processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id, code:item.code } }) }; }
        }
    };

    $scope.rejectionReasonOptions = {
        placeholder: "Select Reason", width: '100%', data: [],
         ajax: {
            url: __env.PUBLIC + 'picklist/items/rejection_reasons?page=0&size=100',
            data: function (params) { return { search: params.term ? params.term : "" } },
            processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id, code:item.code } }) }; }
        }
    };
    $scope.maidTypeOptions = {
        placeholder: "Select Maid Type", width: '100%',multiple: true, data: [],
    };

    $scope.tempReason = { instructions: '', reason: {} };
    $scope.tempReasonId = '';

    $scope.addReason = function() {
        $scope.model.rejectionReasons.push({
            instructions: $scope.tempReason?.instructions,
            reason: { id: $scope.tempReason?.reason?.id, text: $scope.tempReason?.reason?.text, name: $scope.tempReason?.reason?.text, code: $scope.tempReason?.reason?.code,}
        });
        $scope.tempReasonId = '';
        $scope.tempReason = { instructions: '', reason: {} };
    };

    $scope.editReason = function(index) {
        $scope.rejectionReasonOptions.data = [$scope.model.rejectionReasons[index]?.reason];
        $scope.tempReasonId = $scope.model.rejectionReasons[index]?.reason?.id;
        $scope.tempReason = {...$scope.model.rejectionReasons[index], editing: true, index };
    };

    $scope.saveReason = function() {
        $scope.model.rejectionReasons[$scope.tempReason.index] = {
            instructions: $scope.tempReason?.instructions,
            reason: { id: $scope.tempReason?.reason?.id, name: $scope.tempReason?.reason?.text, text: $scope.tempReason?.reason?.text, code: $scope.tempReason?.reason?.code,}
        };
        $scope.tempReasonId = '';
        $scope.tempReason = { instructions: '', reason: {} };
    };

    $scope.deleteReason = function(index) {
        $scope.model.rejectionReasons.splice(index, 1);
    };

    $scope.tempSample = { description: '', attachment: {} };

    $scope.downloadSample = function(uuid) {
        magnaHttpService.downloadFile(__env.PUBLIC + "download/" + uuid, {}, { preview_file: true });
    };

    $scope.addSample = function() {
        $scope.model.documentSamples.push({ ...$scope.tempSample });
        $scope.tempSample = { description: '', attachment: {} };
    };

    $scope.editSample = function(index) {
        $scope.tempSample = {...$scope.model.documentSamples[index], editing: true, index };
    };

    $scope.deleteSample = function(index) {
        $scope.model.documentSamples.splice(index, 1);
    };

    $scope.saveSample = function() {
        $scope.model.documentSamples[$scope.tempSample.index] = { ...$scope.tempSample };
        $scope.tempSample = { description: '', attachment: {} };
    };

    $scope.cancel = function() {
        $location.path('/visa/documents-setup');
    };

    $scope.ageChanges = function() {
        if($scope.model.minAge || $scope.model.maxAge) {
            $scope.model.allAges = false;
        }
    };

    $scope.allAgesChanges = function() {
        if($scope.model.allAges) {
            $scope.model.minAge = '';
            $scope.model.maxAge = '';
        }
    };

    $scope.save = function() {
        let validObj = {
            "model.documentTag": ["required"],
            "model.documentName": ["required"],
            "model.required": ["required"],
            "model.gender": ["required"],
        };

        let url = __env.VISA + 'required-document-config/' + ($scope.id > 0 ? 'update' : 'create');
        if(magnaValidationService.validate($scope, validObj)) {
            let data = {
                documentTag: $scope.model.documentTag,
                documentName: $scope.model.documentName,
                nationalities: $scope.model.nationalities?.map(_ => ({ id: _ })),
                gender: $scope.model.gender=='All'?'':$scope.model.gender,
                minAge: $scope.model.minAge,
                maxAge: $scope.model.maxAge,
                jobs: $scope.model.jobs?.map(_ => ({ id: _ })),
                maidTypes: $scope.model.maidTypes,
                required: $scope.model.required,
                rejectionReasons: $scope.model.rejectionReasons,
                documentSamples: $.map($scope.model.documentSamples,function (item) {
                    if(item.attachment && !item.attachment.id) delete item.attachment
                    return item;
                }),
                // Add image enhancement options
                enableEnhanceDocument: $scope.model.enhanceDocument || false,
                enableAiEnhancer: $scope.model.aiEnhancer || false
            };
            if($scope.id > 0) data['id'] = $scope.id;
            magnaHttpService.HttpWrapper({
                method: "POST",
                url,
                data,
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Saved Successfully!');
                $scope.cancel();
            }, {needs_loading_icon: true, ignore_error_messages: true, error_handler: function (error) {
                let msg = error?.data?.exception == "org.springframework.dao.DataIntegrityViolationException"?'Document name already defined':(error.data.message?error.data.message:error.data);
                    magnaMainService.DialogBox.showErrorMsg(msg);
                }});
        }
    };

});

mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env) {
    $scope.currentPage = 0;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'Approved Operators Interactions',
        }
    ];

    $scope.search = {
        fromDate: "",
        toDate: "",
        operatorOptions: {
            placeholder: "Select Operator", width: '100%', data: [], ajax: {
                url: function (params) {
                    return __env.VISA + 'applicant/searchNameFreedom';
                },
                data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id } }) }; }
            }
        }
    }
    $scope.selectedOperator = "";

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
    });
    $scope.doSearch = function (){
        $scope.getTableData(0);
    }

    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA  + 'applicant/applicantList?page=' + pageNo + "&size=" + __env.DATAGRID_PAGE_SIZE+($scope.search.fromDate?'&from='+$scope.search.fromDate:'')+($scope.search.toDate?'&from='+$scope.search.toDate:'')+($scope.search.selectedOperator?'&freedomId='+$scope.search.selectedOperator:''),
            headers:{
                'Content-Type': "application/json"
            },
        }, function (response) {
            $scope.mainDataGrid.data = response.content;
            $scope.mainDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: "Operator",
                type: "text",
                valueExp: "$data['freedomOperator']?$data['freedomOperator']['label']:''"
            },
            {
                label: "Applicant",
                type: "text",
                valueExp: "$data['name']"
            },
            {
                label: "Status",
                type: "text",
                valueExp: "$data['applicantStatus']"
            },
            {
                label: "Notes",
                type: "text",
                valueExp: "$data['notes']"
            },
            {
                label: "Date",
                type: "text",
                valueExp: "$data['date']"
            },
            {
                label: "Waiting Time",
                type: "text",
                valueExp: "$data['waitingTime']"
            },
            {
                label: "Closing Date",
                type: "text",
                valueExp: "$data['closingDate']"
            }
        ],
        data: [],
        actions: [
        ]
    };
    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    };


});
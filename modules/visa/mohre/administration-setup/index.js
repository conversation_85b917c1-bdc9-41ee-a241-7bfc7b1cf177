mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, maidccService, magnaValidationService,
    magnaMainService, magnaHttpService, __env, $window, $compile, $routeParams, $location,$filter) {
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'MOHRE Complaints Todos Administration Setup',
        }
    ];
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    $scope.getUsersOptions = function () {
        return {
            placeholder: "Select User", width: '100%', data: [], ajax: {
                url: __env.VISA + 'mohreUserPermission/getUsers',
                data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id } }) }; }
            },
            multiple: true
        };
    }
    $scope.model = {
        ProceedWithMOHREComplaintOptions: $scope.getUsersOptions(),
        WaitingConfirmationSMSOptions: $scope.getUsersOptions(),
        PendingForAppointmentOptions: $scope.getUsersOptions(),
        PendingCourtOptions: $scope.getUsersOptions(),
        MeetingIsTodayOptions: $scope.getUsersOptions(),
        PendingCourtOnDateOptions: $scope.getUsersOptions(),
        PendingCourtDecisionOptions: $scope.getUsersOptions(),
        MeetingIsPendingDecisionOptions: $scope.getUsersOptions(),
        ProceedWithMOHREComplaint:[],
        WaitingConfirmationSMS:[],
        PendingForAppointment:[],
        PendingCourt:[],
        MeetingIsToday:[],
        PendingCourtOnDate:[],
        PendingCourtDecision:[],
        MeetingIsPendingDecision:[],
        todoPurposeList : {
            "ProceedWithMOHREComplaint": "Proceed With MOHRE Complaint",
            "WaitingConfirmationSMS": "Approve Confirmation SMS",
            "PendingForAppointment": "Add Appointment",
            "PendingCourt": "Set first court date",
            "MeetingIsToday": "Add meeting info",
            "PendingCourtOnDate": "Add Meeting Decision",
            "PendingCourtDecision": "Add Court Date",
            "MeetingIsPendingDecision": "Add Court Decision",
        }
    };

    $scope.getUsersPermissions = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA  + 'mohreUserPermission/list',
                headers:{
                    'Content-Type': "application/json"
                },
            }, function (response) {
                $.each(response,function (todoIndex,todoItem) {
                    var todoKey = $scope.getPurposeKey(todoItem.mohreToDoPurpose);
                    $scope.model[todoKey+'Options'].data = $.map(todoItem.users, function (item) {
                        return {
                            id:item.id,
                            text:item.label
                        };
                    });
                    $scope.model[todoKey] = $.map(todoItem.users, function (item) {
                        return item.id;
                    })
                })
            }, { needs_loading_icon: true }
        );
    }
    $scope.getPurposeKey = function (val) {
        var k = '';
        $.each($scope.model.todoPurposeList,function (index,item) {
            if(item == val){
                k = index;
            }
        })
        return k;
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getUsersPermissions();
    });

    $scope.clearValues = function () {
        $scope.model.ProceedWithMOHREComplaint= [];
        $scope.model.WaitingConfirmationSMS= [];
        $scope.model.PendingForAppointment= [];
        $scope.model.PendingCourt= [];
        $scope.model.MeetingIsToday= [];
        $scope.model.PendingCourtOnDate= [];
        $scope.model.PendingCourtDecision= [];
        $scope.model.MeetingIsPendingDecision= [];
    }

    $scope.save = function () {
        $scope.saveOperator();
    }
    $scope.addToData = function (key,data) {
        if($scope.model[key]&&$scope.model[key].length>0){
            var users = [];
            $.each($scope.model[key],function (index,item) {
                users.push({id:item});
            })
            data.push({
                mohreToDoPurpose:$scope.model.todoPurposeList[key],
                users:users,
            });
        }
        return data;
    }
    $scope.saveOperator = function () {
        var data = [];
        data = $scope.addToData('ProceedWithMOHREComplaint',data);
        data = $scope.addToData('WaitingConfirmationSMS',data);
        data = $scope.addToData('PendingForAppointment',data);
        data = $scope.addToData('PendingCourt',data);
        data = $scope.addToData('MeetingIsToday',data);
        data = $scope.addToData('PendingCourtOnDate',data);
        data = $scope.addToData('PendingCourtDecision',data);
        data = $scope.addToData('MeetingIsPendingDecision',data);
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + "mohreUserPermission/savePermissions",
            data: data,
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("Permissions Saved Successfully");
            $scope.goToReturnPage();
        }, { needs_loading_icon: true });
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPageUrl)
            $location.path($scope.returnPageUrl);
    }

}); 
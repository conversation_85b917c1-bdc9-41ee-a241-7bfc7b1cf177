<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<h4 ng-if="complaintId" >Enter MOHRE Complaints to {{model.complaint.housemaid.name}}</h4>
<div class="container-fluid  add-content">
    <form class="form-horizontal" name="new_applicant_form">
        <div class="row  w3-margin-0">
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-4">Proceed With MOHRE Complaint :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.ProceedWithMOHREComplaintOptions" ng-model="model.ProceedWithMOHREComplaint"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Approve Confirmation SMS :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.WaitingConfirmationSMSOptions" ng-model="model.WaitingConfirmationSMS"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Add Appointment :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.PendingForAppointmentOptions" ng-model="model.PendingForAppointment"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Set first court date :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.PendingCourtOptions" ng-model="model.PendingCourt"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Add meeting info :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.MeetingIsTodayOptions" ng-model="model.MeetingIsToday"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Add Meeting Decision :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.PendingCourtOnDateOptions" ng-model="model.PendingCourtOnDate"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Add Court Date :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.PendingCourtDecisionOptions" ng-model="model.PendingCourtDecision"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4">Add Court Decision :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.MeetingIsPendingDecisionOptions" ng-model="model.MeetingIsPendingDecision"></magna-select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-actions-container">
            <div class="form-group">
                <div class="col-sm-6 text-right pull-right  w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button ng-if="returnPageUrl? true : false" type="button" class="btn btn-default  btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
                        <button type="submit" class="btn btn-default btn-md btn-raised" ng-click="save()">{{editMode?'Update':'Save'}}</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="row w3-margin-0" ng-if="pageType == '<PERSON><PERSON><PERSON> Complaints Manager To-Do List'">
    <div class="pull-right">
        <a class="btn btn-default w3-light-grey w3-hover-text-red" ng-click="addNewComplaint()">
            <i class="glyphicon glyphicon-plus"></i> Add New Complaint </a>
    </div>
    <div class="pull-right" ng-if="pageType == 'MOHRE Complaints Manager To-Do List'">
        <a class="btn btn-default w3-light-grey w3-hover-text-red" ng-click="sendEmailToPro()">
            <i class="glyphicon glyphicon-send"></i> Send Email to PRO </a>
    </div>
</div>
<div class="row w3-margin-0">
    <magna-data-grid config="mainDataGrid"></magna-data-grid>
    <magna-pagination config="mainDataGridPagination"></magna-pagination>
</div>


<div id="scheduleAppointment_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Add Appointment </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">Appointment Date:</label>
                            <div class="col-md-9">
                                <magna-date-input ng-model="model.appointmentDate" options="{startDate: '1900-01-01'}"></magna-date-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">Appointment Date:</label>
                            <div class="col-md-9">
                                <magna-time-input ng-model="model.appointmentTime" ></magna-time-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">Location:</label>
                            <div class="col-md-9">
                                <magna-select-input options="model.locationOptions" ng-model="model.selectedLocation"></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group" ng-show="currentRow.complaint.mOHREComplaintStatus=='Appointment Scheduled'" >
                            <label class="col-md-3 control-label required-label">Complaint Number:</label>
                            <div class="col-md-9">
                                <input class="col-md-10 form-control" ng-model="model.complaintNumber" />
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-click="submitAddAppointment()" ng-disabled="( !(model.selectedLocation&&model.appointmentDate) || (currentRow.complaint.mOHREComplaintStatus=='Appointment Scheduled' && !model.complaintNumber) )" >Add</button>
            </div>
        </div>
    </div>
</div>

<div id="firstCourtDate_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Enter the first court session date ({{firstCourtDate.housemaidName}}) </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">First court date:</label>
                            <div class="col-md-9">
                                <magna-date-input ng-model="firstCourtDate.firstCourtDate" options="{startDate: '1900-01-01'}"></magna-date-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">Court Number:</label>
                            <div class="col-md-9">
                                <input class="col-md-10 form-control" ng-model="firstCourtDate.courtNumber" />
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label">Court Announcement:</label>
                            <div class="col-md-9">
                                <magna-file-input ng-model="firstCourtDate.Announcement" tag="CourtAnnouncement" name="name1" ></magna-file-input>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="!(firstCourtDate.firstCourtDate&&firstCourtDate.courtNumber)" ng-click="submitFirstCourtDate()" >Add</button>
            </div>
        </div>
    </div>
</div>



<div id="AddCourtInfo_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Add Court Info </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">decision:</label>
                            <div class="col-md-9">
                                <magna-select-input options="courtInfo.decisionOptions" ng-model="courtInfo.selectedDecision"></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group" ng-show="courtInfo.selectedDecision=='POSTPONDED'" >
                            <label class="col-md-3 control-label required-label">Next court date:</label>
                            <div class="col-md-9">
                                <magna-date-input ng-model="courtInfo.nextCourtDate" options=""></magna-date-input>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label ">Notes:</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="courtInfo.notes"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label ">What documents did you prepare for court?</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="courtInfo.whatdocumentsdidyouprepareforcourt"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label ">What is the lawyer strategy to win the case?</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="courtInfo.whatisthelawyerstrategytowinthecase"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label ">Could we have improved our documents?</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="courtInfo.couldwehaveimprovedourdocuments"></textarea>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="!(courtInfo.selectedDecision)||(courtInfo.selectedDecision=='POSTPONDED'&&!courtInfo.nextCourtDate)" ng-click="submitAddCourtInfo()" >Add</button>
            </div>
        </div>
    </div>
</div>

<div id="AddMeetingInfo_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Add Meeting Info </h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset >
                        <div class="radio radio-primary">
                            <label>
                                <input type="radio" ng-model="meetingInfo.meetingStatus" value="decision taken"> Decision Taken
                            </label>
                        </div>
                        <div class="radio radio-primary">
                            <label>
                                <input type="radio" ng-model="meetingInfo.meetingStatus" value="Schedule new appointment"> Schedule new appointment
                            </label>
                        </div>
                        <div ng-if="currentModal=='AddMeetingInfo_modal'" >
                            <div class="form-group" ng-show="meetingInfo.meetingStatus=='decision taken'">
                                <label class="col-md-3 control-label required-label">decision:</label>
                                <div class="col-md-9">
                                    <magna-select-input options="MaidDecision.decisionOptions" ng-model="MaidDecision.selectedDecision"></magna-select-input>
                                </div>
                            </div>
                            <div class="form-group" ng-show="meetingInfo.meetingStatus=='decision taken'&&MaidDecision.selectedDecision=='Complaint Cancelled'">
                                <label class="col-md-3 control-label required-label">Cancellation reasons:</label>
                                <div class="col-md-9">
                                    <magna-select-input options="MaidDecision.cancellationReasonsOptions" ng-model="MaidDecision.selectedCancellationReasons"></magna-select-input>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label " >What did the legal advisor say?:</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="meetingInfo.whatDidTheLegalAdvisorSay"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label " >What documents should we improve to win the case?</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="meetingInfo.whatDocumentsShouldWeImproveToWinTheCase"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label " >Notes:</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="meetingInfo.notes"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label " >Why is the complaint raised?</label>
                            <div class="col-md-9">
                                <textarea rows="3" style="width: 100%" ng-model="meetingInfo.whyIsTheComplaintRaised"></textarea>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="(meetingInfo.meetingStatus=='decision taken'&&!MaidDecision.selectedDecision)||(meetingInfo.meetingStatus=='decision taken'&&MaidDecision.selectedDecision=='Complaint Cancelled'&&!MaidDecision.selectedCancellationReasons)" ng-click="submitAddMeetingInfo()" >Add</button>
            </div>
        </div>
    </div>
</div>

<div id="addCourtDecision_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Add Court Decision for maid ({{currentRow.complaint['hosemaid name']}})</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset>
                        <div class="form-group" >
                            <label class="col-md-3 control-label required-label">Decision:</label>
                            <div class="col-md-9">
                                <magna-select-input options="CourtDecision.decisionOptions" ng-model="CourtDecision.selectedDecision"></magna-select-input>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="!CourtDecision.selectedDecision" ng-click="submitAddCourtDecision()" >Add</button>
            </div>
        </div>
    </div>
</div>

<div id="addDecisionForMaid_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Add decision for maid ({{MaidDecision.housemaidName}})</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal" ng-if="currentModal=='addDecisionForMaid_modal'">
                    <fieldset>
                        <div class="form-group" >
                            <label class="col-md-3 control-label required-label">decision:</label>
                            <div class="col-md-9">
                                <magna-select-input options="MaidDecision.decisionOptions" ng-model="MaidDecision.selectedDecision"></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group" ng-show="MaidDecision.selectedDecision=='Complaint Cancelled'">
                            <label class="col-md-3 control-label required-label">Cancellation reasons:</label>
                            <div class="col-md-9">
                                <magna-select-input options="MaidDecision.cancellationReasonsOptions" ng-model="MaidDecision.selectedCancellationReasons"></magna-select-input>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="(!MaidDecision.selectedDecision)||(MaidDecision.selectedDecision=='Complaint Cancelled'&&!MaidDecision.selectedCancellationReasons)" ng-click="submitAddMaidDecision()" >Add</button>
            </div>
        </div>
    </div>
</div>

<div id="uploadBanPaper_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Upload Ban Paper for maid ({{currentRow.complaint['hosemaid name']}})</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset>
                        <div class="form-group" >
                            <label class="col-md-3 control-label required-label">Ban Paper:</label>
                            <div class="col-md-9">
                                <magna-file-input ng-model="banPaperFile" tag="banPaper" name="name1" ></magna-file-input>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="!banPaperFile" ng-click="submitBanPaper()" >Add</button>
            </div>
        </div>
    </div>
</div>


<div id="cancellationReason_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true" >×</button>
                <h4 class="modal-title"> Cancellation Reason For ({{currentRow.complaint['hosemaid name']}})</h4>
            </div>
            <div class="modal-body">
                <form class="form-horizontal">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label required-label">Cancellation reason:</label>
                            <div class="col-md-9">
                                <magna-select-input options="cancelComplaintReasonOptions" ng-model="cancelComplaintReason" ></magna-select-input>
                            </div>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" ng-disabled="!cancelComplaintReason" ng-click="cancelComplaint()" >Add</button>
            </div>
        </div>
    </div>
</div>
mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile) {
    $scope.page_options = $route.current.$$route.page_options;
    $scope.pageType = $scope.page_options.pageType;
    $scope.pageTitle = $scope.page_options.pageTitle;
    $scope.currentPage = 0;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: $scope.pageTitle,
        }
    ];
    $scope.currentRow = {};
    $scope.model = {
        appointmentDate:'',
        appointmentTime:'',
        selectedLocation:'',
        locationOptions:{
            placeholder: "Select Location",
            width: '100%',
            data: [{id:'MOHRE',text:'MOHRE'},{id:'Tawafuq',text:'Tawafuq'}],
        }
    };

    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        var url = '';
        switch ($scope.pageType) {
            case 'MOHRE Complaints Manager To-Do List':
                url = "getToDoMohreManager";
                break;
            case 'PRO To-Do List':
                url = "getToDoProList";
                break;
            case 'MOHRE complaints todo':
                url = "getUserToDo/"+$rootScope.user.id;
                break;
        }
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'mohreToDo/'+url+'?page='+pageNo+'&size='+__env.DATAGRID_PAGE_SIZE ,
            headers: {
                'Content-Type': "application/json"
            }
        }, function (response) {
            $scope.mainDataGrid.data = response.content;
            $scope.mainDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }

    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    };
    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
    });

    $scope.mainDataGrid = {
        columns: [
            {
                label: "Todo",
                type: "html",
                valueExp: function ($data,$index){
                    $data.toDoLabel = $data.toDoLabel?$data.toDoLabel:'Complaint (2) is requested and waiting Confirmation SMS';
                    var htm='';
                    if($data['mohreToDoPurpose']=='Proceed With MOHRE Complaint'){
                        htm = '<a href="javascript:void(0)" ng-click="goToLink('+'\'/visa/mohre/complaint-form/'+$data.id+'/'+$data['complaint'].id+'\')" >'+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Approve Confirmation SMS'){
                        htm = '<a href="javascript:void(0)" ng-click="smsReceived('+$data.id+','+$data["complaint"].id+')" >'+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Add Appointment'){
                        htm = '<a href="javascript:void(0)" ng-click="showAddAppointmentModal('+($index-1)+')" >'+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Set first court date'){
                        htm = '<a href="javascript:void(0)" ng-click="showFirstCourtDateModal('+($index-1)+')" >'+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Add meeting info'){
                        htm = '<a href="javascript:void(0)" ng-click="showAddMeetingInfoModal('+($index-1)+')" > '+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Add Meeting Decision'){
                        htm = '<a href="javascript:void(0)" ng-click="showAddCourtModal('+($index-1)+')" > '+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Add Court Date'){
                        htm = '<a href="javascript:void(0)" ng-click="showAddCourtModal('+($index-1)+')" > '+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Add Court Decision'){
                        htm = '<a href="javascript:void(0)" ng-click="showAddMaidDecisionModal('+($index-1)+')" > '+$data.toDoLabel+' </a>';
                    }
                    if($data['mohreToDoPurpose']=='Upload BAN Paper'){
                        htm = '<a href="javascript:void(0)" ng-click="showUploadBanPaperModal('+($index-1)+')" > '+$data.toDoLabel+' </a>';
                    }
                    return $compile(htm)($scope);
                }
            },
            {
                label: "Maid",
                type: "text",
                valueExp: "$data['complaint']['hosemaid name']"
            },
            {
                label: "Termination",
                type: "text",
                valueExp: "$data['complaint']['terminationDate']"
            }, {
                label: "waitingTime",
                type: "text",
                valueExp: "$data['waitingTime']"
            }
        ],
        data: [],
        actions: [{
            label: "Cancel",
            callbackFunc: function ($data) {
                $scope.$apply(function () {
                    $scope.currentRow = $data;
                    magnaMainService.DialogBox.showModal($("#cancellationReason_modal"));
                })
            },
            visiblityCond: function ($data) {
                var showSteps = ['Proceed With MOHRE Complaint','Approve Confirmation SMS','Add Appointment','Add meeting info','Add Meeting Decision'];
                if(showSteps.indexOf($data['mohreToDoPurpose'])>=0)
                    return true;
                return false;
            },
        }]
    };

    $scope.cancelComplaintReason = '';
    $scope.cancelComplaintReasonOptions = {
        placeholder: "Select Cancellation Reason",
            data: [],
            ajax: {
            url: __env.PUBLIC + '/picklist/items/Mohre_Complaint_Cancellation_Reasons?page=0&size=50',
                data: function (params) {
                return { search: params.term ? params.term : "" }
            },
            processResults: function (data) {
                return { results: $.map(data, function (item) { return { text: item.label, id: item.id,} }) };
            }
        },
        width: "100%"
    },

    $scope.cancelComplaint = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/cancelComplaint/'+$scope.currentRow["complaint"].id+'?cancellationReasonID='+$scope.cancelComplaintReason,
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $('#cancellationReason_modal').modal('hide');
                magnaMainService.DialogBox.showSuccessMsg("Complaint Cancelled Successfully");
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }

    $scope.addNewComplaint = function () {
        $scope.goToLink('/visa/mohre/complaint-form');
    }

    $scope.smsReceived = function (mohreTodoId,ComplaintId) {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/smsReceived/'+ComplaintId+'/'+mohreTodoId,
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }

    $scope.showAddAppointmentModal = function (index){
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.model.selectedLocation = '';
        $scope.model.complaintNumber = $scope.currentRow.complaint.numberofComplaint;
        $scope.model.appointmentDate = '';
        $scope.model.appointmentTime = '';
        magnaMainService.DialogBox.showModal($("#scheduleAppointment_modal"));
    }

    $scope.submitAddAppointment = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/addAppointment/'+$scope.currentRow.complaint.id+'/'+$scope.currentRow.id+'?appointmentDate='+$scope.model.appointmentDate+' '+$scope.model.appointmentTime+'&location='+$scope.model.selectedLocation+($scope.currentRow.complaint.mOHREComplaintStatus=='Appointment Scheduled'?('&complaintNumber=' + $scope.model.complaintNumber):''),
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $('#scheduleAppointment_modal').modal('hide');
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }
    $scope.firstCourtDate = {
        firstCourtDate:'',
        courtNumber:'',
        housemaidName:'',
        Announcement:'',
    }
    $scope.showFirstCourtDateModal = function (index){
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.firstCourtDate.firstCourtDate = '';
        $scope.firstCourtDate.courtNumber = '';
        $scope.firstCourtDate.Announcement = '';
        $scope.firstCourtDate.housemaidName = $scope.currentRow.complaint['hosemaid name'];
        magnaMainService.DialogBox.showModal($("#firstCourtDate_modal"));
    }

    $scope.submitFirstCourtDate = function () {
        var data = {
            id : $scope.currentRow.complaint.id,
            firstCourtDate : $scope.firstCourtDate.firstCourtDate,
            courtNumber :$scope.firstCourtDate.courtNumber,
        };
        if($scope.firstCourtDate.Announcement){
            data.attachments =[{id:$scope.firstCourtDate.Announcement.id}];
        }
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'mohreComplaint/addFirstCourtSessionDate/'+$scope.currentRow.id,
                data:data
            }, function (response) {
                $('#firstCourtDate_modal').modal('hide');
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }

    $scope.courtInfo = {
        decisionOptions:{
            placeholder: "Select Decision",
            width: '100%',
            data: [
                {id:'POSTPONDED',text:'Postponded'},
                {id:'PENDING_COURT',text:'Pending court decision'},
                {id:'COURT_CASE_WON',text:'Court case won'},
                {id:'COURT_CASE_LOST',text:'Court case lost'}
                ],
        },
        selectedDecision:'',
        nextCourtDate:'',
        notes:'',
        whatdocumentsdidyouprepareforcourt:'',
        whatisthelawyerstrategytowinthecase:'',
        couldwehaveimprovedourdocuments:'',
    }

    $scope.showAddCourtModal = function (index){
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.courtInfo.selectedDecision= '';
        $scope.courtInfo.nextCourtDate= '';
        $scope.courtInfo.notes= '';
        $scope.courtInfo.whatdocumentsdidyouprepareforcourt= '';
        $scope.courtInfo.whatisthelawyerstrategytowinthecase= '';
        $scope.courtInfo.couldwehaveimprovedourdocuments= '';
        magnaMainService.DialogBox.showModal($("#AddCourtInfo_modal"));
    }
    $scope.submitAddCourtInfo = function () {
        var data = {
            resultOfCourt: $scope.courtInfo.notes,
            documentsPrepared: $scope.courtInfo.whatdocumentsdidyouprepareforcourt,
            lawyerStrategy: $scope.courtInfo.whatisthelawyerstrategytowinthecase,
            improvedDocuments: $scope.courtInfo.couldwehaveimprovedourdocuments,
            id: $scope.currentRow.id
        };
        if( $scope.courtInfo.selectedDecision == 'POSTPONDED' ){
            data.nextCourtDate = $scope.courtInfo.nextCourtDate+" 00:00:00";
        }
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'mohreComplaint/addCourtDecisionForMaid/'+$scope.currentRow.complaint.id+'?courtStatus='+$scope.courtInfo.selectedDecision,
                data:data
            }, function (response) {
                $('#AddCourtInfo_modal').modal('hide');
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }

    $scope.CourtDecision = {
        decisionOptions:{
            placeholder: "Select Decision",
            width: '100%',
            data: [{id:'true',text:'Case won'},{id:'false',text:'Case lost'}],
        },
        selectedDecision:'',
    }

    $scope.showAddCourtDecisionModal = function (index){
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.CourtDecision.selectedDecision= '';
        magnaMainService.DialogBox.showModal($("#addCourtDecision_modal"));
    }
    $scope.submitAddCourtDecision = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'mohreComplaint/addCourtDecisionForMaid/'+$scope.currentRow.complaint.id+'/'+$scope.currentRow.id+'?decision='+$scope.CourtDecision.selectedDecision,
            }, function (response) {
                $('#addCourtDecision_modal').modal('hide');
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }
    $scope.meetingInfo = {
        meetingStatus:'',
        notes:'',
        whatDidTheLegalAdvisorSay:'',
        whatDocumentsShouldWeImproveToWinTheCase:'',
        whyIsTheComplaintRaised:'',
    }
    $scope.MaidDecision = {
        decisionOptions:{
            placeholder: "Select Decision",
            width: '100%',
            data:[{id:'Abscond HM',text:'Abscond HM'}, {id:'Company won complaint',text:'Company won complaint'}, {id:'Maid will appeal in court',text:'Maid will appeal in court'},{id:'Complaint Cancelled',text:'Complaint Cancelled'}],
        },
        cancellationReasonsOptions:{
            placeholder: "Select Cancellation Reason",
            data: [],
            ajax: {
                url: __env.PUBLIC + '/picklist/items/Mohre_Complaint_Cancellation_Reasons?page=0&size=50',
                data: function (params) {
                    return {
                        search: params.term ? params.term : ""
                    }
                },
                processResults: function (data) {
                    return {
                        results: $.map(data, function (item) {
                            return {
                                text: item.label,
                                id: item.id,
                            }
                        })
                    };
                }
            },
            width: "100%"
        },
        selectedDecision:'',
        selectedCancellationReasons:'',
    };

    $scope.showAddMeetingInfoModal = function (index){
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.MaidDecision.selectedDecision= '';
        $scope.MaidDecision.selectedCancellationReasons= '';
        $scope.meetingInfo.meetingStatus = 'decision taken';
        $scope.meetingInfo.notes= '';
        $scope.meetingInfo.whatDidTheLegalAdvisorSay= '';
        $scope.meetingInfo.whatDocumentsShouldWeImproveToWinTheCase= '';
        $scope.meetingInfo.whyIsTheComplaintRaised= $scope.currentRow.complaint.reasonOfTermination?$scope.currentRow.complaint.reasonOfTermination:'';
        $scope.currentModal = "AddMeetingInfo_modal";
        magnaMainService.DialogBox.showModal($("#AddMeetingInfo_modal"));
    }

    $scope.addMeetingDeferred = $.Deferred();
    $scope.addDecisionDeferred = $.Deferred();
    $scope.submitAddMeetingInfo= function () {
        $scope.addMeetingDeferred = $.Deferred();
        $scope.addDecisionDeferred = $.Deferred();
        if($scope.meetingInfo.meetingStatus == 'decision taken'){
            $scope.AddMaidDecision(function (response){
                $scope.addDecisionDeferred.resolve(response);
            });
        }else{
            $scope.addDecisionDeferred.resolve({});
        }
        var metting = {
            id:$scope.currentRow.complaint.meeting,
            "meetingStatus": $scope.meetingInfo.meetingStatus,
            "notes": $scope.meetingInfo.notes,
            "whatDidTheLegalAdvisorSay": $scope.meetingInfo.whatDidTheLegalAdvisorSay,
            "whatDocumentsShouldWeImproveToWinTheCase": $scope.meetingInfo.whatDocumentsShouldWeImproveToWinTheCase,
            "whyIsTheComplaintRaised": $scope.meetingInfo.whyIsTheComplaintRaised
        };

        $.when($scope.addDecisionDeferred.promise()).done(function () {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'meeting/addMeetingInfo/'+$scope.currentRow.id,
                data: metting
            }, function (response) {
                $scope.addMeetingDeferred.resolve(response);
            }, { needs_loading_icon: true });
        });
        $.when($scope.addMeetingDeferred.promise(), $scope.addDecisionDeferred.promise()).done(function () {
            $('#AddMeetingInfo_modal').modal('hide');
            $scope.getTableData($scope.currentPage);
        });
    }

    $scope.showAddMaidDecisionModal = function (index) {
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.MaidDecision.selectedDecision= '';
        $scope.MaidDecision.selectedCancellationReasons= '';
        $scope.MaidDecision.housemaidName = $scope.currentRow.complaint['hosemaid name'];
        $scope.currentModal = "addDecisionForMaid_modal";
        magnaMainService.DialogBox.showModal($("#addDecisionForMaid_modal"));
    }

    $scope.submitAddMaidDecision = function (callback) {
        $scope.AddMaidDecision(function (response){
            $('#addDecisionForMaid_modal').modal('hide');
            $scope.getTableData($scope.currentPage);
        });
    }
    $scope.AddMaidDecision = function (callback) {
        var decision = {
            id:$scope.currentRow.complaint.id,
            mohreComplaintDecisions:$scope.MaidDecision.selectedDecision,
        };
        if($scope.MaidDecision.selectedDecision == 'Complaint Cancelled'){
            decision.cancellationReasons = {id:$scope.MaidDecision.selectedCancellationReasons};
        }
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + 'mohreComplaint/addDecisionForMaid/'+$scope.currentRow.id,
            data: decision
        }, function (response) {
            callback(response);
        }, { needs_loading_icon: true });
    }
    $scope.sendEmailToPro = function (){
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'mohreComplaint/sendEmailtoPRO',
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("Email Sent Successfully");
            $scope.getTableData($scope.currentPage);
        }, { needs_loading_icon: true });
    }

    // BAN PAPER

    $scope.banPaperFile = {};

    $scope.showUploadBanPaperModal = function (index) {
        $scope.currentRow = $scope.mainDataGrid.data[index];
        $scope.banPaperFile = {};
        magnaMainService.DialogBox.showModal($("#uploadBanPaper_modal"));
    }

    $scope.submitBanPaper = function () {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + 'mohreComplaint/uploadBanPaper/'+$scope.currentRow.id,
            data: {
                id:$scope.currentRow.complaint.id,
                attachments:[{id:$scope.banPaperFile.id}]
            }
        }, function (response) {
            $('#uploadBanPaper_modal').modal('hide');
            $scope.getTableData($scope.currentPage);
        }, { needs_loading_icon: true });

    }



    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPageUrl', url);
        $location.path(destinationUrl);
    }

});
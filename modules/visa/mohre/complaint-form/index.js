mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, maidccService, magnaValidationService,
    magnaMainService, magnaHttpService, __env, $window, $compile, $routeParams, $location,$filter) {
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    $scope.complaintId = $routeParams.complaintId;
    $scope.mohreTodoId = $routeParams.mohreTodoId;
    var breadcrumbs = magnaMainService.RouteData.getStoredData('breadcrumb');
    if (!breadcrumbs)
        breadcrumbs = [
            {
                label: MaidccModules.getModule('visa').label
            }
        ];
    $scope.breadcrumbs = breadcrumbs;
    $scope.breadcrumbs.push({ label: 'MOHRE Complaint' });
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    $scope.model = {
        requestedByOptions:{
            placeholder: "Select Requested by",
                width: '100%',
                data: [{id:'Company',text:'Company'},{id:'Maid',text:'Maid'}],
        },
        maidsOptions : {
            placeholder: "Select Maid", width: '100%', data: [], ajax: {
                url: __env.VISA + 'mohreComplaint/searchHousemaid',
                data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data.content, function (item) { return { text: item.label, id: item.id } }) }; }
            }
        },
        selectedMaid:'',
        selectedRequestedBy:'',
        complaintDate:'',
        complaintNumber:'',
        notes:'',
    };
    $scope.getComplaintDetails = function (){
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + "mohreComplaint/"+$scope.complaintId,
        }, function (response) {
            $scope.model.complaint = response;
            $scope.model.selectedMaid = response.housemaid.id;
        }, { needs_loading_icon: true });
    };
    $scope.getHousemaidDetails = function (){
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + "mohreComplaint/maidInfo/"+$scope.model.selectedMaid,
        }, function (response) {
            $scope.housemaid = response;
        }, { needs_loading_icon: true });
    };

    $scope.$watch("model.selectedMaid",function (newVal, oldVal){
        if(newVal){
            $scope.getHousemaidDetails();
        }
    })

    $scope.$on('$viewContentLoaded', function () {
        if($scope.complaintId){
            $scope.getComplaintDetails();
        }
    });

    $scope.clearValues = function () {
        $scope.model.selectedMaid= '';
        $scope.model.selectedRequestedBy= '';
        $scope.model.complaintDate= '';
        $scope.model.complaintNumber= '';
        $scope.model.notes= '';
    }

    $scope.save = function () {
        var validate_obj = {
            "model.complaintDate": ["required"],
            "model.complaintNumber": ["required","number"],
        };
        if(!$scope.complaintId){
            validate_obj["model.selectedMaid"] =["required"];
            validate_obj["model.selectedRequestedBy"] =["required"];
        }
        if (magnaValidationService.validate($scope, validate_obj))
            $scope.saveOperator();
    }
    $scope.saveOperator = function () {
        var url = 'create';
        var data = {
            complaintDate:$scope.model.complaintDate,
            notes:$scope.model.notes,
            numberofComplaint:$scope.model.complaintNumber
        }
        if(!$scope.complaintId){
            data.housemaid={"id":$scope.model.selectedMaid};
            data.requestedBy=$scope.model.selectedRequestedBy;
        }else{
            url = 'addcomplaintFromTodoPage/'+$scope.complaintId+'/'+$scope.mohreTodoId;
        }
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + "mohreComplaint/"+url,
            data: data,
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("Complaint Added Successfully");
            $scope.goToReturnPage();
        }, { needs_loading_icon: true });
    }

    $scope.getOPT = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + "receivedSms/getOTP/"+$scope.complaintId,
        }, function (response) {
            magnaMainService.DialogBox.showInfoMsg(response);
        }, { needs_loading_icon: true });
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPageUrl)
            $location.path($scope.returnPageUrl);
        else{
            $scope.clearValues();
        }
    }

}); 
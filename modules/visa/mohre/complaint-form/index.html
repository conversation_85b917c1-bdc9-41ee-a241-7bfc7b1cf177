<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<h4 ng-if="complaintId" >Enter MOHRE Complaints to {{model.complaint.housemaid.name}}</h4>
<div class="container-fluid  add-content">
    <form class="form-horizontal" name="new_applicant_form">
        <div class="row  w3-margin-0">
            <div class="col-sm-6 w3-padding-32-h" ng-if="!complaintId" >
                <div class="form-group" >
                    <label class="control-label col-md-4 required-label">Maid:</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.maidsOptions" ng-model="model.selectedMaid"></magna-select-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4 required-label">Requested by:</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.requestedByOptions" ng-model="model.selectedRequestedBy"></magna-select-input>
                    </div>
                </div>
            </div>
            <div class="col-sm-6 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-4 required-label">Date of Complaint:</label>
                    <div class="col-md-8">
                        <magna-date-input ng-model="model.complaintDate" options="{startDate: '1900-01-01'}"></magna-date-input>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label col-md-4 required-label">Number of Complaint:</label>
                    <div class="col-md-8">
                        <input class="col-md-10 form-control" ng-model="model.complaintNumber" />
                    </div>
                </div>
            </div>
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group">
                    <label class="control-label col-md-2">Notes:</label>
                    <div class="col-md-10">
                        <textarea rows="3" style="width: 100%" ng-model="model.notes"></textarea>
                    </div>
                </div>
            </div>
            <div ng-show="complaintId" class="col-sm-12 w3-padding-32-h" >
                <button type="submit" class="btn btn-default btn-md btn-info btn-raised" ng-click="getOPT()">Get OTP</button>
            </div>
        </div>
        <div class="row form-actions-container">
            <div class="form-group">
                <div class="col-sm-6 text-right pull-right  w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button ng-if="returnPageUrl? true : false" type="button" class="btn btn-default  btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
                        <button type="submit" class="btn btn-default btn-md btn-raised" ng-click="save()">{{editMode?'Update':'Save'}}</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div class="panel-group">
    <div class="panel panel-default light_grey">
        <div class="panel-heading">
            <div class="row w3-margin-0">
                <div class="col-sm-9">
                    <div class="bold">
                        <i class="glyphicon glyphicon-info-sign"></i> Housemaid Details
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-body housemaid-details">
            <div class="row  w3-margin-0">
                <div class="form-group col-md-6">
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-6 text-center">Termination date:</label>
                        <div class="col-md-6 ">
                            <p class="form-control">{{housemaid['Termination date']}}</p>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-6 text-center">Termination Type:</label>
                        <div class="col-md-6 ">
                            <p class="form-control">{{housemaid['Termination Type']}}</p>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-6 text-center">Visa Number:</label>
                        <div class="col-md-6 ">
                            <p class="form-control">{{housemaid['Visa Number']}}</p>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-6 text-center">EID Number:</label>
                        <div class="col-md-6 ">
                            <p class="form-control">{{housemaid['EID Number']}}</p>
                        </div>
                    </div>
                </div>
                <div class="form-group col-md-6">
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-4 text-left">Phone number:</label>
                        <div class="col-md-8 ">
                            <p class="form-control">{{housemaid['Phone number']}}</p>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-4 text-left">MOHRE salary:</label>
                        <div class="col-md-8 ">
                            <p class="form-control">{{housemaid['MOHRE salary']}}</p>
                        </div>
                    </div>
                    <div class="form-group col-md-12">
                        <label class="control-label col-md-4 text-left">Company salary:</label>
                        <div class="col-md-8 ">
                            <p class="form-control">{{housemaid['Company salary']}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

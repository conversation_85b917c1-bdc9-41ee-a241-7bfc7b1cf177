mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, magnaMainService, magnaHttpService, magnaValidationService, $routeParams,maidccService,$location) {
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule("visa").label
        }, {
            label: 'MOH<PERSON> Complaints'
        }
    ];
    $scope.search = {
        statusOptions:{
            placeholder: "Select Status",
            width: '100%',
            data: [{id:'Requested',text:'Requested'},{id:'Pending Appointment',text:'Pending Appointment'},{id:'Appointment Scheduled',text:'Appointment Scheduled'},{id:'Meeting Finished and Decision Taken',text:'Meeting Finished and Decision Taken'},{id:'Meeting Finished and Pending Decision',text:'Meeting Finished and Pending Decision'},],
        },
        selectedStatus:'',
        fromDate:'',
        maidOptions:{
            placeholder: "Select Maid", width: '100%', data: [], ajax: {
                url: __env.VISA + 'mohreComplaint/searchHousemaid',
                data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data.content, function (item) { return { text: item.label, id: item.id } }) }; }
            }
        },
        selectedMaid:'',
        decisionOptions:{
            placeholder: "Select Status",
            width: '100%',
            data: [{id:'Abscond HM',text:'Abscond HM'}, {id:'Pending Decision',text:'Pending Decision'}, {id:'Company won complaint',text:'Company won complaint'}, {id:'Pending court',text:'Pending court'}, {id:'Company will not proceed with ban',text:'Company will not proceed with ban'}, {id:'Complaint Cancelled',text:'Complaint Cancelled'}, {id:'Complaint closed',text:'Complaint closed'},],
        },
        selectedDecision:'',
        toDate:'',
        opened:true,
        courtDecisionOptions:{
            placeholder: "Select Status",
            width: '100%',
            data: [{id:'Pending Court',text:'Pending Court'},{id:'Court Case Won',text:'Court Case Won'},{id:'Court Case Lost',text:'Court Case Lost'}],
        },
        selectedCourtDecision:'',
    };
    $scope.currentRow = {};

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData();
    });

    $scope.getTableData = function () {
        var deferred = $.Deferred();
        var filters = {
            mOHREComplaintStatus:$scope.search.selectedStatus,
            mohreReportDecisionOptions:$scope.search.selectedDecision,
            courtStatus:$scope.search.selectedCourtDecision,
            housemaid:{id:$scope.search.selectedMaid},
            opened:$scope.search.opened?'true':'false',
        };
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'mohreComplaint/report?from='+$scope.search.fromDate+'&to='+$scope.search.toDate + ($scope.datagridSortBy ? "&sort=" + $scope.datagridSortBy : ""),
                data: filters
            }, function (response) {
                deferred.resolve();
                $scope.mainDataGrid.data = response;
            }, { needs_loading_icon: true }
        );
        return deferred.promise();
    }

    $scope.exportExcel = function () {
        var filters = {
            mOHREComplaintStatus:$scope.search.selectedStatus,
            mohreReportDecisionOptions:$scope.search.selectedDecision,
            courtStatus:$scope.search.selectedCourtDecision,
            housemaid:{id:$scope.search.selectedMaid},
            opened:$scope.search.opened?'true':'false',
        };
        magnaHttpService.downloadFile(__env.VISA + 'mohreComplaint/reportExcel/?from='+$scope.search.fromDate+'&to='+$scope.search.toDate,{headers: { accept: '*/*' },data: filters,method:'POST'});
    }




    $scope.search.nationalityOptions = {
        placeholder: "Select Nationality",
        width: '100%',
        data: [{id:'Ethiopians',text:'Ethiopians'},{id:'Filipinos',text:'Filipinos'}],
    }

    $scope.search.typeOptions = {
        placeholder: "Select Type", width: '100%', data: [], ajax: {
            url: __env.PUBLIC + 'picklist/items/FreedomOperatorType?page=0&size=50',
            data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id } }) }; }
        }
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: "Maid",
                type: "text",
                valueExp: "$data['housemaid']?$data['housemaid']['name']:''",
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "housemaid.name," + sortDirection
                    return $scope.getTableData(0);
                }
            },
            {
                label: "Complaint Date",
                type: "text",
                valueExp: "$data['complaintDate']",
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "complaintDate," + sortDirection
                    return $scope.getTableData(0);
                }
            },
            {
                label: "Requested By",
                type: "text",
                valueExp: "$data['requestedBy']",
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "requestedBy," + sortDirection
                    return $scope.getTableData(0);
                }
            },
            {
                label: "Terminator",
                type: "text",
                valueExp: function ($data){
                    return $data['housemaid']?$data['housemaid']['reasonOfTermination']:'';
                },
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "housemaid.reasonOfTermination," + sortDirection
                    return $scope.getTableData(0);
                }
            },
            {
                label: "Status",
                type: "text",
                valueExp: "$data['mOHREComplaintStatus']",
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "mOHREComplaintStatus," + sortDirection
                    return $scope.getTableData(0);
                }
            }, {
                label: "Decision",
                type: "text",
                valueExp: "$data['mohreComplaintDecisions']",
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "mohreComplaintDecisions," + sortDirection
                    return $scope.getTableData(0);
                }
            }, {
                label: "Court Decision",
                type: "text",
                valueExp: "$data['courtStatus']",
                sortFunc: function (sortDirection) {
                    $scope.datagridSortBy = "courtStatus," + sortDirection
                    return $scope.getTableData(0);
                }
            }
        ],
        data: [],
        actions: [{
            label: "Meetings",
            callbackFunc: function ($data) {
                $scope.getMeetings($data.id);
            },
            visiblityCond: 'true',
        },{
            label: "Hearings",
            callbackFunc: function ($data) {
                $scope.getHearings($data.id);
            },
            visiblityCond: 'true',
        }]
    };
    $scope.filterOperators = function () {
        $scope.getTableData();
    }
    $scope.getMeetings = function(complaintID){
        magnaMainService.DialogBox.showModal($("#meetings-modal"));
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'meeting/getMeeting'+(complaintID?'?mohreComplaintID='+complaintID:''),
                headers: {
                    'Content-Type': "application/json"
                },
            }, function (response) {
                $scope.meetingsDataGrid.data = response;
            }, { needs_loading_icon: true }
        );
    }
    $scope.meetingsDataGrid = {
        columns: [
            {
                label: "Maid",
                type: "text",
                valueExp: "$data['complaint']&&$data['complaint']['housemaid']?$data['complaint']['housemaid']['name']:''"
            },
            {
                label: "Complaint Number",
                type: "text",
                valueExp: "$data['complaint']?$data['complaint']['numberofComplaint']:''"
            },
            {
                label: "Requested By",
                type: "text",
                valueExp: "$data['complaint']?$data['complaint']['requestedBy']:''"
            },
            {
                label: "Meeting Date",
                type: "text",
                valueExp: function ($data) {
                    return $data['appointmentDate']?moment($data['appointmentDate']).format("YYYY-MM-DD"):''
                }
            },
            {
                label: "Legal Advisor Notes",
                type: "text",
                valueExp: "$data['whatDidTheLegalAdvisorSay']"
            }, {
                label: "Improving Documents",
                type: "text",
                valueExp: "$data['whatDocumentsShouldWeImproveToWinTheCase']"
            }, {
                label: "Notes",
                type: "text",
                valueExp: "$data['notes']"
            }
        ],
        data: [],
        actions: []
    };
    $scope.getHearings = function(complaintID){
        magnaMainService.DialogBox.showModal($("#hearings-modal"));
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'court/getHearings'+(complaintID?'?mohreComplaintID='+complaintID:''),
                headers: {
                    'Content-Type': "application/json"
                },
            }, function (response) {
                $scope.hearingsDataGrid.data = response;
            }, { needs_loading_icon: true }
        );
    }
    $scope.hearingsDataGrid = {
        columns: [
            {
                label: "Maid",
                type: "text",
                valueExp: "$data['complaint']&&$data['complaint']['housemaid']?$data['complaint']['housemaid']['name']:''"
            },
            {
                label: "Complaint Number",
                type: "text",
                valueExp: "$data['complaint']?$data['complaint']['numberofComplaint']:''"
            },
            {
                label: "Requested By",
                type: "text",
                valueExp: "$data['complaint']?$data['complaint']['requestedBy']:''"
            },
            {
                label: "Court Number",
                type: "text",
                valueExp: "$data['complaint']?$data['complaint']['courtNumber']:''"
            },
            {
                label: "Hearing Date",
                type: "text",
                valueExp: function ($data) {
                    return $data['hearingDate']?moment($data['hearingDate']).format("YYYY-MM-DD"):''
                }
            },
            {
                label: "Notes",
                type: "text",
                valueExp: "$data['notes']"
            },
            {
                label: "Legal Strategy",
                type: "text",
                valueExp: "$data['whatisthelawyerstrategytowinthecase']"
            },
            {
                label: "Documents Used",
                type: "text",
                valueExp: "$data['whatdocumentsdidyouprepareforcourt']"
            }, {
                label: "Improving Documents",
                type: "text",
                valueExp: "$data['couldwehaveimprovedourdocuments']"
            }
        ],
        data: [],
        actions: []
    };

});
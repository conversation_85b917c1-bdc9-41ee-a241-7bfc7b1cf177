var publicPage = angular.module("public-page-app", ["magna-app", "ngCookies"]);
 ;


publicPage.config(function config(
  $locationProvider,
  $compileProvider,
  $routeProvider,
  $controllerProvider,
  __env
) {
  $compileProvider.aHrefSanitizationWhitelist(
    /^\s*(https?|ftp|mailto|file|javascript|tel):/
  );
  $locationProvider.hashPrefix("!");
  /* Creating a more synthesized form of service of $controllerProvider.register */
  publicPage.registerCtrl = $controllerProvider.register;
  $routeProvider
    .when("/401", {
      templateUrl: "../../../unauthorized.html?_v=" + __env.PUBLIC_PAGE_ID,
    })
    .when("/404", {
      templateUrl: "../../../not-found.html?_v=" + __env.PUBLIC_PAGE_ID,
    })
    .when("/:page/:id", {
      templateUrl: "content.html?_v=" + __env.PUBLIC_PAGE_ID,
      controller: "PublicPageController", 
    }) 
    .otherwise("/505");
});

publicPage.controller(
  "PublicPageController",
  function (
    $routeParams,
    $route,
    $window,
    $sce,
    $http,
    $timeout,
    $location,
    $scope,
    magnaAuthenticationService,
    magnaMainService,
    magnaHttpService,
    maidccService,
    $cookies,
    $rootScope, 
    __env
  ) {
    $scope.maidccService = maidccService;
    $rootScope.globals = $cookies.getObject("globals") || {};
    __env = window.__env;
    $scope.id = $routeParams.id; 
    $scope.page = $routeParams.page; 
    magnaHttpService.HttpWrapper(
      {
        method: "GET",
        url: __env.VISA + `tracker/${$scope.page}?housemaid_id=${$scope.id}`  
      },
      function (response) {
        magnaMainService.DialogBo
        $scope.html = response 
        $scope.html = $sce.trustAsHtml(response)
        debugger
      },
      { needs_loading_icon: true, ignore_authorization: true }
    ); 
  }
);

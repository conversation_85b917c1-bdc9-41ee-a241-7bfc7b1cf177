mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile, $timeout, maidccService, $compile,$httpParamSerializer) {
    $scope.currentPage = 0;
    $scope.currentRow = {};
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'Zajel Shipments',
        }
    ];

    $scope.modules = [{
        module: 'VISA',
        processTaskFilter: {
            zajelWorkFlow: [],
        }
    }];
    $scope.bulkActions = {
        zajelWorkFlow: {
            "Hand ID and Passport Manually": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
            "Hand Package to Zajel": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
            "Receive Package from Zajel": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
        }
    }
    $scope.rowActions = {
        zajelWorkFlow: {
            'Request Zajel Shipment': [{ label: 'View Task', code: 'view_task' }]
        }
    }
    $scope.handleAction = function (dataObj) {
        switch (dataObj.actionCode) {
            case 'view_task':
                viewTask(dataObj.data);
                break;
            case 'confirm_selected':
                confirmSelected(dataObj.data);
                break;
            default:
                break;
        }
    }

    function viewTask(data) {
        const params = getQueryStringRouteData();
        params.housemaidName = data.housemaidName;
        window.open(`#!/visa/zajel-shipment/todo-details/${data.id}/${data.taskKey}?${$httpParamSerializer(params)}`, '_blank');
    }

    function getQueryStringRouteData() {
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!';
        return params = {
            taskPageOptions: JSON.stringify({
                returnPage: url,
                breadcrumbs: newBreadCrumbs
            })
        };
    }

    function confirmSelected(data) {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA +  'zajel-todo/completeBulk/'+data.taskKey,
            data:{tasksIds:data.tasksIds}
        }, function (response) {
            $route.reload();
        }, { needs_loading_icon: true });
    }



});

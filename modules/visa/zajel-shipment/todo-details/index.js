mainApp.registerCtrl('page-ctrl', function ($scope, magnaValidationService, $routeParams, $location, $route, $timeout, magnaMainService, magnaHttpService, maidccService, __env,$httpParamSerializer) {
    $scope.page_options = $route.current.$$route.page_options;
    $scope.stepId = $routeParams.stepId;
    $scope.apiLink = $scope.page_options.apiLink;
    $scope.taskId = $routeParams.taskId;
    $scope.nexTaskId = '';
    $scope.showRemoveOptionInStepOne = false;
    $scope.finishedTask = false;
    $scope.isProductionMode = __env.PRODUCTION_MODE;
    $scope.step = $routeParams.stepId;
    $scope.breadcrumbs = [];
    $scope.returnPage = $scope.page_options.returnPage;
    $scope.obj = { attrsVal: {}, attrsValObj: {}, attrsOpt: {}, postedData: {}, actions: { done: {},save: {}, cancel: {}},};
    $scope.activeProcess = null;
    $scope.systemParams = {};

    if ($routeParams.taskPageOptions) {
        const taskPageOptions = JSON.parse($routeParams.taskPageOptions);
        $scope.breadcrumbs = taskPageOptions.breadcrumbs;
        $scope.returnPage = taskPageOptions.returnPage;
        $scope.activeProcess = taskPageOptions.activeProcess;
    }
    $scope.breadcrumbs.push({ label: $scope.step });
    $scope.$on('onSaveTask', function () {
        magnaMainService.DialogBox.showSuccessMsg('Current Step Saved');
        $scope.goToReturnPage();
    })


    $scope.$on('onFinishAllTasks', function (evt, data) {
        magnaMainService.DialogBox.showSuccessMsg('All Tasks are done');
        $scope.goToReturnPage();
    })

    $scope.$on('onCancelAction', function () {
        $scope.goToReturnPage();
    })

    $scope.goToReturnPage = function () {
        $location.path($scope.returnPage);
    }

});

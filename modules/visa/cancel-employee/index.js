mainApp.registerCtrl("cancel-employee", function ($scope, $rootScope, $routeParams, $location, $route, magnaMainService, magnaHttpService, __env, magnaValidationService) {
    $scope.$parent.cancel_employee = {};
    $scope.requiredModal  = ['Upload Signed contract and EID to ERP','Sign Loan Agreement','Pending medical certificate approval from DHA','Upload Contract to Tasheel','E-visa Issue solved, Pending to Get E-visa out'];
    $scope.requiredCancellationMethod  = ['Prepare after entry visa documents','Housemaids with visa pending to fly to Dubai','Prepare Folder','Apply for Ansari','Waiting for reply of Ansari','Upload passport and Evisa','E-visa Issue solved, Pending to Get E-visa out'];
    $scope.requiredModal = $scope.requiredModal.concat($scope.requiredCancellationMethod);
    $scope.cancel_employee.currentCallback = '';
    $scope.cancel_employee.cancellationDate = '';
    $scope.cancel_employee.cancellationMethod = '';

    $scope.cancel_employee.showConfirmation = function (){
        if( $scope.requiredCancellationMethod.indexOf($scope.currentstepName) > -1 ){
            if(!magnaValidationService.validate($scope, { "cancel_employee.cancellationMethod": ['required'] })){
                return;
            }
        }
        magnaMainService.DialogBox.showWarningMsg('Are you sure you Cancel This Visa?', function () {
            eval($scope.cancel_employee.currentCallback);
        });
    }

    $scope.cancel_employee.cancelByEmployee = function(employeeType, employeeId){
        $scope.currentstepName = '';
        $scope.cancel_employee.currentCallback = '$scope.cancel_employee.cancelByEmployeeRequest("'+employeeType+'", '+employeeId+')';
        $scope.cancel_employee.showConfirmation();
    }

    $scope.cancel_employee.cancelByEmployeeRequest = function(employeeType, employeeId){
        var url = employeeType == 'Housemaid' ? 'housemaid/' : 'officestaff/';
        magnaHttpService.HttpWrapper({
            method: "POST",
            data:{},
            url: __env.VISA + 'newRequest/cancel/' + url + employeeId,
        }, function (response) {
            $scope.$emit('visa_cancellation',response);
        }, { needs_loading_icon: true });
    }

    $scope.cancel_employee.cancelByProcess = function(processType, processId, stepName){
        $scope.cancel_employee.cancellationDate = '';
        $scope.cancel_employee.cancellationMethod = '';
        $scope.currentstepName = stepName.name;
        $scope.cancel_employee.currentCallback = '$scope.cancel_employee.cancelByProcessRequest("'+processType+'", '+processId+',"'+$scope.currentstepName+'")';
        if($scope.requiredModal.indexOf($scope.currentstepName) > -1){
            magnaMainService.DialogBox.showModal($('#cancel_employee_modal'));
        }else{
            $scope.cancel_employee.showConfirmation();
        }
    }

    $scope.cancel_employee.cancelByProcessRequest = function(processType, processId){
        magnaHttpService.HttpWrapper({
            method: "POST",
            data:{
                cancellationDate:$scope.cancel_employee.cancellationDate?$scope.cancel_employee.cancellationDate+' 00:00:00':'',
                moveTo:$scope.cancel_employee.cancellationMethod?$scope.cancel_employee.cancellationMethod:''
            },
            url: __env.VISA + processType + "/cancel/" + processId,
        }, function (response) {
            $scope.$emit('visa_cancellation',response);
        }, { error_handler: function (response) { magnaMainService.LoadingIcon.hide(); } });
    }
})
<html ng-app="public-page-app">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <link rel="shortcut icon" href="images/favicon.png?_v=20180408" type="image/png">
    <link href="../../../plugins/materialfont/material-icons.css" rel="stylesheet">
    <!-- Styles -->
    <link href="../../../plugins/bootstrap/dist/css/bootstrap.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-material-design/dist/css/bootstrap-material-design.min.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-material-design/dist/css/ripples.min.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-material-design/dist/css/ripples.min.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/custom-scrollbar/jquery.mCustomScrollbar.min.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/dropzone/min/dropzone.min.css" rel="stylesheet">
    <link href="../../../plugins/animate.css/animate.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/select2/css/select2.min.css" rel="stylesheet">
    <link href="../../../plugins/jstree/themes/default/style.min.css" rel="stylesheet" />

    <script>document.write('<scr' + 'ipt src="../../../custom/custom-environment.js?_v=' + (new Date()).getTime() + '"></sc' + 'ript>');</script>
    <script>document.write('<scr' + 'ipt src="../../../js/magna-environment.js?_v=' + (new Date()).getTime() + '"></sc' + 'ript>');</script>
    <script>window.__env.PUBLIC_PAGE_ID = __env.VERSION_ID + "_24";</script>
    <script>document.write('<link href="../../../css/style.css?_v=' + window.__env.VERSION_ID + '" rel="stylesheet" type="text/css">');</script>
    <script>document.write('<link href="../../../css/mobile.css?_v=' + window.__env.VERSION_ID + '" rel="stylesheet" type="text/css">');</script>
    <script src="../../../plugins/jquery/dist/jquery.min.js"></script>
    <!-- Angular -->
    <script src="../../../plugins/angular-1.6.6/angular.min.js"></script>
    <script src="../../../plugins/angular-1.6.6/angular-route.min.js"></script>
    <script src="../../../plugins/angular-1.6.6/angular-cookies.min.js"></script>
    <script src="../../../plugins/angular-1.6.6/angular-sanitize.min.js"></script>
    <title>Housemaid Documents</title>
    <meta name="google-signin-scope" content="profile email">
    <script>document.write('<meta name="google-signin-client_id" content="'+window.__env.GOOGLE_ACC_CONFIG.CLIENT_ID+'"></meta>');</script>
    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <style>
        .question-label {
            text-align: left !important;
            font-weight: bold !important;
        }
        body{
            background: #ccc;
        }
        .header{
            background-color: #4267b0;
            height: 5em;
            padding:1em 1.5em;
        }
        .specialTimeInput .glyphicon-th{
            margin:10px;
        }
        #page-view{
            max-width: 600px;
            display: block;
            margin: 0 auto;
            background: #fff;
            padding-bottom: 10px;
            height: 100%;
        }
        #page-container{
            background: #fff;
            padding: 0px 20px;
            clear:both;
            width: 100%;
            max-width: 100%;
        }
        .btn{
            text-transform:none !important;
        }
        .yellow-btn{
            text-transform: capitalize;
            -moz-border-radius: 4px;
            -webkit-border-radius: 4px;
            border-radius: 4px;
            background: #f2ad58;
            background: -webkit-linear-gradient(top,#f6c88f,#ed9220);
            background: linear-gradient(to bottom,#f6c88f,#ed9220);
            border-style: solid;
            border-color: #ca7c1b #be751a #a56616;
            border-width: 1px !important;
            transition: auto !important;
            text-transform: capitalize;
            color: #4D4D4D !important;
            outline: none;
            box-shadow: 0 1px 0 rgba(255,255,255,.6) inset;
            -webkit-transition: all .2s;
            -moz-transition: all .2s;
            -ms-transition: all .2s;
            -o-transition: all .2s;
            transition: all .2s;
            width: 100%;
        }
        .yellow-btn:disabled{
            cursor:not-allowed;
            color: #555 !important;
            background: #f2ad58 !important;
            background: -webkit-linear-gradient(top,#f6c88f,#ed9220) !important;
            background: linear-gradient(to bottom,#f6c88f,#ed9220) !important;
            border-style: solid;
            border-color: #ca7c1b #be751a #a56616 !important;
        }
        .yellow-btn:hover {
            background: #eba145 !important;
            background: -webkit-linear-gradient(top,#f4bc77,#e18512) !important;
            background: linear-gradient(to bottom,#f4bc77,#e18512) !important;
            border-color: #be751a #b26d18 #9a5f15 !important;
        }
        .yellow-btn:active {
            background: #ed9220 !important;
            border-color: #ca7c1b #be751a #be751a !important;
            box-shadow: 0 0 3px 2px rgba(228,121,17,.5) , 0 1px 3px rgba(0,0,0,.2) inset !important;
        }
        .yellow-btn:focus {
            box-shadow: 0 0 3px 2px rgba(228,121,17,.5) , 0 1px 0 rgba(255,255,255,.6) inset;
        }
        .btn-blue {
            color: #fff !important;
            background: -webkit-gradient(linear, left top, left bottom, from(#6a7bb0), to(#4267b0)) !important;
            -moz-border-radius: 4px !important;
            -webkit-border-radius: 4px !important;
            border-radius: 4px !important;
            border: 1px solid #315ab0 !important;
            background: #315ab0 !important;
            background: -moz-linear-gradient(top, #6a7bb0 0%, #4267b0 100%) !important;
            background: -webkit-linear-gradient(top, #6a7bb0 0%, #4267b0 100%) !important;
            background: linear-gradient(to bottom, #6a7bb0 0%, #4267b0 100%) !important;
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#f5e5bf", endColorstr="#f7cb6e",GradientType=0 ) !important;;
        }
        .btn-blue:hover {
            color: #fff!important;
            background: #315ab0!important;
            background: -moz-linear-gradient(top, #4267b0 0%, #284ab0 100%) !important;
            background: -webkit-linear-gradient(top, #4267b0 0%, #284ab0 100%) !important;
            background: linear-gradient(to bottom, #4267b0 0%, #284ab0 100%) !important;
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#f7cb6e", endColorstr="#f59e00",GradientType=0 ) !important;;
        }
        .back-btn{
            margin: 0;
            margin-right: 20px;
            font-size: 1.5em;
        }
        .back-btn i{
            line-height: 1em;
            color: #fff;
        }
        @font-face {
            font-family: 'Airbnb Cereal App Book';
            font-style: normal;
            font-weight: normal;
            src: local('Airbnb Cereal App Book'), url('fonts/AirbnbCerealBook.woff') format('woff');
        }


        @font-face {
            font-family: 'Airbnb Cereal App Light';
            font-style: normal;
            font-weight: normal;
            src: local('Airbnb Cereal App Light'), url('fonts/AirbnbCerealLight.woff') format('woff');
        }


        @font-face {
            font-family: 'Airbnb Cereal App Medium';
            font-style: normal;
            font-weight: normal;
            src: local('Airbnb Cereal App Medium'), url('fonts/AirbnbCerealMedium.woff') format('woff');
        }


        @font-face {
            font-family: 'Airbnb Cereal App Bold';
            font-style: normal;
            font-weight: normal;
            src: local('Airbnb Cereal App Bold'), url('fonts/AirbnbCerealBold.woff') format('woff');
        }


        @font-face {
            font-family: 'Airbnb Cereal App Extra Bold';
            font-style: normal;
            font-weight: normal;
            src: local('Airbnb Cereal App Extra Bold'), url('fonts/AirbnbCerealExtraBold.woff') format('woff');
        }


        @font-face {
            font-family: 'Airbnb Cereal App Black';
            font-style: normal;
            font-weight: normal;
            src: local('Airbnb Cereal App Black'), url('fonts/AirbnbCerealBlack.woff') format('woff');
        }
        *:not(.glyphicon){
            font-family: 'Airbnb Cereal App Medium' !important;
        }

        .thumbContainer{
            display: block;
            position: relative;
            width: fit-content;
            text-align: center;
            margin: 50px auto;
        }
        .deleteThumb{
            pacity: 0.6;
            border: 2px solid #000 !important;
            font-size: 18pt;
            font-weight: bold;
            padding: 0 5px !important;
            text-align: center;
            margin-left: 20px;
            float: none;
            display: block;
            position: absolute;
            right: -30px;
            top: -33px;
        }
        .form-actions-container{
            margin-top: 0;
            border-top: 0px !important;
        }
        .form-actions-container .form-group{
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .normal-link{
            color: #1257b7 !important;
            text-decoration: underline !important;
        }
        .thanks-msg{
            margin-top: 30px;
            text-align: center;
        }
        .tanks-title{
            color:#007400;
            font-weight: 500;
            margin-top: 25px;
            display: block;
            font-size: 18px;
        }
        .tanks-label{
            line-height: 1.6;
            margin-top: 20px;
            color: #000;
            font-size: 16px;
        }
        .gray-label{
            text-align: center !important;
            font-size: 13px;
        }

        @media only screen and (max-width: 450px) {
            .back-btn {
                padding: 10px 0;
            }
            .header img{
                max-width: 40%;
            }

            .already-applied {
                font-size: 12px;
            }
        }
        @media only screen and (max-width: 350px) {
            .gray-label{
                text-align: center !important;
                font-size: 11px;
            }
        }


    </style>
</head>

<body class="content">
<div id="page-view" ng-view></div>


<script src="../../../plugins/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="../../../plugins/bootstrap-material-design/dist/js/ripples.min.js"></script>
<script src="../../../plugins/bootstrap-material-design/dist/js/material.min.js"></script>
<script src="../../../plugins/select2/js/select2.min.js"></script>
<script src="../../../plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="../../../plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
<script src="../../../plugins/bootstrap-notify/bootstrap-notify.min.js"></script>
<script src="../../../plugins/jquery.mousewheel/jquery.mousewheel.min.js"></script>
<script src="../../../plugins/custom-scrollbar/jquery.mCustomScrollbar.js"></script>
<script src="../../../plugins/dropzone/min/dropzone.min.js"></script>
<script src="../../../plugins/moment/moment.min.js"></script>
<script src="../../../plugins/moment/moment-timezone.min.js"></script>
<script src="../../../plugins/moment/moment-precise-range.js"></script>
<script src="../../../plugins/inputmask/dist/min/jquery.inputmask.bundle.min.js"></script>
<script src="../../../plugins/noUiSlider/nouislider.min.js"></script>
<script src="../../../plugins/jstree/jstree.min.js"></script>
<!-- html2pdf libraries -->
<script src="../../../plugins/html2pdf/jspdf.min.js"></script>
<script src="../../../plugins/html2canvas-1.0.0/html2canvas.min.js"></script>
<script src="../../../plugins/html2pdf/html2pdf.bundle.min.js?_=20180708"></script>
<script src="../../../plugins/jszip/dist/jszip.min.js"></script>
<!-- Bezier Signature Libraries -->
<script src="../../../plugins/jquery.signaturepad/numeric-1.2.6.min.js"></script>
<script src="../../../plugins/jquery.signaturepad/bezier.js"></script>
<script src="../../../plugins/jquery.signaturepad/jquery.signaturepad-saf16-fix.js"></script>
<!-- core libraries -->
<script>document.write('<scr' + 'ipt src="../../../js/magna-app.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="../../../js/magna-services.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="../../../js/magna-environment.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="../../../js/config.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="index.js?_v=' + window.__env.PUBLIC_PAGE_ID + '"></sc' + 'ript>');</script>
<script>
    $(document).ready(function () {
        $.material.options.autofill = true
        $.material.init();
    });
</script>
<div id="file_viewer_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">Edit Notes</h4>
            </div>
            <div class="modal-body" style="text-align: center;">

            </div>
            <div class="modal-footer">
                <a id="generic-download-link" class="btn btn-primary btn-raised">
                    <i class="glyphicon glyphicon-download-alt"></i> Download </a>
            </div>
        </div>
    </div>
</div>
</body>

</html>

var publicPage = angular.module('public-page-app', ['magna-app', 'ngCookies']);
publicPage.config(function config($locationProvider, $compileProvider, $routeProvider, $controllerProvider, __env) {
    $compileProvider.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|file|javascript|tel):/);
    $locationProvider.hashPrefix('!');
    /*Creating a more synthesized form of service of $ controllerProvider.register*/
    publicPage.registerCtrl = $controllerProvider.register;
    $routeProvider.

    when('/401', {
        templateUrl: '../../../unauthorized.html?_v=' + __env.PUBLIC_PAGE_ID
    }).
    when('/404', {
        templateUrl: '../../../not-found.html?_v=' + __env.PUBLIC_PAGE_ID
    }).
    when('/:token', {
        templateUrl: 'content.html?_v=' + __env.PUBLIC_PAGE_ID,
        controller: 'PublicPageController',
        page_info: {
            code: "visa__public_oec-maid-doc"
        }
    }).
    otherwise('/505');
});

publicPage.controller('PublicPageController', function ($routeParams, $route, $window, $sce, $http, $timeout, $location, $scope, magnaAuthenticationService, magnaMainService, magnaHttpService,maidccService, $cookies, $rootScope, $filter, __env) {
    $scope.maidccService = maidccService;
    $rootScope.globals = $cookies.getObject('globals') || {};
    __env = window.__env;
    $scope.token = $routeParams.token; // should get from parameter
    $scope.finishedLoading = false;
    $scope.details = {};
    $scope.addPageCodeHeader = function (headers={}){
        headers.pageCode = $route.current.$$route.page_info.code;
        return headers;
    }

    $scope.getDetails = function () {
        magnaHttpService.HttpWrapper({
            method:'GET',
            url: __env.VISA + "oecrequest/oec-maid-doc/decode-token?token=" + $scope.token,
            headers:$scope.addPageCodeHeader()
        }, function (response) {
            $scope.details = response;
            $scope.finishedLoading = true;
        }, { needs_loading_icon: true, ignore_authorization: true });
    }

    $scope.downloadFile = function(uuid){
        magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + uuid)
    }
    $scope.downloadStaffFile = function(housemaidID){
        magnaHttpService.downloadFile(__env.STAFFMGMT + "extradocs/certificateofemployment/" + housemaidID)
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getDetails();
    })


});

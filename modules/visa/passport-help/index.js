var PassportHelpApp = angular.module('passport-help-app', ['magna-app', 'ngCookies']);
PassportHelpApp.config(function config($locationProvider, $compileProvider, $routeProvider, $controllerProvider, __env) {
    $compileProvider.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|file|javascript|tel):/);
    $locationProvider.hashPrefix('!');
    /*Creating a more synthesized form of service of $ controllerProvider.register*/
    PassportHelpApp.registerCtrl = $controllerProvider.register;
    $routeProvider.

    when('/401', {
        templateUrl: '../../../unauthorized.html?_v=' + __env.PASSPORT_HELP_PAGE_VERSION_ID
    }).
    when('/404', {
        templateUrl: '../../../not-found.html?_v=' + __env.PASSPORT_HELP_PAGE_VERSION_ID
    }).
    when('/', {
        templateUrl: 'content.html?_v=' + __env.PASSPORT_HELP_PAGE_VERSION_ID,
        controller: 'PassportHelpController',
        page_info: {
            code: "visa__public_passport-help"
        }
    }).
    otherwise('/505');
});

PassportHelpApp.controller('PassportHelpController', function ($routeParams, $route, $window, $sce, $http, $timeout, $location, $scope, magnaAuthenticationService, magnaMainService, magnaHttpService, $cookies, $rootScope, $filter, __env) {
    $rootScope.globals = $cookies.getObject('globals') || {};
    __env = window.__env;
});
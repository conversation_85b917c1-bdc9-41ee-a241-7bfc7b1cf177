<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="container-fluid  add-content">
    <form class="form-horizontal" name="new_applicant_form">
        <div class="row  w3-margin-0">
            <div class="col-md-12 w3-padding-32-h" >
                <h3>Repeat Medical for {{model.housemaid.label}}</h3>
                <h5>Maid's medical test result has been issued please collect it and define the medical result</h5>
                <div class="form-group">
                    <label class="control-label col-md-4" >Maid medical status:</label>
                    <div class="col-md-8">
                        <div class="radio radio-primary  w3-padding-right">
                            <label>
                                <input type="radio" name="fitToWorkInUAE" ng-model="model.fitToWorkInUAE" value="true" >
                                Fit to work in UAE
                            </label>
                        </div>
                        <div class="radio radio-primary  w3-padding-right">
                            <label>
                                <input type="radio" name="fitToWorkInUAE" ng-model="model.fitToWorkInUAE" value="false" >
                                Unfit to work in UAE
                            </label>
                        </div>
                        <div class="radio radio-primary  w3-padding-right" ng-show="todoDetails.repeatMedicalNewForm">
                            <label>
                                <input type="radio" name="fitToWorkInUAE" ng-model="model.fitToWorkInUAE" value="kept" >
                                Not fit to work in UAE and was kept in Muhaisnah
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group" ng-show="model.fitToWorkInUAE=='true'" >
                    <label class="control-label col-md-4 required-label">Medical certificate :</label>
                    <div class="col-md-8">
                        <magna-file-input ng-model="model.medicalCertificate" tag="medicalCertificate" ></magna-file-input>
                    </div>
                </div>
                <div class="form-group" ng-show="todoDetails.repeatMedicalNewForm && model.fitToWorkInUAE=='false'||model.fitToWorkInUAE=='kept'" >
                    <label class="control-label col-md-4 required-label">Sick immigration cancellation paper :</label>
                    <div class="col-md-8">
                        <magna-file-input ng-model="model.sickImmigrationCancellationPaper" tag="immigrationCancellationPaper" ></magna-file-input>
                    </div>
                </div>
                <div class="form-group" ng-show="model.fitToWorkInUAE=='false'||model.fitToWorkInUAE=='kept'" >
                    <label class="control-label col-md-4 required-label">Medical test result (English) :</label>
                    <div class="col-md-8">
                        <magna-file-input ng-model="model.medicalTestResultEN" tag="medicalTestResult" ></magna-file-input>
                    </div>
                </div>
                <div class="form-group" ng-show="model.fitToWorkInUAE=='false'||model.fitToWorkInUAE=='kept'" >
                    <label class="control-label col-md-4 required-label">Medical test result (Arabic) :</label>
                    <div class="col-md-8">
                        <magna-file-input ng-model="model.medicalTestResultAR" tag="medicalTestResultArabic" ></magna-file-input>
                    </div>
                </div>
                <div class="form-group" ng-show="todoDetails.repeatMedicalNewForm && ( model.fitToWorkInUAE=='false' || model.fitToWorkInUAE=='kept') " >
                    <label class="control-label col-md-4 required-label">Failed medical test reason :</label>
                    <div class="col-md-8">
                        <magna-select-input options="model.failedMedicalTestReasonOptions" ng-model="model.failedMedicalTestReason" ></magna-select-input>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-actions-container">
            <div class="form-group">
                <div class="col-sm-6 text-right pull-right  w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button type="button" class="btn btn-default btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
                        <button type="submit" class="btn btn-default btn-md btn-raised" ng-click="save()">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
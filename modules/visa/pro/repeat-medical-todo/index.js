mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService,magnaValidationService) {
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: '"PRO" To-Do Details',
        }
    ];
    $scope.maidccService = maidccService;
    $scope.todoID = $routeParams.todoID;
    $scope.model = {
        housemaid : '',
        medicalCertificate:'',
        sickImmigrationCancellationPaper:'',
        medicalTestResultEN:'',
        medicalTestResultAR:'',
        fitToWorkInUAE:'',
        failedMedicalTestReason:'',
        failedMedicalTestReasonOptions:{
            placeholder: "Select Cancellation Reason",
            data: [],
            ajax: {
                url: __env.PUBLIC + '/picklist/items/failedMedicalTestReasons?page=0&size=50',
                data: function (params) {
                    return { search: params.term ? params.term : "" }
                },
                processResults: function (data) {
                    return { results: $.map(data, function (item) { return { text: item.label, id: item.id,} }) };
                }
            },
            width: "100%"
        },
    };

    $scope.getTodoDetails = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'proToDo/'+$scope.todoID,
            headers: { 'Content-Type': "application/json" }
        }, function (response) {
            $scope.todoDetails = response;
            $scope.model.housemaid = response.housemaid;
        }, { needs_loading_icon: true });
    }


    $scope.goToReturnPage = function () {
        if ($scope.returnPageUrl)
            $scope.goToLink($scope.returnPageUrl);
        else
            $scope.goToLink('/visa/pro/vp-todo-list');
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTodoDetails();
    });

    $scope.save = function () {
        var validate_obj = {
            "model.fitToWorkInUAE": ["required"],
        };
        if($scope.model.fitToWorkInUAE=='true')
            validate_obj["model.medicalCertificate"] = ["required"];
        else{
            validate_obj["model.medicalTestResultEN"] = ["required"];
            validate_obj["model.medicalTestResultAR"] = ["required"];
            if($scope.todoDetails.repeatMedicalNewForm){
                validate_obj["model.sickImmigrationCancellationPaper"] = ["required"];
                validate_obj["model.failedMedicalTestReason"] = ["required"];
            }
        }
        if(magnaValidationService.validate($scope, validate_obj)) {
            var data = [{
                "id": $scope.model.fitToWorkInUAE=='true'?$scope.model.medicalCertificate.id:$scope.model.sickImmigrationCancellationPaper.id
            }];
            if($scope.model.fitToWorkInUAE=='true'){
                data.push({
                    "id": $scope.model.medicalCertificate.id
                });
            }else{
                data.push({id:$scope.model.medicalTestResultEN.id});
                data.push({id:$scope.model.medicalTestResultAR.id});
                if($scope.todoDetails.repeatMedicalNewForm)
                    data.push({id:$scope.model.sickImmigrationCancellationPaper.id});
            }
            var params = {fitToWorkInUAE:$scope.model.fitToWorkInUAE};
            if($scope.todoDetails.repeatMedicalNewForm){// send related label
                params = {repeatMedicalOutcome:($scope.model.fitToWorkInUAE=='true'?'Fit to work in UAE':($scope.model.fitToWorkInUAE=='false'?'Unfit to work in UAE':'Not fit to work in UAE and was kept in Muhaisnah'))};
                params.reasonId = $scope.model.failedMedicalTestReason;
            }
            magnaHttpService.HttpWrapper({
                    method: "POST",
                    url: __env.VISA + 'proToDo/completeRepeatMedicalTodo/'+$scope.todoID ,
                    data:data,
                    params:params
                }, function (response) {
                    magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
                    $scope.goToReturnPage();
                }, { needs_loading_icon: true }
            );
        }
    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPageUrl', url);
        $location.path(destinationUrl);
    }


});
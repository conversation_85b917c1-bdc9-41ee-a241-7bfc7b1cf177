mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, maidccService, magnaValidationService,
    magnaMainService, magnaHttpService, __env, $window, $compile, $routeParams, $location,$filter) {
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'Add New Todo',
        }
    ];
    $scope.model = {
        title:'',
        dueOn:'',
        description:'',
        housemaidOptions:{
            placeholder: "Select Housemaid", width: '100%', data: [], ajax: {
                url: __env.VISA + 'proToDo/searchName?page=0&size=50',
                data: function (params) { return { search: params.term ? params.term : "", } }, processResults: function (data) { return { results: $.map(data.content, function (item) { return { text: item.label, id: item.id } }) }; }
            }
        },
        selectedHousemaid:''
    };

    $scope.$on('$viewContentLoaded', function () {

    });

    $scope.clearValues = function () {
        $scope.model.title= '';
        $scope.model.dueOn= '';
        $scope.model.description= '';
        $scope.model.selectedHousemaid= '';
    }

    $scope.save = function () {
        var validate_obj = {
            "model.title": ["required"],
            "model.dueOn": ["required"],
        };
        if (magnaValidationService.validate($scope, validate_obj))
            $scope.saveOperator();
    }
    $scope.saveOperator = function () {
        var data = {
            title:$scope.model.title,
            dueOn:$scope.model.dueOn+' 00:00:00',
            description:$scope.model.description,
            housemaid:{id:$scope.model.selectedHousemaid}
        }
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + "proToDo/addToDo",
            data: data,
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg("Todo Added Successfully");
            $scope.goToReturnPage();
        }, { needs_loading_icon: true });
    }

    $scope.goToReturnPage = function () {
        if ($scope.returnPageUrl)
            $location.path($scope.returnPageUrl);
        else{
            $scope.clearValues();
        }
    }

}); 
mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,maidccService,magnaValidationService) {
    $scope.returnPageUrl = magnaMainService.RouteData.getStoredData('returnPageUrl');
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: '"PRO" To-Do Details',
        }
    ];
    $scope.maidccService = maidccService;
    $scope.todoID = $routeParams.todoID;
    $scope.model = {
        housemaid : '',
        mohreSickCancellationPaper:'',
    };

    $scope.getTodoDetails = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'proToDo/'+$scope.todoID,
                headers: { 'Content-Type': "application/json" }
            }, function (response) {
                $scope.todoDetails = response;
                $scope.model.housemaid = response.housemaid;
            }, { needs_loading_icon: true }
        );
    }


    $scope.goToReturnPage = function () {
        if ($scope.returnPageUrl)
            $scope.goToLink($scope.returnPageUrl);
        else
            $scope.goToLink('/visa/pro/vp-todo-list');
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTodoDetails();
    });

    $scope.save = function () {
        var validate_obj = {
            "model.mohreSickCancellationPaper": ["required"],
        };
        if(magnaValidationService.validate($scope, validate_obj)) {
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'proToDo/completeMohreSickCancellationProToDo/'+$scope.todoID ,
                data:[{
                    "id": $scope.model.mohreSickCancellationPaper.id
                }],
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
                $scope.goToReturnPage();
            }, { needs_loading_icon: true });
        }
    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPageUrl', url);
        $location.path(destinationUrl);
    }


});
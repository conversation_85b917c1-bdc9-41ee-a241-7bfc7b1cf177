mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile,$timeout) {
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'PRO Manager',
        }
    ];
    $scope.currentRow = {};
    $scope.currentPage = 0;
    $scope.currentPage1 = 0;
    $scope.accomplishedTodos = [];
    $scope.notAccomplishedTodos = [];
    $scope.orderChanged = false;
    $scope.search = {
        housemaidName:'',
        lastActionDate:'',
    };

    $scope.$on('$viewContentLoaded', function () {
        $scope.getAccomplishedTodos();
        $scope.getNotAccomplishedTodos();
        $scope.getOngoningTodos();
    });

    $scope.refreshTables = function () {
        $scope.getAccomplishedTodos($scope.currentPage);
        $scope.getNotAccomplishedTodos($scope.currentPage1);
    }

    $scope.getAccomplishedTodos = function (pageNo) {
        $scope.currentPage = pageNo;
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'proToDo/getAllAccomplishedToDos?page='+pageNo+'&size='+__env.DATAGRID_PAGE_SIZE,
            headers: { 'Content-Type': "application/json" }
        }, function (response) {
            $scope.accomplishedDataGrid.data = response.content;
            $scope.accomplishedDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }
    $scope.getNotAccomplishedTodos = function (pageNo) {
        $scope.currentPage1 = pageNo;
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'proToDo/getNotAccomplishedToDos?page='+pageNo+'&size='+__env.DATAGRID_PAGE_SIZE,
            headers: { 'Content-Type': "application/json" }
        }, function (response) {
            $scope.notAccomplishedDataGrid.data = response.content;
            $scope.notAccomplishedDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }
    $scope.getOngoningTodos = function () {
        let params = {}
        if($scope.search.housemaidName)
            params.housemaidName = $scope.search.housemaidName;
        if($scope.search.lastActionDate)
            params.lastActionDate = $scope.search.lastActionDate;
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + 'proToDo/getAllNotAccomplishedToDos' ,
            params:params,
            headers: { 'Content-Type': "application/json" }
        }, function (response) {
            $scope.notAccomplishedTodos = $.map(response, function (item, index) {
                item["todoOrder"] = index;
                return item;
            });
            $scope.updateSort();
        }, { needs_loading_icon: true }
        );
    }

    $scope.updateSort = function () {
        $timeout(function () {
            var sortableElm = $('.sortable');
            sortableElm.sortable();
            if (!sortableElm.data('sortupdate_binded')) {
                sortableElm.bind('sortupdate', function (e, ui) {
                    $scope.orderChanged = true;
                    $scope.$apply(function () {
                        $.map(e.target.children,function (item,i) {
                            var arrIndex = $(item).data('index');
                            $scope.notAccomplishedTodos[arrIndex]["todoOrder"] = i;
                            return $(item).data('index');
                        })
                    });
                });
                sortableElm.data('sortupdate_binded', true);
            }
        });
    }
    $scope.saveTodosOrder = function () {
        var newArr = $.map($scope.notAccomplishedTodos, function (item, index) {
            var obj = {
                todoOrder: item.todoOrder,
                id:item.id
            };
            return obj;
        });
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + "proToDo/setProToDoOrder",
                data: newArr
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Updated Successfully");
                $scope.getOngoningTodos();
                $scope.getAccomplishedTodos();
                $scope.orderChanged = false;
            }, { needs_loading_icon: true }
        );
    }

    $scope.printedTodo = function (todoId) {
        magnaMainService.DialogBox.showWarningMsg("Are you sure documents are printed?", function () {
            magnaHttpService.HttpWrapper({
                    method: "POST",
                    url: __env.VISA + 'proToDo/update',
                    data:{id:todoId,isPrinted:true},
                    headers: {'Content-Type': "application/json"}
                }, function (response) {
                    magnaMainService.DialogBox.showSuccessMsg("Todo Marked As Printed Successfully");
                    $scope.getOngoningTodos();
                }, {needs_loading_icon: true}
            );
        });
    }
    $scope.cancelTodo = function (todoId) {
        magnaMainService.DialogBox.showWarningMsg("Are you sure you want to cancel this todo?", function () {
            magnaHttpService.HttpWrapper({
                    method: "GET",
                    url: __env.VISA + 'proToDo/cancelProToDo/'+todoId,
                    headers: {'Content-Type': "application/json"}
                }, function (response) {
                    magnaMainService.DialogBox.showSuccessMsg("Todo Cancelled Successfully");
                    $scope.getOngoningTodos();
                }, {needs_loading_icon: true}
            );
        });
    }

    $scope.extraDocs = {
        name:'',
        document:{}
    };
    $scope.showUploadExtraDocs = function (todo) {
        $scope.extraDocs = {
            name:'',
            document:{}
        };
        $scope.currentRow = todo;
        magnaMainService.DialogBox.showModal($('#upload-extra-docs-modal'));
    }
    $scope.uploadExtraDocs = function () {
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA + 'proToDo/uploadExtraDocument',
                data:{
                    id:$scope.currentRow.id,
                    title:$scope.extraDocs.name,
                    attachments:[{id:$scope.extraDocs.document.id}]
                },
                headers: {'Content-Type': "application/json"}
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Extra Docs Added Successfully");
                $('#upload-extra-docs-modal').modal('hide');
            }, {needs_loading_icon: true}
        );
    }

    $scope.accomplishedDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getAccomplishedTodos(pageNo);
        }
    };

    $scope.notAccomplishedDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getNotAccomplishedTodos(pageNo);
        }
    };


    $scope.managerNotes = '';

    $scope.getDataGrid = function () {
        return {
            columns: [
                {
                    label: "Todo",
                    type: "html",
                    valueExp: function ($data,$index){
                        var htm = `<a href="#!/visa/pro/todo-details/${$data.id}" target="_blank"  >${$data.title}</a>`;
                        return $compile(htm)($scope);
                    }
                },
                {
                    label: "Housemaid",
                    type: "text",
                    valueExp: "$data['housemaid']?$data['housemaid']['label']:''",
                },
                {
                    label: "Added Since",
                    type: "text",
                    valueExp: function ($data){
                        return moment($data['creationDate'], 'YYYY-MM-DD h:mm:ss').fromNow();
                    },
                }, {
                    label: "Due on",
                    type: "text",
                    valueExp: function ($data){
                        return moment($data['dueOn'], 'YYYY-MM-DD h:mm:ss').format('YYYY-MM-DD');
                    },
                }, {
                    label: "Completed On",
                    type: "text",
                    valueExp: function ($data){
                        return moment($data['completedOn'], 'YYYY-MM-DD h:mm:ss').format('YYYY-MM-DD');
                    },
                }
            ],
            data: [],
            actions: [{
            label: "Reopen",
            callbackFunc: function ($data) {
                $scope.managerNotes = '';
                $scope.currentRow = $data;
                $('#reopen-modal').modal('show');
            },
            visiblityCond: 'true',
            htmlAttributes: { class: 'btn-default' }
        },{
            label: "Done",
            callbackFunc: function ($data) {
                magnaMainService.DialogBox.showWarningMsg("Are you sure this todo is done?", function () {
                    magnaHttpService.HttpWrapper({
                            method: "GET",
                            url: __env.VISA + 'proToDo/doneProToDo/' + $data.id,
                            headers: {'Content-Type': "application/json"}
                        }, function (response) {
                            magnaMainService.DialogBox.showSuccessMsg("Todo Done");
                            $scope.refreshTables();
                        }, {needs_loading_icon: true}
                    );
                });
            },
            visiblityCond: 'true',
            htmlAttributes: { class: 'btn-default' }
        }]
        };
    }

    $scope.accomplishedDataGrid= $scope.getDataGrid();
    $scope.notAccomplishedDataGrid= $scope.getDataGrid();

    $scope.reopenTodo = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + 'proToDo/reopenProToDo/'+$scope.currentRow.id+'?note='+$scope.managerNotes,
                headers: {'Content-Type': "application/json"}
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg("Todo Reopened Successfully");
                $('#reopen-modal').modal('hide');
                $scope.refreshTables();
                $scope.getOngoningTodos();
            }, {needs_loading_icon: true}
        );
    }

    $scope.addNewTodo = function () {
        $scope.goToLink('#!/visa/pro/todo-form');
    }

    $scope.goToLink = function (destinationUrl){
        var url = $location.path();
        var newBreadCrumbs = [];
        angular.copy($scope.breadcrumbs, newBreadCrumbs);
        newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
        magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
        magnaMainService.RouteData.storeData('returnPageUrl', url);
        window.open(destinationUrl,'_blank')
    }
    $scope.dateFromNow = function (date) {
         return moment(date, 'YYYY-MM-DD h:mm:ss').fromNow();
    }
    $scope.dateFormat = function (date) {
        return moment(date, 'YYYY-MM-DD h:mm:ss').format('YYYY-MM-DD');
    }

    $scope.showSnooze = function (todoId) {
        $timeout(function () {
            $scope.currentTodoId = todoId;
            $scope.snoozingTime = '';
            magnaMainService.DialogBox.showModal("#snoozeTime-modal");
        },0)
    }
    $scope.submitSnooze = function(){
        const params = { snoozeUntil:moment($scope.snoozingTime).format('YYYY-MM-DD HH:mm:ss') }
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + '/proToDo/snoozeUntil/'+$scope.currentTodoId,
                params:params,
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Snoozed Successfully');
                $scope.getOngoningTodos();
            }, { needs_loading_icon: true }
        );
    }

});

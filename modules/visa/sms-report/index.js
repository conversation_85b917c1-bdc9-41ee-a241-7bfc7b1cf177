mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, maidccService) {
    $scope.currentPage = 0;
    $scope.currentRow = {};
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'Maid Visa SMS Report',
        }
    ];
    $scope.search = {
        fromDate : moment().startOf('month').format('YYYY-MM-DD'),
        toDate : '',
    }
    $scope.mainDataGrid = {
        columns: [
            {
                label: "Type of Document",
                type: "text",
                valueExp: "$data['typeOfDocument']"
            },
            {
                label: "SMS Sent",
                type: "text",
                valueExp: "$data['smsSent']"
            },
            {
                label: "Reminders Sent",
                type: "text",
                valueExp: "$data['ReminderSent']"
            },
            {
                label: "Unreceived Documents",
                type: "text",
                valueExp: function ($data, $index) {
                    return $data['unReceivedDocuments'];
                }
            },
            {
                label: "Documents Received",
                type: "text",
                valueExp: "$data['receivedDocuments']"
            }
        ],
        data: [],
        actions: []
    };

    $scope.getTableData = function () {
        var params = {};
        if($scope.search.fromDate){
            params.from = $scope.search.fromDate+' 00:00:00';
        }
        if($scope.search.toDate){
            params.to = $scope.search.toDate+' 23:59:59';
        }
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA +  'maidVisaReport/smsReport',
                params:params,
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $scope.mainDataGrid.data = response;
            }, { needs_loading_icon: true }
        );
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData();
    });


});

mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env) {
    $scope.currentPage = 0;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'All Processes',
        }
    ];
    $scope.search = {
        name: "",
        processTypes: {
            placeholder: "Select Type",
            width: '100%',
            allowClear: false,
            data: [
                { id: 'newRequest', text: 'New Processes' },
                { id: 'cancelRequest', text: 'Canceled Processes' },
                { id: 'renewRequest', text: 'Renewal Processes' },
                { id: 'overstayRequest', text: 'Overstay Processes' },
            ]
        },
        selectedProcessType: 'newRequest'
    }
    $scope.ProcessTypeMapping = {
        "newRequest":"NewProcess",
        "cancelRequest":"CancelProcess",
        "renewRequest":"RenewProcess",
        "overstayRequest":"OverstayProcess",
    };
    $scope.selectedMoveToRequestType = "";
    $scope.moveToStepsOptions = {
        placeholder: "Select Status",
        width: '100%',
        data: []
    }
    $scope.selectedMoveTo = '';
    $scope.currentRow = {};

    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + $scope.search.selectedProcessType + '/page/' + $scope.search.name + "?page=" + pageNo + "&size=" + __env.DATAGRID_PAGE_SIZE,
            headers: {
                'Content-Type': "application/json"
            }
        }, function (response) {
            $scope.mainDataGrid.data = response.content;
            $scope.mainDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: "Housemaid",
                type: "text",
                valueExp: "$data['name']"
            },
            {
                label: "Housemaid Status",
                type: "text",
                valueExp: "$data['housemaid']['status']"
            }, {
                label: "Housemaid Type",
                type: "text",
                valueExp: function ($data) {
                    if ($data['housemaid']['isAgency'])
                        return 'Agency';
                    if ($data['housemaid']['freedomMaid'])
                        return 'Freedom';
                    return '';
                }
            }, {
                label: "Visa Status ",
                type: "text",
                valueExp: "$data['taskLabel']"
            }, {
                label: "",
                type: "html",
                valueExp: function ($data) {
                    var htm = '<span class="text-danger">';
                    if ($data['stopped']) {
                        if ($data['cancelRequest'])
                            htm += 'Visa Cancellation';
                        else if ($data['renewRequest'])
                            htm += 'Visa Renewal';
                        else
                            htm += 'Stopped';
                    }
                    return htm + '</span>';
                }
            }
        ],
        data: [],
        actions: [
            {
                label: "Move",
                callbackFunc: function ($data) {
                    $scope.currentRow = $data;
                    $scope.$apply(function () {
                        $scope.selectedMoveToRequestType = $scope.search.selectedProcessType;
                    })
                    magnaMainService.DialogBox.showModal($('#move_to_modal'));
                },
                visiblityCond: "!$data.stopped",
                htmlAttributes: { class: 'btn-default' }
            }
        ]
    };
    $scope.$watch("selectedMoveToRequestType",function(newVal){
        if(newVal){
            magnaHttpService.HttpWrapper({
                    method: "GET",
                    url: __env.VISA + "/visaTaskes/getTasksLabelsByRequestType/" + $scope.ProcessTypeMapping[newVal],
                }, function (response) {
                    $scope.moveToStepsOptions.data = $.map(response, function (item) {
                        return {
                            text: item.taskLabel,
                            id: item.taskName,
                        }
                    });
                },
                {
                    needs_loading_icon: true,
                    loading_show_delay:0
                }
            );
        }
    })
    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    }

    $scope.doSearch = function () {
        $scope.getTableData(0);
    }

    $scope.moveTo = function () {
        var processId = $scope.currentRow.id;
        var selectedMoveToRequestType = "";
        switch ($scope.selectedMoveToRequestType){
            case "newRequest" :
                selectedMoveToRequestType = "new";
            break;
            case "cancelRequest" :
                selectedMoveToRequestType = "cancel";
            break;
            case "renewRequest" :
                selectedMoveToRequestType = "renew";
            break;
            case "overstayRequest" :
                selectedMoveToRequestType = "overstay";
            break;
        }
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA + $scope.search.selectedProcessType + '/move/'+processId+"/"+selectedMoveToRequestType+"/"+$scope.selectedMoveTo,
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Process Moved to ' + $scope.selectedMoveTo);
            $scope.getTableData($scope.currentPage);
            $('#move_to_modal').modal('hide');
        }, { error_handler: function (response) { magnaMainService.LoadingIcon.hide(); } });
    }


    $scope.$watch('search.selectedProcessType', function (newVal, oldVal) {
        $scope.getTableData(0);
    });


});
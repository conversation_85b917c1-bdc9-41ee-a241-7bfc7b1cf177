mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env,$compile) {

    $scope.page_options = $route.current.$$route.page_options;
    $scope.pageUrl = $scope.page_options?.page_url;

    $scope.breadcrumbs = [
        { label: MaidccModules.getModule('visa').label }, { label: 'RPA', }, { label: 'Follow up with canceled maids', }
    ];

    $scope.tabs = [
        { label: 'Canceled maids under our sponsorship', code: 'CANCELLED'},
        { label: 'Removed From GDRFA report maids', code: 'REMOVED'},
    ];

    $scope.activeTab = 'CANCELLED';
    $scope.changeTab = function(code) {
        $scope.activeTab = code;
        if($scope.activeTab === 'CANCELLED') $scope.getCancelledData(0);
        if($scope.activeTab === 'REMOVED') $scope.getRemovedData(0);
    };

    $scope.searchCancelled = {};
    $scope.searchRemoved = {};

    $scope.$on('$viewContentLoaded', function () {
        $scope.getCancelledData(0);
    });

    $scope.getCellHtmlAttribute = function($data){
        return $data.contactedUs? { 'style': { 'background-color': '#66a56c' }} : {};
    };

    $scope.getCancelledData = function(page) {
        let params = {
            status: 'Exist',
            page,
            size: __env.DATAGRID_PAGE_SIZE,
            ...$scope.searchCancelled,
        };
         magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA  + 'MaidsInGdrfaReport/search',
            params,
        }, function (response) {
            $scope.cancelledDataGrid.data = response.content;
            $scope.cancelledDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true });
    };

    $scope.getRemovedData = function(page) {
        let params = {
            status: 'Removed',
            page,
            size: __env.DATAGRID_PAGE_SIZE,
            ...$scope.searchRemoved,
        };
         magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA  + 'MaidsInGdrfaReport/search',
            params,
        }, function (response) {
            $scope.removedDataGrid.data = response.content;
            $scope.removedDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true });
    };

    $scope.openViewNumbersModal = function(id, name) {
        $scope.$broadcast('openMaidNumbersModal', {id: id, name: name});
    }

    $scope.cancelledDataGrid = {
        columns: [
            {
                label: "Name",
                type: "text",
                valueExp: function ($data) {
                    return $data.housemaid?.name;
                },
                htmlAttributes: $scope.getCellHtmlAttribute
            },
            {
                label: "Nationality",
                type: "text",
                valueExp: function ($data) {
                    return $data.housemaid?.nationality?.label;
                },
                htmlAttributes: $scope.getCellHtmlAttribute
            },
            {
                label: 'Phone Number',
                type: "html",
                valueExp: function($data){
                    let col = `<button type='button' style='margin-bottom: 4px' class='btn btn-primary btn-sm w3-light-grey w3-hover-text-red' ng-click='openViewNumbersModal(${$data.housemaid?.id}, "${$data.housemaid?.name}")'>View</button>`;
                    return $compile(col)($scope);
                },
                htmlAttributes: $scope.getCellHtmlAttribute
            },
            {
                label: "Overstay fees",
                type: "text",
                valueExp: function ($data) {
                    return $data.overstayFees;
                },
                htmlAttributes: $scope.getCellHtmlAttribute
            },
            {
                label: "Termination date",
                type: "text",
                valueExp: function ($data) {
                    return $data.housemaid?.dateOfTermination;
                },
                htmlAttributes: $scope.getCellHtmlAttribute
            },
            {
                label: "Creation date",
                type: "text",
                valueExp: function ($data) {
                    return $data.creationDate;
                },
                htmlAttributes: $scope.getCellHtmlAttribute
            },
        ],
        data: [],
        actions: [
            {
                label: "Maid contacted us",
                callbackFunc: function ($data) {
                    $scope.$apply(function () {
                        $scope.contactedUs($data.id);
                    });
                },
                visiblityCond: ($data) => !$data.contactedUs,
                htmlAttributes: {class: 'btn-default'}
            },
        ],
    };

    $scope.cancelledDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getCancelledData(pageNo);
        }
    };

    $scope.currentRemovedDataRow = {};
    $scope.removedDataGrid = {
        columns: [
            {
                label: "Name",
                type: "text",
                valueExp: function ($data) {
                    return $data.housemaid?.name;
                },
            },
            {
                label: "Nationality",
                type: "text",
                valueExp: function ($data) {
                    return $data.housemaid?.nationality?.label;
                },
            },
            {
                label: 'Phone Number',
                type: "html",
                valueExp: function($data){
                    let col = `<button type='button' style='margin-bottom: 4px' class='btn btn-primary btn-sm w3-light-grey w3-hover-text-red' ng-click='openViewNumbersModal(${$data.housemaid?.id}, "${$data.housemaid?.name}")'>View</button>`;
                    return $compile(col)($scope);
                },
            },
            {
                label: "Overstay fees",
                type: "text",
                valueExp: function ($data) {
                    return $data.overstayFees;
                },
            },
            {
                label: "Termination date",
                type: "text",
                valueExp: function ($data) {
                    return $data.housemaid?.dateOfTermination;
                },
            },
            {
                label: "Creation date",
                type: "text",
                valueExp: function ($data) {
                    return $data.creationDate;
                },
            },
        ],
        data: [],
        actions: [
            {
                label: "Maid cleared",
                callbackFunc: function ($data) {
                    $scope.$apply(function () {
                        $scope.currentRemovedDataRow = $data;
                    });
                    magnaMainService.DialogBox.showModal($('#maid-cleared-modal'));
                },
                visiblityCond: ($data) => !$data.contactedUs,
                htmlAttributes: {class: 'btn-default'}
            },
        ],
    };

    $scope.removedDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getRemovedData(pageNo);
        }
    };

    $scope.nationalitiesOptions = {
        placeholder: "Select nationality", width: '100%', data: [],
        ajax: {
            url: __env.PUBLIC + 'picklist/items/nationalities?page=0&size=100',
            data: function (params) { return { search: params.term ? params.term : "" } },
            processResults: function (data) { return { results: $.map(data, function (item) { return { text: item.label, id: item.id, code:item.code } }) }; }
        }
    };

    $scope.maidTypeOptions = {
        placeholder: "Select Type", width: '100%',
        data: [{ id: 'Normal', text: 'Maids.cc'}, { id: 'MAID_VISA', text: 'Maids.visa'},],
    };

    $scope.contactedUs = function (id) {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA  + 'MaidsInGdrfaReport/contactedUs/' + id,
            data: { contactedUs: true }
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Has been set as Contacted Us Successfully!');
            $scope.getCancelledData(0);
        }, { needs_loading_icon: true });
    };

    $scope.maidCleared = {
        status: "Maid_Left_The_Country"
    };

    $scope.maidClearedFunc = function () {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA  + 'MaidsInGdrfaReport/removalConfirmed/' + $scope.currentRemovedDataRow.id,
            data: { maidClearedStatus: $scope.maidCleared.status },
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Has been cleared Successfully!');
            $scope.maidCleared.status = "Maid_Left_The_Country";
            $scope.currentRemovedDataRow = {};
            $('#maid-cleared-modal').modal('hide');
            $scope.getRemovedData(0);
        }, { needs_loading_icon: true });
    };


});
mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env,$compile) {
    $scope.currentPage = 0;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'RPA controller',
            link:'#!/visa/rpa/rpa-controller'
        }, {
            label: 'RPA Errors Resolver',
        }
    ];
    $scope.modulesOptions = {
        placeholder: "Select Module", width: '100%', data: [], ajax: {
            url: __env.ADMIN + 'module/page/?page=0&size=100', data: function (params) { return { search: params.term ? params.term : "" } }, processResults: function (data) { return { results: $.map(data.content, function (item) { return { text: item.name, id: item.code } }) }; }
        }
    };
    $scope.errorTypeOptions = {
        placeholder: "Select Error Type", width: '100%', data: []
    };
    $scope.processOptions = {
        placeholder: "Select Proccess", width: '100%', data: []
    };
    $scope.search = {
        fromDate: "",
        toDate: "",
        selectedModule:"",
        maidName:"",
        selectedprocess:"",
    }
    $scope.selectedErrorType = [];
    $scope.getErrorTypes = function (){
        magnaHttpService.HttpWrapper({
            url: __env.PUBLIC + "picklist/items/rpa_error_type"
        }, function (response) {
            $scope.errorTypeOptions.data = $.map(response, function (item) {
                return {
                    text: item.name,
                    id: item.id,
                    code: item.code,
                }
            });
        }, { needs_loading_icon: false });
    }
    $scope.getProcessOptions = function (){
        magnaHttpService.HttpWrapper({
            url: __env.VISA + "robotic-process/list"
        }, function (response) {
            $scope.processOptions.data = $.map(response, function (item) {
                return {
                    text: item.name,
                    id: item.id,
                }
            });
        }, { needs_loading_icon: false });
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getErrorTypes();
        $scope.getProcessOptions();
        $scope.getTableData(0);
    });
    $scope.doSearch = function (){
        $scope.getTableData(0);
    }

    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        let params ={ page:pageNo, size:__env.DATAGRID_PAGE_SIZE};
        if($scope.search.fromDate) params.from = $scope.search.fromDate;
        if($scope.search.toDate) params.to = $scope.search.toDate;
        if($scope.search.selectedModule) params.moduleCode = $scope.search.selectedModule;
        if($scope.search.maidName) params.maidName = $scope.search.maidName;
        if($scope.search.selectedprocess) params.processId = $scope.search.selectedprocess;
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA  + 'robotic-process/errors',
            data:["UNDER_PROCESS","PENDING"],
            params:params,
            headers:{
                'Content-Type': "application/json"
            },
        }, function (response) {
            $scope.mainDataGrid.data = response.content;
            $scope.mainDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: "Process Name",
                type: "text",
                valueExp: "$data['process']?$data['process']['name']:''"
            },
            {
                label: "Client/Maid Name",
                type: "text",
                valueExp: "$data['name']"
            },
            {
                label: "Error date/time",
                type: "text",
                valueExp: function ($data) {
                    return moment($data.creationDate).format('DD/MM/YYYY HH:mm');
                },
            },
            {
                label: "End user suggestion",
                type: "text",
                valueExp: function ($data) {
                    return $data.suggestedSolution;
                },
            },
            {
                label: "Error type",
                type: "html",
                valueExp: function ($data){
                    var html = `<div class="form-group"><magna-select-input options="errorTypeOptions" ng-model="selectedErrorType[${$data.id}]" ng-model-obj="selectedErrorTypeObj[${$data.id}]"></magna-select-input></div>`;
                    return $compile(html)($scope);
                }
            },
        ],
        data: [],
        actions: [
            {
                label: "Download error screenshot",
                callbackFunc: function ($data) {
                    $scope.$apply(function () {
                        angular.forEach($data.attachments,function (item,index) {
                            magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + item.uuid);
                        })
                    });
                },
                visiblityCond: 'true',
                htmlAttributes: {class: 'btn-default'}
            },
            {
                label: "Resolve",
                callbackFunc: function ($data) {
                    if($scope.selectedErrorType[$data.id]){
                        $scope.changeStatus($data,'SOLVED');
                    }else{
                        magnaMainService.DialogBox.showErrorMsg('Select Error Type First');
                    }
                },
                visiblityCond: function ($data){
                    return $data.status == 'UNDER_PROCESS'?true:false;
                },
                htmlAttributes: {class: 'btn-default'}
            },
            {
                label: "On it",
                callbackFunc: function ($data) {
                    $scope.changeStatus($data,'UNDER_PROCESS');
                },
                visiblityCond: function ($data){
                    return $data.status == 'PENDING'?true:false;
                },
                htmlAttributes: {class: 'btn-default'}
            }
        ]
    };

    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    };

    $scope.changeStatus = function (data,newStatus){
        let postData = {
            status:newStatus
        };
        if($scope.selectedErrorTypeObj[data.id]&&$scope.selectedErrorTypeObj[data.id].code){
            postData.type = $scope.selectedErrorTypeObj[data.id].code;
        }
        magnaHttpService.HttpWrapper({
                method: "PUT",
                url: __env.VISA  + `robotic-process/errors/change-status/${data.id}`,
                data:postData
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Done Successfully');
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true,ignore_error_messages: true,error_handler:function(response){
                debugger
                magnaMainService.DialogBox.showErrorMsg(response.data.message ? response.data.message : response.data);
            } }
        );
    }


});

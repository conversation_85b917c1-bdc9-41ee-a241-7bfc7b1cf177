<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="w3-padding-16 filter-options">
    <div class="panel-group">
        <div class="panel panel-default light_grey">
            <a data-toggle="collapse" data-target="#collapse1" href="javascript:void(0)" class="w3-text-grey w3-hover-text-red" aria-expanded="true">
                <div class="panel-heading">
                    <div class="row w3-margin-0">
                        <div class="pull-left bold"><i class="glyphicon glyphicon-filter"></i> Filters</div>
                        <div class="pull-right">
                            <i class="glyphicon glyphicon-menu-down"></i>
                        </div>
                    </div>
                </div>
            </a>
            <div id="collapse1" class="panel-collapse collapse in">
                <div class="panel-body">
                    <form  class="form-horizontal">
                        <div class="row w3-margin-0">
                            <div class="col-sm-6 w3-padding-8-h">
                                <div class="form-group">
                                    <label class="control-label col-md-3">From Date:</label>
                                    <div class="col-md-9">
                                        <magna-date-time-input ng-model="search.fromDate" options="{startDate: '1900-01-01'}"></magna-date-time-input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 w3-padding-8-h">
                                <div class="form-group">
                                    <label class="control-label col-md-3">To Date:</label>
                                    <div class="col-md-9">
                                        <magna-date-time-input ng-model="search.toDate" options="{startDate: '1900-01-01'}"></magna-date-time-input>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 w3-padding-8-h">
                                <div class="form-group">
                                    <label class="control-label col-md-3">Maid’s name:</label>
                                    <div class="col-md-9">
                                        <input class="form-control" placeholder="Enter Maid’s name" ng-model="search.maidName" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6 w3-padding-8-h">
                                <div class="form-group">
                                    <label class="control-label col-md-3">RPA process name:</label>
                                    <div class="col-md-9">
                                        <magna-select-input options="processOptions" ng-model="search.selectedprocess"></magna-select-input>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row form-actions-container w3-margin-0">
                            <div class="col-sm-12 text-center  w3-padding-32-h w3-padding-top">
                                <button ng-click="doSearch()" class="btn btn-default btn-md btn-raised" >
                                    <i class="glyphicon glyphicon-search"></i> Search </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-12">
        <button class="btn btn-success btn-md btn-raised pull-right" ng-click="exportExcel()">Export Excel</button>
    </div>
</div>
<div class="row w3-margin-0">
    <magna-data-grid config="mainDataGrid"></magna-data-grid>
    <magna-pagination config="mainDataGridPagination"></magna-pagination>
</div>
<div id="dismiss-modal" class="modal fade">
    <div class="modal-dialog" style="width: 50%;">
        <div class="modal-content" style="margin-top: 90px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title text-center bold"> Dismiss Error </h4>
            </div>
            <div class="modal-body add-content">
                <div class="row w3-margin-0 simple-form">
                    <form>
                        <div class="row form-group" >
                            <label class="control-label col-md-12">Requires technical enterfer</label>
                            <div class="col-md-4">
                                <label style="padding-top: 20px">
                                    <input type="radio" ng-model="model.requiresTechEnterfer" value="YES">
                                    Yes
                                </label>
                            </div>
                            <div class="col-md-4">
                                <label style="padding-top: 20px">
                                    <input type="radio" ng-model="model.requiresTechEnterfer" value="NO">
                                    No
                                </label>
                            </div>
                        </div>
                        <div class="row form-group" ng-show="model.requiresTechEnterfer=='YES'" >
                            <label class="col-md-4 control-label">Suggested solution: </label>
                            <div class="col-md-8">
                                <textarea rows="3" style="width: 100%" ng-model="model.suggestedSolution"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-default" ng-click="saveDismiss()" >Save</button>
            </div>
        </div>
    </div>
</div>

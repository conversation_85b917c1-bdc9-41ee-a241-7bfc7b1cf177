mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env,maidccService) {
    $scope.currentPage = 0;
    $scope.pageOptions = $route.current.$$route.page_options;
    $scope.forModule = $scope.pageOptions.forModule;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: $scope.forModule == 'accounting'?'DD RPA controller':'RPA controller',
            link:$scope.forModule == 'accounting'?'#!/visa/rpa/dd-rpa-controller':'#!/visa/rpa/rpa-controller'
        }, {
            label: 'RPA Errors Report',
        }
    ];
    $scope.processOptions = {
        placeholder: "Select Proccess", width: '100%', data: []
    };
    $scope.getProcessOptions = function (){
        magnaHttpService.HttpWrapper({
            url: __env.VISA + "robotic-process/list"
        }, function (response) {
            $scope.processOptions.data = $.map(response, function (item) {
                return {
                    text: item.name,
                    id: item.id,
                }
            });
        }, { needs_loading_icon: false });
    }

    $scope.search = {
        fromDate: "",
        toDate: "",
    }
    $scope.model = {
        suggestedSolution:'',
        requiresTechEnterfer:'',
    }

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
        $scope.getProcessOptions();
    });
    $scope.doSearch = function (){
        $scope.getTableData(0);
    }

    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        let params ={
            page:pageNo, size:__env.DATAGRID_PAGE_SIZE,
            moduleCode:$scope.forModule,
        };
        if($scope.forModule=='accounting') params.exclude = false;
        if($scope.search.fromDate) params.from = $scope.search.fromDate;
        if($scope.search.toDate) params.to = $scope.search.toDate;
        if($scope.search.maidName) params.maidName = $scope.search.maidName;
        if($scope.search.selectedprocess) params.processId = $scope.search.selectedprocess;
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA  + 'robotic-process/errors',
            data:["UNDER_PROCESS","PENDING","SOLVED"],
            params:params,
            headers:{
                'Content-Type': "application/json"
            },
        }, function (response) {
            $scope.mainDataGrid.data = response.content;
            $scope.mainDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true }
        );
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: 'Process Name',
                type: "text",
                valueExp: "$data['process']?$data['process']['name']:''"
            },
            {
                label: ($scope.forModule=='accounting'?'Client Name':'Maid Name'),
                type: "text",
                valueExp: "$data['name']"
            },
            {
                label: "Error type",
                type: "text",
                valueExp: "$data['errorType']?$data['errorType']['name']:''"
            },
            {
                label: "Error date/time",
                type: "text",
                valueExp: function ($data) {
                    return moment($data.creationDate).format('DD/MM/YYYY HH:mm');
                },
            },
            {
                label: "Status",
                type: "text",
                valueExp: function ($data) {
                    return maidccService.getEnumValueLabel($data['status']);
                }
            }
        ],
        data: [],
        actions: [
            {
                label: "Dismiss",
                callbackFunc: function ($data) {
                    $scope.$apply(function () {
                        $scope.currentRow = $data;
                        $scope.model.suggestedSolution = '';
                        $scope.model.requiresTechEnterfer = '';
                        magnaMainService.DialogBox.showModal($('#dismiss-modal'));
                    });
                },
                visiblityCond: function ($data) {
                    return $scope.forModule === 'accounting' && $data.process && $data.process.code == 'UBODTTB' && $data.status!='SOLVED';
                },
                htmlAttributes: {class: 'btn-default'}
            },
        ]
    };
    $scope.saveDismiss = function(){
        magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA  + `robotic-process/errors/${$scope.currentRow.id}/dismiss`,
                params:{
                    requiresTechEnterfer:$scope.model.requiresTechEnterfer == 'YES'?true:false
                },
                data:{
                    suggestedSolution:$scope.model.suggestedSolution,
                }
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Dismissed Successfully');
                $('#dismiss-modal').modal('hide');
                $scope.getTableData($scope.currentPage);
            }, { needs_loading_icon: true }
        );
    }
    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    };

    $scope.exportExcel = function () {
        var params = {moduleCode:$scope.forModule};
        if($scope.search.fromDate)
            params.from = $scope.search.fromDate;
        if($scope.search.toDate)
            params.to = $scope.search.toDate;
        magnaHttpService.downloadFile(__env.VISA  + 'robotic-process/errors/export/excel', { method: "GET", params: params });
    }

});

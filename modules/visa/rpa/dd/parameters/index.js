mainApp.registerCtrl(
  "page-ctrl",
  function (
    $scope,
    $rootScope,
    maidccService,
    magnaMainService,
    magnaHttpService,
    magnaValidationService,
    $route,
    $compile,
    $timeout,
    $location,
  ) {
    $scope.breadcrumbs = [
      {
        label: MaidccModules.getModule("visa").label,
      },
      {
        label: "DD RPA controller",
        link: "#!/visa/rpa/dd-rpa-controller",
      },
      {
        label: "DD RPA Parameters",
      },
    ];


    $scope.$on("$viewContentLoaded", function () {
      $scope.getAutoConfirmDDRPARecordsParam();
    });

    $scope.goToRPAController = function () {
      $location.path("/visa/rpa/dd-rpa-controller");
    };

    $scope.getAutoConfirmDDRPARecordsParam = function () {
      magnaHttpService.HttpWrapper(
        {
          method: "GET",
          url: __env.ADMIN + "parameter/page?page=0&size=20",
          headers: {
            searchFilter: JSON.stringify({
              field: "code",
              operation: "Equals",
              value: "auto_confirm_dd_rpa_records",
              fieldType: "string",
              required: false,
            }),
          },
        },
        function (response) {
          $scope.autoConfirm_DD_RpaRecords = response.content.map((item) => ({
            name: item.name,
            code: item.code,
            id: item.id,
            value: item.value === "true",
          }))[0];

          console.log($scope.autoConfirm_DD_RpaRecords)
        },
        { needs_loading_icon: true },
      );
    };

    $scope.toggleChange = function () {
      magnaHttpService.HttpWrapper(
        {
          method: "POST",
          url: __env.ADMIN + "parameter/update",
          data: { ...$scope.autoConfirm_DD_RpaRecords },
        },
        function (response) {
          magnaMainService.DialogBox.showSuccessMsg("Updated Successfully");
        },
        { needs_loading_icon: true },
      );
    };
  },
);

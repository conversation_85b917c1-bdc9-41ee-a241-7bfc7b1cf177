<style>
    .full-page-card {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: calc(100vh - 120px);
        padding: 1rem;
        margin: 2rem 1rem 1rem;
        border-radius: 5px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        background-color: white;
    }

    .card-content {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 1rem;
        padding: 1rem;
    }

    .button-container {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
        padding: 0 1rem 1rem;
    }
</style>

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>

<div class="full-page-card">
    <div class="card-content">
        <div class="row">
            <div class="col-md-6" ng-show="autoConfirm_DD_RpaRecords.id">
                <div class="form-group">
                    <div class="togglebutton">
                        <label style="font-weight: 500; font-size: 15px !important;">
                            {{autoConfirm_DD_RpaRecords.name}} &nbsp;&nbsp;
                            <input
                                    class="px-1"
                                    ng-model="autoConfirm_DD_RpaRecords.value"
                                    type="checkbox"
                                    ng-click="toggleChange('auto_confirm_dd_rpa_records')"
                            />
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="button-container">
        <button class="btn btn-primary btn-raised" ng-click="goToRPAController()">Close</button>
    </div>
</div>

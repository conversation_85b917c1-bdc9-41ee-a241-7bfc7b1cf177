mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env) {
    $scope.currentPage = 0;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'DD RPA controller',
            link:'#!/visa/rpa/dd-rpa-controller'
        }, {
            label: 'RPA Sent DDs',
        }
    ];

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
    });
    $scope.doSearch = function (){
        $scope.getTableData(0);
    }

    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.ACCOUNTING  + 'ddf_batch_for_rpa/advancesearch?page=' + pageNo + "&size=" + __env.DATAGRID_PAGE_SIZE,
            data:[]
        }, function (response) {
            $scope.mainDataGrid.data = response.content;
            $scope.mainDataGridPagination.paginationInfo = response;
        }, { needs_loading_icon: true });
    }

    $scope.mainDataGrid = {
        columns: [
            {
                label: 'File',
                type: "text",
                valueExp: function ($data){
                    let name = '';
                    $.each($data.attachments,function (index,item){
                        if(item.tag == 'ddf_batch_file_dds'){
                            name = item.name;
                        }
                    })
                    return name;
                }
            },
            {
                label: 'File status',
                type: "text",
                valueExp: "$data['status']?$data['status']['label']:''"
            },
            {
                label: "DDs Reference numbers",
                type: "text",
                valueExp: function ($data){
                    if($data.references){
                        console.log(JSON.parse($data.references));
                        return JSON.parse($data.references).join('/')
                    }
                    return '';
                }
            },
        ],
        data: [],
        actions: [
            {
                label: "Download file",
                callbackFunc: function ($data) {
                    $.each($data.attachments,function (index,item){
                        if(item.tag == 'ddf_batch_file_dds' || item.tag == 'ddf_batch_file_rar'){
                            $scope.downloadFile(item.uuid);
                        }
                    })
                },
                visiblityCond: "true",
                htmlAttributes: { class: 'btn-default' }
            },
            {
                label: "Don't send by RPA",
                callbackFunc: function ($data) {
                    $scope.unsendtobankbyrpa($data.id);
                },
                visiblityCond: function ($data){
                    return $data.status && ($data.status.value == 'ERROR_OCCURRED' || ($data.status.value == 'NOT_SENT' && $data.sendByRPA) );
                },
                htmlAttributes: { class: 'btn-default' }
            }
        ]
    };
    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.getTableData(pageNo);
        }
    };

    $scope.unsendtobankbyrpa = function (id){
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.ACCOUNTING  + 'directdebitfile/unsendtobankbyrpa/'+id,
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Done Successfully');
            $scope.getTableData($scope.currentPage);
        }, { needs_loading_icon: true });
    }
    $scope.downloadFile = function (fileUuid) {
        magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + fileUuid);
    }

});
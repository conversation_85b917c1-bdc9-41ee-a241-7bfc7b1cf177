mainApp.registerCtrl('page-ctrl', function ($scope, $rootScope, maidccService, magnaMainService, magnaHttpService, magnaValidationService, $route,$compile,$timeout,$location) {
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'DD RPA controller'
        }
    ];
    $scope.currentPage = 0;
    $scope.model = {
        statuses:[],
        allActive:null,
    };
    $scope.processorsWatchers = [];

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
    });

    $scope.getTableData = function (pageNo) {
        magnaHttpService.HttpWrapper({
                method: 'POST',
                url: __env.VISA + 'robotic-process/errors?moduleCode=accounting&page=' + pageNo + '&size=' + __env.DATAGRID_PAGE_SIZE,
                data:["UNDER_PROCESS","PENDING"],
            }, function (response) {
                $scope.mainDataGrid.data = response.content;
                $scope.mainDataGridPagination.paginationInfo = response;
            }, {needs_loading_icon: true}
        );
    };

    $scope.mainDataGrid = {
        columns: [{
            label: 'Process name',
            type: 'text',
            valueExp: '$data.process.name'
        }, {
            label: 'Client name',
            type: 'text',
            valueExp: '$data.name'
        }, {
            label: 'Error date/time',
            type: 'text',
            valueExp: function ($data) {
                return moment($data.creationDate).format('DD/MM/YYYY HH:mm');
            },
        }],
        data: [],
        actions: [{
            label: "Download error screenshot",
                callbackFunc: function ($data) {
                    $scope.$apply(function () {
                        angular.forEach($data.attachments,function (item,index) {
                            magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + item.uuid);
                        })
                    });
                },
                visiblityCond: 'true',
                htmlAttributes: {class: 'btn-default'}
            }, {
            label: "Client passed the process",
                callbackFunc: function ($data) {
                    $scope.clientPassedProcess($data.id);
                },
                visiblityCond: 'true',
                htmlAttributes: {class: 'btn-default'}
            }, {
            label: "Stop related RPA processor",
                callbackFunc: function ($data) {
                    $scope.stopRelatedProcessor($data.process.id);
                },
                visiblityCond: function ($data){
                    return $data.process.active;
                },
                htmlAttributes: {class: 'btn-default'}
            }]
    };
    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.currentPage = pageNo;
            $scope.getTableData(pageNo);
        }
    };

    $scope.goToReportPage = function () {
        window.open(`#!/visa/rpa/dd-errors-report`, '_blank');
    }

    // PROCESSORS //
    $scope.showProcessors= function () {
        $scope.allStatusUpdateError = true;
        $scope.model.allActive = null;
        $scope.getProcessorsData();
        magnaMainService.DialogBox.showModal($('#Processors-modal'));
    }
    $scope.getProcessorsData = function () {
        magnaHttpService.HttpWrapper({
                method: 'GET',
                url: __env.VISA + 'robotic-process/list?search=accounting',
            }, function (response) {
                $scope.startWatch = false;
                $scope.allStatusUpdateError = true;
                $scope.statusUpdateError = true;
                $scope.processorsDataGrid.data = response;
                $timeout(function () {
                    $.material.init();
                    $scope.startWatch = true;
                    $scope.allStatusUpdateError = false;
                    $scope.statusUpdateError = false;
                }, 100);
            }, {needs_loading_icon: true}
        );
    };
    $scope.processorsDataGrid = {
        columns: [{
            label: 'Processor name',
            type: 'text',
            valueExp: '$data.name'
        }, {
            label: 'Processor status',
            type: 'html',
            valueExp: function ($data) {
                    var htm=`<div class="form-group"><div class="togglebutton">
                                <label class="">
                                    <input type="checkbox" checked="" ng-model="model.statuses[${$data.id}]"> On/Off
                                </label>
                            </div></div>`;
                    $scope.model.statuses[$data.id] = $data.active;
                    $scope.statusUpdateError = true;
                    $scope.model.allActive = ($data.active && ($scope.model.allActive || $scope.model.allActive === null));
                    $scope.processorsWatchers.push(
                        $scope.$watch('model.statuses['+$data.id+']',function (newVal,oldVal) {
                            if(typeof oldVal !='undefined' && !$scope.statusUpdateError && $scope.startWatch ){
                                $scope.toggleProcessStatus($data.id,newVal);
                            }else{
                                $scope.statusUpdateError = false;
                            }
                        })
                    );
                return $compile(htm)($scope);
            },
        }],
        data: [],
        actions: [
            {
                label: "Run processor for 1 time",
                callbackFunc: function ($data) {
                    alert('not implemented yet');
                },
                visiblityCond: 'true',
                htmlAttributes: { class: 'btn-default' }
            },{
            label: "Control periods",
                callbackFunc: function ($data) {
                    $scope.WorkingDaysHours.showWorkingHours($data.code);
                },
                visiblityCond: function ($data){
                    return true;
                },
                htmlAttributes: {class: 'btn-default'}
            },
        ]
    };

    $scope.checkIfAllChecked = function () {
        $scope.allStatusUpdateError = true;
        $scope.model.allActive =null;
        angular.forEach($scope.model.statuses,function (item,index) {
            $scope.model.allActive = (item && ($scope.model.allActive || $scope.model.allActive === null));
        })
        $timeout(function () {
            $scope.allStatusUpdateError = false;
            $.material.init();
        }, 100);
    }

    $scope.toggleProcessStatus = function(id,status){
        magnaHttpService.HttpWrapper({
                method: 'PUT',
                url: __env.VISA + 'robotic-process/'+id+'/status/toggle',
                data:{active:status,module:'accounting'}
            }, function (response) {
                $scope.checkIfAllChecked();
                magnaMainService.DialogBox.showSuccessMsg('Status Updated Successfully');
            },{needs_loading_icon: true, error_handler: function () {
                $scope.model.statuses[id] = !status;
                $scope.statusUpdateError = true;
            }}
        );
    }

    $scope.stopRelatedProcessor = function(id){
        magnaHttpService.HttpWrapper({
                method: 'PUT',
                url: __env.VISA + 'robotic-process/'+id+'/status/toggle',
                data:{active:false,module:'accounting'}
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Related Processor Stopped Successfully');
                $scope.getTableData($scope.currentPage);
            },{needs_loading_icon: true}
        );
    }
    $scope.clientPassedProcess = function(id){
        magnaHttpService.HttpWrapper({
                method: 'GET',
                url: __env.VISA + 'robotic-process/errors/'+id+'/exclude?exclude=true',
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Done Successfully');
                $scope.getTableData($scope.currentPage);
            },{needs_loading_icon: true}
        );
    }

    $scope.$watch('model.allActive',function (oldVal,newVal) {
        if(typeof oldVal !='undefined' && typeof newVal !='undefined' && !$scope.allStatusUpdateError){
            $scope.toggleAllProcessStatus();
        }else{
            $scope.allStatusUpdateError = false;
        }

    })

    $scope.toggleAllProcessStatus = function(){
        if(typeof $scope.model.allActive === 'boolean'){
            magnaHttpService.HttpWrapper({
                    method: 'PUT',
                    url: __env.VISA + 'robotic-process/status/toggle',
                    data:{active:$scope.model.allActive,module:'accounting'}
                }, function (response) {
                    angular.forEach($scope.model.statuses,function (item,index) {
                        $scope.startWatch = false;
                        $scope.model.statuses[index] = $scope.model.allActive;
                    })
                    $timeout(function () {
                        $scope.startWatch = true;
                        $.material.init();
                    }, 0);
                    magnaMainService.DialogBox.showSuccessMsg('Status Updated Successfully');
                },{needs_loading_icon: true, error_handler: function () {
                    $scope.allStatusUpdateError = true;
                    $scope.model.allActive = !$scope.model.allActive;
                    $scope.allStatusUpdateError = false;
                    $scope.startWatch = false;
                    $.each($scope.model.statuses,function (index,item) {
                        $scope.model.statuses[index] = !$scope.model.allActive;
                    })
                    $timeout(function () {
                        $scope.startWatch = true;
                        $.material.init();
                    }, 0);
                }}
            );
        }
    }

    $('#Processors-modal').on('hidden.bs.modal', function () {
        angular.forEach($scope.processorsWatchers,function (item,index) {
            item();
        })
    })

    $scope.goToSendDDs = function (){
        $location.path('/visa/rpa/sent-dds');
    }

    $scope.goToParametersPage = function () {
        window.open(`#!/visa/rpa/dd-rpa-parameters`, '_blank');
    }


    $scope.workingHours = {
        days:["Sunday","Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
        selectedDays:[],
        workingHours:[],
        timeUnitOptions:{ placeholder: "Select Unit", width: '100%',allowClear:false, data: [
                {id:'Second',text:'Seconds',factor:1},
                {id:'Minute',text:'Minutes',factor:60},
                {id:'Hour',text:'Hours',factor:3600},
            ],
        },
        getUnitFactor:function(id){
            return id?this.timeUnitOptions.data.filter(_=>_.id == id)[0]?.factor??1:1;
        },
        snoozePeriod: 0,
        recheckPeriod: 0,
        pausePeriodBetweenMaids: 0,
        selectedUnitSnoozePeriod: '',
        selectedUnitRecheckPeriod: '',
        selectedUnitPausePeriodBetweenMaids: '',
        selectedUnitSnoozePeriodObj: '',
        selectedUnitRecheckPeriodObj: '',
        selectedUnitPausePeriodBetweenMaidsObj: '',
        addWorkingHours:function () {
            $scope.workingHours.workingHours.push({fromTime:'',toTime:''})
        },
        removeWorkingHours:function (i) {
            $scope.workingHours.workingHours.splice(i, 1)
        },
        showDialog:function () {
            this.selectedDays = [];
            this.workingHours = [];
            this.snoozePeriod = 0;
            this.recheckPeriod = 0;
            this.pausePeriodBetweenMaids = 0;
            this.selectedUnitSnoozePeriod = 'Second';
            this.selectedUnitRecheckPeriod = 'Second';
            this.selectedUnitPausePeriodBetweenMaids = 'Second';

            magnaMainService.DialogBox.showModal($('#workingHours-modal'));
            $timeout(function () {
                $.material.checkbox();
            });
            this.getData();
        },
        save:function(){
            let data = {
                "days": $.map(this.selectedDays,function (item,index){ if(item) return $scope.workingHours.days[index] }).join(','),
                "snoozePeriod": this.snoozePeriod&&this.selectedUnitSnoozePeriod?this.snoozePeriod*this.selectedUnitSnoozePeriodObj.factor:0,
                "recheckPeriod": this.recheckPeriod&&this.selectedUnitRecheckPeriod?this.recheckPeriod*this.selectedUnitRecheckPeriodObj.factor:0,
                "pausePeriodBetweenMaids": this.pausePeriodBetweenMaids&&this.selectedUnitPausePeriodBetweenMaids?this.pausePeriodBetweenMaids*this.selectedUnitPausePeriodBetweenMaidsObj.factor:0,
                "snoozePeriodUnit": this.selectedUnitSnoozePeriod?this.selectedUnitSnoozePeriod:'',
                "recheckPeriodUnit": this.selectedUnitRecheckPeriod?this.selectedUnitRecheckPeriod:'',
                "pausePeriodBetweenMaidsUnit": this.selectedUnitPausePeriodBetweenMaids?this.selectedUnitPausePeriodBetweenMaids:'',
                "workingHours": this.workingHours
            };

            magnaHttpService.HttpWrapper({
                    method: 'POST',
                    url: __env.VISA + 'rpa-working-times/create',
                    data: data
                }, function (response) {
                    $('#workingHours-modal').modal('hide');
                    magnaMainService.DialogBox.showSuccessMsg('Updated successfully');
                }, {needs_loading_icon: true}
            );
        },
        getData:function () {
            magnaHttpService.HttpWrapper({
                    method: 'GET',
                    url: __env.VISA + 'rpa-working-times/list'
                }, function (response) {
                    if (response && response.length > 0) {
                        response = response[0];
                        // Reset selected days
                        $scope.workingHours.selectedDays = [];

                        // Set working hours
                        if (response.workingHours && response.workingHours.length > 0) {
                            $scope.workingHours.workingHours = $.map(response.workingHours, function (item) {
                                return {fromTime: item.fromTimeString || item.fromTime, toTime: item.toTimeString || item.toTime};
                            });
                        } else {
                            $scope.workingHours.workingHours = [{fromTime:'', toTime:''}];
                        }

                        // Set selected days
                        if (response.days) {
                            $.each(response.days.split(','), function (index, item) {
                                if (item && $scope.workingHours.days.indexOf(item.trim()) !== -1) {
                                    $scope.workingHours.selectedDays[$scope.workingHours.days.indexOf(item.trim())] = true;
                                }
                            });
                        }

                        // Set period values
                        $scope.workingHours.snoozePeriod = response.snoozePeriod/$scope.workingHours.getUnitFactor(response.snoozePeriodUnit);
                        $scope.workingHours.recheckPeriod = response.recheckPeriod/$scope.workingHours.getUnitFactor(response.recheckPeriodUnit);
                        $scope.workingHours.pausePeriodBetweenMaids = response.pausePeriodBetweenMaids/$scope.workingHours.getUnitFactor(response.pausePeriodBetweenMaidsUnit);
                        $scope.workingHours.selectedUnitSnoozePeriod = response.snoozePeriodUnit || 'Second';
                        $scope.workingHours.selectedUnitRecheckPeriod = response.recheckPeriodUnit || 'Second';
                        $scope.workingHours.selectedUnitPausePeriodBetweenMaids = response.pausePeriodBetweenMaidsUnit || 'Second';
                    } else {
                        // Initialize with default values
                        $scope.workingHours.selectedDays = [];
                        $scope.workingHours.workingHours = [{fromTime:'', toTime:''}];
                        $scope.workingHours.snoozePeriod = 0;
                        $scope.workingHours.recheckPeriod = 0;
                        $scope.workingHours.pausePeriodBetweenMaids = 0;
                        $scope.workingHours.selectedUnitSnoozePeriod = 'Second';
                        $scope.workingHours.selectedUnitRecheckPeriod = 'Second';
                        $scope.workingHours.selectedUnitPausePeriodBetweenMaids = 'Second';
                    }
                }, {needs_loading_icon: true}
            );
        }
    };

    //Working days
    $scope.WorkingDaysHours = {
        currentCode:'',
        workingHours:[],
        days:["Sunday","Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
        timeUnitOptions:{ placeholder: "Select Unit", width: '100%',allowClear:false, data: [
                {id:'Second',text:'Seconds',factor:1},
                {id:'Minute',text:'Minutes',factor:60},
                {id:'Hour',text:'Hours',factor:3600},
            ],
        },
        getUnitFactor:function(id){
            return id?this.timeUnitOptions.data.filter(_=>_.id == id)[0]?.factor??1:1;
        },
        selectedDays:[],
        snoozePeriod: 0,
        recheckPeriod: 0,
        pausePeriodBetweenMaids: 0,
        selectedUnitSnoozePeriod: '',
        selectedUnitRecheckPeriod: '',
        selectedUnitPausePeriodBetweenMaids: '',
        selectedUnitSnoozePeriodObj: '',
        selectedUnitRecheckPeriodObj: '',
        selectedUnitPausePeriodBetweenMaidsObj: '',
        addWorkingHours:function () {
            $scope.WorkingDaysHours.workingHours.push({fromTime:'',toTime:''})
        },
        removeWorkingHours:function (i) {
            $scope.WorkingDaysHours.workingHours.splice(i, 1)
        },
        showWorkingHours:function (code) {
            //$('#scheduledDateOfTerminationFieldContainer').html($compile(`<magna-date-input ng-disabled="runAwaySelected" ng-model="cancellationData.contract.scheduledDateOfTermination" options="scheduledDateOfTerminationOptions"></magna-date-input>`)($scope));
            this.currentCode = code;
            let params = code?{code:code}:{}
            magnaHttpService.HttpWrapper({
                method:'GET',
                url: __env.VISA + 'rpa-working-times/list',
                params:params
            }, function (response) {
                if(response.length>0){
                    response = response[0];
                    $.each(response.days.split(','),function (index,item) {
                        if(item&&$scope.WorkingDaysHours.days.indexOf(item.trim())!=-1){
                            $scope.WorkingDaysHours.selectedDays[$scope.WorkingDaysHours.days.indexOf(item.trim())] = true;
                        }
                    })
                    $scope.WorkingDaysHours.workingHours = $.map(response.workingHours,function (item) {
                        return {fromTime:item.fromTimeString,toTime:item.toTimeString}
                    })
                    $scope.WorkingDaysHours.snoozePeriod= response.snoozePeriod/$scope.WorkingDaysHours.getUnitFactor(response.snoozePeriodUnit);
                    $scope.WorkingDaysHours.recheckPeriod= response.recheckPeriod/$scope.WorkingDaysHours.getUnitFactor(response.recheckPeriodUnit);
                    $scope.WorkingDaysHours.pausePeriodBetweenMaids= response.pausePeriodBetweenMaids/$scope.WorkingDaysHours.getUnitFactor(response.pausePeriodBetweenMaidsUnit);
                    $scope.WorkingDaysHours.selectedUnitSnoozePeriod= response.snoozePeriodUnit;
                    $scope.WorkingDaysHours.selectedUnitRecheckPeriod= response.recheckPeriodUnit;
                    $scope.WorkingDaysHours.selectedUnitPausePeriodBetweenMaids= response.pausePeriodBetweenMaidsUnit;
                }else{
                    $scope.WorkingDaysHours.selectedDays = [];
                    $scope.WorkingDaysHours.workingHours = [];
                    $scope.WorkingDaysHours.snoozePeriod= 0;
                    $scope.WorkingDaysHours.recheckPeriod= 0;
                    $scope.WorkingDaysHours.pausePeriodBetweenMaids= 0;
                    $scope.WorkingDaysHours.selectedUnitSnoozePeriod= 'Second';
                    $scope.WorkingDaysHours.selectedUnitRecheckPeriod= 'Second';
                    $scope.WorkingDaysHours.selectedUnitPausePeriodBetweenMaids= 'Second';
                }
                $scope.ignoreCLearWatchers = true;
                magnaMainService.DialogBox.showModal($('#working-days-modal'))
            },{needs_loading_icon: true});
        },
        // getWorkingHours:function () {
        //     $scope.WorkingDaysHours.workingHours
        // },
        save:function () {
            debugger
            let params = this.currentCode?{code:this.currentCode}:{}
            let data = {
                "days": $.map(this.selectedDays,function (item,index){ if(item) return $scope.WorkingDaysHours.days[index] }).join(','),
                "snoozePeriod": this.snoozePeriod&&this.selectedUnitSnoozePeriod?this.snoozePeriod*this.selectedUnitSnoozePeriodObj.factor:0,
                "recheckPeriod": this.recheckPeriod&&this.selectedUnitRecheckPeriod?this.recheckPeriod*this.selectedUnitRecheckPeriodObj.factor:0,
                "pausePeriodBetweenMaids": this.pausePeriodBetweenMaids&&this.selectedUnitPausePeriodBetweenMaids?this.pausePeriodBetweenMaids*this.selectedUnitPausePeriodBetweenMaidsObj.factor:0,
                "snoozePeriodUnit": this.selectedUnitSnoozePeriod?this.selectedUnitSnoozePeriod:'',
                "recheckPeriodUnit": this.selectedUnitRecheckPeriod?this.selectedUnitRecheckPeriod:'',
                "pausePeriodBetweenMaidsUnit": this.selectedUnitPausePeriodBetweenMaids?this.selectedUnitPausePeriodBetweenMaids:'',
                "workingHours":this.workingHours
            };
            magnaHttpService.HttpWrapper({
                method:'POST',
                url: __env.VISA + 'rpa-working-times/create',
                data:data,
                params:params
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg(`Saved Successfully`);
                $('#working-days-modal').modal('hide')
            },{needs_loading_icon: true});

        }
    }

});

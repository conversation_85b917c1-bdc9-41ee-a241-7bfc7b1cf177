<style>
    .btn-circle {
        margin-top: 16px;
        border-radius: 50px;
        padding: 1rem 1.4rem;
    }
    .btn-circle-xs {
        padding: 0.6rem 0.9rem !important;
    }
</style>

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>

<div class="row w3-margin-0 ">
    <button type="button" class="btn btn-primary btn-raised" ng-click="showProcessors()">RPA Processors</button>
    <button type="button" class="btn btn-primary btn-raised" ng-click="goToReportPage()">Show error report</button>
    <button type="button" class="btn btn-primary btn-raised" ng-click="goToSendDDs()">Show sent DDs</button>
    <button type="button" class="btn btn-primary btn-raised" ng-click="goToParametersPage()">RPA Parameters</button>
<!--    <button type="button" class="btn btn-primary btn-raised" ng-click="workingHours.showDialog()">RPA working days/hours</button>-->
</div>

<div class="row w3-margin-0">
    <magna-data-grid config="mainDataGrid"></magna-data-grid>
    <magna-pagination config="mainDataGridPagination"></magna-pagination>
</div>

<div id="Processors-modal" class="modal fade">
    <div class="modal-dialog" style="width: 70%;">
        <div class="modal-content" style="margin-top: 90px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title text-center bold"> RPA Processors </h4>
            </div>
            <div class="modal-body add-content">
                <div class="row w3-margin-0 simple-form">
                    <form>
                        <div class="form-group is-empty col-md-6">
                            <div class="col-md-12">
                                <div class="togglebutton">
                                    <label class="">
                                        Turn ON/OFF all processors &nbsp;&nbsp;
                                        <input type="checkbox" checked="" ng-model="model.allActive">
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 w3-padding-0 ">
                            <button type="button" class="btn btn-primary btn-raised pull-right"
                                    ng-click="workingHours.showDialog()">
                                RPA Working Days/Hours Default Values
                            </button>
                        </div>
                    </form>
                <div>
                </div>
                </div>
                <div class="row w3-margin-0 relative">
                    <magna-data-grid config="processorsDataGrid"></magna-data-grid>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>
<div id="workingHours-modal" class="modal fade">
    <div class="modal-dialog" style="width: 70%;">
        <div class="modal-content" style="margin-top: 90px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title text-center bold"> Default Working Days/Hours </h4>
            </div>
            <div class="modal-body add-content">
                <div class="row w3-margin-0 simple-form">
                    <form>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-3">Daily working hours:</label>
                            <div class="col-md-7">
                                <div ng-repeat="workingHour in workingHours.workingHours" class="row w3-margin-0">
                                    <div class="col-md-5">
                                        <label class="control-label">From:</label>
                                        <div>
                                            <magna-time-input ng-model="workingHour.fromTime"></magna-time-input>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <label class="control-label">To:</label>
                                        <div>
                                            <magna-time-input ng-model="workingHour.toTime"></magna-time-input>
                                        </div>
                                    </div>
                                    <div class="col-md-2" style="padding-top: 3rem;padding-top: 3.5rem;padding-left: 0;">
                                        <button class="btn btn-circle btn-circle-xs btn-xs btn-danger btn-raised" ng-click="workingHours.removeWorkingHours($index)" style="margin-top:0;">
                                            <i class="glyphicon glyphicon-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-circle btn-danger btn-raised" ng-click="workingHours.addWorkingHours()" style="margin-top:0;">
                                    <i class="glyphicon glyphicon-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Working days:</label>
                            <div class="col-md-8">
                                <div class="form-group" style="margin: 0!important;" ng-repeat="day in workingHours.days">
                                    <div class="checkbox w3-margin-0">
                                        <label>
                                            <input type="checkbox" ng-model="workingHours.selectedDays[$index]">
                                            <span class="checkbox-material"></span> {{day}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Process snooze period:</label>
                            <div class="col-md-2">
                                <input class="form-control" ng-model="workingHours.snoozePeriod" />
                            </div>
                            <div class="col-md-3">
                                <magna-select-input options="workingHours.timeUnitOptions" ng-model="workingHours.selectedUnitSnoozePeriod" ng-model-obj="workingHours.selectedUnitSnoozePeriodObj"></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Process recheck period:</label>
                            <div class="col-md-2">
                                <input class="form-control" ng-model="workingHours.recheckPeriod" />
                            </div>
                            <div class="col-md-3">
                                <magna-select-input options="workingHours.timeUnitOptions" ng-model="workingHours.selectedUnitRecheckPeriod" ng-model-obj="workingHours.selectedUnitRecheckPeriodObj"></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Pause period between maids:</label>
                            <div class="col-md-2">
                                <input class="form-control" ng-model="workingHours.pausePeriodBetweenMaids" />
                            </div>
                            <div class="col-md-3">
                                <magna-select-input options="workingHours.timeUnitOptions" ng-model="workingHours.selectedUnitPausePeriodBetweenMaids" ng-model-obj="workingHours.selectedUnitPausePeriodBetweenMaidsObj"></magna-select-input>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-default btn-raised" ng-click="workingHours.save()">Save</button>
            </div>
        </div>
    </div>
</div>
<div id="working-days-modal" class="modal fade">
    <div class="modal-dialog" style="width: 70%;">
        <div class="modal-content" style="margin-top: 90px">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title text-center bold"> Working Days/Hours </h4>
            </div>
            <div class="modal-body add-content">
                <div class="row w3-margin-0 simple-form">
                    <form>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-3">Daily working hours:</label>
                            <div class="col-md-7">
                                <div ng-repeat="workingHour in WorkingDaysHours.workingHours" class="row w3-margin-0">
                                    <div class="col-md-5">
                                        <label class="control-label">From:</label>
                                        <div>
                                            <magna-time-input ng-model="workingHour.fromTime" ></magna-time-input>
                                        </div>
                                    </div>
                                    <div class="col-md-5">
                                        <label class="control-label">To:</label>
                                        <div>
                                            <magna-time-input ng-model="workingHour.toTime" ></magna-time-input>
                                        </div>
                                    </div>
                                    <div class="col-md-2" style="padding-top: 3rem;padding-top: 3.5rem;padding-left: 0;">
                                        <button class="btn btn-circle btn-circle-xs btn-xs btn-danger btn-raised" ng-click="WorkingDaysHours.removeWorkingHours($index)" style="margin-top:0;">
                                            <i class="glyphicon glyphicon-minus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-circle btn-danger btn-raised" ng-click="WorkingDaysHours.addWorkingHours()" style="margin-top:0;">
                                    <i class="glyphicon glyphicon-plus"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Working days:</label>
                            <div class="col-md-8">
                                <div class="form-group" style="margin: 0!important;" ng-repeat="day in WorkingDaysHours.days">
                                    <div class="checkbox w3-margin-0">
                                        <label>
                                            <input type="checkbox" ng-model="WorkingDaysHours.selectedDays[$index]">
                                            <span class="checkbox-material"></span> {{day}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Process snooze period:</label>
                            <div class="col-md-2">
                                <input class="form-control" ng-model="WorkingDaysHours.snoozePeriod" />
                            </div>
                            <div class="col-md-3">
                                <magna-select-input options="WorkingDaysHours.timeUnitOptions" ng-model="WorkingDaysHours.selectedUnitSnoozePeriod" ng-model-obj="WorkingDaysHours.selectedUnitSnoozePeriodObj" ></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Process recheck period:</label>
                            <div class="col-md-2">
                                <input class="form-control" ng-model="WorkingDaysHours.recheckPeriod" />
                            </div>
                            <div class="col-md-3">
                                <magna-select-input options="WorkingDaysHours.timeUnitOptions" ng-model="WorkingDaysHours.selectedUnitRecheckPeriod" ng-model-obj="WorkingDaysHours.selectedUnitRecheckPeriodObj" ></magna-select-input>
                            </div>
                        </div>
                        <div class="form-group row w3-margin-0">
                            <label class="control-label col-md-4">Pause period between maids:</label>
                            <div class="col-md-2">
                                <input class="form-control" ng-model="WorkingDaysHours.pausePeriodBetweenMaids" />
                            </div>
                            <div class="col-md-3">
                                <magna-select-input options="WorkingDaysHours.timeUnitOptions" ng-model="WorkingDaysHours.selectedUnitPausePeriodBetweenMaids" ng-model-obj="WorkingDaysHours.selectedUnitPausePeriodBetweenMaidsObj" ></magna-select-input>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-default btn-raised" ng-click="WorkingDaysHours.save()" >Save</button>
            </div>
        </div>
    </div>
</div>

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<div class="container-fluid add-content">
    <form class="form-horizontal" name="parameters_form">
        <div class="row  w3-margin-0">
            <div class="col-sm-12 w3-padding-32-h" >
                <div class="form-group" ng-repeat="param in allParameters" >
                    <label class="control-label col-md-4">{{param.keyLabel}}</label>
                    <div class="col-md-7">
                        <input ng-if="!param.valueOptions" class="form-control" type="text" ng-model="param.keyValue"  ng-change="param.changed=true"/>
                        <magna-select-input ng-if="param.valueOptions" options="param.valueOptions" ng-model="param.keyValue" on-change="selectChanged"></magna-select-input>
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-success btn-md btn-raised w3-margin-0" ng-disabled="!param.changed" ng-click="saveIndividual(param)">Save</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="row form-actions-container">
            <div class="form-group">
                <div class="col-sm-6 text-right pull-right  w3-padding-32-h">
                    <div class="col-md-offset-4 col-md-8">
                        <button type="button" class="btn btn-default btn-md" ng-click="goToReturnPage()">Cancel</button>&nbsp;
<!--                        <button type="submit" class="btn btn-default btn-md btn-raised" ng-click="save()">Save</button>-->
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

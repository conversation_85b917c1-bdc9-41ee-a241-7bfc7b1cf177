mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env,$compile) {
    $scope.currentPage = 0;
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'RPA controller',
            link:'#!/visa/rpa/rpa-controller'
        }, {
            label: 'RPA Parameters',
        }
    ];
    $scope.$on('$viewContentLoaded', function () {
        $scope.getParametersData();
    });

    $scope.getParametersData = function () {
        magnaHttpService.HttpWrapper({
            method: "GET",
            url: __env.VISA  + 'rpa-config/parameters?type=USER',
        }, function (response) {
            $scope.allParameters = $.map(response,function (item){
                if(item.valueOptions){
                    item.valueOptions = {
                            placeholder: "Select Value",
                            width: "100%",
                            data: $.map(item.valueOptions.split(','),function (optionItem){
                                return {id:optionItem,text:optionItem,paramID:item.id}
                            }),
                        };
                }
                return item;
            });
        }, { needs_loading_icon: true });
    }
    $scope.goToReturnPage = function (){
        $location.path("/visa/rpa/rpa-controller");
    }
    $scope.selectChanged = function(param,b,v){
        $.each($scope.allParameters,function (index,item) {
            if(item.id == param.paramID && param.id != item.keyValue){
                item.changed = true;
                $scope.allParameters[index] = item;
            }
        })
    }

    $scope.saveIndividual = function (param){
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA  + 'rpa-config/update',
            data:{id:param.id,keyValue:param.keyValue},
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Saved Successfully');
            param.changed=false;
            if(param.keyName.includes('mohreInsuranceCreditName')){
                magnaHttpService.HttpWrapper({
                    method: "GET",
                    url: __env.VISA  + 'rpa-config/updateMohreActiveCardValueOptions',
                }, function (response) {
                }, { needs_loading_icon: true });
            }
        }, { needs_loading_icon: true });
    }

});

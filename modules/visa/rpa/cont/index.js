mainApp.registerCtrl('page-ctrl', function ($scope, $rootScope, maidccService, magnaMainService, magnaHttpService, magnaValidationService, $route,$compile,$timeout) {
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'RPA controller'
        }
    ];
    $scope.currentPage = 0;
    $scope.excluded = null;
    $scope.model = {
        statuses:[],
        includedMaidsOnly:[],
        allActive:null,
        excludedMaid:'',
        housemaidFilter:'',
        housemaidOptions: {
            placeholder: "Select maids",
            width: '100%',
            data: [],
            ajax: {
                url: function (params){
                    return __env.VISA + 'robotic-process/getExcludedRequestSuggestionsInProcess/' + $scope.currentProcessor.requestType;
                },
                data: function (params) {
                    return {
                        name: params.term ? params.term : ""
                    };
                },
                processResults: function (data) {
                    // Tranforms the top-level key of the response object from 'items' to 'results'
                    return {
                        results: $.map(data.content, function (item) {
                            return {
                                text: item.name,
                                id: item.id
                            };
                        })
                    };
                }

            }
        }
    };
    $scope.processorsWatchers = [];

    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
    });

    $scope.getTableData = function (pageNo) {
        magnaHttpService.HttpWrapper({
                method: 'POST',
                url: __env.VISA + 'robotic-process/errors?moduleCode=visa&page=' + pageNo + '&size=' + __env.DATAGRID_PAGE_SIZE,
                data:["UNDER_PROCESS","PENDING"],
            }, function (response) {
                $scope.mainDataGrid.data = response.content;
                $scope.mainDataGridPagination.paginationInfo = response;
            }, {needs_loading_icon: true}
        );
    };

    $scope.mainDataGrid = {
        columns: [{
            label: 'VP Step name',
            type: 'text',
            valueExp: '$data.process.name'
        }, {
            label: 'Maid name',
            type: 'text',
            valueExp: '$data.name'
        }, {
            label: 'Error date/time',
            type: 'text',
            valueExp: function ($data) {
                return moment($data.creationDate).format('DD/MM/YYYY HH:mm');
            },
        }],
        data: [],
        actions: [{
            label: "Download error screenshot",
                callbackFunc: function ($data) {
                    $scope.$apply(function () {
                        angular.forEach($data.attachments,function (item,index) {
                            magnaHttpService.downloadFile(__env.PUBLIC + 'download/' + item.uuid);
                        })
                    });
                },
                visiblityCond: 'true',
                htmlAttributes: {class: 'btn-default'}
            }, {
            label: "Go to related VP step",
                callbackFunc: function ($data) {
                    window.open(`#!/${$data.url}`, '_blank');
                },
                visiblityCond: 'true',
                htmlAttributes: {class: 'btn-default'}
            }, {
            label: "Stop related RPA processor",
                callbackFunc: function ($data) {
                    $scope.stopRelatedProcessor($data.process.id);
                },
                visiblityCond: function ($data){
                    return $data.process.active;
                },
                htmlAttributes: {class: 'btn-default'}
            }]
    };
    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.currentPage = pageNo;
            $scope.getTableData(pageNo);
        }
    };

    $scope.execludedDataGrid = {
        columns: [{
            label: 'Maid Name',
            type: 'text',
            valueExp: '$data.name'
        }],
        data: [],
        actions: [{
            label: "Remove",
                callbackFunc: function ($data) {
                    magnaMainService.DialogBox.showWarningMsg(`Are you sure you want to remove ${$data.name} ?`, function () {
                        $scope.removeExcluded($data.requestId);
                    },null,{ okBtnLabel: "Yes", cancelBtnLabel: "No" });

                },
                visiblityCond: function ($data){
                    return true;
                },
                htmlAttributes: {class: 'btn-default'}
            }]
    };
    $scope.execludedDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
            $scope.currentExecludedPageNum = pageNo;
            $scope.getExcludedMaidForProcess(pageNo);
        }
    };

    $scope.goToReportPage = function () {
        window.open(`#!/visa/rpa/errors-report`, '_blank');
    }
    $scope.goToParametersPage = function () {
        window.open(`#!/visa/rpa/parameters`, '_blank');
    }

    // PROCESSORS //
    $scope.showProcessors= function () {
        $scope.allStatusUpdateError = true;
        $scope.model.allActive = null;
        $scope.getProcessorsData();
        magnaMainService.DialogBox.showModal($('#Processors-modal'));
    }
    $scope.getProcessorsData = function () {
        magnaHttpService.HttpWrapper({
                method: 'GET',
                url: __env.VISA + 'robotic-process/list?search=visa',
            }, function (response) {
                $scope.startWatch = false;
                $scope.allStatusUpdateError = true;
                $scope.statusUpdateError = true;
                $scope.processorsDataGrid.data = response;
                $timeout(function () {
                    $.material.init();
                    $scope.startWatch = true;
                    $scope.allStatusUpdateError = false;
                    $scope.statusUpdateError = false;
                }, 100);
            }, {needs_loading_icon: true}
        );
    };
    $scope.processorsDataGrid = {
        columns: [{
            label: 'Processor name',
            type: 'text',
            valueExp: '$data.name'
        },{
            label: 'Excluded maids',
            type: 'text',
            valueExp: '$data.excludedCount'
        }, {
            label: 'Included maids only',
            type: 'html',
            valueExp: function ($data) {
                var htm=`<div class="form-group"><div class="togglebutton">
                                <label class="">
                                    <input type="checkbox" checked="" ng-model="model.includedMaidsOnly[${$data.id}]"> On/Off
                                </label>
                            </div></div>`;
                $scope.model.includedMaidsOnly[$data.id] = $data.includedMaidsOnly;
                $scope.IncludedMaidsUpdateError = true;
                $scope.processorsWatchers.push(
                    $scope.$watch('model.includedMaidsOnly['+$data.id+']',function (newVal,oldVal) {
                        if(typeof oldVal !='undefined' && !$scope.IncludedMaidsUpdateError && $scope.startWatch ){
                            $scope.toggleProcessIncludedMaidsOnly($data.id,newVal);
                        }else{
                            $scope.IncludedMaidsUpdateError = false;
                        }
                    })
                );
                return $compile(htm)($scope);
            },
        }, {
            label: 'Processor status',
            type: 'html',
            valueExp: function ($data) {
                    var htm=`<div class="form-group"><div class="togglebutton">
                                <label class="">
                                    <input type="checkbox" checked="" ng-model="model.statuses[${$data.id}]"> On/Off
                                </label>
                            </div></div>`;
                    $scope.model.statuses[$data.id] = $data.active;
                    $scope.statusUpdateError = true;
                    $scope.model.allActive = ($data.active && ($scope.model.allActive || $scope.model.allActive === null));
                    $scope.processorsWatchers.push(
                        $scope.$watch('model.statuses['+$data.id+']',function (newVal,oldVal) {
                            if(typeof oldVal !='undefined' && !$scope.statusUpdateError && $scope.startWatch ){
                                $scope.toggleProcessStatus($data.id,newVal);
                            }else{
                                $scope.statusUpdateError = false;
                            }
                        })
                    );
                return $compile(htm)($scope);
            },
        }],
        data: [],
        actions: [
            {
                label: "Exclude maids",
                callbackFunc: function ($data) {
                    $scope.excluded = true;
                    $scope.currentProcessor = $data;
                    $scope.model.housemaidFilter = '';
                    $scope.getExcludedMaidForProcess(0,true);
                },
                visiblityCond: 'true',
                htmlAttributes: { class: 'btn-default' }
            },
            {
                label: "Included maids",
                callbackFunc: function ($data) {
                    $scope.excluded = false;
                    $scope.currentProcessor = $data;
                    $scope.model.housemaidFilter = '';
                    $scope.getExcludedMaidForProcess(0,true);
                },
                visiblityCond: 'true',
                htmlAttributes: { class: 'btn-default' }
            },
            {
                label: "Control periods",
                callbackFunc: function ($data) {
                    $scope.WorkingDaysHours.showWorkingHours($data.code);
                },
                visiblityCond: function ($data){
                    return true;
                },
                htmlAttributes: {class: 'btn-default'}
            },
            {
                label: "Run processor for 1 time",
                callbackFunc: function ($data) {
                    alert('not implemented yet');
                },
                visiblityCond: 'true',
                htmlAttributes: { class: 'btn-default' }
            },
        ]
    };

    $scope.checkIfAllChecked = function () {
        $scope.allStatusUpdateError = true;
        $scope.model.allActive =null;
        angular.forEach($scope.model.statuses,function (item,index) {
            $scope.model.allActive = (item && ($scope.model.allActive || $scope.model.allActive === null));
        })
        $timeout(function () {
            $scope.allStatusUpdateError = false;
            $.material.init();
        }, 100);
    }

    $scope.toggleProcessStatus = function(id,status){
        magnaHttpService.HttpWrapper({
                method: 'PUT',
                url: __env.VISA + 'robotic-process/'+id+'/status/toggle',
                data:{active:status,module:'visa'}
            }, function (response) {
                $scope.checkIfAllChecked();
                magnaMainService.DialogBox.showSuccessMsg('Status Updated Successfully');
            },{needs_loading_icon: true, error_handler: function () {
                $scope.model.statuses[id] = !status;
                $scope.statusUpdateError = true;
            }}
        );
    }


    $scope.toggleProcessIncludedMaidsOnly = function(id,status){
        magnaHttpService.HttpWrapper({
                method: 'PUT',
                url: __env.VISA + 'robotic-process/'+id+'/includedMaidsOnly/toggle',
                data:{active:status}
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Updated Successfully');
            },{needs_loading_icon: true, error_handler: function () {
                $scope.model.statuses[id] = !status;
                $scope.IncludedMaidsUpdateError = true;
            }}
        );
    }

    $scope.stopRelatedProcessor = function(id){
        magnaHttpService.HttpWrapper({
                method: 'PUT',
                url: __env.VISA + 'robotic-process/'+id+'/status/toggle',
                data:{active:false,module:'visa'}
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Related Processor Stopped Successfully');
                $scope.getTableData($scope.currentPage);
            },{needs_loading_icon: true}
        );
    }

    $scope.$watch('model.allActive',function (oldVal,newVal) {
        if(typeof oldVal !='undefined' && typeof newVal !='undefined' && !$scope.allStatusUpdateError){
            $scope.toggleAllProcessStatus();
        }else{
            $scope.allStatusUpdateError = false;
        }

    })

    $scope.toggleAllProcessStatus = function(){
        if(typeof $scope.model.allActive === 'boolean'){
            magnaHttpService.HttpWrapper({
                    method: 'PUT',
                    url: __env.VISA + 'robotic-process/status/toggle',
                    data:{active:$scope.model.allActive,module:'visa'}
                }, function (response) {
                    angular.forEach($scope.model.statuses,function (item,index) {
                        $scope.startWatch = false;
                        $scope.model.statuses[index] = $scope.model.allActive;
                    })
                    $timeout(function () {
                        $scope.startWatch = true;
                        $.material.init();
                    }, 0);
                    magnaMainService.DialogBox.showSuccessMsg('Status Updated Successfully');
                },{needs_loading_icon: true, error_handler: function () {
                    $scope.allStatusUpdateError = true;
                    $scope.model.allActive = !$scope.model.allActive;
                    $scope.allStatusUpdateError = false;
                    $scope.startWatch = false;
                    $.each($scope.model.statuses,function (index,item) {
                        $scope.model.statuses[index] = !$scope.model.allActive;
                    })
                    $timeout(function () {
                        $scope.startWatch = true;
                        $.material.init();
                    }, 0);
                }}
            );
        }
    }

    $('#Processors-modal').on('hidden.bs.modal', function () {
        if(!$scope.ignoreCLearWatchers){
            angular.forEach($scope.processorsWatchers,function (item,index) {
                item();
            })
        }
        $scope.ignoreCLearWatchers = false;
    })

    $scope.getExcludedMaidForProcess = function (pageNum=0,withOpen){
        $scope.currentExecludedPageNum = pageNum;
        let params = {
            page:pageNum,
            size:__env.DATAGRID_PAGE_SIZE,
            housemaidName:$scope.model.housemaidFilter,
        }
        magnaHttpService.HttpWrapper({
            url: __env.VISA + 'robotic-process/'+$scope.currentProcessor.id+'/'+($scope.excluded?'getRoboticProcessExcludedUI':'getRoboticProcessIncludedUI'),
            params:params
        }, function (response) {
            $scope.execludedDataGrid.data = response.content;
            $scope.execludedDataGridPagination.paginationInfo = response;
            if(withOpen){
                $scope.ignoreCLearWatchers = true;
                magnaMainService.DialogBox.showModal($("#exclude-maids-modal"));
            }
        },{needs_loading_icon: true});
    }

    $scope.saveExcluded = function (){
        magnaHttpService.HttpWrapper({
            method:'PUT',
            url: __env.VISA + 'robotic-process/'+$scope.currentProcessor.id+`/request/${$scope.excluded?'excludedNew':'includedNew'}`,
            data:[$scope.model.excludedMaid]
        }, function (response) {
            $scope.model.excludedMaid = '';
            magnaMainService.DialogBox.showSuccessMsg(`Added Successfully`);
            $scope.getExcludedMaidForProcess($scope.currentExecludedPageNum);
            //$("#exclude-maids-modal").modal('hide');
        },{needs_loading_icon: true});

    }

    $scope.removeExcluded = function (id){
        magnaHttpService.HttpWrapper({
            method:'DELETE',
            url: __env.VISA + 'robotic-process/'+$scope.currentProcessor.id+`/request/${$scope.excluded?'excludedDelete':'includedDelete'}`,
            data:[id]
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg(`Removed Successfully`);
            $scope.getExcludedMaidForProcess($scope.currentExecludedPageNum);
            //$("#exclude-maids-modal").modal('hide');
        },{needs_loading_icon: true});

    }
    //Working days
    $scope.WorkingDaysHours = {
        currentCode:'',
        workingHours:[],
        days:["Sunday","Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"],
        timeUnitOptions:{ placeholder: "Select Unit", width: '100%',allowClear:false, data: [
                {id:'Second',text:'Seconds',factor:1},
                {id:'Minute',text:'Minutes',factor:60},
                {id:'Hour',text:'Hours',factor:3600},
            ],
        },
        getUnitFactor:function(id){
            return id?this.timeUnitOptions.data.filter(_=>_.id == id)[0]?.factor??1:1;
        },
        selectedDays:[],
        snoozePeriod: 0,
        recheckPeriod: 0,
        pausePeriodBetweenMaids: 0,
        selectedUnitSnoozePeriod: '',
        selectedUnitRecheckPeriod: '',
        selectedUnitPausePeriodBetweenMaids: '',
        selectedUnitSnoozePeriodObj: '',
        selectedUnitRecheckPeriodObj: '',
        selectedUnitPausePeriodBetweenMaidsObj: '',
        addWorkingHours:function () {
            $scope.WorkingDaysHours.workingHours.push({fromTime:'',toTime:''})
        },
        removeWorkingHours:function (i) {
            $scope.WorkingDaysHours.workingHours.splice(i, 1)
        },
        showWorkingHours:function (code) {
            //$('#scheduledDateOfTerminationFieldContainer').html($compile(`<magna-date-input ng-disabled="runAwaySelected" ng-model="cancellationData.contract.scheduledDateOfTermination" options="scheduledDateOfTerminationOptions"></magna-date-input>`)($scope));
            this.currentCode = code;
            let params = code?{code:code}:{}
            magnaHttpService.HttpWrapper({
                method:'GET',
                url: __env.VISA + 'rpa-working-times/list',
                params:params
            }, function (response) {
                if(response.length>0){
                    response = response[0];
                    $.each(response.days.split(','),function (index,item) {
                        if(item&&$scope.WorkingDaysHours.days.indexOf(item.trim())!=-1){
                            $scope.WorkingDaysHours.selectedDays[$scope.WorkingDaysHours.days.indexOf(item.trim())] = true;
                        }
                    })
                    $scope.WorkingDaysHours.workingHours = $.map(response.workingHours,function (item) {
                        return {fromTime:item.fromTimeString,toTime:item.toTimeString}
                    })
                    $scope.WorkingDaysHours.snoozePeriod= response.snoozePeriod/$scope.WorkingDaysHours.getUnitFactor(response.snoozePeriodUnit);
                    $scope.WorkingDaysHours.recheckPeriod= response.recheckPeriod/$scope.WorkingDaysHours.getUnitFactor(response.recheckPeriodUnit);
                    $scope.WorkingDaysHours.pausePeriodBetweenMaids= response.pausePeriodBetweenMaids/$scope.WorkingDaysHours.getUnitFactor(response.pausePeriodBetweenMaidsUnit);
                    $scope.WorkingDaysHours.selectedUnitSnoozePeriod= response.snoozePeriodUnit;
                    $scope.WorkingDaysHours.selectedUnitRecheckPeriod= response.recheckPeriodUnit;
                    $scope.WorkingDaysHours.selectedUnitPausePeriodBetweenMaids= response.pausePeriodBetweenMaidsUnit;
                }else{
                    $scope.WorkingDaysHours.selectedDays = [];
                    $scope.WorkingDaysHours.workingHours = [];
                    $scope.WorkingDaysHours.snoozePeriod= 0;
                    $scope.WorkingDaysHours.recheckPeriod= 0;
                    $scope.WorkingDaysHours.pausePeriodBetweenMaids= 0;
                    $scope.WorkingDaysHours.selectedUnitSnoozePeriod= 'Second';
                    $scope.WorkingDaysHours.selectedUnitRecheckPeriod= 'Second';
                    $scope.WorkingDaysHours.selectedUnitPausePeriodBetweenMaids= 'Second';
                }
                $scope.ignoreCLearWatchers = true;
                magnaMainService.DialogBox.showModal($('#working-days-modal'))
            },{needs_loading_icon: true});
        },
        // getWorkingHours:function () {
        //     $scope.WorkingDaysHours.workingHours
        // },
        save:function () {
            debugger
            let params = this.currentCode?{code:this.currentCode}:{}
            let data = {
                "days": $.map(this.selectedDays,function (item,index){ if(item) return $scope.WorkingDaysHours.days[index] }).join(','),
                "snoozePeriod": this.snoozePeriod&&this.selectedUnitSnoozePeriod?this.snoozePeriod*this.selectedUnitSnoozePeriodObj.factor:0,
                "recheckPeriod": this.recheckPeriod&&this.selectedUnitRecheckPeriod?this.recheckPeriod*this.selectedUnitRecheckPeriodObj.factor:0,
                "pausePeriodBetweenMaids": this.pausePeriodBetweenMaids&&this.selectedUnitPausePeriodBetweenMaids?this.pausePeriodBetweenMaids*this.selectedUnitPausePeriodBetweenMaidsObj.factor:0,
                "snoozePeriodUnit": this.selectedUnitSnoozePeriod?this.selectedUnitSnoozePeriod:'',
                "recheckPeriodUnit": this.selectedUnitRecheckPeriod?this.selectedUnitRecheckPeriod:'',
                "pausePeriodBetweenMaidsUnit": this.selectedUnitPausePeriodBetweenMaids?this.selectedUnitPausePeriodBetweenMaids:'',
                "workingHours":this.workingHours
            };
            magnaHttpService.HttpWrapper({
                method:'POST',
                url: __env.VISA + 'rpa-working-times/create',
                data:data,
                params:params
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg(`Saved Successfully`);
                $('#working-days-modal').modal('hide')
            },{needs_loading_icon: true});

        }
    }

});

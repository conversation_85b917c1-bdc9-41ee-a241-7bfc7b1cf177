mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, $compile, $timeout, maidccService, $compile) {
    $scope.currentPage = 0;
    $scope.currentRow = {};
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'Aramex Shipments',
        }
    ];

    $scope.modules = [{
        module: 'VISA',
        processTaskFilter: {
            aramexProcessWorkflow: ['Prepare Aramex Package Automated','Hand Package to Aramex','Collect Package from Aramex','Hand EID and Passport Manually'],
        }
    }];
    $scope.bulkActions = {
        aramexProcessWorkflow: {
            "Collect Package from Aramex": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
            "Hand EID and Passport Manually": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
            "Hand Package to Aramex": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
            "Prepare Aramex Package Automated": [{ label: 'Confirm', code: 'confirm_selected', class: 'btn-success' }],
        }
    }

    $scope.handleAction = function (dataObj) {
        switch (dataObj.actionCode) {
            case 'confirm_selected':
                confirmSelected(dataObj.data);
                break;
            default:
                break;
        }

    }
    function confirmSelected(data) {
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA +  'aramex/completeBulk/'+data.taskKey,
            data:{tasksIds:data.tasksIds}
        }, function (response) {
            $route.reload();
        }, { needs_loading_icon: true });
    }

    $scope.$on('$viewContentLoaded', function () {
        //$scope.getTableData();
    });

    $scope.prepareAramexDataGrid = {
        columns: [
            {
                label: "Name",
                type: "text",
                valueExp: "$data.housemaid ? $data.housemaid.housemaidType : ''"
            },
            {
                label: "Employee Type",
                type: "text",
                valueExp: "$data.housemaid ? $data.housemaid.housemaidType : ''"
            },
            {
                label: "Maid Type",
                type: "text",
                valueExp: "$data.housemaid ? $data.housemaid.housemaidType : ''"
            },
            {
                label: "New/Renew",
                type: "text",
                valueExp: "$data.housemaid ? $data.housemaid.housemaidType : ''"
            },
            {
                label: "Package Content",
                type: "text",
                valueExp: "$data.housemaid ? $data.housemaid.housemaidType : ''"
            },
            {
                label: "Date",
                type: "text",
                valueExp: "$data.housemaid ? $data.housemaid.housemaidType : ''"
            }
        ],
        data: [],
        actions: []
    };

    $scope.getTableData = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                timeout: 1200000,
                url: __env.VISA +  'eidmedicalassistant/getall',
                params:params,
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                console.log("first response", response);
                $scope.firstMainDataGrid.data = response.maidsPendingToComeToMedicalExam;
                $scope.secondMainDataGrid.data = response.maidsPendingToComeToEIDFingerprinting;
            }, { needs_loading_icon: true }
        );
    }
// deprecated transfered to doc-manager based on VPM-3053 starts
    $scope.showPassportsModal = function () {
        $scope.getSectionsData();
        magnaMainService.DialogBox.showModal($('#passportsWithUs'));
    }
    $scope.getSectionsData = function(){
        $.each($scope.passportSections,function (index,item) {
            item.currentPage = 0;
            item.getData();
        })
    }
    $scope.passportCols = [{
        label: "Select",
        type: "html",
        valueExp: function ($data) {
                var html = `<div class="checkbox">
                                    <label>
                                        <input type="checkbox" ng-model="model.selectedArr[${$data.id}]" >
                                        <span class="checkbox-material"></span>
                                    </label>
                                </div>`;
                return $compile(html)($scope);
            }
        },
        {
            label: "Maid Name",
            type: "text",
            valueExp: function ($data) {
                return $data.name;
            }
        }];
    $scope.passportSections = [
        {
            title:'For Medical',
            code:'FOR_MEDICAL',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_MEDICAL');
                    sec.currentPage = pageNo
                    $scope.getSectionData(sec)
                }
            }
        },
        {
            title:'For Biometrics',
            code:'FOR_BIOMETRICS',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_BIOMETRICS');
                    sec.currentPage = pageNo
                    $scope.getSectionData(this)
                }
            }
        },
        {
            title:'For Visa Stamping',
            code:'FOR_ZAJEL',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_ZAJEL');
                    sec.currentPage = pageNo
                    $scope.getSectionData(this)
                }
            }
        },
        {
            title:'For Delivery',
            code:'FOR_DELIVERY',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('FOR_DELIVERY');
                    sec.currentPage = pageNo
                    $scope.getSectionData(this)
                }
            }
        },
        {
            title:'Others',
            code:'OTHERS',
            getData:function(){
                $scope.getSectionData(this)
            },
            grid:{
                columns:$scope.passportCols,
                data: [],
                actions: []
            },
            currentPage:0,
            gridPagination:{
                paginationInfo: {},
                submitFunction: function (pageNo) {
                    let sec = $scope.getSectionByCode('OTHERS');
                    sec.currentPage = pageNo
                    $scope.getSectionData(this)
                }
            }
        },
    ];
    $scope.model = {
        searchPassports:'',
        selectedArr:[],
        receivePassportHousemaid:''
    };
    $scope.getSelectedCount = function(){
        let selected = $.map($scope.model.selectedArr,function (item,index) {
            if(item)
                return index
        })
        return selected.length;
    }
    $scope.getSectionByCode = function(code){
        let sec = {};
        $.each($scope.passportSections,function (index,item){
            if(item.code == code)
                sec = item;
        })
        return sec;
    } 
    $scope.getSectionData = function(section){
        magnaHttpService.HttpWrapper({
                url: __env.VISA +  'passport-location/list-passport-with-us-maids',
                params:{
                    section:section.code,
                    housemaidName:$scope.model.searchPassports,
                    page:section.currentPage,
                    size:__env.DATAGRID_PAGE_SIZE,
                }
            }, function (response) {
                section.grid.data = response.content;
                section.gridPagination.paginationInfo = response;
                $timeout(function () {
                    $.material.checkbox();
                });
            }, { needs_loading_icon: true }
        );
    }
    $scope.searchPassports = function(){
        $.each($scope.passportSections,function (index,item) {
            item.getData();
        })
    }

    $scope.ReceivePassportHousemaidOptions = {
        placeholder: "Select Housemaid",
        width: '100%',
        ajax: {
            url: __env.VISA +  'passport-location/list-not-passport-with-us-maids',
            data: function (params) {
                return {
                    housemaidName: params.term ? params.term : "",
                }
            },
            processResults: function (data) {
                return {
                    results: $.map(data.content, function (item) {
                        return {
                            text: item.name,
                            id: item.id,
                        }
                    })
                };
            }
        }
    };

    $scope.handAPassport = function(){
        magnaMainService.DialogBox.showWarningMsg(`Are you sure you want to hand this passport?`, () => {
            let selected = $.map($scope.model.selectedArr,function (item,index) {
                if(item)
                    return index
            })
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: __env.VISA +  'passport-location/change-passport-location',
                params:{
                    passportLocation:'with_maid',
                },
                data:selected
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Done Successfully')
                $scope.model.selectedArr = [];
                $scope.getSectionsData();
            }, { needs_loading_icon: true });
        });
    }

    $scope.showReceiveAPassport = function(){
        $scope.model.receivePassportHousemaid = '';
        magnaMainService.DialogBox.showModal($('#receiveAPassportModal'));
    }
    $scope.receiveAPassport = function(){
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA +  'passport-location/change-passport-location',
            params:{
                passportLocation:'with_us',
            },
            data:[$scope.model.receivePassportHousemaid]
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Done Successfully')
            $('#receiveAPassportModal').modal('hide');
            $scope.searchPassports();
        }, { needs_loading_icon: true });

    }
// deprecated transfered to doc-manager based on VPM-3053 Ends


});

<magna-breadcrumbs links="breadcrumbs"></magna-breadcrumbs>
<!--<div class="panel-group">-->
<!--    <div class="panel panel-default light_grey">-->
<!--        <div class="panel-heading text-center">-->
<!--            <div class="row w3-margin-0">-->
<!--                <div class="col-sm-12">-->
<!--                    <div class="bold">-->
<!--                        Prepare Aramex Package (Automated)-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--        <div class="panel-body">-->
<!--            <div class="row w3-margin-0">-->
<!--                <magna-data-grid config="prepareAramexDataGrid"></magna-data-grid>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->
<!--<div class="row w3-margin-0">-->
<!--    <button ng-click="showPassportsModal()" class="btn btn-md btn-raised"> Passports with us </button>-->
<!--</div>-->
<tasks-list modules="modules"  row-actions="rowActions" bulk-actions="bulkActions"
            on-bulk-action="handleAction(data)" ></tasks-list>

<!--<div id="passportsWithUs" class="modal fade">-->
<!--    <div class="modal-dialog">-->
<!--        <div class="modal-content">-->
<!--            <div class="modal-header">-->
<!--                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>-->
<!--                <h4 class="modal-title"> Passports with us </h4>-->
<!--            </div>-->
<!--            <div class="modal-body">-->
<!--                <div class="row w3-padding-16-h">-->
<!--                    <button ng-click="handAPassport()" class="btn btn-md btn-raised" ng-disabled="getSelectedCount()==0"> Hand a passport </button>-->
<!--                    <button ng-click="showReceiveAPassport()" class="btn btn-md btn-raised pull-right"> Receive a passport </button>-->
<!--                </div>-->
<!--                <div class="row">-->
<!--                    <div class="col-md-10 col-md-offset-1 w3-padding-8-h">-->
<!--                        <div class="form-group label-floating">-->
<!--                            <div class="input-group">-->
<!--                                <input ng-model="model.searchPassports" value="{{model.searchPassports}}" type="text" class="form-control">-->
<!--                                <span class="input-group-btn">-->
<!--                                    <button type="submit" class="btn btn-fab btn-fab-mini" ng-click="searchPassports()">-->
<!--                                        <i class="material-icons">search</i>-->
<!--                                    </button>-->
<!--                                </span>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--                <div class="panel-group" ng-repeat="section in passportSections" >-->
<!--                    <div class="panel panel-default light_grey">-->
<!--                        <a data-toggle="collapse" data-target="#collapse_{{section.code}}" href="javascript:void(0)" aria-expanded="true"-->
<!--                           class=" w3-text-grey w3-hover-text-red">-->
<!--                            <div class="panel-heading">-->
<!--                                <div class="row w3-margin-0">-->
<!--                                    <div class="pull-left bold text-center"><i class="glyphicon glyphicon-filter"></i> {{section.title}}</div>-->
<!--                                    <div class="pull-right">-->
<!--                                        <i class="glyphicon glyphicon-menu-down"></i>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </a>-->
<!--                        <div id="collapse_{{section.code}}" class="panel-collapse collapse in">-->
<!--                            <div class="panel-body">-->
<!--                                <div class="row relative">-->
<!--                                    <magna-data-grid config="section.grid"></magna-data-grid>-->
<!--                                    <magna-pagination config="section.gridPagination"></magna-pagination>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->
<!--<div id="receiveAPassportModal" class="modal fade">-->
<!--    <div class="modal-dialog">-->
<!--        <div class="modal-content">-->
<!--            <div class="modal-header">-->
<!--                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>-->
<!--                <h4 class="modal-title"> Receive a passport </h4>-->
<!--            </div>-->
<!--            <div class="modal-body">-->
<!--                <div class="row">-->
<!--                    <div class="form-group w3-padding-16-h">-->
<!--                        <label class="w3-padding-16-h">Please select the maid you want to receive the passport from:</label>-->
<!--                        <div class="col-md-12">-->
<!--                            <magna-select-input options="ReceivePassportHousemaidOptions" ng-model="model.receivePassportHousemaid" name="housemaid" ></magna-select-input>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            <div class="modal-footer">-->
<!--                <button type="button" class="btn btn-primary pull-right" ng-click="receiveAPassport()" ng-disabled="!model.receivePassportHousemaid" >Receive</button>-->
<!--                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>-->
<!--            </div>-->
<!--        </div>-->
<!--    </div>-->
<!--</div>-->
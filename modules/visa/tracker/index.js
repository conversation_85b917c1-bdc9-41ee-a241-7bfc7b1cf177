var publicPage = angular.module("public-page-app", ["magna-app", "ngCookies"]);
publicPage.directive("onFileChange", function ($timeout) {
  return {
    restrict: "A",
    link: function (scope, element, attrs) {
      var onChangeHandler = scope.$eval(attrs.onFileChange);
      var debounceTimeout = null;

      if (typeof onChangeHandler === "function") {
        element.off("change");
        element.on("change", function () {
          if (debounceTimeout) {
            $timeout.cancel(debounceTimeout);
          }

          debounceTimeout = $timeout(function () {
            scope.$apply(function () {
              var files = element[0].files;
              if (files.length > 0) {
                onChangeHandler(files);
              }
            });
          }, 200); // Debounce time in milliseconds
        });
      } else {
        console.error(
          "onFileChange handler is not a function:",
          onChangeHandler
        );
      }
    },
  };
});


publicPage.config(function config(
  $locationProvider,
  $compileProvider,
  $routeProvider,
  $controllerProvider,
  __env
) {
  $compileProvider.aHrefSanitizationWhitelist(
    /^\s*(https?|ftp|mailto|file|javascript|tel):/
  );
  $locationProvider.hashPrefix("!");
  /* Creating a more synthesized form of service of $controllerProvider.register */
  publicPage.registerCtrl = $controllerProvider.register;
  $routeProvider
    .when("/401", {
      templateUrl: "../../../unauthorized.html?_v=" + __env.PUBLIC_PAGE_ID,
    })
    .when("/404", {
      templateUrl: "../../../not-found.html?_v=" + __env.PUBLIC_PAGE_ID,
    })
    .when("/:webAppUploaderUuid", {
      templateUrl: "content.html?_v=" + __env.PUBLIC_PAGE_ID,
      controller: "PublicPageController",
      page_info: {
        code: "visa__public_tracker",
      },
    })
    .when("/:webAppUploaderUuid/:mobile", {
      templateUrl: "content.html?_v=" + __env.PUBLIC_PAGE_ID,
      controller: "PublicPageController",
      page_info: {
        code: "visa__public_tracker",
      },
    })
    .otherwise("/505");
});

publicPage.controller(
  "PublicPageController",
  function (
    $routeParams,
    $route,
    $window,
    $sce,
    $http,
    $timeout,
    $location,
    $scope,
    magnaAuthenticationService,
    magnaMainService,
    magnaHttpService,
    maidccService,
    $cookies,
    $rootScope,
    $filter,
    __env
  ) {
    $scope.maidccService = maidccService;
    $rootScope.globals = $cookies.getObject("globals") || {};
    __env = window.__env;
    $scope.webAppUploaderUuid = $routeParams.webAppUploaderUuid;
    $scope.mobile = $routeParams.mobile;
    $scope.showMsg = false;
    $scope.finishedLoading = false;
    $scope.currentFile = {};

    // Initialize documents array to prevent 'undefined' errors
    $scope.documents = [];

    $scope.addPageCodeHeader = function (headers = {}) {
      headers.pageCode = $route.current.$$route.page_info.code;
      return headers;
    };

    $scope.specialCase = false;

    $scope.openDocumentModal = function (doc) {
      $scope.currentDocument = doc;
      $scope.currentDocument.uploaded = false;
      // Automatically open file dialog when the function is called
      document.getElementById("fileInput_" + doc.documentTag).click();
    };
    $scope.handleFileChange = function (files) {
      if (files.length > 0) {
        var file = files[0]; // Directly get the first file
    
        console.log("File input changed: ", file);
        console.log("Current document: ", $scope.currentDocument);
    
        if (file) {
          console.log("File selected: ", file);
    
          // Check if file has already been uploaded to avoid duplicate uploads
          if (!$scope.currentDocument.uploaded) {
            $scope.uploadFile(
              file,
              $scope.currentDocument.documentTag,
              `img_${$scope.currentDocument.documentTag}`
            );
            $scope.currentDocument.uploaded = true; // Mark as uploaded
          } else {
            console.log("File already uploaded.");
          }
        }
      } else {
        console.error("No file selected.");
      }
    };
    

    $scope.getDownloadLink = function (fileObj) {
      let pub = __env.PUBLIC;
      return fileObj.uuid ? __env.PUBLIC + "download/" + fileObj.uuid : "";
    };

    $scope.downloadFile = function (uuid) {
      magnaHttpService.downloadFile(__env.PUBLIC + "download/" + uuid);
    };

    magnaHttpService.HttpWrapper(
      {
        method: "GET",
        url:
          __env.VISA +
          "web-app-uploader/get-details?webAppUploaderUuid=" +
          $scope.webAppUploaderUuid,
        headers: $scope.addPageCodeHeader(),
      },
      function (response) {
        console.log("response", response);
        $scope.documents = response.documents || []; // Ensure documents is an array
        $scope.configCode = response.configCode;
        $scope.title = response.title;
        $scope.finishedLoading = true;
      },
      {
        needs_loading_icon: true,
        ignore_authorization: true,
        error_handler: function (response) {
          $scope.showMsg = true;
        },
      }
    );

    $timeout(function () {
      magnaMainService.initGUI();
    });

    $scope.disableButton = function () {
      // Check if $scope.documents is defined and is an array
      return (
        Array.isArray($scope.documents) &&
        $scope.documents.some(function (document) {
          return document.attachment == "";
        })
      );
    };
    function dataURLtoBlob(dataURL) {
      // Decode the data URL
      var byteString = atob(dataURL.split(",")[1]);
      var mimeString = dataURL.split(",")[0].split(":")[1].split(";")[0];

      // Create an ArrayBuffer and view to hold the bytes
      var arrayBuffer = new ArrayBuffer(byteString.length);
      var uint8Array = new Uint8Array(arrayBuffer);

      // Populate the ArrayBuffer with the decoded bytes
      for (var i = 0; i < byteString.length; i++) {
        uint8Array[i] = byteString.charCodeAt(i);
      }

      // Create and return a Blob from the ArrayBuffer
      return new Blob([arrayBuffer], { type: mimeString });
    }

    $scope.uploadFile = function (file, tag, targetImageElement = null) { 
      var reader = new FileReader();
      const formData = new FormData();
      formData.append("tag", tag);
      reader.onload = function () {
        var blob = dataURLtoBlob(reader.result);
        formData.append("file", blob, file.name);
        magnaHttpService.HttpWrapper(
          {
            method: "POST",
            url: __env.PUBLIC + "upload",
            data: formData,
            timeout: 300000,
            headers: $.extend(
              { "Content-Type": undefined },
              magnaAuthenticationService.addAuthorizationHeaders({})
            ),
          },
          function (response) {
            $scope.currentDocument.attachment = response;

            if (targetImageElement) targetImageElement.src = reader.result;
            magnaMainService.DialogBox.showSuccessMsg("uploaded Successfully ");
          },
          { needs_loading_icon: true }
        );
      };
      reader.readAsDataURL(file);
    };

    $scope.submit = function () {
      let data = $scope.documents.map(function (document) {
        return { id: document.attachment.id };
      });
      debugger
      magnaHttpService.HttpWrapper(
        {
          method: "POST",
          url:
            __env.VISA +
            "web-app-uploader/upload-documents?webAppUploaderUuid=" +
            $scope.webAppUploaderUuid,
          data: data,
          headers: $scope.addPageCodeHeader(),
        },
        function (response) {
          magnaMainService.DialogBox.showSuccessMsg("Saved Successfully");
          $scope.showMsg = true;
        },
        { needs_loading_icon: true, ignore_authorization: true }
      );
    };
  }
);

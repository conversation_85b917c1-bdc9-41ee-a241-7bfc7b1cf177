<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      http-equiv="Cache-Control"
      content="no-cache, no-store, must-revalidate"
    />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Tracker</title>
    <link
      rel="shortcut icon"
      href="images/favicon.png?_v=20180408"
      type="image/png"
    />
    <link
      href="../../../../plugins/materialfont/material-icons.css"
      rel="stylesheet"
    />
    <!-- Styles -->
    <link
      href="../../../../plugins/bootstrap/dist/css/bootstrap.css"
      rel="stylesheet"
    />
    <link
      href="../../../../plugins/bootstrap-material-design/dist/css/bootstrap-material-design.min.css"
      rel="stylesheet"
    />
    <link
      href="../../../../plugins/bootstrap-material-design/dist/css/ripples.min.css"
      rel="stylesheet"
    />
    <link
      href="../../../../plugins/bootstrap-material-design/dist/css/ripples.min.css"
      rel="stylesheet"
    />
    <link
      href="../../../../plugins/custom-scrollbar/jquery.mCustomScrollbar.min.css"
      rel="stylesheet"
      type="text/css"
    />
    <link
      href="../../../../plugins/dropzone/min/dropzone.min.css"
      rel="stylesheet"
    />
    <link
      href="../../../../plugins/animate.css/animate.css"
      rel="stylesheet"
      type="text/css"
    />
    <link
      href="../../../../plugins/jstree/themes/default/style.min.css"
      rel="stylesheet"
    />

    <script>
      document.write(
        "<scr" +
          'ipt src="../../../../custom/custom-environment.js?_v=' +
          new Date().getTime() +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      document.write(
        "<scr" +
          'ipt src="../../../../js/magna-environment.js?_v=' +
          new Date().getTime() +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      window.__env.PUBLIC_PAGE_ID = __env.VERSION_ID + "_9";
    </script>
    <script>
      document.write(
        '<link href="../../../../css/style.css?_v=' +
          window.__env.VERSION_ID +
          '" rel="stylesheet" type="text/css">'
      );
    </script>
    <script src="../../../../plugins/jquery/dist/jquery.min.js"></script>
    <!-- Angular -->
    <script src="../../../../plugins/angular-1.6.6/angular.min.js"></script>
    <script src="../../../../plugins/angular-1.6.6/angular-route.min.js"></script>
    <script src="../../../../plugins/angular-1.6.6/angular-cookies.min.js"></script>
    <script src="../../../../plugins/angular-1.6.6/angular-sanitize.min.js"></script>
    <style>
      @font-face {
        font-family: "Airbnb Cereal App Book";
        font-style: normal;
        font-weight: normal;
        src: local("Airbnb Cereal App Book"),
          url("fonts/AirbnbCerealBook.woff") format("woff");
      }

      @font-face {
        font-family: "Airbnb Cereal App Light";
        font-style: normal;
        font-weight: normal;
        src: local("Airbnb Cereal App Light"),
          url("fonts/AirbnbCerealLight.woff") format("woff");
      }

      @font-face {
        font-family: "Airbnb Cereal App Medium";
        font-style: normal;
        font-weight: normal;
        src: local("Airbnb Cereal App Medium"),
          url("fonts/AirbnbCerealMedium.woff") format("woff");
      }

      @font-face {
        font-family: "Airbnb Cereal App Bold";
        font-style: normal;
        font-weight: normal;
        src: local("Airbnb Cereal App Bold"),
          url("fonts/AirbnbCerealBold.woff") format("woff");
      }

      @font-face {
        font-family: "Airbnb Cereal App Extra Bold";
        font-style: normal;
        font-weight: normal;
        src: local("Airbnb Cereal App Extra Bold"),
          url("fonts/AirbnbCerealExtraBold.woff") format("woff");
      }

      @font-face {
        font-family: "Airbnb Cereal App Black";
        font-style: normal;
        font-weight: normal;
        src: local("Airbnb Cereal App Black"),
          url("fonts/AirbnbCerealBlack.woff") format("woff");
      }

      *:not(.glyphicon):not(.material-icons) {
        font-family: "Airbnb Cereal App Book";
      }

      .airbnb-bold {
        font-family: "Airbnb Cereal App Bold" !important;
      }
      .form-horizontal label {
        text-align: left !important;
      }
      .question-label {
        text-align: left !important;
        font-weight: bold !important;
      }
      .header {
        background-color: #4267b0;
        height: 5em;
        padding: 1em 1.5em;
      }
      .specialTimeInput .glyphicon-th {
        margin: 10px;
      }
      .btn-blue {
        color: #fff !important;
        background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(#6a7bb0),
          to(#4267b0)
        ) !important;
        -moz-border-radius: 4px !important;
        -webkit-border-radius: 4px !important;
        border-radius: 4px !important;
        border: 1px solid #315ab0 !important;
        background: #315ab0 !important;
        background: -moz-linear-gradient(
          top,
          #6a7bb0 0%,
          #4267b0 100%
        ) !important;
        background: -webkit-linear-gradient(
          top,
          #6a7bb0 0%,
          #4267b0 100%
        ) !important;
        background: linear-gradient(
          to bottom,
          #6a7bb0 0%,
          #4267b0 100%
        ) !important;
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#f5e5bf", endColorstr="#f7cb6e",GradientType=0 ) !important;
      }
      .btn-blue:hover {
        color: #fff !important;
        background: #315ab0 !important;
        background: -moz-linear-gradient(
          top,
          #4267b0 0%,
          #284ab0 100%
        ) !important;
        background: -webkit-linear-gradient(
          top,
          #4267b0 0%,
          #284ab0 100%
        ) !important;
        background: linear-gradient(
          to bottom,
          #4267b0 0%,
          #284ab0 100%
        ) !important;
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr="#f7cb6e", endColorstr="#f59e00",GradientType=0 ) !important;
      }
      .card-header {
        padding: 10px 10px 0 10px;
        font-size: larger;
        font-weight: 600;
      }
      .card-body {
        height: auto !important;
        padding: 10px 0px !important;
      }
      .card {
        border-radius: 20px;
      }
      .no-border-top {
        border-top: none !important;
        width: 98%;
        margin-left: auto;
        margin-right: auto;
      }
      .clickable {
        cursor: pointer;
      }
      .thanks-msg {
        margin-top: 30px;
        text-align: center;
      }
      .tanks-title {
        color: #007400;
        font-weight: 500;
        margin-top: 25px;
        display: block;
        font-size: 18px;
      }
      .tanks-label {
        line-height: 1.6;
        margin-top: 20px;
        color: #007400;
        font-size: 16px;
        font-weight: 600;
      }
      .logo-container img {
        width: 70%;
        margin: 20px 15%;
      }

      .yellow-btn {
        width: 100%;
        height: 58px;
        text-align: center;
        font-size: 15px;
        line-height: 19px;
        color: #000 !important;
        outline: none;
        -moz-border-radius: 19px;
        -webkit-border-radius: 19px;
        border-radius: 19px;
        background: #ffa41c !important;
        border: 1px solid #ff8f00;
        transition: auto !important;
        text-transform: capitalize;
        color: #000 !important;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.35);
      }
      .yellow-btn.with-plus {
        padding-left: 0;
        overflow: hidden;
      }
      .yellow-btn span {
        width: calc(100% - 60px);
        float: left;
        line-height: 2.5;
        font-size: 17px;
        font-weight: normal;
      }
      .btn-plus {
        width: 58px !important;
        font-size: 45px !important;
        font-weight: lighter !important;
        line-height: 0.8 !important;
        border-right: 1px solid #000;
        height: 100%;
      }

      .yellow-btn:hover,
      .yellow-btn:active,
      .yellow-btn:focus {
        background: #eba145 !important;
        background: -webkit-linear-gradient(top, #f4bc77, #e18512) !important;
        background: linear-gradient(to bottom, #f4bc77, #e18512) !important;
        border-color: #be751a #b26d18 #9a5f15 !important;
      }
      body .yellow-btn:hover {
        background: #fa8900 !important;
        border-color: #e3931e;
        color: #000 !important;
      }
      body .yellow-btn:active {
        background: #ffa41c !important;
        border-color: #ff8f00;
        box-shadow: none;
      }
      body .yellow-btn:focus {
        background: #fff !important;
        border: 1px solid #ffa41c !important;
        box-shadow: 0 2px 5px 0 rgb(213, 217, 217);
      }
      .sticky-foot {
        position: fixed;
        bottom: 25px;
        left: 35px;
        right: 55px;
      }
      .col-md-11 {
        display: inline-block;
        width: 84%;
      }
      .col-md-1 {
        display: inline-block;
        padding: 3% 0;
      }

      .gray-btn {
        width: 100%;
        height: 58px;
        text-align: center;
        font-size: 15px;
        line-height: 19px;
        color: #000 !important;
        outline: none;
        -moz-border-radius: 19px;
        -webkit-border-radius: 19px;
        border-radius: 19px;
        background: #9e9e9e !important;
        border: 1px solid #9e9e9e;
        transition: auto !important;
        text-transform: capitalize;
        color: #000 !important;
        box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.35);
      }
      .gray-btn.with-plus {
        padding-left: 0;
        overflow: hidden;
      }
      .gray-btn span {
        width: calc(100% - 60px);
        float: left;
        line-height: 2.5;
        font-size: 17px;
        font-weight: normal;
      }

      .gray-btn:hover,
      .gray-btn:active,
      .gray-btn:focus {
        background: #9e9e9e !important;
        background: -webkit-linear-gradient(top, #9e9e9e, #9e9e9e) !important;
        background: linear-gradient(to bottom, #9e9e9e, #9e9e9e) !important;
        border-color: #9e9e9e #9e9e9e #9e9e9e !important;
      }
      body .gray-btn:hover {
        background: #9e9e9e !important;
        border-color: #9e9e9e;
        color: #000 !important;
      }
      body .gray-btn:active {
        background: #9e9e9e !important;
        border-color: #9e9e9e;
        box-shadow: none;
      }
      body .gray-btn:focus {
        background: #fff !important;
        border: 1px solid #9e9e9e !important;
        box-shadow: 0 2px 5px 0 rgb(213, 217, 217);
      }

      .btn-secondary:hover,
      .btn-secondary:focus {
        color: black;
        border-color: lightgray !important;
        background-color: lightgray !important;
      }
      .btn-secondary {
        border-color: lightgray !important;
        background-color: lightgray !important;
      }
      .btn {
        width: 100%;
        color: #000 !important;
        border-color: #ee972a;
        background-color: #ee972a;
        color: black;
        border-radius: 12px;
      }

      .btn-primary:hover {
        color: #fff;
        background-color: #ee972a !important;
        border-color: #ee972a !important;
      }
    </style>
  </head>

  <body class="content" ng-app="public-page-app">
    <div id="page-container" ng-view></div>

    <script src="../../../../plugins/bootstrap/dist/js/bootstrap.min.js"></script>
    <script src="../../../../plugins/bootstrap-material-design/dist/js/ripples.min.js"></script>
    <script src="../../../../plugins/bootstrap-material-design/dist/js/material.min.js"></script>
    <script src="../../../../plugins/bootstrap-notify/bootstrap-notify.min.js"></script>
    <script src="../../../../plugins/jquery.mousewheel/jquery.mousewheel.min.js"></script>
    <script src="../../../../plugins/custom-scrollbar/jquery.mCustomScrollbar.js"></script>
    <script src="../../../../plugins/dropzone/min/dropzone.min.js"></script>
    <script src="../../../../plugins/moment/moment.min.js"></script>
    <script src="../../../../plugins/moment/moment-timezone.min.js"></script>
    <script src="../../../../plugins/moment/moment-precise-range.js"></script>
    <!-- html2pdf libraries -->
    <script src="../../../../plugins/html2pdf/jspdf.min.js"></script>
    <script src="../../../../plugins/html2canvas-1.0.0/html2canvas.min.js"></script>
    <script src="../../../../plugins/html2pdf/html2pdf.bundle.min.js?_=20180708"></script>
    <script src="../../../../plugins/jszip/dist/jszip.min.js"></script>

    <!-- core libraries -->
    <script>
      document.write(
        "<scr" +
          'ipt src="../../../../js/magna-app.js?_v=' +
          window.__env.VERSION_ID +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      document.write(
        "<scr" +
          'ipt src="../../../../js/magna-services.js?_v=' +
          window.__env.VERSION_ID +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      document.write(
        "<scr" +
          'ipt src="../../../../js/magna-environment.js?_v=' +
          window.__env.VERSION_ID +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      document.write(
        "<scr" +
          'ipt src="../../../../js/config.js?_v=' +
          window.__env.VERSION_ID +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      document.write(
        "<scr" +
          'ipt src="index.js?_v=' +
          window.__env.PUBLIC_PAGE_ID +
          '"></sc' +
          "ript>"
      );
    </script>
    <script>
      $(document).ready(function () {
        $.material.options.autofill = true;
        $.material.init();
      });
    </script>
  </body>

  <div id="file_viewer_modal" class="modal fade">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <button
            type="button"
            class="close"
            data-dismiss="modal"
            aria-hidden="true"
          >
            ×
          </button>
          <h4 class="modal-title">Edit Notes</h4>
        </div>
        <div class="modal-body" style="text-align: center"></div>
        <div class="modal-footer">
          <a id="generic-download-link" class="btn btn-primary btn-raised">
            <i class="glyphicon glyphicon-download-alt"></i> Download
          </a>
        </div>
      </div>
    </div>
  </div>
</html>

{"string": "@\"<input class='form-control' placeholder='Enter {{obj.label}}' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'>\"", "boolean": "@\"<div class='checkbox'><label><input type='checkbox' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'><span class='checkbox-material'></span> {{obj.label}}</label></div>\"", "date": "@\"<magna-date-input ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]' options='{startDate: \\\"1900-01-01\\\"}'></magna-date-input>\"", "time": "@\"<magna-time-input ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'></magna-time-input>\"", "dateTime": "@\"<magna-date-input ng-model='obj.attrsVal[\\\"\"+obj.name+\"__date\\\"]' options='{startDate: \\\"1900-01-01\\\"}' ></magna-date-input><magna-time-input ng-model='obj.attrsVal[\\\"\"+obj.name+\"__time\\\"]'></magna-time-input>\"", "file": "@\"<magna-file-input ng-disabled='{{obj.disabled}}' tag='{{obj.tag}}' options='{maxFiles: obj.maxFiles}' id='{{obj.inputId}}' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'></magna-file-input>\"", "enum": "@\"<magna-select-input options='obj.options' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'></magna-select-input>\"", "long": "@\"<input class='form-control' type='number' placeholder='Enter {{obj.label}}' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'>\"", "radio": "@\"<div class='radio radio-primary'><label style='display:block;    text-align: left;' ng-repeat='item in obj.options.data'><input type='radio' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]' value='{{item.value}}'><span class='checkbox-material'></span>{{item.label}}</label></div>\"", "static": "@\"<p style='background:#eee;margin: 0;line-height: 25px;margin-top: 10px;padding-left: 10px;'>\"+obj.defaultValue+\"</p>\""}
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>Set Field</title>
    <link rel="shortcut icon" href="images/favicon.png?_v=20180408" type="image/png">
    <link href="../../../plugins/materialfont/material-icons.css" rel="stylesheet">
    <!-- Styles -->
    <link href="../../../plugins/bootstrap/dist/css/bootstrap.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-material-design/dist/css/bootstrap-material-design.min.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-material-design/dist/css/ripples.min.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-material-design/dist/css/ripples.min.css" rel="stylesheet">
    <link href="../../../plugins/bootstrap-datepicker/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/bootstrap-timepicker/css/bootstrap-timepicker.min.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/custom-scrollbar/jquery.mCustomScrollbar.min.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/dropzone/min/dropzone.min.css" rel="stylesheet">
    <link href="../../../plugins/animate.css/animate.css" rel="stylesheet" type="text/css">
    <link href="../../../plugins/select2/css/select2.min.css" rel="stylesheet">
    <link href="../../../plugins/jstree/themes/default/style.min.css" rel="stylesheet" />

    <script>document.write('<scr' + 'ipt src="../../../custom/custom-environment.js?_v=' + (new Date()).getTime() + '"></sc' + 'ript>');</script>
    <script>document.write('<scr' + 'ipt src="../../../js/magna-environment.js?_v=' + (new Date()).getTime() + '"></sc' + 'ript>');</script>
    <script>window.__env.SET_FIELD_PAGE_VERSION_ID = __env.VERSION_ID + "_13";</script>
    <script>document.write('<link href="../../../css/style.css?_v=' + window.__env.VERSION_ID + '" rel="stylesheet" type="text/css">');</script>
    <script>document.write('<link href="../../../css/mobile.css?_v=' + window.__env.VERSION_ID + '" rel="stylesheet" type="text/css">');</script>
    <script src="../../../plugins/jquery/dist/jquery.min.js"></script>
    <!-- Angular -->
    <script src="../../../plugins/angular-1.6.6/angular.min.js"></script>
    <script src="../../../plugins/angular-1.6.6/angular-route.min.js"></script>
    <script src="../../../plugins/angular-1.6.6/angular-cookies.min.js"></script>
    <script src="../../../plugins/angular-1.6.6/angular-sanitize.min.js"></script>
    <meta name="google-signin-scope" content="profile email">
    <script>document.write('<meta name="google-signin-client_id" content="'+window.__env.GOOGLE_ACC_CONFIG.CLIENT_ID+'"></meta>');</script>
    <script src="https://apis.google.com/js/platform.js" async defer></script>
    <style>
        .dropzone{
            min-height:30px;
            background: #2fb3ff;
        }
        .upload-label{
            border: 0;
            text-align: center;
            background: #2FB3FF !important;
            color: #fff;
            font-weight: bold;
            cursor: pointer;
        }
        #id0-file-browse,#id0-image-upload{
            background: #0000ff !important;
        }
        #id1-file-browse,#id1-image-upload{
            background: #356fff !important;
        }
        #id2-file-browse,#id2-image-upload{
            background: #5687ff !important;
        }
        #id3-file-browse,#id3-image-upload{
            background: #50abff !important;
        }
        #id4-file-browse,#id4-image-upload{
            background: #7878ff !important;
        }
        #id5-file-browse,#id5-image-upload{
            background: #0000ff !important;
        }
        #id6-file-browse,#id6-image-upload{
            background: #356fff !important;
        }
        #id7-file-browse,#id7-image-upload{
            background: #5687ff !important;
        }
        #id8-file-browse,#id8-image-upload{
            background: #50abff !important;
        }
        .dz-max-files-reached{
            background: #fff;
            padding: 0 !important;
        }
        .dz-max-files-reached .upload-label{
            display: none;
        }
        .dropzone .label__choice{
            color:  #5a5a5a;
        }
        .mybtn{
            box-shadow: none !important;
            background: none !important;
        }
        .mybtn:hover{
            background:none !important;
        }
        body .yellow-btn,body .yellow-btn.button {
            -moz-border-radius: 19px;
            -webkit-border-radius: 19px;
            border-radius: 19px;
            background: #FFA41C !important;
            border:1px solid  #FF8F00;
            transition: auto !important;
            text-transform: capitalize;
            color: #000 !important;
            box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.35);
        }
        body .yellow-btn:hover {
            background: #FA8900 !important;
            border-color: #E3931E;
            color: #000 !important;
        }
        body .yellow-btn:active {
            background: #FFA41C !important;
            border-color: #FF8F00;
            box-shadow: none;
        }
        body .yellow-btn:focus {
            background: #FFF !important;
            border: 1px solid #FFA41C !important;
            box-shadow: 0 2px 5px 0 rgb(213, 217, 217);
        }
    </style>
</head>

<body ng-app="set-field-app" class="content">
<div id="page-view" ng-view></div>


<script src="../../../plugins/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="../../../plugins/bootstrap-material-design/dist/js/ripples.min.js"></script>
<script src="../../../plugins/bootstrap-material-design/dist/js/material.min.js"></script>
<script src="../../../plugins/select2/js/select2.min.js"></script>
<script src="../../../plugins/bootstrap-datepicker/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
<script src="../../../plugins/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
<script src="../../../plugins/bootstrap-notify/bootstrap-notify.min.js"></script>
<script src="../../../plugins/jquery.mousewheel/jquery.mousewheel.min.js"></script>
<script src="../../../plugins/custom-scrollbar/jquery.mCustomScrollbar.js"></script>
<script src="../../../plugins/dropzone/min/dropzone.min.js"></script>
<script src="../../../plugins/moment/moment.min.js"></script>
<script src="../../../plugins/moment/moment-timezone.min.js"></script>
<script src="../../../plugins/moment/moment-precise-range.js"></script>
<script src="../../../plugins/inputmask/dist/min/jquery.inputmask.bundle.min.js"></script>
<script src="../../../plugins/noUiSlider/nouislider.min.js"></script>
<script src="../../../plugins/jstree/jstree.min.js"></script>
<!-- html2pdf libraries -->
<script src="../../../plugins/html2pdf/jspdf.min.js"></script>
<script src="../../../plugins/html2canvas-1.0.0/html2canvas.min.js"></script>
<script src="../../../plugins/html2pdf/html2pdf.bundle.min.js?_=20180708"></script>
<script src="../../../plugins/jszip/dist/jszip.min.js"></script>

<!-- core libraries -->
<script>document.write('<scr' + 'ipt src="../../../js/magna-app.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="../../../js/magna-services.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="../../../js/magna-environment.js?_v=' + window.__env.VERSION_ID + '"></sc' + 'ript>');</script>
<script>document.write('<scr' + 'ipt src="index.js?_v=' + window.__env.SET_FIELD_PAGE_VERSION_ID + '"></sc' + 'ript>');</script>
<script>
    $(document).ready(function () {
        $.material.options.autofill = true
        $.material.init();
    });
</script>
<div id="file_viewer_modal" class="modal fade">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
                <h4 class="modal-title">Edit Notes</h4>
            </div>
            <div class="modal-body" style="text-align: center;">

            </div>
            <div class="modal-footer">
                <a id="generic-download-link" class="btn btn-primary btn-raised">
                    <i class="glyphicon glyphicon-download-alt"></i> Download </a>
            </div>
        </div>
    </div>
</div>
</body>

</html>

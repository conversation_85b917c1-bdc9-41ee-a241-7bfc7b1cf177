var SetFieldApp = angular.module('set-field-app', ['magna-app', 'ngCookies']);
SetFieldApp.config(function config($locationProvider, $compileProvider, $routeProvider, $controllerProvider, __env) {
    $compileProvider.aHrefSanitizationWhitelist(/^\s*(https?|ftp|mailto|file|javascript|tel):/);
    $locationProvider.hashPrefix('!');
    /*Creating a more synthesized form of service of $ controllerProvider.register*/
    SetFieldApp.registerCtrl = $controllerProvider.register;
    $routeProvider.

    when('/401', {
        templateUrl: '../../../unauthorized.html?_v=' + __env.SET_FIELD_PAGE_VERSION_ID
    }).
    when('/404', {
        templateUrl: '../../../not-found.html?_v=' + __env.SET_FIELD_PAGE_VERSION_ID
    }).
    when('/:token', {
        templateUrl: 'content.html?_v=' + __env.SET_FIELD_PAGE_VERSION_ID,
        controller: 'SetFieldController',
        page_info: {
            code: "visa__public_set-field"
        }
    }).
    otherwise('/505');
});


SetFieldApp.controller('SetFieldController', function ($routeParams, $route, $window, $sce, $http, $timeout, $location, $scope, magnaAuthenticationService, magnaMainService, magnaHttpService,magnaValidationService, $cookies, $rootScope, $filter, __env) {
    $rootScope.globals = $cookies.getObject('globals') || {};
    __env = window.__env;
    $scope.token = $routeParams.token; // should get from parameter
    $scope.files = [];
    $scope.previousEidBack = {};
    $scope.templates = "";
    $scope.details = {
        requestID: "",
        workerName: "",
        fieldName: "",
        fieldlabel: "",
        fieldType: "",
        fieldValue:""
    };
    $scope.addPageCodeHeader = function (headers={}){
        headers.pageCode = $route.current.$$route.page_info.code;
        return headers;
    }
    $scope.getTemplates = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: "templates.json",
            }, function (response) {
                $scope.templates = response;
            }, { needs_loading_icon: true, ignore_authorization: true }
        );
    }
    $scope.getTemplates();

    $scope.getFieldsDetails = function () {
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA + "maidVisaFields/getFieldsDetail?token=" + $scope.token,
                headers:$scope.addPageCodeHeader()
            }, function (response) {
                $scope.details = response;
                var fields = [{
                    defaultValue: "",
                    label: response.fieldlabel,
                    name: response.fieldName,
                    properties: {},
                    type: response.fieldType,
                }];
                $scope.DynamicForm.setInfo(fields);
            }, { needs_loading_icon: true, ignore_authorization: true }
        );
    }
    $scope.getFieldsDetails();


    $scope.save = function (saveOnly) {
        var vaildation = {};
        for (var i = 0; i < $scope.attrs.length ; i++) {
            var field = $scope.attrs[i];
            if (($scope.DynamicForm.isRequiredField(field)||field.obj.required) && $scope.DynamicForm.isVisibleField(field)) { // except hidden_fields
                if(field.template != 'dateTime'){
                    vaildation['obj.attrsVal["' + field.obj.name + '"]'] = ["required"];
                    if (field.template == 'boolean')
                        vaildation['obj.attrsVal["' + field.obj.name + '"]'].push(function (attr, val) {
                            return !val ? 'Please Confirm' : true;
                        })
                }else{
                    vaildation['obj.attrsVal["' + field.obj.name+'__date' + '"]'] = ["required"];
                    vaildation['obj.attrsVal["' + field.obj.name+'__time' + '"]'] = ["required"];
                }
            }
        }
        var isValid =  (saveOnly && $scope.isLastStep )  || magnaValidationService.validate($scope, vaildation);
        if (isValid){
            magnaMainService.LoadingIcon.show();
            var cleanData = Object.assign({}, $scope.obj.attrsVal);
            $scope.attrs.forEach(function (e) {
                if (!$scope.DynamicForm.isVisibleField(e)||e.template === 'static') { // remove hidden_fields and static fields
                    delete cleanData[e.obj.name];
                    return;
                }
                if (e.template === 'dateTime') { // remove hidden_fields and static fields
                    cleanData[e.obj.name] = cleanData[e.obj.name+"__date"]+" "+cleanData[e.obj.name+"__time"];
                    delete cleanData[e.obj.name+"__date"];
                    delete cleanData[e.obj.name+"__time"];
                    return;
                }
                if (e.template === 'enum') {
                    if (e.obj.list) {
                        cleanData[e.obj.name] = {
                            id: cleanData[e.obj.name]
                        };
                    } else {
                        cleanData[e.obj.name] = $scope.obj.attrsVal[e.obj.name];
                    }
                }

                if (e.template === 'file') {
                    if (!cleanData.attachments) {
                        cleanData.attachments = [];
                    }
                    if($scope.obj.attrsVal[e.obj.name] && $scope.obj.attrsVal[e.obj.name].length>=1){
                        $.each($scope.obj.attrsVal[e.obj.name],function (index, item) {
                            cleanData.attachments.push({
                                id: item.id
                            });
                        });
                    }else{
                        cleanData.attachments.push({
                            id: $scope.obj.attrsVal[e.obj.name] ? $scope.obj.attrsVal[e.obj.name].id : null
                        });
                    }
                    delete cleanData[e.obj.name];
                }
            });
            //cleanData.id = $scope.details.requestID;
            magnaHttpService.HttpWrapper({
                method: "POST",
                url: (__env.VISA + 'maidVisaFields/setFieldsValue?token='+$scope.token),
                data: cleanData,
                headers:$scope.addPageCodeHeader()
            }, function (response) {
                magnaMainService.DialogBox.showSuccessMsg('Sent Successfully');
            }, { needs_loading_icon: true, ignore_authorization: true });

        }

    };

    // Dynamic Form
    $scope.DynamicForm = new (function () {
        var transform = function (inObj) {
            var outObj = {};
            var transformers = {
                'string': function (inObj, outObj) {

                },
                'long': function (inObj, outObj) {

                },
                'date': function (inObj, outObj) {

                },
                'boolean': function (inObj, outObj) {
                    if (inObj.properties.options) {
                        outObj.template = 'radio';
                        outObj.obj.options.data = $.map(inObj.properties.options.split(";"), function (item, idx) {
                            return {
                                value: (idx === 0) ? true : false,
                                label: item,
                            }
                        });
                        outObj.obj.defaultValue = inObj.defaultValue ? 'true' : 'false';
                    }
                    else
                        outObj.obj.defaultValue = inObj.defaultValue ? true : false;
                },
                'radio': function (inObj, outObj) {
                    var labels = inObj.properties.labels?inObj.properties.labels.split(";"):[];
                    outObj.obj.options.data = $.map(inObj.properties.options.split(";"), function (item, idx) {
                        return {
                            value: item,
                            label: labels[idx]?labels[idx]:item,
                        }
                    });

                },
                'file': function (inObj, outObj) {
                    outObj.obj.tag = inObj.name;
                    outObj.obj.defaultValue = inObj.defaultValue ? JSON.parse(inObj.defaultValue) : null;
                    outObj.obj.currentStep = $scope.currentTask.id;
                    outObj.obj.inputId = outObj.obj.tag + "-" + (new Date()).getTime();
                    outObj.obj.maxFiles = inObj.properties&&inObj.properties.multi?100:1;
                    outObj.obj.disabled = inObj.properties&&inObj.properties.disabled?true:false;
                },
                'enum': function (inObj, outObj) {
                    if (inObj.properties.options) {
                        outObj.obj.options.data = $.map(inObj.properties.options.split(";"), function (item) {
                            return {
                                text: maidccService.getEnumValueLabel(item),
                                id: item,
                            }
                        });
                    } else if (inObj.properties.list) {
                        outObj.obj.list = inObj.properties.list;
                        if (inObj.defaultValue) {
                            outObj.obj.defaultValue =
                                inObj.defaultValue.id;
                        }

                        outObj.obj.options = {
                            data: [{
                                id: (inObj.defaultValue) ? inObj.defaultValue.id : '',
                                text: (inObj.defaultValue) ? inObj.defaultValue.name : '',
                            }],
                            ajax: {
                                url: __env.PUBLIC + 'picklist/items/' + inObj.properties.list,
                                headers:$scope.addPageCodeHeader(),
                                data: function (params) {
                                    return {
                                        search: params.term ? params.term : ""
                                    }
                                },
                                processResults: function (data) {
                                    // Tranforms the top-level key of the response object from 'items' to 'results'
                                    return {
                                        results: $.map(data, function (item) {
                                            return {
                                                text: item.label,
                                                id: item.id,
                                            }
                                        })
                                    };
                                }

                            }
                        }

                    }

                },
                'static': function (inObj, outObj) {
                },
                'dateTime': function (inObj, outObj) {
                }
            };

            if(inObj.properties&&inObj.properties.disableSave=="true"){
                $scope.$watch("obj.attrsVal['"+inObj.name+"']",function(newVal,OldVal){
                    if(newVal){
                        $scope.disableSave = true;
                    }
                })
            }
            if(inObj.properties&&inObj.properties.disableSaveAndNextWhenGreaterThanNow=="true"){
                $scope.$watch("obj.attrsVal['"+inObj.name+"']",function(newVal,OldVal){
                    $scope.disableSaveAndNext = moment(newVal, 'YYYY-MM-DD').isBefore(moment())||moment().isSame(moment())<1?false:true;
                })
            }
            if(inObj.properties&&inObj.properties.disableSaveAndNext=="true"){
                $scope.disableSaveAndNext = true;
            }
            outObj = {};
            outObj.template = inObj.type;
            outObj.obj = {
                label: inObj.label,
                name: inObj.name,
                attrsVal: $scope.obj.attrsVal,
                defaultValue: inObj.defaultValue,
                required: true,
                options: {},
                properties:inObj.properties
            }
            //hidden_fields
            var visiblevalues = inObj.properties.visible && !inObj.properties.visibleCondition && inObj.properties.visible.indexOf(';') > -1 ? inObj.properties.visible.split(';') : [];
            if (visiblevalues[2] == 'form')
                outObj.obj.visibleCond = ' "" + $scope.obj.attrsVal["' + visiblevalues[0] + '"] == "' + visiblevalues[1] + '"';
            if(inObj.properties.visible && inObj.properties.visibleCondition){
                var conditions = inObj.properties.visibleCondition;
                var jss =  JSON.parse(inObj.properties.visible);
                $.each(JSON.parse(inObj.properties.visible),function (index,item) {
                    visiblevalues = item.indexOf(';') > -1 ? item.split(';') : [];
                    if (visiblevalues[2] == 'form'){
                        var parts = conditions.split(('#'+(index+1)+'#'));
                        conditions = parts.join((' "" + $scope.obj.attrsVal["' + visiblevalues[0] + '"] == "' + visiblevalues[1] + '" '));
                    }
                })
                outObj.obj.visibleCond = conditions;
            }
            if (inObj.properties.required && inObj.properties.required == 'false' ){
                outObj.obj.required = false;
            }
            var requiredvalues = inObj.properties.requiredIf && !inObj.properties.requiredCondition && inObj.properties.requiredIf.indexOf(';') > -1 ? inObj.properties.requiredIf.split(';') : [];
            if (requiredvalues[2] == 'form'){
                outObj.obj.requiredCond = ' "" + $scope.obj.attrsVal["' + requiredvalues[0] + '"] == "' + requiredvalues[1] + '"';
                outObj.obj.required = false;
            }
            if(inObj.properties.requiredIf && inObj.properties.requiredCondition){
                var conditions = inObj.properties.requiredCondition;
                var jss =  JSON.parse(inObj.properties.visible);
                $.each(JSON.parse(inObj.properties.requiredIf),function (index,item) {
                    requiredvalues = item.indexOf(';') > -1 ? item.split(';') : [];
                    if (requiredvalues[2] == 'form'){
                        var parts = conditions.split(('#'+(index+1)+'#'));
                        conditions = parts.join((' "" + $scope.obj.attrsVal["' + requiredvalues[0] + '"] == "' + requiredvalues[1] + '" '));
                    }
                })
                outObj.obj.requiredCond = conditions;
            }

            transformers[inObj.type](inObj, outObj);
            $scope.obj.attrsVal[inObj.name] = outObj.obj.defaultValue;
            if(inObj.type == 'dateTime'&&outObj.obj.defaultValue){
                $scope.obj.attrsVal[inObj.name+"__date"] = outObj.obj.defaultValue?moment(outObj.obj.defaultValue,'YYYY-MM-DD h:mm:ss').format('YYYY-MM-DD'):'';
                $scope.obj.attrsVal[inObj.name+"__time"] = outObj.obj.defaultValue?moment(outObj.obj.defaultValue,'YYYY-MM-DD h:mm:ss').format('h:mm:ss'):'';
            }
            return outObj;

        }

        $scope.checkSaveAndNextShow = function () {
            if( ($scope.prevStepName?$scope.prevStepName == 'Waiting for the employee to sign and send back the offer letter':($scope.currentTask && $scope.currentTask.name == 'Waiting for the employee to sign and send back the offer letter'))&&($scope.all_req_info.sentFromOperatorPortal ) )
                return false;
            if( ($scope.process_type.url == 'cancelRequest'||!$scope.all_req_info.stopped ) && ( $scope.isLastStep&& !$scope.all_req_info.completed )){
                return true;
            }
        }

        $scope.checkSaveShow = function () {
            if( ($scope.prevStepName?$scope.prevStepName == 'Waiting for the employee to sign and send back the offer letter':($scope.currentTask && $scope.currentTask.name == 'Waiting for the employee to sign and send back the offer letter'))&&($scope.all_req_info.sentFromOperatorPortal ) )
                return false;
            return true;
        }

        var loadData = function (data) {
            for (var key in $scope.obj.attrsVal) {
                delete $scope.obj.attrsVal[key];
            }

            $scope.attrs = [];
            data.forEach(function (element) {
                $scope.attrs.push(transform(element, $scope));
            });

            $timeout(function () {
                magnaMainService.initGUI();
            });

        }

        $scope.attrs = [];
        $scope.templates = [];
        $scope.obj = { attrsVal: {} };

        return {
            hasTemplates: function () {
                return $scope.templates.length > 0;
            },
            setTemplates: function (templates) {
                $scope.templates = templates;
            },
            setInfo: function (data) {
                loadData(data);
            },
            isVisibleField: function (field) {
                return field.obj.visibleCond ? eval(field.obj.visibleCond) : true;
            },
            isRequiredField: function (field) {
                return field.obj.requiredCond ? eval(field.obj.requiredCond) : false;
            }
        }

    })();

});
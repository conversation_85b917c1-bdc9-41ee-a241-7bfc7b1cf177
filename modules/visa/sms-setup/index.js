mainApp.registerCtrl("page-ctrl", function ($scope, $rootScope, $location, $route, $routeParams, magnaMainService, magnaHttpService, __env, maidccService) {
    $scope.currentPage = 0;
    $scope.currentRow = {};
    $scope.breadcrumbs = [
        {
            label: MaidccModules.getModule('visa').label
        }, {
            label: 'SMS Setup',
        }
    ];
    $scope.mainDataGrid = {
        columns: [
            {
                label: "Event",
                type: "text",
                valueExp: "$data['event']"
            },
            {
                label: "Trigger",
                type: "text",
                valueExp: "$data['triggerDescription']"
            },
            {
                label: "Message",
                type: "text",
                valueExp: "$data['smsText']"
            },
            {
                label: "Reminder Every",
                type: "text",
                valueExp: function ($data, $index) {
                    return ($data['reminder']?$data['reminder']:0) +' Hours';
                }
            },
            {
                label: "Times",
                type: "text",
                valueExp: function ($data, $index) {
                    return $data['maxReminders']?$data['maxReminders']:0;
                }
            },
            {
                label: "Follow-up",
                type: "text",
                valueExp: "$data['followUps']=='None'?'No':$data['followUps']"
            }
        ],
        data: [],
        actions: [{
            label: "Edit",
            callbackFunc: function ($data) {
                var url = $location.path();
                var newBreadCrumbs = [];
                angular.copy($scope.breadcrumbs, newBreadCrumbs);
                newBreadCrumbs[newBreadCrumbs.length - 1].link = '#!' + url;
                magnaMainService.RouteData.storeData('breadcrumb', newBreadCrumbs);
                magnaMainService.RouteData.storeData('returnPageUrl', url);
                $scope.$apply(function () {
                    $location.path('/visa/edit-message/' + $data["id"]);
                });
            },
            visiblityCond: "!$data.stopped",
            htmlAttributes: { class: 'btn-default' }
        },{
            label: "Disable",
            callbackFunc: function ($data) {
                $scope.currentRow = $data;
                $scope.DisableEnableMessage();
            },
            visiblityCond: function ($data) { return ($data.enabled !== false) },
            htmlAttributes: { class: 'btn-default' }
        },{
            label: "Enable",
            callbackFunc: function ($data) {
                $scope.currentRow = $data;
                $scope.DisableEnableMessage();
            },
            visiblityCond: function ($data) { return $data.enabled === false},
            htmlAttributes: { class: 'btn-default' }
        }
        ]
    };

    $scope.mainDataGridPagination = {
        paginationInfo: {},
        submitFunction: function (pageNo) {
        $scope.getTableData(pageNo);
        }
    }
    $scope.getTableData = function (pageNo) {
        $scope.currentPage = pageNo;
        magnaHttpService.HttpWrapper({
                method: "GET",
                url: __env.VISA +  'messageTemplate/getAllMessageTemplates?page=' + pageNo + "&size=" + 50,
                headers: {
                    'Content-Type': "application/json"
                }
            }, function (response) {
                $scope.mainDataGrid.data = response.content;
                $scope.mainDataGridPagination.paginationInfo = response;
            }, { needs_loading_icon: true }
        );
    }
    $scope.$on('$viewContentLoaded', function () {
        $scope.getTableData(0);
    });

    $scope.DisableEnableMessage = function () {
        var requestData = {
            id: $scope.currentRow.id,
            enabled: ($scope.currentRow.enabled !== false)?false:true,
        }
        magnaHttpService.HttpWrapper({
            method: "POST",
            url: __env.VISA + "messageTemplate/update",
            data: requestData
        }, function (response) {
            magnaMainService.DialogBox.showSuccessMsg('Message '+(($scope.currentRow.enabled !== false )?'Disabled':'Enabled')+' Successfully');
            $scope.getTableData($scope.currentPage);
        }, {
            needs_loading_icon: true
        });
    }

});
{"string": "@\"<input class='form-control' ng-disabled='{{obj.disabled}}' placeholder='Enter {{obj.label}}' ng-focus='obj.properties.onFocus()' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'>\"", "boolean": "@\"<div class='checkbox'><label id='{{obj.name}}'><input type='checkbox' ng-disabled='{{obj.disabled}}' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'><span class='checkbox-material'></span> {{obj.label}}</label></div>\"", "date": "@\"<magna-date-input ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]' options='{startDate: \\\"01-01-1900\\\",format: \\\"dd/mm/yyyy\\\"}'></magna-date-input>\"", "time": "@\"<magna-time-input ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'></magna-time-input>\"", "dateTime": "@\"<magna-date-input ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' ng-model='obj.attrsVal[\\\"\"+obj.name+\"__date\\\"]' options='{startDate: \\\"01-01-1900\\\",format: \\\"dd/mm/yyyy\\\"}' ></magna-date-input><magna-time-input ng-model='obj.attrsVal[\\\"\"+obj.name+\"__time\\\"]'></magna-time-input>\"", "file": "@\"<magna-file-input-crop ng-disabled='{{obj.disabled}}' tag='{{obj.tag}}' options='{maxFiles: obj.maxFiles}' id='{{obj.inputId}}' other-options='obj.otherOptions' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'></magna-file-input-crop>\"", "enum": "@\"<magna-select-input ng-disabled='{{obj.disabled}}' options='obj.attrsOpt[\\\"\"+obj.name+\"\\\"]' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]' ng-model-obj='obj.attrsValObj[\\\"\"+obj.name+\"\\\"]' ></magna-select-input>\"", "long": "@\"<input class='form-control' ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' type='number' placeholder='Enter {{obj.label}}' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'>\"", "double": "@\"<input class='form-control' ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' type='number' placeholder='Enter {{obj.label}}' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'>\"", "radio": "@\"<div class='radio radio-primary'><label  id='{{obj.name + item.value}}' style='display:block;    text-align: left;' ng-repeat='item in obj.options.data'><input ng-disabled='{{obj.disabled}}' type='radio' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]' value='{{item.value}}'><span class='checkbox-material'></span>{{item.label}}</label></div>\"", "static": "@\"<p ng-class='obj.customCssClass' style='line-height: 25px;margin-top: 10px;padding-left: 10px;cursor:pointer;' ng-click='obj.copyText()'>{{obj.attrsVal[\\\"\"+obj.name+\"\\\"]}}</p>\"", "downloadFile": "@\"<magna-download-thumbnail  options='obj.options'  other-options='obj.otherOptions' id='{{obj.inputId}}' class='lh-40' ng-show='obj.defaultValue' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'> </magna-download-thumbnail>\"", "mask": "@\"<magna-input-mask ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' options='obj.options' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'></magna-input-mask>\"", "stringTextArea": "@\"<textarea ng-disabled='{{obj.disabled}}' ng-focus='obj.properties.onFocus()' options='obj.options' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]' class='col-md-12' rows='3'></textarea>\"", "signature": "@\"<canvas id='signature-pad-{{obj.name}}' class='signature-pad' style='border:1px solid #aaa !important;' width='400' height='400'></canvas><input type='hidden' ng-model='obj.attrsVal[\\\"\"+obj.name+\"\\\"]'>\"", "hyperLink": "@\"<a ng-href='{{obj.properties.hyperLink}}' target='_blank' >{{obj.attrsVal[\\\"\"+obj.name+\"\\\"]}}</a>\"", "column": "@\"\""}
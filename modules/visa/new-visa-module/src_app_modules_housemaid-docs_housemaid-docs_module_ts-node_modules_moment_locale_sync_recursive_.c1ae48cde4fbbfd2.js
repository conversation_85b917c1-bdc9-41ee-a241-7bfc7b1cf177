(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_housemaid-docs_housemaid-docs_module_ts-node_modules_moment_locale_sync_recursive_"],{65484:(k,N,r)=>{"use strict";r.r(N),r.d(N,{HousemaidDocsModule:()=>bt});var m=r(69808),p=r(1402),t=r(5e3),c=r(93075),g=r(88476),T=r(61135);const w={maidSearch:{search:{maidName:"",nationalityId:""},params:{page:0,size:20}},docSearch:{search:{fromDate:"",toDate:"",statusReason:"",user:""},params:{page:0,size:20}},maidName:""};var h=r(65620);const F=(0,h.PH)("[navigated to view route | Store Service] Set Current Maid name",(0,h.Ky)()),O=(0,h.PH)("[reset state | Store Service] reset state"),J="housemaid-docs",B=(0,h.Lq)(w,(0,h.on)(F,(a,{name:i})=>Object.assign(Object.assign({},a),{maidName:i})),(0,h.on)(O,a=>w)),Q=(0,h.ZF)(J),H=(0,h.P1)(Q,a=>a.maidName);let j=(()=>{class a extends g.il{constructor(e){super(e),this.maidSearchSubject=new T.X(w.maidSearch),this.docSearchSubject=new T.X(w.docSearch),this.selectHousemaidName$=this.store.select(H),this.nationalities$=this.picklistSelectors("nationalities"),this.statusReason$=this.picklistSelectors("Status_Reason")}setName(e){this.store.dispatch(F({name:e}))}pickListsCodes(){return["nationalities","Status_Reason"]}resetState(){this.store.dispatch(O())}}return a.\u0275fac=function(e){return new(e||a)(t.LFG(h.yh))},a.\u0275prov=t.Yz7({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var b=r(40520),G=r(8188),f=r(43604);let S=(()=>{class a{constructor(e,s){this._api=e,this._http=s}getRecords$(e){var s,o;return this._http.get(`${this._api}/${f.b.allRecords}`,{params:new b.LE({fromObject:Object.assign(Object.assign(Object.assign({},e.params),(null===(s=e.search)||void 0===s?void 0:s.maidName)?{maidName:e.search.maidName}:{}),(null===(o=e.search)||void 0===o?void 0:o.nationalityId)?{nationalityId:e.search.nationalityId}:{})})})}getDocs$(e){return this._http.get(`${this._api}/${f.b.getDocs}`,{params:new b.LE({fromObject:{maidId:e}})})}getUsers$(e){return this._http.get(`${f.b.users}`,{params:new b.LE({fromObject:{page:e.params.page||0,size:e.params.size||100,search:e.search||"",_:1718795400765}})})}getDocsStatus$(e){var s,o,n,l;return this._http.get(`${this._api}/${f.b.getDocsStatus}`,{params:new b.LE({fromObject:Object.assign(Object.assign(Object.assign(Object.assign({page:e.params.page||0,size:e.params.size||20},(null===(s=e.search)||void 0===s?void 0:s.fromDate)?{fromDate:e.search.fromDate}:{}),(null===(o=e.search)||void 0===o?void 0:o.toDate)?{toDate:e.search.toDate}:{}),(null===(n=e.search)||void 0===n?void 0:n.statusReason)?{statusReason:e.search.statusReason}:{}),(null===(l=e.search)||void 0===l?void 0:l.user)?{user:e.search.user}:{})})})}updateDocStatus$(e){return this._http.post(`${this._api}/${f.b.updateStatus}`,e)}saveDocsStatus(e){return this._http.post(`${this._api}/${f.b.createAll}`,e)}}return a.\u0275fac=function(e){return new(e||a)(t.LFG(G.JV),t.LFG(b.eN))},a.\u0275prov=t.Yz7({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var x=r(43687),y=r(26523),v=r(65868),L=r(45834),Z=r(62764);const z=function(){return[]};let P=(()=>{class a{constructor(e,s,o,n){this._formBuilder=e,this._storeService=s,this._docsService=o,this._router=n,this.searchForm=this._formBuilder.group({name:"",nationality:""}),this.nationalities$=this._storeService.nationalities$,this.gridCols=[{field:"name",header:"Maid's Name"},{field:"nationality.label",header:"Nationality"},{field:"operations",header:"View",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"stroked",text:"View",color:"accent",mode:"single",disabled:!1,hidden:l=>!1,callback:l=>this.view(l)}]}}]}ngOnInit(){this.updateDataList(this._storeService.maidSearchSubject.value)}updateDataList(e){this.dataList$=this._docsService.getRecords$(e),this.dataList$.subscribe(s=>{this.dataList=s})}search(){this.updateDataList(Object.assign(Object.assign({},this._storeService.maidSearchSubject.value),{search:{maidName:this.searchForm.value.name,nationalityId:this.searchForm.value.nationality}}))}getNextPage(e){this.updateDataList(Object.assign(Object.assign({},this._storeService.maidSearchSubject.value),{search:{maidName:this.searchForm.value.name,nationalityId:this.searchForm.value.nationality},params:{page:e.pageIndex,size:e.pageSize}}))}view(e){this._storeService.setName(e.name),this._router.navigateByUrl(`/visa/v2/housemaid-docs/${e.id}`)}redirectToAuditedDocs(){this._router.navigateByUrl("/visa/v2/housemaid-docs/housemaid-docs-status-audited")}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(c.qu),t.Y36(j),t.Y36(S),t.Y36(p.F0))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-housemaid-docs"]],decls:14,vars:15,consts:[[1,"row","p-3"],[1,"col-sm-8"],[3,"formGroup"],["label","Name","formControlName","name"],["formControlName","nationality","label","Nationality",3,"data"],["cc-raised-button","","color","basic",1,"m-2",3,"click"],[2,"font-size","36px"],[1,"col-sm-4","d-flex","justify-content-end","align-items-center"],["cc-button","","color","accent",3,"click"],[1,"d-flex","w-100",3,"columns","data","stickyHeader","columnMenuButtonIcon","showPaginator","length","pageOnFront","pageIndex","pageSize","page"]],template:function(e,s){if(1&e&&(t.TgZ(0,"div",0)(1,"div",1)(2,"form",2),t._UZ(3,"cc-input",3)(4,"cc-select",4),t.ALo(5,"async"),t.TgZ(6,"button",5),t.NdJ("click",function(){return s.search()}),t.TgZ(7,"cc-icon",6),t._uU(8,"search"),t.qZA(),t._uU(9," Search "),t.qZA()()(),t.TgZ(10,"div",7)(11,"button",8),t.NdJ("click",function(){return s.redirectToAuditedDocs()}),t._uU(12," Housemaid Audited Docs "),t.qZA()()(),t.TgZ(13,"cc-datagrid",9),t.NdJ("page",function(n){return s.getNextPage(n)}),t.qZA()),2&e){let o;t.xp6(2),t.Q6J("formGroup",s.searchForm),t.xp6(2),t.Q6J("data",null!==(o=t.lcZ(5,11,s.nationalities$))&&void 0!==o?o:t.DdM(13,z)),t.xp6(9),t.Q6J("columns",s.gridCols)("data",null!=s.dataList?s.dataList.content:t.DdM(14,z))("stickyHeader",!0)("columnMenuButtonIcon","settings")("showPaginator",!0)("length",null!=s.dataList?s.dataList.totalElements:0)("pageOnFront",!1)("pageIndex",null!=s.dataList?s.dataList.number:0)("pageSize",null!=s.dataList?s.dataList.size:0)}},directives:[c._Y,c.JL,c.sg,x.G,c.JJ,c.u,y.jB,v.uu,L.Q9,Z.Ge],pipes:[m.Ov],styles:[""]}),a})();var E=r(78372),q=r(71884),X=r(63900),W=r(54004),D=r(48966),K=r(18505),u=r(82599),_=r(21799),V=r(22313);let $=(()=>{class a{constructor(e){this.sanitizer=e}transform(e){return this.sanitizer.bypassSecurityTrustResourceUrl(e)}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(V.H7,16))},a.\u0275pipe=t.Yjl({name:"safe",type:a,pure:!0}),a})();function tt(a,i){if(1&a&&(t.ynx(0),t._UZ(1,"iframe",8),t.ALo(2,"safe"),t.BQk()),2&a){const e=t.oxw();t.xp6(1),t.Q6J("src",t.lcZ(2,1,e.data.url),t.uOi)}}function et(a,i){if(1&a&&(t._UZ(0,"img",9),t.ALo(1,"safe")),2&a){const e=t.oxw();t.Q6J("src",t.lcZ(1,2,e.data.url),t.LSH)("alt",e.data.name)}}let U=(()=>{class a{constructor(e,s,o,n){this.data=e,this._ccDialogRef=s,this._mediaService=o,this._notificationsService=n}ngOnInit(){}download(){this._mediaService.downloadFile(`public/download/${this.data.uuid}`).pipe((0,K.b)(()=>this._notificationsService.notifyInfo("Downloading the file..."))).subscribe()}close(){this._ccDialogRef.close()}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(D.WI),t.Y36(D.so),t.Y36(_.yJ),t.Y36(_.zg))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-doc-preview-dialog"]],decls:15,vars:3,consts:[["cc-std-dialog",""],["cc-dialog-title",""],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","",3,"click"],[1,"d-flex","justify-content-center"],[4,"ngIf","ngIfElse"],["imgTpl",""],["cc-raised-button","","color","accent",3,"click"],[2,"font-size","36px"],["type","application/pdf","width","100%","height","100%",2,"overflow","auto",3,"src"],["height","auto",2,"max-width","100%",3,"src","alt"]],template:function(e,s){if(1&e&&(t.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),t._uU(3),t.qZA(),t.TgZ(4,"a",2),t.NdJ("click",function(){return s.close()}),t.qZA()(),t.TgZ(5,"cc-dialog-content")(6,"div",3),t.YNc(7,tt,3,3,"ng-container",4),t.YNc(8,et,2,4,"ng-template",null,5,t.W1O),t.qZA()(),t.TgZ(10,"cc-dialog-actions")(11,"button",6),t.NdJ("click",function(){return s.download()}),t.TgZ(12,"cc-icon",7),t._uU(13,"download"),t.qZA(),t._uU(14," Download "),t.qZA()()()),2&e){const o=t.MAs(9);t.xp6(3),t.Oqu(s.data.name),t.xp6(4),t.Q6J("ngIf","pdf"===s.data.type)("ngIfElse",o)}},directives:[u.iK,u.Cj,u.Zb,v.uu,u.fX,u.kL,m.O5,u.Zu,L.Q9],pipes:[$],styles:[""]}),a})();const st=function(){return[]};let at=(()=>{class a{constructor(e,s,o,n,l,d){this.data=e,this._ccDialogRef=s,this._storeService=o,this._docsService=n,this._notificationService=l,this._formBuilder=d,this.statusReason$=this._storeService.statusReason$,this.form=this._formBuilder.group({statusReasonId:""})}ngOnInit(){}save(){this._docsService.updateDocStatus$({id:this.data.id,statusReason:{id:this.form.value.statusReasonId}}).subscribe(e=>{"Updated"===e.message&&this._notificationService.notifySuccess("Changed Successfully"),this.close()})}close(){this._ccDialogRef.close()}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(D.WI),t.Y36(D.so),t.Y36(j),t.Y36(S),t.Y36(_.zg),t.Y36(c.qu))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-status-dialog"]],decls:13,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-title","",1,"text-center"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","",3,"click"],[3,"formGroup"],["formControlName","statusReasonId","label","Status",3,"data"],["cc-button","","color","primary",3,"click"],["cc-button","","color","accent",3,"click"]],template:function(e,s){if(1&e&&(t.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),t._uU(3,"Change Maid Status"),t.qZA(),t.TgZ(4,"a",2),t.NdJ("click",function(){return s.close()}),t.qZA()(),t.TgZ(5,"cc-dialog-content",3),t._UZ(6,"cc-select",4),t.ALo(7,"async"),t.qZA(),t.TgZ(8,"cc-dialog-actions")(9,"button",5),t.NdJ("click",function(){return s.close()}),t._uU(10,"Cancel"),t.qZA(),t.TgZ(11,"button",6),t.NdJ("click",function(){return s.save()}),t._uU(12,"Save"),t.qZA()()()),2&e){let o;t.xp6(5),t.Q6J("formGroup",s.form),t.xp6(1),t.Q6J("data",null!==(o=t.lcZ(7,2,s.statusReason$))&&void 0!==o?o:t.DdM(4,st))}},directives:[u.iK,u.Cj,u.Zb,v.uu,u.fX,u.kL,c.JL,c.sg,y.jB,c.JJ,c.u,u.Zu],pipes:[m.Ov],styles:[""]}),a})();var Y=r(92340),I=r(28172);function ot(a,i){if(1&a&&(t.TgZ(0,"a",14),t._uU(1),t.qZA()),2&a){const e=i.$implicit,s=t.oxw();t.Q6J("href",s.getDetailsLink(e.housemaid.id),t.LSH),t.xp6(1),t.Oqu(e.housemaid.name)}}function it(a,i){if(1&a){const e=t.EpF();t.TgZ(0,"a",15),t.NdJ("click",function(){const n=t.CHM(e).$implicit;return t.oxw().preview(n)}),t._uU(1),t.qZA()}if(2&a){const e=i.$implicit;t.xp6(1),t.Oqu(e.attachment.name)}}const C=function(){return[]},nt=function(a,i){return{"housemaid.name":a,"attachment.name":i}};let ct=(()=>{class a{constructor(e,s,o,n,l,d){this._storeService=e,this._formBuilder=s,this._docsService=o,this._ccDialog=n,this._dialog=l,this._mediaService=d,this.statusReason$=this._storeService.statusReason$,this.searchTerms=new T.X(""),this.gridCols=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"stroked",text:"Change Status",color:"primary",mode:"single",disabled:!1,hidden:A=>!1,callback:A=>this.openStatusDialog(A.id)}]}},{field:"housemaid.name",header:"Maid's Name"},{field:"housemaid.nationality.label",header:"Nationality"},{field:"docType",header:"Doc Type"},{field:"attachment.name",header:"File Name"},{field:"statusReason.label",header:"Status"}],this.searchForm=this._formBuilder.group({fromDate:"",toDate:"",user:"",statusReason:""})}ngOnInit(){this.updateDataList(this._storeService.docSearchSubject.value),this.users$=this.searchTerms.pipe((0,E.b)(400),(0,q.x)(),(0,X.w)(e=>this._docsService.getUsers$({params:{page:0,size:100},search:e}).pipe((0,W.U)(s=>s.content.map(o=>({id:o.id,text:o.label}))))))}searchUsers(e){this.searchTerms.next(e)}updateDataList(e){this.dataList$=this._docsService.getDocsStatus$(e),this.dataList$.subscribe(s=>{this.dataList=s})}search(){this.updateDataList(Object.assign(Object.assign({},this._storeService.docSearchSubject.value),{search:Object.assign({},this.searchForm.value)}))}getNextPage(e){this.updateDataList(Object.assign(Object.assign({},this._storeService.docSearchSubject.value),{search:Object.assign({},this.searchForm.value),params:{page:e.pageIndex,size:e.pageSize}}))}openStatusDialog(e){this._ccDialog.originalOpen(at,{width:"500px",data:{id:e}}).afterClosed().subscribe(o=>{this.updateDataList(this._storeService.docSearchSubject.value)})}getDetailsLink(e){return Y.N.production&&!Y.N.newErp?"/main.html#!":`/${f.b.housemaidDetails}/${e}`}preview(e){let s=e.attachment.uuid,o=e.attachment.name;return this._mediaService.getFile("public/download/"+s).subscribe(n=>{this.previewFile(n,o,s)})}previewFile(e,s,o){let n=new Blob([e],{type:e.type});const l=URL.createObjectURL(n);let d=e.type.split("/")[1];this._ccDialog.originalOpen(U,{width:"100%",data:{name:s,url:l,blob:n,type:d,uuid:o}})}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(j),t.Y36(c.qu),t.Y36(S),t.Y36(u.uY),t.Y36(D.uw),t.Y36(_.yJ))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-audited-docs"]],decls:20,vars:34,consts:[[3,"formGroup"],[1,"row","my-4","p-3"],[1,"col-md-6"],["formControlName","fromDate","label","From Date","placeholder","yyyy-mm-dd",3,"icon","floatLabel","disabled"],["formControlName","toDate","label","To Date","placeholder","yyyy-mm-dd",3,"icon","floatLabel","disabled"],["formControlName","user","label","User",3,"data","searchOptions"],["formControlName","statusReason","label","Status",3,"data"],[1,"col-md-3"],["cc-raised-button","","color","basic",1,"m-2",3,"disabled","click"],[2,"font-size","36px"],[1,"d-flex","w-100",3,"columns","data","stickyHeader","columnMenuButtonIcon","showPaginator","length","pageOnFront","pageIndex","pageSize","cellTemplate","page"],[3,"ccGridCell"],["maidNameTpl",""],["fileNameTpl",""],[1,"reddish-link",3,"href"],[1,"reddish-link",3,"click"]],template:function(e,s){if(1&e&&(t.TgZ(0,"form",0)(1,"div",1)(2,"div",2),t._UZ(3,"cc-datepicker",3)(4,"cc-datepicker",4),t.qZA(),t.TgZ(5,"div",2)(6,"cc-select",5),t.NdJ("searchOptions",function(n){return s.searchUsers(n)}),t.ALo(7,"async"),t.qZA(),t._UZ(8,"cc-select",6),t.ALo(9,"async"),t.qZA(),t.TgZ(10,"div",7)(11,"button",8),t.NdJ("click",function(){return s.search()}),t.TgZ(12,"cc-icon",9),t._uU(13,"search"),t.qZA(),t._uU(14," Search "),t.qZA()()()(),t.TgZ(15,"cc-datagrid",10),t.NdJ("page",function(n){return s.getNextPage(n)}),t.qZA(),t.YNc(16,ot,2,2,"ng-template",11,12,t.W1O),t.YNc(18,it,2,1,"ng-template",11,13,t.W1O)),2&e){const o=t.MAs(17),n=t.MAs(19);let l,d;t.Q6J("formGroup",s.searchForm),t.xp6(3),t.Q6J("icon","date_range")("floatLabel","always")("disabled",!0),t.xp6(1),t.Q6J("icon","date_range")("floatLabel","always")("disabled",!0),t.xp6(2),t.Q6J("data",null!==(l=t.lcZ(7,22,s.users$))&&void 0!==l?l:t.DdM(26,C)),t.xp6(2),t.Q6J("data",null!==(d=t.lcZ(9,24,s.statusReason$))&&void 0!==d?d:t.DdM(27,C)),t.xp6(3),t.Q6J("disabled",!s.searchForm.valid),t.xp6(4),t.Q6J("columns",s.gridCols)("data",null!=s.dataList?s.dataList.content:t.DdM(28,C))("stickyHeader",!0)("columnMenuButtonIcon","settings")("showPaginator",!0)("length",null!=s.dataList?s.dataList.totalElements:0)("pageOnFront",!1)("pageIndex",null!=s.dataList?s.dataList.number:0)("pageSize",null!=s.dataList?s.dataList.size:0)("cellTemplate",t.WLB(29,nt,o,n)),t.xp6(1),t.Q6J("ccGridCell",null!=s.dataList?s.dataList.content:t.DdM(32,C)),t.xp6(2),t.Q6J("ccGridCell",null!=s.dataList?s.dataList.content:t.DdM(33,C))}},directives:[c._Y,c.JL,c.sg,I.AC,c.JJ,c.u,y.jB,v.uu,L.Q9,Z.Ge,Z.VC],pipes:[m.Ov],styles:[""]}),a})();var rt=r(50727);function lt(a,i){if(1&a){const e=t.EpF();t.TgZ(0,"a",9),t.NdJ("click",function(){t.CHM(e);const o=t.oxw();return o.doc?o.preview(o.doc):null}),t._uU(1),t.qZA()}if(2&a){const e=t.oxw();t.xp6(1),t.Oqu(null==e.doc?null:e.doc.name)}}function dt(a,i){if(1&a){const e=t.EpF();t.TgZ(0,"img",11),t.NdJ("click",function(){t.CHM(e);const o=t.oxw(2);return o.doc?o.preview(o.doc):null}),t.ALo(1,"safe"),t.qZA()}if(2&a){const e=t.oxw(2);t.Q6J("src",t.lcZ(1,1,e.mediaUrl),t.LSH)}}function ut(a,i){if(1&a&&t.YNc(0,dt,2,3,"img",10),2&a){const e=t.oxw();t.Q6J("ngIf",e.doc)}}const R=function(){return[]};function mt(a,i){if(1&a&&(t.ynx(0),t._UZ(1,"cc-select",12),t.ALo(2,"async"),t.BQk()),2&a){const e=t.oxw();let s;t.xp6(1),t.Q6J("formControlName",e.controlName)("required",!0)("data",null!==(s=t.lcZ(2,3,e.statusReason$))&&void 0!==s?s:t.DdM(5,R))}}function pt(a,i){if(1&a&&(t._UZ(0,"cc-select",13),t.ALo(1,"async")),2&a){const e=t.oxw();let s;t.Q6J("data",null!==(s=t.lcZ(1,1,e.statusReason$))&&void 0!==s?s:t.DdM(3,R))}}let ht=(()=>{class a{constructor(e,s,o){this._storeService=e,this._ccDialog=s,this._mediaService=o,this.statusReason$=this._storeService.statusReason$}ngOnInit(){}ngOnChanges(e){e.doc&&e.doc.currentValue&&this.doc&&this._mediaService.getFile("public/download/"+this.doc.uuid).subscribe(s=>{let o=new Blob([s],{type:s.type});this.mediaUrl=URL.createObjectURL(o)})}preview(e){let s=e.uuid,o=e.name;return this._mediaService.getFile("public/download/"+s).subscribe(n=>{this.previewFile(n,o,s)})}previewFile(e,s,o){let n=new Blob([e],{type:e.type});const l=URL.createObjectURL(n);let d=e.type.split("/")[1];this._ccDialog.originalOpen(U,{width:"100%",data:{name:s,url:l,blob:n,type:d,uuid:o}})}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(j),t.Y36(u.uY),t.Y36(_.yJ))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-attachement-card"]],inputs:{title:"title",controlName:"controlName",parentFormGroup:"parentFormGroup",doc:"doc"},features:[t.TTD],decls:14,vars:7,consts:[[1,"m-3",3,"formGroup"],[1,"card","attachement-card","shadow","p-5"],[1,"d-flex","justify-content-between"],[1,"card-info"],[1,"d-flex","justify-content-center","py-4",2,"max-height","80%"],["class","reddish-link",3,"click",4,"ngIf","ngIfElse"],["imgPreviewTpl",""],[4,"ngIf","ngIfElse"],["dummySelect",""],[1,"reddish-link",3,"click"],["class","img-fluid h-100","alt","Preview",3,"src","click",4,"ngIf"],["alt","Preview",1,"img-fluid","h-100",3,"src","click"],["label","Select Status",3,"formControlName","required","data"],["label","Select Status",3,"data"]],template:function(e,s){if(1&e&&(t.TgZ(0,"div",0)(1,"div",1)(2,"div",2)(3,"span",3),t._uU(4),t.qZA(),t.TgZ(5,"span",3),t._uU(6),t.qZA()(),t.TgZ(7,"div",4),t.YNc(8,lt,2,1,"a",5),t.qZA(),t.YNc(9,ut,1,1,"ng-template",null,6,t.W1O),t.YNc(11,mt,3,6,"ng-container",7),t.YNc(12,pt,2,4,"ng-template",null,8,t.W1O),t.qZA()()),2&e){const o=t.MAs(10),n=t.MAs(13);t.Q6J("formGroup",s.parentFormGroup),t.xp6(4),t.Oqu(s.title),t.xp6(2),t.hij("Last Update: ",(null==s.doc?null:s.doc.lastModificationDate)||"N/A",""),t.xp6(2),t.Q6J("ngIf","pdf"===(null==s.doc?null:s.doc.fileType))("ngIfElse",o),t.xp6(3),t.Q6J("ngIf",s.controlName)("ngIfElse",n)}},directives:[c.JL,c.sg,m.O5,y.jB,c.JJ,c.u,c.Q7],pipes:[$,m.Ov],styles:[""]}),a})();function gt(a,i){if(1&a&&(t.ynx(0),t._UZ(1,"app-attachement-card",6),t.BQk()),2&a){const e=i.$implicit,s=t.oxw();t.xp6(1),t.Q6J("title",e.name)("controlName",s.getDoc(e.name)?s.removeSpaces(e.name):"")("doc",s.getDoc(e.name))("parentFormGroup",s.statusForm)}}const ft=[{path:"",component:P,data:{pageCode:"HousemaidsAuditDocs"}},{path:"housemaid-docs-status-audited",component:ct,data:{pageCode:"HousemaidsAuditDocs"}},{path:":id",component:(()=>{class a{constructor(e,s,o,n,l,d,A){this._route=e,this._store=s,this._docsService=o,this._formBuilder=n,this._notificationService=l,this._router=d,this._cdRef=A,this.housemaidName$=this._store.selectHousemaidName$,this.docsList=[],this.docsNames=[{name:"Residency Visa"},{name:"Passport"},{name:"EID Front"},{name:"EID Back"}],this.statusForm=this._formBuilder.group({}),this.subscriptions=new rt.w0}ngOnInit(){this.subscriptions.add(this._route.paramMap.subscribe(e=>{const s=e.get("id");s&&(this.id=s,this.docs$=this._docsService.getDocs$(this.id),this.subscriptions.add(this.docs$.subscribe(o=>{this.statusForm=this._formBuilder.group({}),Object.entries(o).forEach(([n,l])=>{const d=this.removeSpaces(n);this.statusForm.addControl(d,new c.NI("")),this.docsList.push({name:d,doc:l})}),this._cdRef.detectChanges()})))}))}getDoc(e){var s;return(null===(s=this.docsList.find(o=>o.name===e))||void 0===s?void 0:s.doc)||null}ngOnDestroy(){this.subscriptions.unsubscribe()}removeSpaces(e){return e.replace(/ /g,"")}save(){0===Object.keys(this.statusForm.value).length?this._notificationService.notifyError("No attachments to save."):this._docsService.saveDocsStatus(this.generateDocStatusArray()).subscribe(()=>{this._notificationService.notifySuccess("Saved Successfully."),this._router.navigateByUrl("/visa/v2/housemaid-docs")})}generateDocStatusArray(){var e;let s=[];for(let o of this.docsList){let n={housemaid:{id:this.id},attachment:{id:o.doc.id},statusReason:{id:null===(e=this.statusForm.get(o.name))||void 0===e?void 0:e.value}};s.push(n)}return s}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(p.gz),t.Y36(j),t.Y36(S),t.Y36(c.qu),t.Y36(_.zg),t.Y36(p.F0),t.Y36(t.sBO))},a.\u0275cmp=t.Xpm({type:a,selectors:[["app-docs-status"]],decls:9,vars:6,consts:[[1,"doc-status-title","mt-4","py-3"],[3,"formGroup"],[1,"row","row-cols-1","row-cols-md-2"],[4,"ngFor","ngForOf"],[1,"d-flex","justify-content-end","px-3"],["cc-stroked-button","","color","accent",3,"disabled","click"],[3,"title","controlName","doc","parentFormGroup"]],template:function(e,s){1&e&&(t.TgZ(0,"h1",0),t._uU(1),t.ALo(2,"async"),t.qZA(),t.TgZ(3,"form",1)(4,"div",2),t.YNc(5,gt,2,4,"ng-container",3),t.qZA(),t.TgZ(6,"div",4)(7,"button",5),t.NdJ("click",function(){return s.save()}),t._uU(8," Save "),t.qZA()()()),2&e&&(t.xp6(1),t.Oqu(t.lcZ(2,4,s.housemaidName$)),t.xp6(2),t.Q6J("formGroup",s.statusForm),t.xp6(2),t.Q6J("ngForOf",s.docsNames),t.xp6(2),t.Q6J("disabled",!s.statusForm.valid))},directives:[c._Y,c.JL,c.sg,m.sg,ht,v.uu],pipes:[m.Ov],styles:[""]}),a})(),data:{pageCode:"HousemaidsAuditDocs"}}];let _t=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=t.oAB({type:a}),a.\u0275inj=t.cJS({imports:[[p.Bz.forChild(ft)],p.Bz]}),a})();var M=r(26991);let vt=(()=>{class a{constructor(e,s){this._actions=e,this._docsService=s}}return a.\u0275fac=function(e){return new(e||a)(t.LFG(M.eX),t.LFG(S))},a.\u0275prov=t.Yz7({token:a,factory:a.\u0275fac}),a})(),jt=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=t.oAB({type:a}),a.\u0275inj=t.cJS({imports:[[m.ez,h.Aw.forFeature(J,B),M.sQ.forFeature([vt])]]}),a})(),bt=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=t.oAB({type:a}),a.\u0275inj=t.cJS({imports:[[m.ez,_t,v.S6,y.lK,x.f,Z.Gz,c.UX,jt,g.gZ,g.n_.forFeature(),L.L,I.Bp,u.I8,_.Bw]]}),a})()},46700:(k,N,r)=>{var m={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function p(c){var g=t(c);return r(g)}function t(c){if(!r.o(m,c)){var g=new Error("Cannot find module '"+c+"'");throw g.code="MODULE_NOT_FOUND",g}return m[c]}p.keys=function(){return Object.keys(m)},p.resolve=t,k.exports=p,p.id=46700}}]);
(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_initialize-visa-processing_initialize-visa-processing_module_ts-node_modules_-4abf80"],{38512:(P,z,i)=>{"use strict";i.r(z),i.d(z,{InitializeVisaProcessingsModule:()=>cs});var d=i(69808),c=i(65620);const u=(0,c.PH)("[InitializeVisaProcessing List | Store Service] fetch housemaids",(0,c.Ky)()),j=(0,c.PH)("[InitializeVisaProcessings | Effect ] fetch housemaids success",(0,c.Ky)()),S=(0,c.PH)("[InitializeVisaProcessing List | Store Service] create housemaid request",(0,c.Ky)()),C=(0,c.PH)("[InitializeVisaProcessing Create Request | Store Service] create new request"),x="medical",J=(0,c.Lq)({maidsList:{content:[],number:0,size:20,totalElements:0,totalPages:0}},(0,c.on)(j,(e,{payload:n})=>{var s;return Object.assign(Object.assign({},e),{maidsList:{content:null!==(s=n.content)&&void 0!==s?s:[],number:n.number,size:n.size,totalElements:n.totalElements,totalPages:n.totalPages}})}));var m=i(26991),b=i(63900),g=i(54004),N=i(11365),t=i(5e3);const V=(0,c.ZF)(x),Z=((0,c.P1)(V,e=>e),(0,c.P1)(V,e=>e.maidsList));var v=i(40520),E=i(8188),M=i(43604);let F=(()=>{class e{constructor(s,a){this._api=s,this._http=a}fetchHousemaidsList(s){var a,o,l;const r=new v.LE({fromObject:{page:null!==(a=s.page)&&void 0!==a?a:0,size:null!==(o=s.size)&&void 0!==o?o:20,search:null!==(l=s.search)&&void 0!==l?l:""}});return this._http.get([this._api,M.b.housemaidsList].join("/"),{params:r}).pipe()}createRequest(s){const a={housemaid:{id:s}};return this._http.post([this._api,M.b.createRequest].join("/"),a).pipe()}markDone(s){const o=new v.LE({fromObject:{id:s}});return this._http.get([this._api,""].join("/"),{params:o}).pipe()}addPhoneNumber(s){const a={id:s.id,phoneNumber:s.phone};new v.LE({fromObject:a});let l=[this._api,""].join("/");return this._http.post(l,a).pipe()}}return e.\u0275fac=function(s){return new(s||e)(t.LFG(E.JV),t.LFG(v.eN))},e.\u0275prov=t.Yz7({token:e,factory:e.\u0275fac,providedIn:"root"}),e})(),y=(()=>{class e{constructor(s,a){this.store=s,this._initVisaProcess=a,this.selectMaidList=this.store.select(Z).pipe((0,g.U)(o=>o))}fetchHousemaidsList(s){this.store.dispatch(u({search:s}))}createRequest(s){this.store.dispatch(S({id:s}))}}return e.\u0275fac=function(s){return new(s||e)(t.LFG(c.yh),t.LFG(F))},e.\u0275prov=t.Yz7({token:e,factory:e.\u0275fac}),e})();var H=i(21799);let T=(()=>{class e{constructor(s,a,o,l){this._actions=s,this._store=a,this._service=o,this._notificationService=l,this.fetchHousemaidsList=(0,m.GW)(()=>this._actions.pipe((0,m.l4)(u),(0,b.w)(r=>this._service.fetchHousemaidsList(r.search)),(0,g.U)(r=>j({payload:r})))),this.createRequest=(0,m.GW)(()=>this._actions.pipe((0,m.l4)(S),(0,b.w)(r=>this._service.createRequest(r.id)),(0,g.U)(r=>C()))),this.createRequestSuccess=(0,m.GW)(()=>this._actions.pipe((0,m.l4)(C),(0,N.M)(this._store.selectMaidList),(0,b.w)(([r,rs])=>this._notificationService.notifySuccess("Request Created Successfully").pipe((0,g.U)(()=>u({search:Object.assign(Object.assign({},rs),{page:0})}))))))}}return e.\u0275fac=function(s){return new(s||e)(t.LFG(m.eX),t.LFG(y),t.LFG(F),t.LFG(H.zg))},e.\u0275prov=t.Yz7({token:e,factory:e.\u0275fac}),e})(),U=(()=>{class e{}return e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[d.ez,c.Aw.forFeature(x,J),m.sQ.forFeature([T])]]}),e})();var Y=i(1402),L=i(88476),D=i(50727),B=i(18505),Q=i(95698),h=i(93075),I=i(82599),p=i(34378),O=i(45834),R=i(43687),k=i(65868),G=i(62764);function K(e,n){1&e&&(t.TgZ(0,"span"),t._uU(1,"No Result"),t.qZA())}const W=function(){return[10,20,30,40,50]};function w(e,n){if(1&e){const s=t.EpF();t.ynx(0),t.TgZ(1,"div",1)(2,"cc-accordion",2)(3,"cc-panel",3)(4,"cc-panel-title",4)(5,"cc-icon"),t._uU(6,"filter_alt"),t.qZA(),t.TgZ(7,"span"),t._uU(8,"Filter"),t.qZA()(),t.TgZ(9,"cc-panel-body")(10,"form",5)(11,"div",6)(12,"cc-input",7)(13,"cc-icon",8),t._uU(14,"search"),t.qZA()()(),t.TgZ(15,"div")(16,"button",9),t.NdJ("click",function(){const l=t.CHM(s).ngIf;return t.oxw().filter(l.number,l.size)}),t._uU(17," Search "),t.qZA()()(),t.TgZ(18,"div",10)(19,"div",11)(20,"cc-datagrid",12),t.NdJ("page",function(o){return t.CHM(s),t.oxw().fetchMaidsListNextPage(o)}),t.qZA(),t.YNc(21,K,2,0,"ng-template",null,13,t.W1O),t.qZA()()()()()(),t.BQk()}if(2&e){const s=n.ngIf,a=t.MAs(22),o=t.oxw();t.xp6(10),t.Q6J("formGroup",o.form),t.xp6(10),t.Q6J("noResultTemplate",a)("data",s.content)("columns",o.columns)("length",s.totalElements)("pageOnFront",!1)("pageIndex",s.number)("pageSize",s.size)("pageSizeOptions",t.DdM(15,W))("showColumnMenuButton",!0)("stickyHeader",!0)("columnMovable",!0)("columnHideable",!0)("showColumnMenuHeader",!1)("columnMenuButtonIcon","settings")}}let X=(()=>{class e{constructor(s,a,o,l){this._store=s,this.formBuilder=a,this._dialog=o,this.mediaService=l,this.form=this.formBuilder.group({search:""}),this.previousData=[],this.subscription=new D.w0,this.createRequestCalled=!1,this.columns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"raised",text:"Create Request",color:"primary",mode:"single",disabled:!1,callback:r=>this.createRequest(r.id)}]}},{field:"name",header:"Name"},{field:"nationality",header:"Nationality",formatter:r=>r.nationality?r.nationality.label:""},{field:"initialLocation",header:"Initial Location",formatter:r=>r.initialLocation?r.initialLocation.label:""},{field:"status",header:"Status"}]}ngOnInit(){this.vm$=this._store.selectMaidList.pipe((0,B.b)(s=>{this.createRequestCalled&&this.dataChanged(s.content)&&(this.form.controls.search.setValue(""),this.createRequestCalled=!1),this.previousData=s.content})),this._store.fetchHousemaidsList({page:0})}ngOnDestroy(){this.subscription.unsubscribe()}filter(s,a){this._store.fetchHousemaidsList({page:s,size:a,search:this.form.controls.search.value})}createRequest(s){this.createRequestCalled=!0,this._store.createRequest(s),this.subscription.add(this._store.selectMaidList.pipe((0,Q.q)(1)).subscribe(()=>{this._store.fetchHousemaidsList({page:0})}))}fetchMaidsListNextPage(s){this._store.fetchHousemaidsList({page:s.pageIndex,size:s.pageSize,search:this.form.controls.search.value})}dataChanged(s){return s.length!==this.previousData.length||s.some((a,o)=>a.id!==this.previousData[o].id)}}return e.\u0275fac=function(s){return new(s||e)(t.Y36(y),t.Y36(h.qu),t.Y36(I.uY),t.Y36(H.yJ))},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-initialize-visa-processing"]],decls:2,vars:3,consts:[[4,"ngIf"],[1,"fluid-container","m-4"],[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[1,"row","mt-3",3,"formGroup"],[1,"form-group","col-md-4"],["formControlName","search","label","Search","placeholder","Search"],[1,"input-icon-suffix"],["cc-raised-button","","color","accent",2,"margin-top","8px",3,"click"],[1,"card"],[1,"card-body"],[3,"noResultTemplate","data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","showColumnMenuButton","stickyHeader","columnMovable","columnHideable","showColumnMenuHeader","columnMenuButtonIcon","page"],["noResultTpl",""]],template:function(s,a){1&s&&(t.YNc(0,w,23,16,"ng-container",0),t.ALo(1,"async")),2&s&&t.Q6J("ngIf",t.lcZ(1,1,a.vm$))},directives:[d.O5,p.I,p.CW,p.LL,O.Q9,p.G9,h._Y,h.JL,h.sg,R.G,h.JJ,h.u,k.uu,G.Ge],pipes:[d.Ov],styles:[""],changeDetection:0}),e})();var $=i(57902),q=i(4882),_=i(26523),ss=i(69202),ts=i(92431),es=i(58015),A=i(11523),is=i(54657),as=i(467),ns=i(63372);const os=[{path:"",component:X,data:{label:"Initialize Housemaids Visa Processing",pageCode:"InitializeVisaProcessing"}}];let cs=(()=>{class e{}return e.\u0275fac=function(s){return new(s||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({providers:[y],imports:[[d.ez,U,Y.Bz.forChild(os),ss.Ev,h.UX,h.u5,L.er,q.$,_.lK,L.gZ,L.n_.forFeature({defaultPageSize:30}),is.JC,R.f,$.A,es.YV,k.S6,I.I8,O.L,ts.XD,A.D$,p.yU,G.Gz,I.I8,A.bY,ns.N,as.sJ.forChild({})]]}),e})()},46700:(P,z,i)=>{var d={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function c(u){var j=f(u);return i(j)}function f(u){if(!i.o(d,u)){var j=new Error("Cannot find module '"+u+"'");throw j.code="MODULE_NOT_FOUND",j}return d[u]}c.keys=function(){return Object.keys(d)},c.resolve=f,P.exports=c,c.id=46700}}]);
(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_vp-todo-list_vp-todo-list_module_ts-node_modules_moment_locale_sync_recursive_"],{98440:(L,x,r)=>{"use strict";r.r(x),r.d(x,{VpTodoListModule:()=>ot});var p,T,S,f=r(69808),t=r(5e3),d=r(97582),m=r(61135);const F={page:0,size:20};class u{constructor(){p.set(this,new m.X(F)),T.set(this,new m.X(null)),S.set(this,new m.X(0)),this.search$=(0,d.Q_)(this,p,"f").asObservable(),this.reload$=(0,d.Q_)(this,S,"f").asObservable(),this.sort$=(0,d.Q_)(this,T,"f").asObservable()}updateSearch(n){const e=Object.assign(Object.assign({},(0,d.Q_)(this,p,"f").value),n);(0,d.Q_)(this,p,"f").next(e)}updateSortState(n){(0,d.Q_)(this,T,"f").next(n)}resetSearch(){(0,d.Q_)(this,p,"f").next(F)}reloadData(){(0,d.Q_)(this,S,"f").next(1)}}p=new WeakMap,T=new WeakMap,S=new WeakMap,u.\u0275fac=function(n){return new(n||u)},u.\u0275prov=t.Yz7({token:u,factory:u.\u0275fac});var _=r(15439),w=r(88476),a=r(93075),A=r(21799),j=r(34378),k=r(45834),Y=r(43687),v=r(28172),O=r(4882),b=r(65868);function U(s,n){if(1&s){const e=t.EpF();t.ynx(0),t.TgZ(1,"div",6)(2,"cc-datetimepicker",16),t.NdJ("keyup.enter",function(){return t.CHM(e),t.oxw().onSearch()}),t.qZA()(),t.TgZ(3,"div",6)(4,"cc-datetimepicker",17),t.NdJ("keyup.enter",function(){return t.CHM(e),t.oxw().onSearch()}),t.qZA()(),t.BQk()}}let g=class{constructor(n,e,o){this.fb=n,this.notification=e,this.cdr=o,this.search=new t.vpe,this.reset=new t.vpe,this.hasFilterApplied=!1,this.searchFilterHasValues=!1,this.DATE_FORMAT="YYYY-MM-DD",this.DATE_TIME_FORMAT="YYYY-MM-DD hh:mm:ss",this.form=this.fb.group({housemaidName:"",lastActionDate:"",snoozed:"",snoozedFrom:"",snoozedTo:""})}ngOnInit(){this.form.valueChanges.subscribe({next:n=>{this.searchFilterHasValues=Object.values(n).some(e=>!!e),this.cdr.detectChanges(),console.log(this.searchFilterHasValues)}})}onSearch(){const{snoozed:n,snoozedFrom:e,snoozedTo:o,lastActionDate:i,housemaidName:c}=this.form.value;let l={housemaidName:c,lastActionDate:this.formatDate(i,this.DATE_FORMAT)};this.validateSnoozeDates(n,e,o)&&(l=Object.assign(Object.assign({},l),{snoozedFrom:this.formatDate(e,this.DATE_TIME_FORMAT),snoozedTo:this.formatDate(o,this.DATE_TIME_FORMAT)}),this.hasFilterApplied=!0,this.search.emit(l),this.cdr.detectChanges())}onReset(){this.searchFilterHasValues&&!this.hasFilterApplied?this.form.reset():(this.form.reset(),this.reset.emit(),this.hasFilterApplied=!1,this.cdr.detectChanges())}validateSnoozeDates(n,e,o){if(!n)return!0;const i=new Date,c=new Date(e),l=new Date(o);return c<i?(this.notification.notifyError("Snoozed From time cannot be before the current time."),!1):!(l<c&&(this.notification.notifyError("Snoozed To date and time cannot be before the Snoozed From date and time."),1))}formatDate(n,e){return n?_(n).format(e):""}};g.\u0275fac=function(n){return new(n||g)(t.Y36(a.qu),t.Y36(A.zg),t.Y36(t.sBO))},g.\u0275cmp=t.Xpm({type:g,selectors:[["search-filter"]],outputs:{search:"search",reset:"reset"},decls:29,vars:3,consts:[[1,"my-4"],["expanded","false"],[1,"d-flex","justify-content-center","align-items-center"],[2,"position","relative","left","-4px"],[1,"d-flex","flex-column","gap-2"],[1,"row","px-4",3,"formGroup"],[1,"col-md-6"],["label","Housemaid","formControlName","housemaidName",3,"keyup.enter"],["label","Last action date","formControlName","lastActionDate",3,"keyup.enter"],[1,"col-md-12"],["formControlName","snoozed"],[4,"ngIf"],[1,"d-flex","row","justify-content-center","gap-2"],["cc-raised-button","","color","accent","type","submit",1,"px-4",3,"click"],[1,"mr-1",2,"font-size","36px"],["cc-raised-button","","color","warn","id","reset-btn",1,"px-4",3,"disabled","click"],["label","From","formControlName","snoozedFrom",3,"keyup.enter"],["label","To","formControlName","snoozedTo",3,"keyup.enter"]],template:function(n,e){1&n&&(t.TgZ(0,"cc-accordion",0)(1,"cc-panel",1)(2,"cc-panel-title",2)(3,"cc-icon",3),t._uU(4,"filter_alt"),t.qZA(),t.TgZ(5,"span"),t._uU(6,"Filters"),t.qZA()(),t.TgZ(7,"cc-panel-body")(8,"div",4)(9,"form",5)(10,"div",6)(11,"cc-input",7),t.NdJ("keyup.enter",function(){return e.onSearch()}),t.qZA()(),t.TgZ(12,"div",6)(13,"cc-datepicker",8),t.NdJ("keyup.enter",function(){return e.onSearch()}),t.qZA()(),t.TgZ(14,"div",9)(15,"cc-checkbox",10),t._uU(16,"Snoozed"),t.qZA()(),t.YNc(17,U,5,0,"ng-container",11),t.qZA(),t.TgZ(18,"div",12)(19,"button",13),t.NdJ("click",function(){return e.onSearch()}),t.TgZ(20,"cc-icon",14),t._uU(21,"search"),t.qZA(),t.TgZ(22,"span"),t._uU(23,"Search"),t.qZA()(),t.TgZ(24,"button",15),t.NdJ("click",function(){return e.onReset()}),t.TgZ(25,"cc-icon",14),t._uU(26,"restart_alt"),t.qZA(),t.TgZ(27,"span"),t._uU(28,"Reset"),t.qZA()()()()()()()),2&n&&(t.xp6(9),t.Q6J("formGroup",e.form),t.xp6(8),t.Q6J("ngIf",e.form.value.snoozed),t.xp6(7),t.Q6J("disabled",!e.searchFilterHasValues&&!e.hasFilterApplied))},directives:[j.I,j.CW,j.LL,k.Q9,j.G9,a._Y,a.JL,a.sg,Y.G,a.JJ,a.u,v.AC,O.E,f.O5,v.ZC,b.uu],encapsulation:2,changeDetection:0}),g=(0,d.gn)([w.kG],g);var V=r(39841),Q=r(63900),M=r(48966),z=r(43604),I=r(40520);let Z=(()=>{class s{constructor(e){this.http=e,this.snoozeUntil=(o,i)=>this.http.post(`${z.b.proToDoSnoozeUntil}/${o}`,i),this.updatePriority=(o,i)=>this.http.post(`${z.b.updateTodoPriority}/${o}`,JSON.stringify(i)),this.unSnoozeToDo=o=>this.http.post(`${z.b.unSnoozeToDo}/${o}`,{})}getActiveToDos(e,o){return this.http.post(z.b.getActiveToDos,o?[o]:[],{params:Object.assign(Object.assign({},this.getTruthyProps(e)),{page:e.page||0,size:e.size||20})})}getTruthyProps(e){return Object.fromEntries(Object.entries(e).filter(([o,i])=>!!i))}}return s.\u0275fac=function(e){return new(e||s)(t.LFG(I.eN))},s.\u0275prov=t.Yz7({token:s,factory:s.\u0275fac}),s})();var h=r(82599),N=r(42002);let E=(()=>{class s{constructor(e,o,i,c,l,y){this.fb=e,this.apiService=o,this.stateService=i,this.notification=c,this.dialogRef=l,this.data=y,this.form=this.fb.group({snoozeUntil:"",note:""})}onSnooze(){const e={note:this.form.value.note,snoozeUntil:_(this.form.value.snoozeUntil).format("YYYY-MM-DD HH:mm:ss")};this.apiService.snoozeUntil(this.data.todoId,e).subscribe({next:()=>{this.notification.notifySuccess("Snoozed Successfully"),this.dialogRef.close(),this.stateService.reloadData()}})}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(a.qu),t.Y36(Z),t.Y36(u),t.Y36(A.zg),t.Y36(M.so),t.Y36(M.WI))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-snooze-todo"]],decls:16,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-close","","cc-dialog-close-button",""],[3,"formGroup"],[1,""],["formControlName","snoozeUntil","label","Snooze Date/Time",3,"required"],["label","To-do Note","formControlName","note",3,"required"],["cc-raised-button","","color","accent",3,"disabled","click"],["cc-flat-button","","cc-dialog-close",""]],template:function(e,o){1&e&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3,"Snooze Todo"),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"cc-dialog-content")(6,"form",4)(7,"div",5),t._UZ(8,"cc-datetimepicker",6),t.qZA(),t.TgZ(9,"div",5),t._UZ(10,"cc-textarea",7),t.qZA()()(),t.TgZ(11,"cc-dialog-actions")(12,"button",8),t.NdJ("click",function(){return o.onSnooze()}),t._uU(13,"Ok"),t.qZA(),t.TgZ(14,"button",9),t._uU(15,"Cancel"),t.qZA()()()),2&e&&(t.xp6(2),t.Q6J("align","center"),t.xp6(4),t.Q6J("formGroup",o.form),t.xp6(2),t.Q6J("required",!0),t.xp6(2),t.Q6J("required",!0),t.xp6(2),t.Q6J("disabled",!o.form.valid))},directives:[h.iK,h.Cj,h.Zb,h.zn,h.fX,h.kL,a._Y,a.JL,a.sg,v.ZC,a.JJ,a.u,a.Q7,N.Qf,h.Zu,b.uu],encapsulation:2}),s})();var C=r(92340),D=r(62764),J=r(26523);function H(s,n){if(1&s){const e=t.EpF();t.TgZ(0,"button",8),t.NdJ("click",function(){t.CHM(e);const i=t.oxw().$implicit;return t.oxw().OpenSnoozeTodoDialog(i.id)}),t.TgZ(1,"cc-icon",9),t._uU(2,"timer"),t.qZA(),t.TgZ(3,"span"),t._uU(4,"Snooze"),t.qZA()()}}function P(s,n){if(1&s){const e=t.EpF();t.TgZ(0,"button",10),t.NdJ("click",function(){t.CHM(e);const i=t.oxw().$implicit;return t.oxw().unSnooze(i.id)}),t.TgZ(1,"cc-icon",9),t._uU(2,"timer_off"),t.qZA(),t.TgZ(3,"span"),t._uU(4,"Unsnooze"),t.qZA()()}}function $(s,n){if(1&s&&(t.YNc(0,H,5,0,"button",6),t.YNc(1,P,5,0,"button",7)),2&s){const e=n.$implicit;t.Q6J("ngIf",!e.snoozed),t.xp6(1),t.Q6J("ngIf",e.snoozed)}}function G(s,n){if(1&s&&(t.TgZ(0,"a",11),t._uU(1),t.qZA()),2&s){const e=n.$implicit,o=t.oxw();t.Q6J("href",o.getTodoDetailsLink(e.id),t.LSH),t.xp6(1),t.Oqu(e.title)}}function R(s,n){if(1&s&&(t.TgZ(0,"a",11),t._uU(1),t.qZA()),2&s){const e=n.$implicit,o=t.oxw();t.Q6J("href",o.getHousemaidLink(e.housemaid.id),t.LSH),t.xp6(1),t.Oqu(e.housemaid.label)}}function W(s,n){if(1&s){const e=t.EpF();t.TgZ(0,"form",null,12)(2,"cc-select",13),t.NdJ("ngModelChange",function(i){return t.CHM(e).$implicit.priority=i})("userSelect",function(i){const l=t.CHM(e).$implicit;return t.oxw().updatePriority(i,l.id)}),t.qZA()()}if(2&s){const e=n.$implicit,o=n.index,i=t.oxw();t.xp6(2),t.s9C("name","priority"+o),t.Q6J("data",i.priorityOpts)("ngModel",e.priority)}}const q=function(s,n,e,o){return{operation:s,priority:n,title:e,"housemaid.label":o}},B=[{field:"operation",header:"Actions"},{field:"title",header:"Todo",width:"20ch"},{field:"housemaid.label",header:"Maid"},{field:"creationDate",header:"Added Since",formatter:s=>_(s.creationDate).toNow(!0),sortable:!0,sortProp:{arrowPosition:"after",id:"creationDate"}},{field:"dueOn",header:"Due On",formatter:s=>_(s.dueOn).format("YYYY-MM-DD"),sortable:!0,sortProp:{arrowPosition:"after",id:"dueOn"}},{field:"lastActionDate",header:"Last action date",formatter:s=>_(s.lastActionDate).format("YYYY-MM-DD")},{field:"priority",header:"Priority",sortable:!0,sortProp:{arrowPosition:"after",id:"priority"}},{field:"lastNote",header:"Last Note"}];let X=(()=>{class s{constructor(e,o,i,c,l){this.apiService=e,this.stateService=o,this.notifications=i,this.cdr=c,this.dialog=l,this.gridCols=B,this.vm={content:[],number:0,size:0,totalElements:0,totalPages:0},this.priorityOpts=[{id:"",text:"None"},{id:"HIGH",text:"High"},{id:"MEDIUM",text:"Medium"},{id:"LOW",text:"Low"}]}ngOnInit(){(0,V.a)([this.stateService.reload$,this.stateService.search$,this.stateService.sort$]).pipe((0,Q.w)(([e,o,i])=>this.apiService.getActiveToDos(o,i))).subscribe({next:e=>{this.vm=e,this.cdr.detectChanges()}})}updatePriority(e,o){this.apiService.updatePriority(o,e).subscribe({next:()=>{this.stateService.reloadData(),this.notifications.notifySuccess("Priority Updated Successfully.")}})}handleNextPage(e){this.stateService.updateSearch({page:e.pageIndex,size:e.pageSize})}unSnooze(e){return this.apiService.unSnoozeToDo(e).subscribe({next:()=>{this.notifications.notifySuccess("Unsnoozed Successfully."),this.stateService.reloadData()}})}OpenSnoozeTodoDialog(e){this.dialog.originalOpen(E,{panelClass:["col-md-6"],data:{todoId:e}})}onSort(e){this.stateService.updateSortState({property:e.active,direction:e.direction})}getHousemaidLink(e){return C.N.production&&!C.N.newErp?"main.html#!/housemaid/details/"+e:"housemaid/details/"+e}getTodoDetailsLink(e){return C.N.production&&!C.N.newErp?"main.html#!/visa/v2/pro/todo-details/"+e:"visa/v2/pro/todo-details/"+e}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(Z),t.Y36(u),t.Y36(A.zg),t.Y36(t.sBO),t.Y36(h.uY))},s.\u0275cmp=t.Xpm({type:s,selectors:[["vp-todo-list"]],decls:9,vars:15,consts:[[3,"columns","data","pageIndex","pageSize","length","cellTemplate","page","sortChange"],[3,"ccGridCell"],["actions",""],["title",""],["maid",""],["priority",""],["cc-raised-button","","color","accent",3,"click",4,"ngIf"],["cc-raised-button","","color","warn",3,"click",4,"ngIf"],["cc-raised-button","","color","accent",3,"click"],[1,"mr-1",2,"font-size","36px"],["cc-raised-button","","color","warn",3,"click"],["target","_blank",3,"href"],["form","ngForm"],[3,"data","ngModel","name","ngModelChange","userSelect"]],template:function(e,o){if(1&e&&(t.TgZ(0,"cc-datagrid",0),t.NdJ("page",function(c){return o.handleNextPage(c)})("sortChange",function(c){return o.onSort(c)}),t.qZA(),t.YNc(1,$,2,2,"ng-template",1,2,t.W1O),t.YNc(3,G,2,2,"ng-template",1,3,t.W1O),t.YNc(5,R,2,2,"ng-template",1,4,t.W1O),t.YNc(7,W,3,3,"ng-template",1,5,t.W1O)),2&e){const i=t.MAs(2),c=t.MAs(4),l=t.MAs(6),y=t.MAs(8);t.Q6J("columns",o.gridCols)("data",o.vm.content)("pageIndex",o.vm.number)("pageSize",o.vm.size)("length",o.vm.totalElements)("cellTemplate",t.l5B(10,q,i,y,c,l)),t.xp6(1),t.Q6J("ccGridCell",o.vm.content),t.xp6(2),t.Q6J("ccGridCell",o.vm.content),t.xp6(2),t.Q6J("ccGridCell",o.vm.content),t.xp6(2),t.Q6J("ccGridCell",o.vm.content)}},directives:[D.Ge,D.VC,f.O5,b.uu,k.Q9,a._Y,a.JL,a.F,J.jB,a.JJ,a.On],encapsulation:2,changeDetection:0}),s})(),K=(()=>{class s{constructor(e){this.state=e}onSearch(e){this.state.updateSearch(e)}onReset(){this.state.resetSearch()}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(u))},s.\u0275cmp=t.Xpm({type:s,selectors:[["ng-component"]],decls:3,vars:0,consts:[[1,"px-4"],[3,"search","reset"]],template:function(e,o){1&e&&(t.TgZ(0,"div",0)(1,"search-filter",1),t.NdJ("search",function(c){return o.onSearch(c)})("reset",function(){return o.onReset()}),t.qZA(),t._UZ(2,"vp-todo-list"),t.qZA())},directives:[g,X],encapsulation:2}),s})();var tt=r(1402);const et=[{path:"",component:K}];let ot=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({providers:[Z,u],imports:[[f.ez,a.UX,a.u5,tt.Bz.forChild(et),D.Gz,j.yU,h.I8,Y.f,J.lK,v.Bp,v.To,k.L,b.S6,O.$,N.l2]]}),s})()},46700:(L,x,r)=>{var f={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function t(m){var p=d(m);return r(p)}function d(m){if(!r.o(f,m)){var p=new Error("Cannot find module '"+m+"'");throw p.code="MODULE_NOT_FOUND",p}return f[m]}t.keys=function(){return Object.keys(f)},t.resolve=d,L.exports=t,t.id=46700}}]);
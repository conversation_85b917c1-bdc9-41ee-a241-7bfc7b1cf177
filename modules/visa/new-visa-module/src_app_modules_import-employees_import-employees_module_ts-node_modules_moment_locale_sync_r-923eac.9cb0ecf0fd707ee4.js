(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_import-employees_import-employees_module_ts-node_modules_moment_locale_sync_r-923eac"],{74900:(_,h,o)=>{"use strict";o.r(h),o.d(h,{ImportEmployeesModule:()=>ne});var a=o(69808),c=o(65620);const u=(0,c.Lq)({proccesingAccess:{permissions:[]}});var T=o(26991),e=o(5e3),y=o(88476),F=o(8188),p=o(43604),N=o(40520);let g=(()=>{class t{constructor(s,r){this._api=s,this._http=r}getSpentQuota(){return this._http.get(p.b.spentQuotas,{}).pipe()}getFilterdListPerType(s){let r=p.b.filterListPerType(s);return this._http.get(r,{}).pipe()}getFilterdListPerStatus(s){let r=p.b.filterListPerStatus(s);return this._http.get(r,{}).pipe()}exportFiltered(s){let r=p.b.exportExcelFiltered(s);return this._http.get(r,{}).pipe()}exportType(s){let r=p.b.exportExcelPerType(s);return this._http.get(r,{}).pipe()}}return t.\u0275fac=function(s){return new(s||t)(e.LFG(F.JV),e.LFG(N.eN))},t.\u0275prov=e.Yz7({token:t,factory:t.\u0275fac,providedIn:"root"}),t})(),v=(()=>{class t extends y.il{constructor(s,r){super(s),this._initVisaProcess=r}pickListsCodes(){return["nationalities"]}resetState(){}}return t.\u0275fac=function(s){return new(s||t)(e.LFG(c.yh),e.LFG(g))},t.\u0275prov=e.Yz7({token:t,factory:t.\u0275fac}),t})();var b=o(21799);let L=(()=>{class t{constructor(s,r,i,d){this._actions=s,this._store=r,this._service=i,this._notificationService=d}}return t.\u0275fac=function(s){return new(s||t)(e.LFG(T.eX),e.LFG(v),e.LFG(g),e.LFG(b.zg))},t.\u0275prov=e.Yz7({token:t,factory:t.\u0275fac}),t})(),z=(()=>{class t{}return t.\u0275fac=function(s){return new(s||t)},t.\u0275mod=e.oAB({type:t}),t.\u0275inj=e.cJS({imports:[[a.ez,c.Aw.forFeature("manage-access",u),T.sQ.forFeature([L])]]}),t})();var A=o(1402),l=o(93075),E=o(467),C=o(65868),f=o(69202),x=o(62764);function J(t,n){if(1&t&&(e.TgZ(0,"span"),e._uU(1),e.qZA()),2&t){const s=n.ngIf;e.xp6(1),e.Oqu(s)}}function S(t,n){1&t&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}function k(t,n){if(1&t&&(e.TgZ(0,"cc-card",13)(1,"cc-card-header")(2,"h5"),e._uU(3,"Work Permits about to expire"),e.qZA()(),e.TgZ(4,"cc-card-content"),e._UZ(5,"cc-datagrid",14),e.YNc(6,S,2,0,"ng-template",null,15,e.W1O),e.qZA()()),2&t){const s=e.MAs(7),r=e.oxw().ngIf,i=e.oxw();e.xp6(5),e.Q6J("noResultTemplate",s)("data",r.workPermit)("columns",i.workPermitColumns)("length",r.workPermit.length)("pageOnFront",!0)}}function P(t,n){1&t&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}function U(t,n){if(1&t&&(e.TgZ(0,"cc-card",13)(1,"cc-card-header")(2,"h5"),e._uU(3,"New Labour Card about to expire"),e.qZA()(),e.TgZ(4,"cc-card-content"),e._UZ(5,"cc-datagrid",14),e.YNc(6,P,2,0,"ng-template",null,15,e.W1O),e.qZA()()),2&t){const s=e.MAs(7),r=e.oxw().ngIf,i=e.oxw();e.xp6(5),e.Q6J("noResultTemplate",s)("data",r.labourCard)("columns",i.labourCardColumns)("length",r.labourCard.length)("pageOnFront",!0)}}function Q(t,n){if(1&t&&(e.TgZ(0,"div",1)(1,"a",16),e._uU(2,"Export"),e.qZA()()),2&t){const s=e.oxw(2);e.xp6(1),e.Q6J("href",s.exportToExcelByType(),e.LSH)}}function Y(t,n){if(1&t&&(e.TgZ(0,"div",1),e.YNc(1,k,8,5,"cc-card",12),e.YNc(2,U,8,5,"cc-card",12),e.YNc(3,Q,3,1,"div",11),e.qZA()),2&t){const s=e.oxw();e.xp6(1),e.Q6J("ngIf",!s.filter),e.xp6(1),e.Q6J("ngIf",!s.filter),e.xp6(1),e.Q6J("ngIf",!s.filter)}}function w(t,n){1&t&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}function O(t,n){1&t&&e._uU(0),2&t&&e.hij(" ",n.index+1," ")}const G=function(t){return{id:t}};function B(t,n){if(1&t&&(e.TgZ(0,"cc-card",13)(1,"cc-card-header")(2,"h5"),e._uU(3,"Filtered List"),e.qZA()(),e.TgZ(4,"cc-card-content"),e._UZ(5,"cc-datagrid",17),e.YNc(6,w,2,0,"ng-template",null,15,e.W1O),e.YNc(8,O,1,1,"ng-template",18,19,e.W1O),e.qZA(),e.TgZ(10,"cc-card-footer")(11,"a",20),e._uU(12," Export To Excel "),e.qZA()()()),2&t){const s=e.MAs(7),r=e.MAs(9),i=e.oxw().ngIf,d=e.oxw();e.xp6(5),e.Q6J("noResultTemplate",s)("data",i)("columns",d.filterdList)("length",i.length)("pageOnFront",!0)("cellTemplate",e.VKq(8,G,r)),e.xp6(3),e.Q6J("ccGridCell",d.filterdList),e.xp6(3),e.Q6J("href",d.exportToExcel(),e.LSH)}}function M(t,n){if(1&t&&(e.TgZ(0,"div",1),e.YNc(1,B,13,10,"cc-card",12),e.qZA()),2&t){const s=e.oxw();e.xp6(1),e.Q6J("ngIf",s.filter)}}const $=function(){return{acceptedFiles:".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"}};let D=(()=>{class t{constructor(s,r,i,d){this._store=s,this.formBuilder=r,this.mediaService=i,this._service=d,this.avatar=[],this.form=this.formBuilder.group({employeeFile:new l.NI(this.avatar)}),this.filter=!1,this.labourCardColumns=[{field:"personCode",header:"Person Code"},{field:"cardNo",header:"Card No"},{field:"personName",header:"Person Name"},{field:"nationality",header:"Nationality",type:"tag"},{field:"jobName",header:"Job Name"},{field:"cardExpiry",header:"Card Expiry"}],this.workPermitColumns=[{field:"personCode",header:"Person Code"},{field:"cardNo",header:"Card No"},{field:"personName",header:"Person Name"},{field:"nationality",header:"Nationality",type:"tag"},{field:"jobName",header:"Job Name"},{field:"cardExpiry",header:"Card Expiry"}],this.filterdList=[{field:"id",header:"#"},{field:"personCode",header:"Person Code"},{field:"cardType",header:"Card Type"},{field:"cardExpiry",header:"Card Expiry"},{field:"vpStatus",header:"VP Status"}]}ngOnInit(){this.quota$=this._service.getSpentQuota()}filterdListPerType(){this.listByType$=this._service.getFilterdListPerType(this.form.controls.employeeFile.value[0].id),this.filter=!1}filterdListPerStatus(){this.listByStatus$=this._service.getFilterdListPerStatus(this.form.controls.employeeFile.value[0].id),this.filter=!0}exportToExcel(){return p.b.exportExcelFiltered(this.form.controls.employeeFile.value[0].id)}exportToExcelByType(){return p.b.exportExcelPerType(this.form.controls.employeeFile.value[0].id)}export(){this.mediaService.downloadFile(p.b.exportExcel(this.form.controls.employeeFile.value[0].id),void 0,{reportProgress:!0})}}return t.\u0275fac=function(s){return new(s||t)(e.Y36(v),e.Y36(l.qu),e.Y36(b.yJ),e.Y36(g))},t.\u0275cmp=e.Xpm({type:t,selectors:[["app-Import-employees"]],decls:23,vars:14,consts:[[1,"fluid-container","m-4"],[1,"row"],[4,"ngIf"],[1,"row",3,"formGroup"],[1,"form-group","col-md-8","row"],[1,"col-md-1"],["for","empName",1,"col-md-2","pt-4"],[1,"col-md-8","form-group"],["label","Upload Employees List","required","true","formControlName","employeeFile","name","employeeFile","required","",3,"dropzoneConfig"],[1,"col-md-3","form-group"],["cc-raised-button","",1,"m-3",2,"width","300px",3,"disabled","click"],["class","row",4,"ngIf"],["class","col-md-12 mt-5",4,"ngIf"],[1,"col-md-12","mt-5"],[3,"noResultTemplate","data","columns","length","pageOnFront"],["noResultTpl",""],["cc-raised-button","","target","_blank",2,"margin-left","3vw","margin-top","2vw","margin-bottom","2vw",3,"href"],[3,"noResultTemplate","data","columns","length","pageOnFront","cellTemplate"],[3,"ccGridCell"],["indexTpl",""],["cc-raised-button","","target","_blank",3,"href"]],template:function(s,r){1&s&&(e.ynx(0),e.TgZ(1,"div",0)(2,"div",1)(3,"h5"),e._uU(4," Spent Quotas: "),e.YNc(5,J,2,1,"span",2),e.ALo(6,"async"),e.qZA()(),e.TgZ(7,"form",3)(8,"div",4),e._UZ(9,"div",5),e.TgZ(10,"label",6),e._uU(11,"Employee List:"),e.qZA(),e.TgZ(12,"div",7),e._UZ(13,"cc-file-uploader",8),e.qZA()(),e.TgZ(14,"div",9)(15,"button",10),e.NdJ("click",function(){return r.filterdListPerType()}),e._uU(16," Generate Filtered List Per Type "),e.qZA(),e.TgZ(17,"button",10),e.NdJ("click",function(){return r.filterdListPerStatus()}),e._uU(18," Generate Filtered List Per Status "),e.qZA()()(),e.YNc(19,Y,4,3,"div",11),e.ALo(20,"async"),e.YNc(21,M,2,1,"div",11),e.ALo(22,"async"),e.qZA(),e.BQk()),2&s&&(e.xp6(5),e.Q6J("ngIf",e.lcZ(6,7,r.quota$)),e.xp6(2),e.Q6J("formGroup",r.form),e.xp6(6),e.Q6J("dropzoneConfig",e.DdM(13,$)),e.xp6(2),e.Q6J("disabled",!r.form.valid),e.xp6(2),e.Q6J("disabled",!r.form.valid),e.xp6(2),e.Q6J("ngIf",e.lcZ(20,9,r.listByType$)),e.xp6(2),e.Q6J("ngIf",e.lcZ(22,11,r.listByStatus$)))},directives:[a.O5,l._Y,l.JL,l.sg,E.U2,l.Q7,l.JJ,l.u,C.uu,f.Dt,f.oJ,f.uw,x.Ge,x.VC,f.uC],pipes:[a.Ov],styles:[""],changeDetection:0}),t})();var V=o(57902),W=o(4882),X=o(26523),K=o(43687),H=o(92431),q=o(58015),I=o(82599),Z=o(11523),ee=o(45834),te=o(34378),se=o(54657),oe=o(63372);const re=[{path:"",component:D}];let ne=(()=>{class t{}return t.\u0275fac=function(s){return new(s||t)},t.\u0275mod=e.oAB({type:t}),t.\u0275inj=e.cJS({providers:[v],imports:[[a.ez,z,A.Bz.forChild(re),f.Ev,l.UX,l.u5,y.er,W.$,X.lK,y.gZ,y.n_.forFeature({defaultPageSize:30}),se.JC,K.f,V.A,q.YV,C.S6,I.I8,ee.L,H.XD,Z.D$,te.yU,x.Gz,I.I8,Z.bY,oe.N,E.sJ.forChild({})]]}),t})()},46700:(_,h,o)=>{var a={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function c(m){var u=j(m);return o(u)}function j(m){if(!o.o(a,m)){var u=new Error("Cannot find module '"+m+"'");throw u.code="MODULE_NOT_FOUND",u}return a[m]}c.keys=function(){return Object.keys(a)},c.resolve=j,_.exports=c,c.id=46700}}]);
"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["vendors-node_modules_maids_cc-shared-components_fesm2015_maids-cc-shared-components-workflow--830d14"],{463:(V,k,d)=>{d.d(k,{aM:()=>Y,ZN:()=>X});var n=d(5e3),_=d(69808),F=d(50072),D=d(40520),A=d(77579),x=d(4128),O=d(80188),R=d(54482),j=d(25403),z=d(38737),T=d(24351),P=d(39646),H=d(70207),L=d(82722),w=d(54004),f=d(70262),u=d(28746),c=d(88087),m=d(65868),v=d(26523),S=d(43687),M=d(45834),U=d(62764),y=d(93075);function G(l,g){if(1&l){const e=n.EpF();n.TgZ(0,"cc-advanced-search",5,6),n.NdJ("ngModelChange",function(i){return n.CHM(e),n.oxw(2).advancedSearchValue=i})("onFilter",function(){n.CHM(e);const i=n.MAs(1);return n.oxw(2).fetchData(i.value)}),n.qZA()}if(2&l){const e=n.oxw(2);n.Q6J("metaApi",e.advancedSearchConfig.metaApiUrl)("ngModel",e.advancedSearchValue)}}function J(l,g){if(1&l&&(n.TgZ(0,"div",2),n.YNc(1,G,2,2,"cc-advanced-search",4),n.qZA()),2&l){const e=n.oxw();n.xp6(1),n.Q6J("ngIf",e.advancedSearchConfig&&e.advancedSearchConfig.metaApiUrl)}}function Z(l,g){if(1&l){const e=n.EpF();n.TgZ(0,"div",10)(1,"cc-select",11),n.NdJ("ngModelChange",function(i){return n.CHM(e),n.oxw(2).filteredProperty=i}),n.qZA(),n.TgZ(2,"cc-input",12),n.NdJ("ngModelChange",function(i){return n.CHM(e),n.oxw(2).filteredValue=i}),n.qZA(),n.TgZ(3,"button",13),n.NdJ("click",function(){return n.CHM(e),n.oxw(2).filterData()}),n.TgZ(4,"cc-icon"),n._uU(5,"search"),n.qZA()(),n.TgZ(6,"button",13),n.NdJ("click",function(){return n.CHM(e),n.oxw(2).clearData()}),n.TgZ(7,"cc-icon"),n._uU(8,"close"),n.qZA()()()}if(2&l){const e=n.oxw(2);n.xp6(1),n.Q6J("ngModel",e.filteredProperty)("label","Select Column")("data",e.filterSelectData),n.xp6(1),n.Q6J("label","Search")("ngModel",e.filteredValue),n.xp6(1),n.Q6J("color","accent"),n.xp6(3),n.Q6J("color","accent")}}function W(l,g){if(1&l){const e=n.EpF();n.TgZ(0,"div",7)(1,"div")(2,"button",8),n.NdJ("click",function(){return n.CHM(e),n.oxw().toggleFilter()}),n._uU(3," Show Test Filters "),n.qZA()(),n.YNc(4,Z,9,7,"div",9),n.qZA()}if(2&l){const e=n.oxw();n.xp6(2),n.Q6J("color","accent"),n.xp6(2),n.Q6J("ngIf",e.shownFilters)}}const B=function(){return[]};function Q(l,g){if(1&l&&n._UZ(0,"cc-datagrid",14),2&l){const e=n.oxw();n.Q6J("loading",!(null!=e.processes.dataGrid&&e.processes.dataGrid.loaded))("rowStyleFormatter",e.rowStyleFormatter)("rowClassFormatter",e.rowClassFormatter)("columns",null==e.processes.dataGrid?null:e.processes.dataGrid.columns)("data",e.processes.allData||n.DdM(9,B))("pageOnFront",!0)("pageSize",30)("hidePageSize",!1)("cellTemplate",e.cellTemplate)}}const N={PUBLIC:"public",ADMIN:"admin",RECRUITMENT:"recruitment",TICKETING:"ticketing",IN_EXIT:"inexit",VISA:"visa",SALES:"sales",COMPLAINTS:"complaints",CLIENTMGMT:"clientmgmt",STAFFMGMT:"staffmgmt",ACCOUNTING:"accounting",ACCOUNTING_OP:"accountingop",REPORTING:"reporting",FREEDOMOP:"freedomop",YAYABOT:"yayabot",PAYROLL:"payroll",CHATAI:"chatai",CHATCC:"chatcc"};let $=(()=>{class l{constructor(e){this._http=e}lazyLoadedTasks(e,t,i){return this._http.get(`${e}/workflowTasks/lazyLoad`,{params:t,headers:{searchFilter:i?JSON.stringify(i):""}})}dataList(e,t){return this._http.get(e,{headers:{searchFilter:t?JSON.stringify(t):""},params:{page:0}})}makeRequest(e,t,i){return this._http.request(e,t,i)}}return l.\u0275fac=function(e){return new(e||l)(n.LFG(D.eN))},l.\u0275prov=n.Yz7({token:l,factory:l.\u0275fac,providedIn:"root"}),l})(),Y=(()=>{class l{constructor(e,t){this._cdr=e,this._service=t,this.additionalSearchFilters={employeeName:null,employeeType:null,toDoStatus:null,userId:null},this.shownFilters=!1,this.filterSelectData=[],this.filteredValue="",this.filteredProperty="",this.hideSearch=!1,this.rowStyleFormatter={background:(i,s)=>{let a=i.color;return{condition:""!=a&&null!=a,value:a}},color:(i,s)=>({condition:!0,value:this.getRowDataColor(i.color||"#ffffff")})},this.rowClassFormatter={blinking:(i,s)=>i.urgent},this.destroy$=new A.x,this.onRowAction=new n.vpe,this.onSearchAction=new n.vpe,this.baseAPI="https://devbackerp.teljoy.io:6443",this.isProduction=!1,this.isVPAdmin=!1,this.autoRefresh=!1,this.metaApiUrl="",this.modules=[],this.rowActions={},this.sortOpt={direction:"asc",column:"creationDate"},this.actionMode="multiple"}toggleFilter(){this.shownFilters=!this.shownFilters}fetchData(e){console.log("event: ",e),this._service.dataList(this.advancedSearchConfig.listUrl,e||{}).subscribe(t=>{this.filterProcessesAndTasks(t,this.modules[0]),this.processes=[];const i=this.modules.map(s=>this.getPermittedProcessesAndTasks(s));this.getPageData(i)})}ngOnInit(){this.getUserTasksByModule(this.modules),this.refreshSubject&&this.refreshSubject.pipe((0,L.R)(this.destroy$)).subscribe(e=>{e&&(this.additionalSearchFilters=Object.assign(Object.assign({},this.additionalSearchFilters),e)),this.fetchData(this.advancedSearchValue)})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete(),this.lastTimeoutAutoRefresh&&clearTimeout(this.lastTimeoutAutoRefresh)}setAdvancedSearchConfigValue(e){const t=e.map(s=>s.module),i=e.map(s=>`modules=${s.module}`).join("&");this.advancedSearchConfig={metaApiUrl:this.metaApiUrl?`${this.metaApiUrl}`:`visa/workflowTasks/getAllSearchbleFields?${i}`,listUrl:`${this.baseAPI}/${N[t[0]]}/workflowTasks/lazyLoad`,searchFilterObject:this.advancedSearchValue}}getPermittedProcessesAndTasks(e){const t=(new D.LE).set("adminMode",this.isVPAdmin?"true":"false").set("withContentFlag","false").set("userId",this.getUserId());return e.processName&&t.set("processName",e.processName||""),console.log("this.advancedSearchValue: ",this.advancedSearchValue),this._service.lazyLoadedTasks(`${this.baseAPI}/${N[e.module]}`,t,this.advancedSearchValue).pipe((0,w.U)(i=>(this.filterProcessesAndTasks(i,e),i)))}getUserId(){var e;return this.isVPAdmin&&(null===(e=this.additionalSearchFilters)||void 0===e?void 0:e.userId)?this.additionalSearchFilters.userId:""}filterProcessesAndTasks(e,t){t.processTaskFilter&&this.filterConfiguredProcesses(e,t.processTaskFilter),Object.entries(e).forEach(([i,s])=>{t.processTaskFilter&&this.filterConfiguredTasks(e,i,t.processTaskFilter[i]),s.module=t.module})}filterConfiguredProcesses(e,t){Object.keys(e).forEach(i=>{Object.keys(t).includes(i)||delete e[i]})}filterConfiguredTasks(e,t,i){var s;const a=null===(s=e[t])||void 0===s?void 0:s.tasks;!a||Object.keys(a).forEach(o=>{i&&i.length>0&&!i.includes(o)&&delete a[o]})}getFilterData(e){let t={};return Object.keys(e).forEach(s=>{Object.assign(t,e[s].tableHeader)}),Object.entries(t).map(([s,a])=>({id:s,text:a})).concat([{id:"processLabel",text:"Process"},{id:"taskLabel",text:"Task"}])}getPageData(e){(0,x.D)(e).subscribe(t=>{t.forEach(s=>{Object.assign(this.processes,s)}),this.processes.dataGrid=this.prepareMainGrid(this.processes),this.processes.dataGrid.loaded=!1;const i=this.getFilterData(this.processes);this.filterSelectData=i,this.loadAllData()})}loadAllData(e=!1){var t,i,s;let a=[];const o=(null===(t=this.processes)||void 0===t?void 0:t.allData)?[...this.processes.allData]:[],r=(null===(s=null===(i=this.processes)||void 0===i?void 0:i.dataGrid)||void 0===s?void 0:s.data)?[...this.processes.dataGrid.data]:[];this.processes&&(e||delete this.processes.allData,Object.keys(this.processes).forEach(h=>{var p;const b=null===(p=this.processes[h])||void 0===p?void 0:p.tasks;b&&Object.keys(b).forEach(C=>{a.push(this.loadTaskData(e,this.processes,h,C,this.isVPAdmin?{}:{searchFilter:this.advancedSearchValue?JSON.stringify(this.advancedSearchValue):""},this.additionalSearchFilters))})})),(0,O.D)(a).pipe(function E(l,g=null){return g=null!=g?g:l,(0,R.e)((e,t)=>{let i=[],s=0;e.subscribe((0,j.x)(t,a=>{let o=null;s++%g==0&&i.push([]);for(const r of i)r.push(a),l<=r.length&&(o=null!=o?o:[],o.push(r));if(o)for(const r of o)(0,z.P)(i,r),t.next(r)},()=>{for(const a of i)t.next(a);t.complete()},void 0,()=>{i=null}))})}(3),(0,T.b)(h=>{const p=h.map(b=>(0,O.D)(b).pipe((0,f.K)(()=>(0,P.of)(null))));return(0,x.D)(p)}),(0,H.u)((h,p)=>[...h,...p.filter(Boolean)],[]),(0,u.x)(()=>this.processes.dataGrid.loaded=!0),(0,f.K)(h=>(e&&o.length>0&&(this.processes.allData=o,this.processes.dataGrid.data=r,this.untouchedAllData=o,this.allRefreshedData=[]),(0,P.of)([])))).subscribe(h=>{if(e)if(this.allRefreshedData&&this.allRefreshedData.length>0){const b=this.getMaxCreationDate(this.allRefreshedData);this.updateCreationDates(e,b);const C=o||[],K=this.allRefreshedData.filter(I=>this.isDateAfter(I.creationDate,this.lowerBoundCreationDate)).map(I=>Object.assign(Object.assign({},I),{color:"#c00000",urgent:!0}));this.processes.allData=[...K,...C]}else this.processes.allData=o;else if(this.allRefreshedData=this.processes.allData,this.processes.allData&&this.processes.allData.length>0){const p=this.getMaxCreationDate(this.processes.allData);this.updateCreationDates(e,p)}this.setupAutoRefresh(),this.sortData(this.sortOpt.direction,this.sortOpt.column),this.untouchedAllData=this.processes.allData})}getUserTasksByModule(e){this.isVPAdmin||this.setAdvancedSearchConfigValue(e),this.processes=[];const t=e.map(i=>this.getPermittedProcessesAndTasks(i));this.getPageData(t)}getMaxCreationDate(e){return e.reduce((t,i)=>{const s=new Date(i.creationDate);return s>t?s:t},new Date(0))}isDateAfter(e,t){if(!e||!t)return!1;const i=new Date(e),s=new Date(t);return i.getTime()>s.getTime()}updateCreationDates(e,t){e?this.lastCreationDate&&this.isDateAfter(t,this.lastCreationDate)?(this.lowerBoundCreationDate=this.lastCreationDate,this.lastCreationDate=t):this.lastCreationDate||(this.lastCreationDate=t,this.lowerBoundCreationDate=t):(this.lastCreationDate=t,this.lowerBoundCreationDate=t)}setupAutoRefresh(){this.autoRefresh&&(this.lastTimeoutAutoRefresh&&clearTimeout(this.lastTimeoutAutoRefresh),this.lastTimeoutAutoRefresh=setTimeout(()=>{this.allRefreshedData=[],this.loadAllData(!0)},3e4))}prepareMainGrid(e){const t={columns:[],actions:[],data:[],actionConfig:{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:this.actionMode,icon:"menu"==this.actionMode?"drag_indicator":"",disabled:!1,buttons:[]}}};let i={tasksSummary:"Task Summary",task:"Task",processLabel:"Process"},s=[],a=[];return this.tableHeaders?i=Object.assign(Object.assign({},i),this.tableHeaders.allHeaders):(a=["processLabel","taskLabel"],Object.entries(e).forEach(([o,r])=>{i=Object.assign(Object.assign({},i),r.tableHeader),a=[...a,...Object.keys(r.tableHeader)]})),e&&Object.entries(e).forEach(([o,r])=>{r.tasks&&Object.entries(r.tasks).forEach(([h,p])=>{s.push(r.individualTableHeaders[h]),t.actionConfig.buttonConfig.buttons=[...t.actionConfig.buttonConfig.buttons,...this.getTaskRowActions(o,h)]}),t.actionConfig.buttonConfig.buttons=[...t.actionConfig.buttonConfig.buttons,...this.getTaskRowActions(o,"default",!0)]}),a=a.concat(this.tableHeaders&&this.tableHeaders.shownHeaders?this.tableHeaders.shownHeaders:this.getCommonArrayItems(s)),t.columns=[t.actionConfig,...t.columns,...this.getProcessDataGridColumnsDefObj(i,a)],console.log("dataGrid: ",t),t}getTaskRowActions(e,t,i=!1){var s;const a=t;if(!this.rowActions)return[];i&&(t="default");const o=null===(s=this.rowActions[e])||void 0===s?void 0:s[t];return o?o.map(r=>({type:r.type?r.type:"raised",text:r.label,color:r.color?r.color:"primary",mode:"single",class:r.class?r.class:"",disabled:h=>{var p;return"function"==typeof r.disabled?r.disabled(h):null!==(p=r.disabled)&&void 0!==p&&p},hidden:h=>{var p;const b=h.taskKey===a||"default"===a,C="function"==typeof r.visiblityCond?r.visiblityCond(h):null===(p=r.visiblityCond)||void 0===p||p;return!(h.processKey===e&&b&&C)},callback:h=>{this.onRowAction.emit({data:{actionCode:r.code,data:h}})}})):[]}getProcessDataGridColumnsDefObj(e,t){return Object.entries(e).map(([i,s])=>{const a=!t.includes(i);return"string"==typeof s?this.getConstructedColumnObject(s,i,a,i):this.getConstructedColumnObject(s.label,s.value,a,i)})}getConstructedColumnObject(e,t,i,s){return{field:s,header:e,formatter:o=>"function"==typeof t?t(o):t.split(".").reduce((r,h)=>{const p=null==r?void 0:r[h];return"boolean"==typeof p?p.toString():null!=p?p:""},o)||"",sortable:null!=("string"==typeof t||s?o=>o:null),hide:i}}getRowDataColor(e){return"#c00000"===e||"#0000ff"===e||"#2e66fc"===e?"white":"black"}getCommonArrayItems(e){return e.length>0?e.reduce((t,i)=>t.filter(s=>i.includes(s))):[]}loadTaskData(e=!1,t,i,s,a={},o=null){var r;const h=`${this.baseAPI}/${N[t[i].module]}/workflowTasks/all`,p=o?"POST":"GET",b={headers:new D.WM(a),params:{adminMode:this.isVPAdmin?"true":"false",taskName:s,processName:i,userId:this.isVPAdmin&&(null===(r=this.additionalSearchFilters)||void 0===r?void 0:r.userId)?this.additionalSearchFilters.userId:""},body:o?{additionalSearchFilters:this.additionalSearchFilters}:null};return this._service.makeRequest(p,h,b).pipe((0,w.U)(C=>(this.assignTasksMissingData(t,i,s,C,e),this._cdr.detectChanges(),C)))}assignTasksMissingData(e,t,i,s,a=!1){var o;const r=null===(o=s[t])||void 0===o?void 0:o.tasks[i];!r||!e[t]||(r.forEach(h=>{this.enrichTaskData(h,i,t,s[t],e[t])}),a?(this.allRefreshedData||(this.allRefreshedData=[]),this.allRefreshedData=this.allRefreshedData.concat(r)):(e.dataGrid.data||(e.dataGrid.data=[]),e.allData||(e.allData=[]),e.dataGrid.data=e.dataGrid.data.concat(r),e.allData=e.allData.concat(r)))}enrichTaskData(e,t,i,s,a){e.taskKey=t,e.module=a.module,e.processKey=i,e.tasksSummary=s.tasksSummary[t],e.taskLabel=s.tasksLabels[t],e.processLabel=s.processLabel}orderBy(e,t,i){return e.sort((s,a)=>s[t]>a[t]?i?-1:1:s[t]<a[t]?i?1:-1:0)}sortData(e,t){this.processes.allData=this.orderBy(this.processes.allData,t,"desc"===e)}filter(e,t){return e.filter(i=>Object.keys(t).every(s=>{const a=i[s],o=t[s];return"string"==typeof a&&"string"==typeof o?a.toLowerCase().includes(o.toLowerCase()):a===o}))}clearData(){this.filteredValue="",this.filteredProperty="",this.processes.allData=[...this.untouchedAllData]}filterData(){const e={[this.filteredProperty]:this.filteredValue};this.processes.allData=this.filter(this.untouchedAllData,e),this.onSearchAction.emit(e)}}return l.\u0275fac=function(e){return new(e||l)(n.Y36(n.sBO),n.Y36($))},l.\u0275cmp=n.Xpm({type:l,selectors:[["cc-workflow-single-task"]],inputs:{hideSearch:"hideSearch",rowStyleFormatter:"rowStyleFormatter",rowClassFormatter:"rowClassFormatter",refreshSubject:"refreshSubject",baseAPI:"baseAPI",isProduction:"isProduction",isVPAdmin:"isVPAdmin",autoRefresh:"autoRefresh",metaApiUrl:"metaApiUrl",modules:"modules",rowActions:"rowActions",tableHeaders:"tableHeaders",cellTemplate:"cellTemplate",sortOpt:"sortOpt",actionMode:"actionMode"},outputs:{onRowAction:"onRowAction",onSearchAction:"onSearchAction"},decls:4,vars:3,consts:[["class","row mt-2 mb-2",4,"ngIf"],["class","row mt-5 mb-5",4,"ngIf"],[1,"row","mt-2","mb-2"],["class","w-100",3,"loading","rowStyleFormatter","rowClassFormatter","columns","data","pageOnFront","pageSize","hidePageSize","cellTemplate",4,"ngIf"],["name","advancedSearch","fetchDataOnInit","false",3,"metaApi","ngModel","ngModelChange","onFilter",4,"ngIf"],["name","advancedSearch","fetchDataOnInit","false",3,"metaApi","ngModel","ngModelChange","onFilter"],["advancedSearch","ngModel"],[1,"row","mt-5","mb-5"],["cc-raised-button","",3,"color","click"],["class","col-12 row mt-2 mb-2 align-items-baseline",4,"ngIf"],[1,"col-12","row","mt-2","mb-2","align-items-baseline"],["name","filteredProperty",1,"col-md-5",3,"ngModel","label","data","ngModelChange"],["name","filteredValue",1,"col-md-5",3,"label","ngModel","ngModelChange"],["cc-icon-button","",3,"color","click"],[1,"w-100",3,"loading","rowStyleFormatter","rowClassFormatter","columns","data","pageOnFront","pageSize","hidePageSize","cellTemplate"]],template:function(e,t){1&e&&(n.YNc(0,J,2,1,"div",0),n.YNc(1,W,5,2,"div",1),n.TgZ(2,"div",2),n.YNc(3,Q,1,10,"cc-datagrid",3),n.qZA()),2&e&&(n.Q6J("ngIf",!t.hideSearch),n.xp6(1),n.Q6J("ngIf",!t.isProduction),n.xp6(2),n.Q6J("ngIf",null==t.processes.dataGrid?null:t.processes.dataGrid.columns))},directives:[c.G2,m.uu,v.jB,S.G,M.Q9,U.Ge,_.O5,y.JJ,y.On],styles:[".default-btn[_ngcontent-%COMP%]{background-color:#eee;color:#000000de}"],changeDetection:0}),l})(),X=(()=>{class l{}return l.\u0275fac=function(e){return new(e||l)},l.\u0275mod=n.oAB({type:l}),l.\u0275inj=n.cJS({imports:[[_.ez,y.UX,y.u5,F.ef,m.S6,U.Gz,v.lK,M.L,S.f,c.pS,v.lK]]}),l})()},50072:(V,k,d)=>{d.d(k,{ef:()=>w});var n=d(5e3),_=d(69808);let w=(()=>{class f{}return f.\u0275fac=function(c){return new(c||f)},f.\u0275mod=n.oAB({type:f}),f.\u0275inj=n.cJS({imports:[[_.ez]]}),f})()}}]);
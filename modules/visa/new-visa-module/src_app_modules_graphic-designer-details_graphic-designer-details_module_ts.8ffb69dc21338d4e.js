"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_graphic-designer-details_graphic-designer-details_module_ts"],{3350:(ht,k,c)=>{c.r(k),c.d(k,{GraphicDesignerDetailsModule:()=>pt});var p=c(69808),S=c(97582),F=c(88476),h=c(92340),t=c(5e3),C=c(1402),b=c(40520),Z=c(43604),J=c(10276);let x=(()=>{class i{constructor(e){this.http=e}icaCheck(e){return this.http.post(`${h.N.apiBase}/${Z.b.icaCheck}/${e}`,{})}markAsDone(e,n,s){return this.http.post(`${h.N.apiBase}/visa/GraphicDesignerWorkFlow/complete/${e}/${n}`,s,{context:"Digitalize client signature"===n?(new b.qT).set(J.yo,!0):new b.qT})}couldNotProcessPhoto(e){return this.http.post(`${h.N.apiBase}/visa/ica/designer/${e}/can-not-process-photo`,{})}couldNotProcessDocument(e){return this.http.post(`${h.N.apiBase}/visa/document-enhancement/designer/${e}/can-not-process-document`,{})}cancelOfferLetter(e){return this.http.get(`${h.N.apiBase}/recruitment/hiringManager/cancel/${e}`)}housemaidRejectedAttachment(e){return this.http.get(`${h.N.apiBase}/visa/GraphicDesignerWorkFlow/${e}/housemaid-rejected-attachments`)}}return i.\u0275fac=function(e){return new(e||i)(t.LFG(b.eN))},i.\u0275prov=t.Yz7({token:i,factory:i.\u0275fac}),i})();var v=c(21799),T=c(42113),d=c(82599),y=c(54657),D=c(48966),A=c(65868),U=c(22313);let O=(()=>{class i{constructor(e){this.sanitizer=e}transform(e){return this.sanitizer.bypassSecurityTrustResourceUrl(e)}}return i.\u0275fac=function(e){return new(e||i)(t.Y36(U.H7,16))},i.\u0275pipe=t.Yjl({name:"safeUrl",type:i,pure:!0}),i})();function Y(i,o){if(1&i&&(t.ynx(0),t._UZ(1,"iframe",9),t.ALo(2,"safeUrl"),t.BQk()),2&i){const e=t.oxw();t.xp6(1),t.Q6J("src",t.lcZ(2,1,e.data.blobUrl),t.uOi)}}function Q(i,o){if(1&i&&(t.ynx(0),t.TgZ(1,"div",10),t._UZ(2,"img",11),t.ALo(3,"safeUrl"),t.qZA(),t.BQk()),2&i){const e=t.oxw();t.xp6(2),t.Q6J("src",t.lcZ(3,1,e.data.blobUrl),t.LSH)}}let g=class{constructor(o,e,n,s){this.media=o,this.notification=e,this.ccDialogRef=n,this.data=s}onDownload(){this.media.downloadFile(this.data.fileLink).subscribe({next:()=>{this.notification.notifyInfo("Downloading the file..."),this.ccDialogRef.close()}})}};g.\u0275fac=function(o){return new(o||g)(t.Y36(v.yJ),t.Y36(v.zg),t.Y36(D.so),t.Y36(D.WI))},g.\u0275cmp=t.Xpm({type:g,selectors:[["ng-component"]],decls:13,vars:3,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close-button","","cc-dialog-close",""],["cc-dialog-content",""],[4,"ngIf"],["cc-dialog-actions","",1,"d-flex","gap-2"],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"click"],["type","application/pdf",3,"src"],[1,"d-flex","justify-content-center"],[2,"max-width","100%",3,"src"]],template:function(o,e){1&o&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"div",4),t.YNc(6,Y,3,3,"ng-container",5),t.YNc(7,Q,4,3,"ng-container",5),t.qZA(),t.TgZ(8,"div",6)(9,"button",7),t._uU(10,"Cancel"),t.qZA(),t.TgZ(11,"button",8),t.NdJ("click",function(){return e.onDownload()}),t._uU(12," Download "),t.qZA()()()),2&o&&(t.xp6(3),t.Oqu(e.data.fileName),t.xp6(3),t.Q6J("ngIf","pdf"===e.data.fileType),t.xp6(1),t.Q6J("ngIf","pdf"!==e.data.fileType))},directives:[d.iK,d.Cj,d.Zb,d.fX,d.zn,d.kL,p.O5,d.Zu,A.uu],pipes:[O],encapsulation:2,changeDetection:0}),g=(0,S.gn)([F.kG],g);var R=c(68675),w=c(15222),_=c(93075),j=c(45834),M=c(85185),I=c(92431);function E(i,o){if(1&i&&(t.TgZ(0,"strong",7),t._UZ(1,"img",8),t._uU(2," Checking photo under ICA standards "),t.qZA()),2&i){const e=t.oxw(2);t.xp6(1),t.Q6J("src",e.getAssets("throbber.gif"),t.LSH)}}function B(i,o){1&i&&(t.TgZ(0,"strong",9)(1,"cc-icon",10),t._uU(2,"check"),t.qZA(),t._uU(3," Photo Accepted "),t.qZA())}function L(i,o){1&i&&(t.TgZ(0,"strong",11)(1,"i",12),t._uU(2,"cancel"),t.qZA(),t._uU(3," The photo is not following the "),t.TgZ(4,"a",13),t._uU(5," ICA standards"),t.qZA()()),2&i&&(t.xp6(4),t.s9C("href","rejectionDetails.icaStandardsLink",t.LSH))}function $(i,o){if(1&i&&(t.TgZ(0,"strong",11)(1,"i",12),t._uU(2,"cancel"),t.qZA(),t._uU(3),t.qZA()),2&i){const e=t.oxw(2);t.xp6(3),t.hij(" ",null==e.ICAField?null:e.ICAField.icaErrorMSG," ")}}function V(i,o){if(1&i&&(t.TgZ(0,"div",2)(1,"div",3),t.YNc(2,E,3,1,"strong",4),t.YNc(3,B,4,0,"strong",5),t.YNc(4,L,6,1,"strong",6),t.YNc(5,$,4,1,"strong",6),t.qZA()()),2&i){const e=t.oxw();t.xp6(2),t.Q6J("ngIf","checking"===(null==e.ICAField?null:e.ICAField.icaVerified)),t.xp6(1),t.Q6J("ngIf","accepted"===(null==e.ICAField?null:e.ICAField.icaVerified)),t.xp6(1),t.Q6J("ngIf","rejected"==(null==e.ICAField?null:e.ICAField.icaVerified)),t.xp6(1),t.Q6J("ngIf","error"==(null==e.ICAField?null:e.ICAField.icaVerified))}}function z(i,o){1&i&&(t.TgZ(0,"cc-label",21),t._uU(1,"Rejected Signatures: "),t.qZA())}function G(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",25)(1,"div",26)(2,"div",27),t._UZ(3,"cc-radio-button",28),t.TgZ(4,"div",29)(5,"div",30),t._UZ(6,"img",31),t.TgZ(7,"span",32),t.NdJ("click",function(){const a=t.CHM(e).$implicit;return t.oxw(4).openPreviewDialog(a)}),t.TgZ(8,"cc-icon",33),t._uU(9,"zoom_in"),t.qZA()(),t.TgZ(10,"span",34),t.NdJ("click",function(){const a=t.CHM(e).$implicit;return t.oxw(4).download(a.uuid)}),t.TgZ(11,"cc-icon",33),t._uU(12,"download"),t.qZA()()(),t.TgZ(13,"div",35),t._uU(14),t.qZA()()()()()}if(2&i){const e=o.$implicit,n=t.oxw(4);t.xp6(3),t.Q6J("value",e),t.xp6(3),t.Q6J("src",n.downloadUrl+"/"+e.uuid,t.LSH),t.xp6(2),t.Q6J("inline",!0),t.xp6(3),t.Q6J("inline",!0),t.xp6(3),t.hij(" ",e.name," ")}}function q(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"cc-radio-group",22),t.NdJ("change",function(){t.CHM(e);const s=t.oxw(3);return s.onSelect(s.customTemplateForm.value.selectedSig,"rejectedSigns")}),t.TgZ(1,"div",23),t.YNc(2,G,15,5,"div",24),t.qZA()()}if(2&i){const e=t.oxw(3);t.xp6(2),t.Q6J("ngForOf",e.rejectedSigns)}}function H(i,o){if(1&i&&(t.TgZ(0,"div",17),t.YNc(1,z,2,0,"cc-label",18),t.TgZ(2,"div",19),t.YNc(3,q,3,1,"cc-radio-group",20),t.qZA()()),2&i){const e=t.oxw(2);t.xp6(1),t.Q6J("ngIf","Maid signature or thumbprint rejected"==e.stepId),t.xp6(1),t.Q6J("ngClass","Maid signature or thumbprint rejected"==e.stepId?"col-md-8":"col-md-12"),t.xp6(1),t.Q6J("ngIf","Maid signature or thumbprint rejected"==e.stepId)}}function W(i,o){1&i&&(t.TgZ(0,"cc-label",21),t._uU(1,"Rejected Thumbprints:"),t.qZA())}function X(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"div",39)(1,"div",26)(2,"div",27),t._UZ(3,"cc-radio-button",28),t.TgZ(4,"div",29)(5,"div",30),t._UZ(6,"img",31),t.TgZ(7,"span",32),t.NdJ("click",function(){const a=t.CHM(e).$implicit;return t.oxw(4).openPreviewDialog(a)}),t.TgZ(8,"cc-icon",33),t._uU(9,"zoom_in"),t.qZA()(),t.TgZ(10,"span",34),t.NdJ("click",function(){const a=t.CHM(e).$implicit;return t.oxw(4).download(a.uuid)}),t.TgZ(11,"cc-icon",33),t._uU(12,"download"),t.qZA()()(),t.TgZ(13,"div",35),t._uU(14),t.qZA()()()()()}if(2&i){const e=o.$implicit,n=t.oxw(4);t.xp6(3),t.Q6J("value",e),t.xp6(3),t.Q6J("src",n.downloadUrl+"/"+e.uuid,t.LSH),t.xp6(2),t.Q6J("inline",!0),t.xp6(3),t.Q6J("inline",!0),t.xp6(3),t.hij(" ",e.name," ")}}function K(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"cc-radio-group",37),t.NdJ("change",function(){t.CHM(e);const s=t.oxw(3);return s.onSelect(s.customTemplateForm.value.selectedThumb,"rejectedThumbs")}),t.TgZ(1,"div",23),t.YNc(2,X,15,5,"div",38),t.qZA()()}if(2&i){const e=t.oxw(3);t.xp6(2),t.Q6J("ngForOf",e.rejectedThumbs)}}function tt(i,o){if(1&i&&(t.TgZ(0,"div",17),t.YNc(1,W,2,0,"cc-label",18),t.TgZ(2,"div",19),t.YNc(3,K,3,1,"cc-radio-group",36),t.qZA()()),2&i){const e=t.oxw(2);t.xp6(1),t.Q6J("ngIf","Maid signature or thumbprint rejected"===e.stepId),t.xp6(1),t.Q6J("ngClass","Maid signature or thumbprint rejected"===e.stepId?"col-md-8":"col-md-12"),t.xp6(1),t.Q6J("ngIf","Maid signature or thumbprint rejected"===e.stepId)}}function et(i,o){if(1&i&&(t.TgZ(0,"div",14)(1,"form",15),t.YNc(2,H,4,3,"div",16),t.YNc(3,tt,4,3,"div",16),t.qZA()()),2&i){const e=t.oxw();t.xp6(1),t.Q6J("formGroup",e.customTemplateForm),t.xp6(1),t.Q6J("ngIf",e.rejectedSigns.length),t.xp6(1),t.Q6J("ngIf",e.rejectedThumbs.length)}}let it=(()=>{class i{constructor(e,n,s,a,r,l,u){this.apiService=e,this.cdRef=n,this.formBuilder=s,this.taskFormService=a,this.mediaService=r,this.notificationService=l,this.dialog=u,this.select=new t.vpe,this.rejectedSigns=[],this.rejectedThumbs=[],this.downloadUrl=Z.b.download,this.ICAField=this.taskFormService.getField("ICA"),this.validationMessage=this.taskFormService.getField("validationMessage"),this.getAssets=m=>h.N.production?w.Ll.getBasePath()+`modules/visa/new-visa-module/assets/images/${m}`:w.Ll.getBasePath()+`/assets/images/${m}`,this.customTemplateForm=this.formBuilder.group({selectedSig:"",selectedThumb:""})}ngOnInit(){var e;this.apiService.housemaidRejectedAttachment(this.taskId).subscribe({next:n=>{var s,a;this.rejectedSigns=n.rejectedSignatures||[],this.rejectedThumbs=n.rejectedThumbprints||[],null===(s=this.customTemplateForm.get("selectedSig"))||void 0===s||s.setValue(n.rejectedSignatures[0]),null===(a=this.customTemplateForm.get("selectedThumb"))||void 0===a||a.setValue(n.rejectedThumbprints[0]),this.onSelect(n.rejectedSignatures[0],"rejectedSigns"),this.onSelect(n.rejectedThumbprints[0],"rejectedThumbs"),this.cdRef.markForCheck()}}),"rejectedAttachments"===this.data.name&&this.taskFormService.updateField("rejectedAttachments",n=>{n.label=this.rejectedSigns.length?"Rejected Signature":this.rejectedThumbs.length?"Rejected Thumbprints":""}),"Maid signature or thumbprint rejected"===this.stepId&&(this.taskFormService.updateField("rejectedAttachments",n=>{n.properties.hideLabel=!0}),null===(e=document.getElementById("rejectedAttachments"))||void 0===e||e.classList.replace("col-md-8","col-md-12")),"Modify maid's photo to fit ICA standards"===this.stepId&&this.checkICA()}onSelect(e,n){var s,a;let r=[];const l="Maid signature or thumbprint rejected"===this.stepId?"all":n;if("all"===l){const u=null===(s=this.customTemplateForm.get("selectedSig"))||void 0===s?void 0:s.value,m=null===(a=this.customTemplateForm.get("selectedThumb"))||void 0===a?void 0:a.value,N=Array.isArray(u)?u[0]:u,P=Array.isArray(m)?m[0]:m;N&&r.push({value:N,flag:"sig"}),P&&r.push({value:P,flag:"thumb"})}else r=[{value:e,flag:"rejectedSigns"===n?"sig":"thumb"}];this.select.emit({attachmentData:r,attachmentType:l})}openPreviewDialog(e){const n=["public","download",e.uuid].join("/");this.mediaService.getFile(n).subscribe(s=>{const a=new Blob([s],{type:s.type}),r={blob:a,blobUrl:URL.createObjectURL(a),fileType:s.type.split("/")[1]};this.dialog.originalOpen(g,{panelClass:["col-md-8"],data:Object.assign(Object.assign({},r),{fileName:e.name})})})}download(e){this.mediaService.downloadFile(["public","download",e].join("/")).subscribe({next:n=>{this.notificationService.notifyInfo("Download the file...")}})}checkICA(){var e,n;const s=null===(e=this.taskFormService.getField("ICA"))||void 0===e?void 0:e.defaultValue;null===(n=this.form.get("ICA"))||void 0===n||n.valueChanges.pipe((0,R.O)(s)).subscribe(a=>{this.verifyICA(a[0].id)})}verifyICA(e){this.ICAField&&(this.ICAField.icaVerified="checking",this.cdRef.detectChanges(),this.apiService.icaCheck(e).subscribe({next:()=>{this.ICAField&&(this.ICAField.icaVerified="accepted",this.cdRef.detectChanges())},error:n=>{this.ICAField&&(400===n.status?this.ICAField.icaVerified="rejected":(this.ICAField.icaVerified="error",this.ICAField.icaErrorMSG=n.data.message),this.cdRef.detectChanges())}}))}}return i.\u0275fac=function(e){return new(e||i)(t.Y36(x),t.Y36(t.sBO),t.Y36(_.qu),t.Y36(T.XI),t.Y36(v.yJ),t.Y36(v.zg),t.Y36(d.uY))},i.\u0275cmp=t.Xpm({type:i,selectors:[["custom-template"]],inputs:{form:"form",taskId:"taskId",stepId:"stepId",data:"data"},outputs:{select:"select"},decls:2,vars:2,consts:[["class","row m-0",4,"ngIf"],["class","rejectedAttachments",4,"ngIf"],[1,"row","m-0"],[1,"offset-md-6"],["class","progress-message",4,"ngIf"],["class","progress-message text-success d-flex align-items-center",4,"ngIf"],["class","progress-message text-danger",4,"ngIf"],[1,"progress-message"],["alt","",1,"px-2",3,"src"],[1,"progress-message","text-success","d-flex","align-items-center"],[2,"font-size","36px"],[1,"progress-message","text-danger"],[1,"material-icons","w3-padding-8-h"],[3,"href"],[1,"rejectedAttachments"],[1,"",3,"formGroup"],["class","row",4,"ngIf"],[1,"row"],["class","col-md-4",4,"ngIf"],[3,"ngClass"],["formControlName","selectedSig",3,"change",4,"ngIf"],[1,"col-md-4"],["formControlName","selectedSig",3,"change"],[1,"d-flex","gap-4","flex-wrap","my-4"],["class","p-4 attachment-container-signatures",4,"ngFor","ngForOf"],[1,"p-4","attachment-container-signatures"],[1,"d-flex","flex-column","attachment-item"],[1,"d-flex","gap-2"],[3,"value"],[1,"d-flex","flex-column"],[1,"position-relative","thumbnail-image-container"],["alt","",1,"thumbnail-image",3,"src"],[1,"thumbnail-preview-btn",3,"click"],[3,"inline"],[1,"position-absolute","download-btn",3,"click"],[1,"clear","text-center","item-name"],["formControlName","selectedThumb",3,"change",4,"ngIf"],["formControlName","selectedThumb",3,"change"],["class","p-4 attachment-container",4,"ngFor","ngForOf"],[1,"p-4","attachment-container"]],template:function(e,n){1&e&&(t.YNc(0,V,6,4,"div",0),t.YNc(1,et,4,3,"div",1)),2&e&&(t.Q6J("ngIf",n.validationMessage),t.xp6(1),t.Q6J("ngIf","rejectedAttachments"==n.data.name))},directives:[p.O5,j.Q9,_._Y,_.JL,_.sg,M.k_,p.mk,I.u6,_.JJ,_.u,p.sg,I.UF],styles:[".rejectedAttachments[_ngcontent-%COMP%]   .attachment-container[_ngcontent-%COMP%]{width:150px}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-container-signatures[_ngcontent-%COMP%]{width:200px}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-item[_ngcontent-%COMP%]   .thumbnail-image[_ngcontent-%COMP%]{width:100%!important;height:120px;box-shadow:#0000001a 0 1px 2px}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-item[_ngcontent-%COMP%]   .thumbnail-preview-btn[_ngcontent-%COMP%]{width:100%!important;height:120px;background:rgba(170,170,170,.57);position:absolute;inset:0;cursor:pointer;display:flex;align-items:center;justify-content:center}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-item[_ngcontent-%COMP%]   .thumbnail-preview-btn[_ngcontent-%COMP%]:hover{background:rgba(170,170,170,.7)}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-item[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]{bottom:5px;right:5px;z-index:999999;cursor:pointer;color:#fff;background:rgba(0,0,0,.5);border-radius:50%;width:24px;height:24px;display:flex;align-items:center;justify-content:center}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-item[_ngcontent-%COMP%]   .download-btn[_ngcontent-%COMP%]:hover{background:rgba(0,0,0,.7)}.rejectedAttachments[_ngcontent-%COMP%]   .attachment-item[_ngcontent-%COMP%]   .item-name[_ngcontent-%COMP%]{width:100%!important}.progress-message[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px}"],changeDetection:0}),i})();function nt(i,o){if(1&i&&(t.TgZ(0,"h2"),t._uU(1),t.qZA()),2&i){const e=t.oxw();t.xp6(1),t.AsE(" ",e.stepId," of (",e.housemaidName,") ")}}function ot(i,o){if(1&i&&(t.TgZ(0,"h2"),t._uU(1),t.qZA()),2&i){const e=t.oxw();t.xp6(1),t.hij(" Signature / thumbprint rejected (",e.housemaidName,") ")}}function st(i,o){if(1&i&&(t.TgZ(0,"h2"),t._uU(1),t.qZA()),2&i){const e=t.oxw();t.xp6(1),t.hij(" Digitize the signature and thumbprint of (",e.housemaidName,") ")}}function at(i,o){if(1&i&&(t.TgZ(0,"h2"),t._uU(1),t.qZA()),2&i){const e=t.oxw();t.xp6(1),t.hij(" Approve signature and thumbprint of (",e.housemaidName,") ")}}function ct(i,o){if(1&i){const e=t.EpF();t.TgZ(0,"custom-template",7),t.NdJ("select",function(s){return t.CHM(e),t.oxw().onSelectAttachment(s)}),t.qZA()}if(2&i){const e=o.data,n=o.form,s=t.oxw();t.Q6J("stepId",s.stepId)("taskId",s.taskId)("form",n)("data",e)}}function rt(i,o){if(1&i){const e=t.EpF();t.ynx(0),t.TgZ(1,"button",10),t.NdJ("click",function(){return t.CHM(e),t.oxw().$implicit.callbackFunc()}),t._uU(2),t.qZA(),t.BQk()}if(2&i){const e=t.oxw().$implicit;t.xp6(1),t.Q6J("disabled",e.disabled),t.xp6(1),t.hij(" ",e.label," ")}}function lt(i,o){if(1&i&&(t.ynx(0),t.YNc(1,rt,3,2,"ng-container",2),t.BQk()),2&i){const e=o.$implicit;t.xp6(1),t.Q6J("ngIf",e.visibilityCond)}}function dt(i,o){if(1&i&&(t.TgZ(0,"div",8),t.YNc(1,lt,2,1,"ng-container",9),t.qZA()),2&i){const e=t.oxw();t.xp6(1),t.Q6J("ngForOf",e.customActions)}}let f=class{constructor(o,e,n,s,a,r,l){this.route=o,this.apiService=e,this.notificationService=n,this.taskFormService=s,this.cdRef=a,this.router=r,this.dialog=l,this.stepId=this.route.snapshot.params.stepId,this.taskId=this.route.snapshot.params.taskId,this.housemaidName=this.route.snapshot.queryParams.housemaidName,this.baseUrl=h.N.apiBase,this.breadcrumbs=[{label:"Visa"},{label:"Graphic designer todo list",url:"/visa/v2/graphic-designer/todo-list"},{label:this.stepId}],this.mainActions=[],this.customActions=[],this.willHire=!1}ngOnInit(){this.setMainActions(),this.setCustomActions(),this.taskFormService.taskForm.subscribe(o=>{this.taskForm=o,this.cdRef.markForCheck()})}onSelectAttachment(o){this.selectedAttachment=o,this.cdRef.markForCheck()}onBeforeSave(o){"Interview Walk-in maid"===this.stepId&&(this.willHire="WILL_HIRE_HER"===o.decision),"Digitalize client signature"===this.stepId&&(this.taskFormService.updateSavePermission(!1),this.markAdDoneCustomMSG(o))}onCancel(){this.routeToTheMainPage()}onDoneTask(){this.routeToTheMainPage()}onAllTasksDone(){this.routeToTheMainPage()}cancelOfferLetter(){this.dialog.confirm("Warning!","Are you sure you want to cancel applicant?",()=>{this.apiService.cancelOfferLetter(this.taskId).subscribe({next:o=>{this.notificationService.notifySuccess("Cancelled Successfully"),this.routeToTheMainPage()}})})}setMainActions(){const o=["Modify maid's photo to fit ICA standards","Modify Rejected Document to get approval"].includes(this.stepId);this.mainActions=o?[]:["Cancel","Done"]}setCustomActions(){"Modify maid's photo to fit ICA standards"===this.stepId&&(this.customActions=this.createModifyMaidPhotoStepActions()),"Modify Rejected Document to get approval"===this.stepId&&(this.customActions=this.createRejectedDocumentToGetApprovalActions()),"Maid signature or thumbprint rejected"===this.stepId&&(this.customActions=this.createMaidSignatureRejectedStepActions())}createModifyMaidPhotoStepActions(){return[{label:"Couldn't process photo",callbackFunc:()=>this.couldNotProcessPhoto(),visibilityCond:!0,disabled:!1},{label:"Done",callbackFunc:()=>this.markAsDone(),visibilityCond:!0,disabled:!1},{label:"Cancel",callbackFunc:()=>this.onCancel(),visibilityCond:!0,disabled:!1}]}createMaidSignatureRejectedStepActions(){return[{label:"Use selected",callbackFunc:()=>this.useSelected(),visibilityCond:!0}]}createRejectedDocumentToGetApprovalActions(){return[{label:"Couldn't process Document",callbackFunc:()=>this.couldNotProcessDocument(),visibilityCond:!0,disabled:!1},{label:"Done",callbackFunc:()=>this.markAsDone(),visibilityCond:!0,disabled:!1},{label:"Cancel",callbackFunc:()=>this.onCancel(),visibilityCond:!0,disabled:!1}]}markAsDone(){if(!this.taskId||!this.stepId)return;const o=Object.keys(this.taskForm.controls).filter(e=>{var n;return"file"===(null===(n=this.taskFormService.getField(e))||void 0===n?void 0:n.type)}).map(e=>{const n=this.taskForm.value[e];return n&&n.length>0?{id:n[0].id}:null}).filter(Boolean);0!==o.length?this.apiService.markAsDone(this.taskId,this.stepId,{attachments:o}).subscribe({next:()=>{this.notificationService.notifySuccess("Task done Successfully"),this.routeToTheMainPage()}}):this.notificationService.notifyError("Please upload a file")}markAdDoneCustomMSG(o){this.apiService.markAsDone(this.taskId,this.stepId,o).subscribe({next:()=>{this.notificationService.notifySuccess("Task done Successfully"),this.routeToTheMainPage()},error:e=>{var n;let s=(null===(n=e.data)||void 0===n?void 0:n.message)||"Unknown Error";500===e.status&&s.includes("The uploaded signature already exist")&&(s=s.substring(s.indexOf("The uploaded signature already exist"))),this.notificationService.notifyError(s)}})}couldNotProcessPhoto(){this.apiService.couldNotProcessPhoto(this.taskId).subscribe({next:()=>{this.notificationService.notifySuccess("Sent Successfully"),this.routeToTheMainPage()}})}couldNotProcessDocument(){this.apiService.couldNotProcessDocument(this.taskId).subscribe(()=>{this.notificationService.notifySuccess('"Sent Successfully"'),this.routeToTheMainPage()})}useSelected(){var o,e,n,s,a,r;"rejectedSigns"===this.selectedAttachment.attachmentType&&(null===(o=this.taskForm.get("maidE-Signature"))||void 0===o||o.reset(),null===(e=this.taskForm.get("maidE-Signature"))||void 0===e||e.setValue([this.selectedAttachment.attachmentData[0].value]),null===(n=this.taskForm.get("maidE-Signature"))||void 0===n||n.updateValueAndValidity()),"rejectedThumbs"===this.selectedAttachment.attachmentType&&(null===(s=this.taskForm.get("maidE-Thumbprint"))||void 0===s||s.reset(),null===(a=this.taskForm.get("maidE-Thumbprint"))||void 0===a||a.setValue([this.selectedAttachment.attachmentData[0].value]),null===(r=this.taskForm.get("maidE-Thumbprint"))||void 0===r||r.updateValueAndValidity()),"all"===this.selectedAttachment.attachmentType&&this.selectedAttachment.attachmentData.forEach(l=>{var u,m;"sig"===l.flag&&(null===(u=this.taskForm.get("maidE-Signature"))||void 0===u||u.setValue([l.value])),"thumb"===l.flag&&(null===(m=this.taskForm.get("maidE-Thumbprint"))||void 0===m||m.setValue([l.value]))}),this.taskFormService.emitSaveRequest({saveOnly:!0,doneAll:!1}),this.taskFormService.updateSavePayloadData({attachments:this.selectedAttachment.attachmentData.map(l=>({id:l.value.id})),id:this.taskId})}routeToTheMainPage(){this.router.navigateByUrl(["visa","v2","graphic-designer","todo-list"].join("/"))}};f.\u0275fac=function(o){return new(o||f)(t.Y36(C.gz),t.Y36(x),t.Y36(v.zg),t.Y36(T.XI),t.Y36(t.sBO),t.Y36(C.F0),t.Y36(d.uY))},f.\u0275cmp=t.Xpm({type:f,selectors:[["app-todo-details"]],decls:11,vars:14,consts:[[3,"links","suppressNavigation"],[1,"p-2","my-2"],[4,"ngIf"],[3,"baseUrl","moduleName","entityName","stepID","taskID","actions","customTemplateRef","actionsTemplateRef","beforeSave","cancel","taskDone","allTasksDone"],[3,"taskFormExtraTemplate"],["customTmp",""],["actionsTmp",""],[3,"stepId","taskId","form","data","select"],[1,"d-flex","gap-1"],[4,"ngFor","ngForOf"],["cc-raised-button","",3,"disabled","click"]],template:function(o,e){if(1&o&&(t._UZ(0,"cc-breadcrumbs",0),t.TgZ(1,"div",1),t.YNc(2,nt,2,2,"h2",2),t.YNc(3,ot,2,1,"h2",2),t.YNc(4,st,2,1,"h2",2),t.YNc(5,at,2,1,"h2",2),t.qZA(),t.TgZ(6,"cc-workflow-task-form",3),t.NdJ("beforeSave",function(s){return e.onBeforeSave(s)})("cancel",function(){return e.onCancel()})("taskDone",function(){return e.onDoneTask()})("allTasksDone",function(){return e.onAllTasksDone()}),t.qZA(),t.YNc(7,ct,1,4,"ng-template",4,5,t.W1O),t.YNc(9,dt,2,1,"ng-template",null,6,t.W1O)),2&o){const n=t.MAs(8),s=t.MAs(10);t.Q6J("links",e.breadcrumbs)("suppressNavigation",!0),t.xp6(2),t.Q6J("ngIf","Digitize maid's signature and thumbprint"!=e.stepId&&"Approve maid's signature or thumbprint"!=e.stepId&&"Maid signature or thumbprint rejected"!=e.stepId),t.xp6(1),t.Q6J("ngIf","Maid signature or thumbprint rejected"==e.stepId),t.xp6(1),t.Q6J("ngIf","Digitize maid's signature and thumbprint"==e.stepId),t.xp6(1),t.Q6J("ngIf","Approve maid's signature or thumbprint"==e.stepId),t.xp6(1),t.Q6J("baseUrl",e.baseUrl)("moduleName","visa")("entityName","GraphicDesignerWorkFlow")("stepID",e.stepId)("taskID",e.taskId)("actions",e.mainActions)("customTemplateRef",n)("actionsTemplateRef",s)}},directives:[y.E4,p.O5,T.y9,T.XP,it,p.sg,A.uu],encapsulation:2,changeDetection:0}),f=(0,S.gn)([F.kG],f);var mt=c(463);const ut=[{path:"",component:f}];let pt=(()=>{class i{}return i.\u0275fac=function(e){return new(e||i)},i.\u0275mod=t.oAB({type:i}),i.\u0275inj=t.cJS({providers:[x],imports:[[p.ez,p.ez,_.UX,C.Bz.forChild(ut),A.S6,mt.ZN,T.SP,I.XD,j.L,d.I8,M.C6,y.JC]]}),i})()}}]);
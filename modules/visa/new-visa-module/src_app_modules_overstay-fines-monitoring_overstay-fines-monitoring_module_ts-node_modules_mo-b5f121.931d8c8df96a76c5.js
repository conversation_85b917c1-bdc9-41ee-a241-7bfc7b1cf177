(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_overstay-fines-monitoring_overstay-fines-monitoring_module_ts-node_modules_mo-b5f121"],{54842:(b,p,a)=>{"use strict";a.r(p),a.d(p,{OverstayFinesMonitoringModule:()=>P});var v=a(69808),l=a(43604),e=a(5e3),r=a(40520);let c=(()=>{class t{constructor(s){this._http=s}getOverstayFines(s){return this._http.get(l.b.getOverstayFines,{params:Object.assign({},this.getTruthy(s))})}getOverstayFinesStatus(){return this._http.get(l.b.getOverstayFinesStatus)}getTruthy(s){return Object.fromEntries(Object.entries(s).filter(([n,o])=>!!o))}}return t.\u0275fac=function(s){return new(s||t)(e.LFG(r.eN))},t.\u0275prov=e.Yz7({token:t,factory:t.\u0275fac}),t})();var F=a(1402),m=a(93075),f=a(62764),O=a(26523),A=a(43687),S=a(28172),Z=a(11523),k=a(97582),z=a(63900),D=a(88476),U=a(15439),N=a(61135);const M={};let y=(()=>{class t{constructor(){this.searchSubject=new N.X(M),this.search$=this.searchSubject.asObservable()}search(s){this.searchSubject.next(s)}}return t.\u0275fac=function(s){return new(s||t)},t.\u0275prov=e.Yz7({token:t,factory:t.\u0275fac}),t})();var T=a(54004),J=a(8188);let C=(()=>{class t{constructor(s,n){this._apiService=s,this._picklist=n,this.housemaidTypes=[{id:"FREEDOM_OPERATOR",text:"Freedom Operator"},{id:"MAID_VISA",text:"Maid visa"},{id:"Normal",text:"Normal"},{id:"WALKIN",text:"Walkin"},{id:"MAIDS_AT",text:"Maids at"}],this.nationalities=o=>this._picklist.getPicklist({code:"nationalities",page:o.page,pageSize:o.size,search:o.searchString||""}).pipe((0,T.U)(u=>u.map(h=>({id:h.id,text:h.label})))),this.overstayFinesStatus$=this._apiService.getOverstayFinesStatus().pipe((0,T.U)(o=>o.flatMap(u=>Object.entries(u).map(([h,G])=>({id:h,text:G})))))}}return t.\u0275fac=function(s){return new(s||t)(e.LFG(c),e.LFG(J.Ab))},t.\u0275prov=e.Yz7({token:t,factory:t.\u0275fac}),t})();var j=a(34378),w=a(85185),g=a(65868),x=a(45834);let I=(()=>{class t{constructor(s,n,o){this._fb=s,this._stateService=n,this._dataService=o,this.nationalitiesPageFetcher=this._dataService.nationalities,this.overstayFinesStatus$=this._dataService.overstayFinesStatus$,this.housemaidTypes=this._dataService.housemaidTypes,this.form=this._fb.group({name:"",housemaidType:"",nationality:"",overstayFinesStatus:"",fromDatePaid:"",toDatePaid:"",fromInitiationDate:"",toInitiationDate:"",betweenReducedAmount:"",toReducedAmount:"",betweenOriginalAmount:"",toOriginalAmount:""})}ngOnInit(){}onSearch(){var s,n,o;const u=Object.assign(Object.assign({},this.form.value),{housemaidType:(null===(s=this.form.value.housemaidType)||void 0===s?void 0:s.toString())||"",nationality:(null===(n=this.form.value.nationality)||void 0===n?void 0:n.toString())||"",overstayFinesStatus:(null===(o=this.form.value.overstayFinesStatus)||void 0===o?void 0:o.toString())||""});this._stateService.search(u)}}return t.\u0275fac=function(s){return new(s||t)(e.Y36(m.qu),e.Y36(y),e.Y36(C))},t.\u0275cmp=e.Xpm({type:t,selectors:[["overstay-fines-search-filter"]],decls:42,vars:13,consts:[["expanded","true"],[3,"formGroup"],[1,"row","px-4","d-flex"],[1,"col-md-3"],["label","Maid name","formControlName","name"],["label","Maid Type","formControlName","housemaidType",3,"data","multiple"],["label","Maid Nationality","formControlName","nationality",3,"lazyPageFetcher","multiple"],["label","Status","formControlName","overstayFinesStatus",3,"data","multiple"],[1,"d-flex","flex-column","row","px-4"],[1,"d-flex","col-md-6","align-items-baseline",2,"height","fit-content"],[1,"col-3","px-0"],["label","From","formControlName","fromDatePaid",1,"col-5"],["label","To","formControlName","toDatePaid",1,"col-5"],["label","From","formControlName","fromInitiationDate",1,"col-5"],["label","To","formControlName","toInitiationDate",1,"col-5"],["label","Between","formControlName","betweenReducedAmount",1,"col-5",3,"symbol"],["label","And","formControlName","toReducedAmount",1,"col-5",3,"symbol"],["label","Between","formControlName","betweenOriginalAmount",1,"col-5",3,"symbol"],["label","And","formControlName","toOriginalAmount",1,"col-5",3,"symbol"],["cc-raised-button","","color","primary",1,"px-5",3,"click"],[2,"font-size","36px"]],template:function(s,n){1&s&&(e.TgZ(0,"cc-accordion")(1,"cc-panel",0),e._UZ(2,"cc-panel-title"),e.TgZ(3,"cc-panel-body")(4,"form",1)(5,"div",2)(6,"div",3),e._UZ(7,"cc-input",4),e.qZA(),e.TgZ(8,"div",3),e._UZ(9,"cc-select",5),e.qZA(),e.TgZ(10,"div",3),e._UZ(11,"cc-select",6),e.qZA(),e.TgZ(12,"div",3),e._UZ(13,"cc-select",7),e.ALo(14,"async"),e.qZA()(),e.TgZ(15,"div",8)(16,"div",9)(17,"cc-label",10),e._uU(18,"Date Paid:"),e.qZA(),e._UZ(19,"cc-datepicker",11)(20,"cc-datepicker",12),e.qZA(),e.TgZ(21,"div",9)(22,"cc-label",10),e._uU(23,"Initiation Date:"),e.qZA(),e._UZ(24,"cc-datepicker",13)(25,"cc-datepicker",14),e.qZA(),e.TgZ(26,"div",9)(27,"cc-label",10),e._uU(28,"Reduced Amount:"),e.qZA(),e._UZ(29,"cc-amount-input",15)(30,"cc-amount-input",16),e.qZA(),e.TgZ(31,"div",9)(32,"cc-label",10),e._uU(33,"Original Amount:"),e.qZA(),e._UZ(34,"cc-amount-input",17)(35,"cc-amount-input",18),e.qZA()()()(),e.TgZ(36,"cc-panel-actions")(37,"button",19),e.NdJ("click",function(){return n.onSearch()}),e.TgZ(38,"cc-icon",20),e._uU(39,"search"),e.qZA(),e.TgZ(40,"span"),e._uU(41,"Search"),e.qZA()()()()()),2&s&&(e.xp6(4),e.Q6J("formGroup",n.form),e.xp6(5),e.Q6J("data",n.housemaidTypes)("multiple",!0),e.xp6(2),e.Q6J("lazyPageFetcher",n.nationalitiesPageFetcher)("multiple",!0),e.xp6(2),e.Q6J("data",e.lcZ(14,11,n.overstayFinesStatus$))("multiple",!0),e.xp6(16),e.Q6J("symbol",""),e.xp6(1),e.Q6J("symbol",""),e.xp6(4),e.Q6J("symbol",""),e.xp6(1),e.Q6J("symbol",""))},directives:[j.I,j.CW,j.LL,j.G9,m._Y,m.JL,m.sg,A.G,m.JJ,m.u,O.jB,w.k_,S.AC,Z.Fi,j.qx,g.uu,x.Q9],pipes:[v.Ov],encapsulation:2,changeDetection:0}),t})();function R(t,i){if(1&t){const s=e.EpF();e.TgZ(0,"button",9),e.NdJ("click",function(){const u=e.CHM(s).$implicit;return e.oxw().onViewDetails(u.housemaid.id)}),e._uU(1,"View Details"),e.qZA()}}const Y=function(t){return{operations:t}},_=[{field:"housemaid.name",header:"Housemaid Name"},{field:"housemaid.housemaidType",header:"Housemaid Type"},{field:"housemaid.nationality.label",header:"Nationality"},{field:"overstayFineAmount",header:"Overstay Fine Amount"},{field:"overstayFinesStatus",header:"Status"},{field:"numOfOverstayDays",header:"Number of Overstay Day"},{field:"creationDate",header:"Initial Date",width:"15ch",formatter:t=>U(t.creationDate).format("YYYY-MM-DD")},{field:"originalOverstayFees",header:"Original Fine Amount"},{field:"reducedAmount",header:"Reduced Amount"},{field:"datePaid",header:"Date Paid",width:"15ch"},{field:"reductionReason.label",header:"Amount reduction reason",width:"25ch"},{field:"operations",header:"Actions"}];let d=class{constructor(i,s,n,o){this._service=i,this._state=s,this._cdRef=n,this._router=o,this.vm={overstayFinesList:[],sumOfReducedAmount:0,sumOfOriginalAmount:0},this.gridCols=_}ngOnInit(){this._state.search$.pipe((0,z.w)(i=>this._service.getOverstayFines(i))).subscribe({next:i=>{this.vm=i,this._cdRef.detectChanges()}})}onViewDetails(i){this._router.navigateByUrl(`housemaid/overstay-fines/${i}`)}};d.\u0275fac=function(i){return new(i||d)(e.Y36(c),e.Y36(y),e.Y36(e.sBO),e.Y36(F.F0))},d.\u0275cmp=e.Xpm({type:d,selectors:[["ng-component"]],decls:18,vars:10,consts:[[1,"p-4"],[1,"pt-2","pb-4"],[1,"py-2"],[1,"pr-1"],[2,"color","green"],[2,"color","red"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["operations",""],["cc-raised-button","","color","accent",3,"click"]],template:function(i,s){if(1&i&&(e.TgZ(0,"div",0)(1,"div",1),e._UZ(2,"overstay-fines-search-filter"),e.qZA(),e.ynx(3),e.TgZ(4,"div",2)(5,"div")(6,"span",3),e._uU(7,"Sum of Reduced Amount:"),e.qZA(),e.TgZ(8,"span",4),e._uU(9),e.qZA()(),e.TgZ(10,"div")(11,"span",3),e._uU(12,"Sum of Original Amount:"),e.qZA(),e.TgZ(13,"span",5),e._uU(14),e.qZA()()(),e._UZ(15,"cc-datagrid",6),e.YNc(16,R,2,0,"ng-template",7,8,e.W1O),e.BQk(),e.qZA()),2&i){const n=e.MAs(17);e.xp6(9),e.Oqu(s.vm.sumOfReducedAmount),e.xp6(5),e.Oqu(s.vm.sumOfOriginalAmount),e.xp6(1),e.Q6J("columns",s.gridCols)("data",s.vm.overstayFinesList)("showPaginator",!1)("loading",!1)("cellTemplate",e.VKq(8,Y,n)),e.xp6(1),e.Q6J("ccGridCell",s.vm.overstayFinesList)}},directives:[I,f.Ge,f.VC,g.uu],encapsulation:2,changeDetection:0}),d=(0,k.gn)([D.kG],d);const L=[{path:"",component:d}];let P=(()=>{class t{}return t.\u0275fac=function(s){return new(s||t)},t.\u0275mod=e.oAB({type:t}),t.\u0275inj=e.cJS({providers:[c,y,C],imports:[[v.ez,F.Bz.forChild(L),m.UX,f.Gz,j.yU,O.lK,A.f,Z.SZ,S.Bp,g.S6,x.L]]}),t})()},46700:(b,p,a)=>{var v={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function l(r){var c=e(r);return a(c)}function e(r){if(!a.o(v,r)){var c=new Error("Cannot find module '"+r+"'");throw c.code="MODULE_NOT_FOUND",c}return v[r]}l.keys=function(){return Object.keys(v)},l.resolve=e,b.exports=l,l.id=46700}}]);
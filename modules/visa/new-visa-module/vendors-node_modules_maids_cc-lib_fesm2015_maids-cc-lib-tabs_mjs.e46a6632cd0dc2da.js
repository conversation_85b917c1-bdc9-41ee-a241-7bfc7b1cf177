"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["vendors-node_modules_maids_cc-lib_fesm2015_maids-cc-lib-tabs_mjs"],{13859:(Le,j,c)=>{c.d(j,{L3:()=>de,ST:()=>le,e6:()=>De,eF:()=>w,wA:()=>Re});var y=c(15664),P=c(17144),h=c(47429),m=c(69808),t=c(5e3),b=c(90508),u=c(76360),v=c(77579),C=c(50727),I=c(54968),W=c(39646),x=c(56451),Q=c(82805),_=c(41777),B=c(68675),Z=c(71884),p=c(82722),f=c(50226),g=c(63191),D=c(91159),T=c(70925),R=c(29071);function U(a,s){1&a&&t.Hsn(0)}const k=["*"];function K(a,s){}const $=function(a){return{animationDuration:a}},J=function(a,s){return{value:a,params:s}},V=["tabListContainer"],q=["tabList"],z=["nextPaginator"],X=["previousPaginator"],ee=["tabBodyWrapper"],te=["tabHeader"];function ie(a,s){}function ne(a,s){if(1&a&&t.YNc(0,ie,0,0,"ng-template",9),2&a){const e=t.oxw().$implicit;t.Q6J("cdkPortalOutlet",e.templateLabel)}}function ae(a,s){if(1&a&&t._uU(0),2&a){const e=t.oxw().$implicit;t.Oqu(e.textLabel)}}function se(a,s){if(1&a){const e=t.EpF();t.TgZ(0,"div",6),t.NdJ("click",function(){const n=t.CHM(e),o=n.$implicit,r=n.index,l=t.oxw(),d=t.MAs(1);return l._handleClick(o,d,r)})("cdkFocusChange",function(n){const r=t.CHM(e).index;return t.oxw()._tabFocusChanged(n,r)}),t.TgZ(1,"div",7),t.YNc(2,ne,1,1,"ng-template",8),t.YNc(3,ae,1,1,"ng-template",8),t.qZA()()}if(2&a){const e=s.$implicit,i=s.index,n=t.oxw();t.ekj("cc-tab-label-active",n.selectedIndex==i),t.Q6J("id",n._getTabLabelId(i))("disabled",e.disabled)("matRippleDisabled",e.disabled||n.disableRipple),t.uIk("tabIndex",n._getTabIndex(e,i))("aria-posinset",i+1)("aria-setsize",n._tabs.length)("aria-controls",n._getTabContentId(i))("aria-selected",n.selectedIndex==i)("aria-label",e.ariaLabel||null)("aria-labelledby",!e.ariaLabel&&e.ariaLabelledby?e.ariaLabelledby:null),t.xp6(2),t.Q6J("ngIf",e.templateLabel),t.xp6(1),t.Q6J("ngIf",!e.templateLabel)}}function oe(a,s){if(1&a){const e=t.EpF();t.TgZ(0,"cc-tab-body",10),t.NdJ("_onCentered",function(){return t.CHM(e),t.oxw()._removeTabBodyWrapperHeight()})("_onCentering",function(n){return t.CHM(e),t.oxw()._setTabBodyWrapperHeight(n)}),t.qZA()}if(2&a){const e=s.$implicit,i=s.index,n=t.oxw();t.ekj("cc-tab-body-active",n.selectedIndex===i),t.Q6J("id",n._getTabContentId(i))("content",e.content)("position",e.position)("origin",e.origin)("animationDuration",n.animationDuration),t.uIk("tabindex",null!=n.contentTabIndex&&n.selectedIndex===i?n.contentTabIndex:null)("aria-labelledby",n._getTabLabelId(i))}}const re=new t.OlP("CCInkBarPositioner",{providedIn:"root",factory:function ce(){return s=>({left:s?(s.offsetLeft||0)+"px":"0",width:s?(s.offsetWidth||0)+"px":"0"})}});let M=(()=>{class a{constructor(e,i,n,o){this._elementRef=e,this._ngZone=i,this._inkBarPositioner=n,this._animationMode=o}alignToElement(e){this.show(),"undefined"!=typeof requestAnimationFrame?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>this._setStyles(e))}):this._setStyles(e)}show(){this._elementRef.nativeElement.style.visibility="visible"}hide(){this._elementRef.nativeElement.style.visibility="hidden"}_setStyles(e){const i=this._inkBarPositioner(e),n=this._elementRef.nativeElement;n.style.left=i.left,n.style.width=i.width}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(t.R0b),t.Y36(re),t.Y36(u.Qb,8))},a.\u0275dir=t.lG2({type:a,selectors:[["cc-ink-bar"]],hostAttrs:[1,"cc-ink-bar"],hostVars:2,hostBindings:function(e,i){2&e&&t.ekj("_cc-animation-noopable","NoopAnimations"===i._animationMode)}}),a})();const E=new t.OlP("CCTabContent");let le=(()=>{class a{constructor(e){this.template=e}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.Rgc))},a.\u0275dir=t.lG2({type:a,selectors:[["","ccTabContent",""]],features:[t._Bn([{provide:E,useExisting:a}])]}),a})();const O=new t.OlP("CCTabLabel");let de=(()=>{class a extends h.ig{}return a.\u0275fac=function(){let s;return function(i){return(s||(s=t.n5z(a)))(i||a)}}(),a.\u0275dir=t.lG2({type:a,selectors:[["","cc-tab-label",""],["","ccTabLabel",""]],features:[t._Bn([{provide:O,useExisting:a}]),t.qOj]}),a})();const _e=(0,b.Id)(class{}),A=new t.OlP("CC_TAB_GROUP");let w=(()=>{class a extends _e{constructor(e,i){super(),this._viewContainerRef=e,this._closestTabGroup=i,this.textLabel="",this.ariaLabel="",this.ariaLabelledby="",this._contentPortal=null,this._stateChanges=new v.x,this.position=null,this.origin=null,this.isActive=!1}get templateLabel(){return this._templateLabel}set templateLabel(e){this._setTemplateLabelInput(e)}get content(){return this._contentPortal}ngOnChanges(e){(e.hasOwnProperty("textLabel")||e.hasOwnProperty("disabled"))&&this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete()}ngOnInit(){this._contentPortal=new h.UE(this._explicitContent||this._implicitContent,this._viewContainerRef)}_setTemplateLabelInput(e){e&&(this._templateLabel=e)}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.s_b),t.Y36(A,8))},a.\u0275cmp=t.Xpm({type:a,selectors:[["cc-tab"]],contentQueries:function(e,i,n){if(1&e&&(t.Suo(n,O,5),t.Suo(n,E,7,t.Rgc)),2&e){let o;t.iGM(o=t.CRH())&&(i.templateLabel=o.first),t.iGM(o=t.CRH())&&(i._explicitContent=o.first)}},viewQuery:function(e,i){if(1&e&&t.Gf(t.Rgc,7),2&e){let n;t.iGM(n=t.CRH())&&(i._implicitContent=n.first)}},inputs:{disabled:"disabled",textLabel:["label","textLabel"],ariaLabel:["aria-label","ariaLabel"],ariaLabelledby:["aria-labelledby","ariaLabelledby"]},exportAs:["ccTab"],features:[t.qOj,t.TTD],ngContentSelectors:k,decls:1,vars:0,template:function(e,i){1&e&&(t.F$t(),t.YNc(0,U,1,0,"ng-template"))},encapsulation:2}),a})();const be={translateTab:(0,_.X$)("translateTab",[(0,_.SB)("center, void, left-origin-center, right-origin-center",(0,_.oB)({transform:"none"})),(0,_.SB)("left",(0,_.oB)({transform:"translate3d(-100%, 0, 0)",minHeight:"1px"})),(0,_.SB)("right",(0,_.oB)({transform:"translate3d(100%, 0, 0)",minHeight:"1px"})),(0,_.eR)("* => left, * => right, left => center, right => center",(0,_.jt)("{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)")),(0,_.eR)("void => left-origin-center",[(0,_.oB)({transform:"translate3d(-100%, 0, 0)"}),(0,_.jt)("{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)")]),(0,_.eR)("void => right-origin-center",[(0,_.oB)({transform:"translate3d(100%, 0, 0)"}),(0,_.jt)("{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)")])])};let he=(()=>{class a extends h.Pl{constructor(e,i,n,o){super(e,i,o),this._host=n,this._centeringSub=C.w0.EMPTY,this._leavingSub=C.w0.EMPTY}ngOnInit(){super.ngOnInit(),this._centeringSub=this._host._beforeCentering.pipe((0,B.O)(this._host._isCenterPosition(this._host._position))).subscribe(e=>{e&&!this.hasAttached()&&this.attach(this._host._content)}),this._leavingSub=this._host._afterLeavingCenter.subscribe(()=>{this.detach()})}ngOnDestroy(){super.ngOnDestroy(),this._centeringSub.unsubscribe(),this._leavingSub.unsubscribe()}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t._Vd),t.Y36(t.s_b),t.Y36((0,t.Gpc)(()=>S)),t.Y36(m.K0))},a.\u0275dir=t.lG2({type:a,selectors:[["","ccTabBodyHost",""]],features:[t.qOj]}),a})(),ue=(()=>{class a{constructor(e,i,n){this._elementRef=e,this._dir=i,this._positionIndex=0,this._dirChangeSubscription=C.w0.EMPTY,this._position="center",this._translateTabComplete=new v.x,this._onCentering=new t.vpe,this._beforeCentering=new t.vpe,this._afterLeavingCenter=new t.vpe,this._onCentered=new t.vpe(!0),this.origin=null,this.animationDuration="500ms",i&&(this._dirChangeSubscription=i.change.subscribe(o=>{this._computePositionAnimationState(o),n.markForCheck()})),this._translateTabComplete.pipe((0,Z.x)((o,r)=>o.fromState===r.fromState&&o.toState===r.toState)).subscribe(o=>{this._isCenterPosition(o.toState)&&this._isCenterPosition(this._position)&&this._onCentered.emit(),this._isCenterPosition(o.fromState)&&!this._isCenterPosition(this._position)&&this._afterLeavingCenter.emit()})}set position(e){this._positionIndex=e,this._computePositionAnimationState()}ngOnInit(){"center"==this._position&&null!=this.origin&&(this._position=this._computePositionFromOrigin(this.origin))}ngOnDestroy(){this._dirChangeSubscription.unsubscribe(),this._translateTabComplete.complete()}_onTranslateTabStarted(e){const i=this._isCenterPosition(e.toState);this._beforeCentering.emit(i),i&&this._onCentering.emit(this._elementRef.nativeElement.clientHeight)}_getLayoutDirection(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}_isCenterPosition(e){return"center"==e||"left-origin-center"==e||"right-origin-center"==e}_computePositionAnimationState(e=this._getLayoutDirection()){this._position=this._positionIndex<0?"ltr"==e?"left":"right":this._positionIndex>0?"ltr"==e?"right":"left":"center"}_computePositionFromOrigin(e){const i=this._getLayoutDirection();return"ltr"==i&&e<=0||"rtl"==i&&e>0?"left-origin-center":"right-origin-center"}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(f.Is,8),t.Y36(t.sBO))},a.\u0275dir=t.lG2({type:a,inputs:{_content:["content","_content"],origin:"origin",animationDuration:"animationDuration",position:"position"},outputs:{_onCentering:"_onCentering",_beforeCentering:"_beforeCentering",_afterLeavingCenter:"_afterLeavingCenter",_onCentered:"_onCentered"}}),a})(),S=(()=>{class a extends ue{constructor(e,i,n){super(e,i,n)}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(f.Is,8),t.Y36(t.sBO))},a.\u0275cmp=t.Xpm({type:a,selectors:[["cc-tab-body"]],viewQuery:function(e,i){if(1&e&&t.Gf(h.Pl,5),2&e){let n;t.iGM(n=t.CRH())&&(i._portalHost=n.first)}},hostAttrs:[1,"cc-tab-body"],features:[t.qOj],decls:3,vars:6,consts:[["cdkScrollable","",1,"cc-tab-body-content"],["content",""],["ccTabBodyHost",""]],template:function(e,i){1&e&&(t.TgZ(0,"div",0,1),t.NdJ("@translateTab.start",function(o){return i._onTranslateTabStarted(o)})("@translateTab.done",function(o){return i._translateTabComplete.next(o)}),t.YNc(2,K,0,0,"ng-template",2),t.qZA()),2&e&&t.Q6J("@translateTab",t.WLB(3,J,i._position,t.VKq(1,$,i.animationDuration)))},directives:[he],encapsulation:2,data:{animation:[be.translateTab]}}),a})();const H=new t.OlP("CC_TABS_CONFIG"),pe=(0,b.Id)(class{});let N=(()=>{class a extends pe{constructor(e){super(),this.elementRef=e}focus(){this.elementRef.nativeElement.focus()}getOffsetLeft(){return this.elementRef.nativeElement.offsetLeft}getOffsetWidth(){return this.elementRef.nativeElement.offsetWidth}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq))},a.\u0275dir=t.lG2({type:a,selectors:[["","ccTabLabelWrapper",""]],hostVars:3,hostBindings:function(e,i){2&e&&(t.uIk("aria-disabled",!!i.disabled),t.ekj("cc-tab-disabled",i.disabled))},inputs:{disabled:"disabled"},features:[t.qOj]}),a})();const F=(0,T.i$)({passive:!0});let ge=(()=>{class a{constructor(e,i,n,o,r,l,d){this._elementRef=e,this._changeDetectorRef=i,this._viewportRuler=n,this._dir=o,this._ngZone=r,this._platform=l,this._animationMode=d,this._scrollDistance=0,this._selectedIndexChanged=!1,this._destroyed=new v.x,this._showPaginationControls=!1,this._disableScrollAfter=!0,this._disableScrollBefore=!0,this._tabLabelCount=1,this._scrollDistanceChanged=!1,this._currentTextContent="",this._stopScrolling=new v.x,this.disablePagination=!1,this._selectedIndex=0,this.selectFocusedIndex=new t.vpe,this.indexFocused=new t.vpe,r.runOutsideAngular(()=>{(0,I.R)(e.nativeElement,"mouseleave").pipe((0,p.R)(this._destroyed)).subscribe(()=>{this._stopInterval()})})}get selectedIndex(){return this._selectedIndex}set selectedIndex(e){e=(0,g.su)(e),this._selectedIndex!=e&&(this._selectedIndexChanged=!0,this._selectedIndex=e,this._keyManager&&this._keyManager.updateActiveItem(e))}ngAfterViewInit(){(0,I.R)(this._previousPaginator.nativeElement,"touchstart",F).pipe((0,p.R)(this._destroyed)).subscribe(()=>{this._handlePaginatorPress("before")}),(0,I.R)(this._nextPaginator.nativeElement,"touchstart",F).pipe((0,p.R)(this._destroyed)).subscribe(()=>{this._handlePaginatorPress("after")})}ngAfterContentInit(){const e=this._dir?this._dir.change:(0,W.of)("ltr"),i=this._viewportRuler.change(150),n=()=>{this.updatePagination(),this._alignInkBarToSelectedTab()};this._keyManager=new y.Em(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap(),this._keyManager.updateActiveItem(this._selectedIndex),"undefined"!=typeof requestAnimationFrame?requestAnimationFrame(n):n(),(0,x.T)(e,i,this._items.changes).pipe((0,p.R)(this._destroyed)).subscribe(()=>{this._ngZone.run(()=>Promise.resolve().then(n)),this._keyManager.withHorizontalOrientation(this._getLayoutDirection())}),this._keyManager.change.pipe((0,p.R)(this._destroyed)).subscribe(o=>{this.indexFocused.emit(o),this._setTabFocus(o)})}ngAfterContentChecked(){this._tabLabelCount!=this._items.length&&(this.updatePagination(),this._tabLabelCount=this._items.length,this._changeDetectorRef.markForCheck()),this._selectedIndexChanged&&(this._scrollToLabel(this._selectedIndex),this._checkScrollingControls(),this._alignInkBarToSelectedTab(),this._selectedIndexChanged=!1,this._changeDetectorRef.markForCheck()),this._scrollDistanceChanged&&(this._updateTabScrollPosition(),this._scrollDistanceChanged=!1,this._changeDetectorRef.markForCheck())}ngOnDestroy(){this._destroyed.next(),this._destroyed.complete(),this._stopScrolling.complete()}_handleKeydown(e){if(!(0,D.Vb)(e))switch(e.keyCode){case D.K5:case D.L_:this.focusIndex!==this.selectedIndex&&(this.selectFocusedIndex.emit(this.focusIndex),this._itemSelected(e));break;default:this._keyManager.onKeydown(e)}}_onContentChanges(){const e=this._elementRef.nativeElement.textContent;e!==this._currentTextContent&&(this._currentTextContent=e||"",this._ngZone.run(()=>{this.updatePagination(),this._alignInkBarToSelectedTab(),this._changeDetectorRef.markForCheck()}))}updatePagination(){this._checkPaginationEnabled(),this._checkScrollingControls(),this._updateTabScrollPosition()}get focusIndex(){return this._keyManager?this._keyManager.activeItemIndex:0}set focusIndex(e){!this._isValidIndex(e)||this.focusIndex===e||!this._keyManager||this._keyManager.setActiveItem(e)}_isValidIndex(e){if(!this._items)return!0;const i=this._items?this._items.toArray()[e]:null;return!!i&&!i.disabled}_setTabFocus(e){if(this._showPaginationControls&&this._scrollToLabel(e),this._items&&this._items.length){this._items.toArray()[e].focus();const i=this._tabListContainer.nativeElement;i.scrollLeft="ltr"==this._getLayoutDirection()?0:i.scrollWidth-i.offsetWidth}}_getLayoutDirection(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}_updateTabScrollPosition(){if(this.disablePagination)return;const e=this.scrollDistance,i="ltr"===this._getLayoutDirection()?-e:e;this._tabList.nativeElement.style.transform=`translateX(${Math.round(i)}px)`,(this._platform.TRIDENT||this._platform.EDGE)&&(this._tabListContainer.nativeElement.scrollLeft=0)}get scrollDistance(){return this._scrollDistance}set scrollDistance(e){this._scrollTo(e)}_scrollHeader(e){return this._scrollTo(this._scrollDistance+("before"==e?-1:1)*this._tabListContainer.nativeElement.offsetWidth/3)}_handlePaginatorClick(e){this._stopInterval(),this._scrollHeader(e)}_scrollToLabel(e){if(this.disablePagination)return;const i=this._items?this._items.toArray()[e]:null;if(!i)return;const n=this._tabListContainer.nativeElement.offsetWidth,{offsetLeft:o,offsetWidth:r}=i.elementRef.nativeElement;let l,d;"ltr"==this._getLayoutDirection()?(l=o,d=l+r):(d=this._tabList.nativeElement.offsetWidth-o,l=d-r);const L=this.scrollDistance,Y=this.scrollDistance+n;l<L?this.scrollDistance-=L-l+60:d>Y&&(this.scrollDistance+=d-Y+60)}_checkPaginationEnabled(){if(this.disablePagination)this._showPaginationControls=!1;else{const e=this._tabList.nativeElement.scrollWidth>this._elementRef.nativeElement.offsetWidth;e||(this.scrollDistance=0),e!==this._showPaginationControls&&this._changeDetectorRef.markForCheck(),this._showPaginationControls=e}}_checkScrollingControls(){this.disablePagination?this._disableScrollAfter=this._disableScrollBefore=!0:(this._disableScrollBefore=0==this.scrollDistance,this._disableScrollAfter=this.scrollDistance==this._getMaxScrollDistance(),this._changeDetectorRef.markForCheck())}_getMaxScrollDistance(){return this._tabList.nativeElement.scrollWidth-this._tabListContainer.nativeElement.offsetWidth||0}_alignInkBarToSelectedTab(){const e=this._items&&this._items.length?this._items.toArray()[this.selectedIndex]:null,i=e?e.elementRef.nativeElement:null;i?this._inkBar.alignToElement(i):this._inkBar.hide()}_stopInterval(){this._stopScrolling.next()}_handlePaginatorPress(e,i){i&&null!=i.button&&0!==i.button||(this._stopInterval(),(0,Q.H)(650,100).pipe((0,p.R)((0,x.T)(this._stopScrolling,this._destroyed))).subscribe(()=>{const{maxScrollDistance:n,distance:o}=this._scrollHeader(e);(0===o||o>=n)&&this._stopInterval()}))}_scrollTo(e){if(this.disablePagination)return{maxScrollDistance:0,distance:0};const i=this._getMaxScrollDistance();return this._scrollDistance=Math.max(0,Math.min(i,e)),this._scrollDistanceChanged=!0,this._checkScrollingControls(),{maxScrollDistance:i,distance:this._scrollDistance}}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(R.rL),t.Y36(f.Is,8),t.Y36(t.R0b),t.Y36(T.t4),t.Y36(u.Qb,8))},a.\u0275dir=t.lG2({type:a,inputs:{disablePagination:"disablePagination"}}),a})(),me=(()=>{class a extends ge{constructor(e,i,n,o,r,l,d){super(e,i,n,o,r,l,d),this._disableRipple=!1}get disableRipple(){return this._disableRipple}set disableRipple(e){this._disableRipple=(0,g.Ig)(e)}_itemSelected(e){e.preventDefault()}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(R.rL),t.Y36(f.Is,8),t.Y36(t.R0b),t.Y36(T.t4),t.Y36(u.Qb,8))},a.\u0275dir=t.lG2({type:a,inputs:{disableRipple:"disableRipple"},features:[t.qOj]}),a})(),ve=(()=>{class a extends me{constructor(e,i,n,o,r,l,d){super(e,i,n,o,r,l,d)}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(R.rL),t.Y36(f.Is,8),t.Y36(t.R0b),t.Y36(T.t4),t.Y36(u.Qb,8))},a.\u0275cmp=t.Xpm({type:a,selectors:[["cc-tab-header"]],contentQueries:function(e,i,n){if(1&e&&t.Suo(n,N,4),2&e){let o;t.iGM(o=t.CRH())&&(i._items=o)}},viewQuery:function(e,i){if(1&e&&(t.Gf(M,7),t.Gf(V,7),t.Gf(q,7),t.Gf(z,5),t.Gf(X,5)),2&e){let n;t.iGM(n=t.CRH())&&(i._inkBar=n.first),t.iGM(n=t.CRH())&&(i._tabListContainer=n.first),t.iGM(n=t.CRH())&&(i._tabList=n.first),t.iGM(n=t.CRH())&&(i._nextPaginator=n.first),t.iGM(n=t.CRH())&&(i._previousPaginator=n.first)}},hostAttrs:[1,"cc-tab-header"],hostVars:4,hostBindings:function(e,i){2&e&&t.ekj("cc-tab-header-pagination-controls-enabled",i._showPaginationControls)("cc-tab-header-rtl","rtl"==i._getLayoutDirection())},inputs:{selectedIndex:"selectedIndex"},outputs:{selectFocusedIndex:"selectFocusedIndex",indexFocused:"indexFocused"},features:[t.qOj],ngContentSelectors:k,decls:13,vars:8,consts:[["aria-hidden","true","mat-ripple","",1,"cc-tab-header-pagination","cc-tab-header-pagination-before","cc-elevation-z4",3,"matRippleDisabled","click","mousedown","touchend"],["previousPaginator",""],[1,"cc-tab-header-pagination-chevron"],[1,"cc-tab-label-container",3,"keydown"],["tabListContainer",""],["role","tablist",1,"cc-tab-list",3,"cdkObserveContent"],["tabList",""],[1,"cc-tab-labels"],["aria-hidden","true","mat-ripple","",1,"cc-tab-header-pagination","cc-tab-header-pagination-after","cc-elevation-z4",3,"matRippleDisabled","mousedown","click","touchend"],["nextPaginator",""]],template:function(e,i){1&e&&(t.F$t(),t.TgZ(0,"div",0,1),t.NdJ("click",function(){return i._handlePaginatorClick("before")})("mousedown",function(o){return i._handlePaginatorPress("before",o)})("touchend",function(){return i._stopInterval()}),t._UZ(2,"div",2),t.qZA(),t.TgZ(3,"div",3,4),t.NdJ("keydown",function(o){return i._handleKeydown(o)}),t.TgZ(5,"div",5,6),t.NdJ("cdkObserveContent",function(){return i._onContentChanges()}),t.TgZ(7,"div",7),t.Hsn(8),t.qZA(),t._UZ(9,"cc-ink-bar"),t.qZA()(),t.TgZ(10,"div",8,9),t.NdJ("mousedown",function(o){return i._handlePaginatorPress("after",o)})("click",function(){return i._handlePaginatorClick("after")})("touchend",function(){return i._stopInterval()}),t._UZ(12,"div",2),t.qZA()),2&e&&(t.ekj("cc-tab-header-pagination-disabled",i._disableScrollBefore),t.Q6J("matRippleDisabled",i._disableScrollBefore||i.disableRipple),t.xp6(5),t.ekj("_cc-animation-noopable","NoopAnimations"===i._animationMode),t.xp6(5),t.ekj("cc-tab-header-pagination-disabled",i._disableScrollAfter),t.Q6J("matRippleDisabled",i._disableScrollAfter||i.disableRipple))},directives:[b.wG,P.wD,M],encapsulation:2}),a})(),Te=0;class ye{constructor(){this.index=0}}const Ie=(0,b.pj)((0,b.Kr)(class{constructor(a){this._elementRef=a}}),"primary");let xe=(()=>{class a extends Ie{constructor(e,i,n,o){var r;super(e),this._changeDetectorRef=i,this._animationMode=o,this._tabs=new t.n_E,this._indexToSelect=0,this._tabBodyWrapperHeight=0,this._tabsSubscription=C.w0.EMPTY,this._tabLabelSubscription=C.w0.EMPTY,this._dynamicHeight=!0,this._selectedIndex=null,this.headerPosition="above",this._animationDuration="300ms",this._contentTabIndex=null,this.selectedIndexChange=new t.vpe,this.focusChange=new t.vpe,this.animationDone=new t.vpe,this.selectedTabChange=new t.vpe(!0),this._groupId=Te++,this.animationDuration=n&&n.animationDuration?n.animationDuration:"500ms",this.disablePagination=!(!n||null==n.disablePagination)&&n.disablePagination,this.dynamicHeight=!(!n||null==n.dynamicHeight)&&n.dynamicHeight,this.contentTabIndex=null!==(r=null==n?void 0:n.contentTabIndex)&&void 0!==r?r:null}get dynamicHeight(){return this._dynamicHeight}set dynamicHeight(e){this._dynamicHeight=(0,g.Ig)(e)}get selectedIndex(){return this._selectedIndex}set selectedIndex(e){this._indexToSelect=(0,g.su)(e,null)}get animationDuration(){return this._animationDuration}set animationDuration(e){this._animationDuration=/^\d+$/.test(e)?e+"ms":e}get contentTabIndex(){return this._contentTabIndex}set contentTabIndex(e){this._contentTabIndex=(0,g.su)(e,null)}get backgroundColor(){return this._backgroundColor}set backgroundColor(e){const i=this._elementRef.nativeElement;i.classList.remove(`cc-background-${this.backgroundColor}`),e&&i.classList.add(`cc-background-${e}`),this._backgroundColor=e}ngAfterContentChecked(){const e=this._indexToSelect=this._clampTabIndex(this._indexToSelect);if(this._selectedIndex!=e){const i=null==this._selectedIndex;if(!i){this.selectedTabChange.emit(this._createChangeEvent(e));const n=this._tabBodyWrapper.nativeElement;n.style.minHeight=n.clientHeight+"px"}Promise.resolve().then(()=>{this._tabs.forEach((n,o)=>n.isActive=o===e),i||(this.selectedIndexChange.emit(e),this._tabBodyWrapper.nativeElement.style.minHeight="")})}this._tabs.forEach((i,n)=>{i.position=n-e,null!=this._selectedIndex&&0==i.position&&!i.origin&&(i.origin=e-this._selectedIndex)}),this._selectedIndex!==e&&(this._selectedIndex=e,this._changeDetectorRef.markForCheck())}ngAfterContentInit(){this._subscribeToAllTabChanges(),this._subscribeToTabLabels(),this._tabsSubscription=this._tabs.changes.subscribe(()=>{if(this._clampTabIndex(this._indexToSelect)===this._selectedIndex){const i=this._tabs.toArray();for(let n=0;n<i.length;n++)if(i[n].isActive){this._indexToSelect=this._selectedIndex=n;break}}this._changeDetectorRef.markForCheck()})}_subscribeToAllTabChanges(){this._allTabs.changes.pipe((0,B.O)(this._allTabs)).subscribe(e=>{this._tabs.reset(e.filter(i=>i._closestTabGroup===this||!i._closestTabGroup)),this._tabs.notifyOnChanges()})}ngOnDestroy(){this._tabs.destroy(),this._tabsSubscription.unsubscribe(),this._tabLabelSubscription.unsubscribe()}realignInkBar(){this._tabHeader&&this._tabHeader._alignInkBarToSelectedTab()}focusTab(e){const i=this._tabHeader;i&&(i.focusIndex=e)}_focusChanged(e){this.focusChange.emit(this._createChangeEvent(e))}_createChangeEvent(e){const i=new ye;return i.index=e,this._tabs&&this._tabs.length&&(i.tab=this._tabs.toArray()[e]),i}_subscribeToTabLabels(){this._tabLabelSubscription&&this._tabLabelSubscription.unsubscribe(),this._tabLabelSubscription=(0,x.T)(...this._tabs.map(e=>e._stateChanges)).subscribe(()=>this._changeDetectorRef.markForCheck())}_clampTabIndex(e){return Math.min(this._tabs.length-1,Math.max(e||0,0))}_getTabLabelId(e){return`cc-tab-label-${this._groupId}-${e}`}_getTabContentId(e){return`cc-tab-content-${this._groupId}-${e}`}_setTabBodyWrapperHeight(e){if(!this._dynamicHeight||!this._tabBodyWrapperHeight)return;const i=this._tabBodyWrapper.nativeElement;i.style.height=this._tabBodyWrapperHeight+"px",this._tabBodyWrapper.nativeElement.offsetHeight&&(i.style.height=e+"px")}_removeTabBodyWrapperHeight(){const e=this._tabBodyWrapper.nativeElement;this._tabBodyWrapperHeight=e.clientHeight,e.style.height="",this.animationDone.emit()}_handleClick(e,i,n){e.disabled||(this.selectedIndex=i.focusIndex=n)}_getTabIndex(e,i){return e.disabled?null:this.selectedIndex===i?0:-1}_tabFocusChanged(e,i){e&&(this._tabHeader.focusIndex=i)}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(H,8),t.Y36(u.Qb,8))},a.\u0275dir=t.lG2({type:a,inputs:{dynamicHeight:"dynamicHeight",selectedIndex:"selectedIndex",headerPosition:"headerPosition",animationDuration:"animationDuration",contentTabIndex:"contentTabIndex",disablePagination:"disablePagination",backgroundColor:"backgroundColor"},outputs:{selectedIndexChange:"selectedIndexChange",focusChange:"focusChange",animationDone:"animationDone",selectedTabChange:"selectedTabChange"},features:[t.qOj]}),a})(),De=(()=>{class a extends xe{constructor(e,i,n,o){super(e,i,n,o)}}return a.\u0275fac=function(e){return new(e||a)(t.Y36(t.SBq),t.Y36(t.sBO),t.Y36(H,8),t.Y36(u.Qb,8))},a.\u0275cmp=t.Xpm({type:a,selectors:[["cc-tab-group"]],contentQueries:function(e,i,n){if(1&e&&t.Suo(n,w,5),2&e){let o;t.iGM(o=t.CRH())&&(i._allTabs=o)}},viewQuery:function(e,i){if(1&e&&(t.Gf(ee,5),t.Gf(te,5)),2&e){let n;t.iGM(n=t.CRH())&&(i._tabBodyWrapper=n.first),t.iGM(n=t.CRH())&&(i._tabHeader=n.first)}},hostAttrs:[1,"cc-tab-group"],hostVars:4,hostBindings:function(e,i){2&e&&t.ekj("cc-tab-group-dynamic-height",i.dynamicHeight)("cc-tab-group-inverted-header","below"===i.headerPosition)},inputs:{color:"color",disableRipple:"disableRipple"},exportAs:["ccTabGroup"],features:[t._Bn([{provide:A,useExisting:a}]),t.qOj],decls:6,vars:7,consts:[[3,"selectedIndex","disableRipple","disablePagination","indexFocused","selectFocusedIndex"],["tabHeader",""],["class","cc-tab-label mat-focus-indicator","role","tab","ccTabLabelWrapper","","mat-ripple","","cdkMonitorElementFocus","",3,"id","cc-tab-label-active","disabled","matRippleDisabled","click","cdkFocusChange",4,"ngFor","ngForOf"],[1,"cc-tab-body-wrapper"],["tabBodyWrapper",""],["role","tabpanel",3,"id","cc-tab-body-active","content","position","origin","animationDuration","_onCentered","_onCentering",4,"ngFor","ngForOf"],["role","tab","ccTabLabelWrapper","","mat-ripple","","cdkMonitorElementFocus","",1,"cc-tab-label","mat-focus-indicator",3,"id","disabled","matRippleDisabled","click","cdkFocusChange"],[1,"cc-tab-label-content"],[3,"ngIf"],[3,"cdkPortalOutlet"],["role","tabpanel",3,"id","content","position","origin","animationDuration","_onCentered","_onCentering"]],template:function(e,i){1&e&&(t.TgZ(0,"cc-tab-header",0,1),t.NdJ("indexFocused",function(o){return i._focusChanged(o)})("selectFocusedIndex",function(o){return i.selectedIndex=o}),t.YNc(2,se,4,14,"div",2),t.qZA(),t.TgZ(3,"div",3,4),t.YNc(5,oe,1,9,"cc-tab-body",5),t.qZA()),2&e&&(t.Q6J("selectedIndex",i.selectedIndex||0)("disableRipple",i.disableRipple)("disablePagination",i.disablePagination),t.xp6(2),t.Q6J("ngForOf",i._tabs),t.xp6(1),t.ekj("_mat-animation-noopable","NoopAnimations"===i._animationMode),t.xp6(2),t.Q6J("ngForOf",i._tabs))},directives:[ve,S,m.sg,N,b.wG,y.kH,m.O5,h.Pl],encapsulation:2}),a})();(0,b.sb)((0,b.Kr)((0,b.Id)(class{})));let Re=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=t.oAB({type:a}),a.\u0275inj=t.cJS({imports:[[m.ez,b.BQ,h.eL,b.si,P.Q8,y.rt],b.BQ]}),a})()}}]);
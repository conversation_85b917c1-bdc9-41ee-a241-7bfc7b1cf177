(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_rpa_rpa_module_ts-node_modules_moment_locale_sync_recursive_"],{51316:(Ce,G,i)=>{"use strict";i.r(G),i.d(G,{FollowupCanceledMaidsModule:()=>A});var u=i(69808),g=i(93075),k=i(88087),L=i(65868),t=i(88476),y=i(62764),o=i(28172),c=i(43687),l=i(26523),O=i(34378),F=i(82599),I=i(45834),ie=i(97582),w=i(63900),se=i(48966),n=i(5e3),q=i(40520),de=i(43604);let D=(()=>{class _{constructor(C){this.http=C}getExistMaids(C){return this.http.get(de.b.MaidsInGdrfaReport,{params:new q.LE({fromObject:Object.assign(Object.assign({},this.getParamsObj(C)),{status:"Exist"})})})}getRemovedMaids(C){return this.http.get(de.b.MaidsInGdrfaReport,{params:new q.LE({fromObject:Object.assign(Object.assign({},this.getParamsObj(C)),{status:"Removed"})})})}getHouseMaidNumbers(C){return this.http.get(`${de.b.housemaidNumbers}/${C}`)}confirmHousemaidRemoval(C,j){return this.http.post(`${de.b.removalConfirmation}/${C}`,{maidClearedStatus:j})}getParamsObj(C){return Object.assign({page:C.params.page,size:C.params.size},C.search)}}return _.\u0275fac=function(C){return new(C||_)(n.LFG(q.eN))},_.\u0275prov=n.Yz7({token:_,factory:_.\u0275fac}),_})();var B=i(21799),x=i(69287);function Y(_,f){if(1&_){const C=n.EpF();n.TgZ(0,"div",1)(1,"cc-dialog-header")(2,"h1",2),n._uU(3),n.qZA(),n._UZ(4,"a",3),n.qZA(),n.TgZ(5,"cc-dialog-content")(6,"div",4)(7,"p",5),n._uU(8,"WhatsApp Number:"),n.qZA(),n.TgZ(9,"p",5),n._uU(10),n.qZA(),n.TgZ(11,"button",6),n.NdJ("click",function(){return n.CHM(C),n.oxw().showCopiedMessage()}),n.TgZ(12,"cc-icon",7),n._uU(13,"content_copy"),n.qZA()()(),n.TgZ(14,"div",4)(15,"p",5),n._uU(16,"UAE Number:"),n.qZA(),n.TgZ(17,"p",5),n._uU(18),n.qZA(),n.TgZ(19,"button",6),n.NdJ("click",function(){return n.CHM(C),n.oxw().showCopiedMessage()}),n.TgZ(20,"cc-icon",7),n._uU(21,"content_copy"),n.qZA()()()(),n.TgZ(22,"cc-dialog-actions")(23,"button",8),n._uU(24,"Close"),n.qZA()()()}if(2&_){const C=f.ngIf,j=n.oxw();n.xp6(3),n.Oqu(j.housemaidName),n.xp6(7),n.Oqu(C.whatsAppPhoneNumber),n.xp6(1),n.Q6J("cdkCopyToClipboard",C.whatsAppPhoneNumber),n.xp6(7),n.Oqu(C.phoneNumber),n.xp6(1),n.Q6J("cdkCopyToClipboard",C.phoneNumber)}}let e=(()=>{class _{constructor(C,j,V){this.data=C,this.service=j,this.notification=V,this.housemaidName="",this.housemaidName=this.data.housemaidName,this.housemaidNumber$=this.service.getHouseMaidNumbers(C.housemaidId)}showCopiedMessage(){this.notification.notifySuccess("copied to the clipboard successfully")}ngOnInit(){}}return _.\u0275fac=function(C){return new(C||_)(n.Y36(se.WI),n.Y36(D),n.Y36(B.zg))},_.\u0275cmp=n.Xpm({type:_,selectors:[["ng-component"]],decls:2,vars:3,consts:[["cc-std-dialog","",4,"ngIf"],["cc-std-dialog",""],["cc-dialog-title","",2,"margin","0 auto"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close",""],[1,"form-group","d-flex","justify-content-around","align-items-center"],[1,"col-5"],["cdkCopyToClipboard","","cc-icon-button","","color","warn",3,"cdkCopyToClipboard","click"],[2,"font-size","30px"],["cc-raised-button","","cc-dialog-close",""]],template:function(C,j){1&C&&(n.YNc(0,Y,25,5,"div",0),n.ALo(1,"async")),2&C&&n.Q6J("ngIf",n.lcZ(1,1,j.housemaidNumber$))},directives:[u.O5,F.iK,F.Cj,F.Zb,L.uu,F.fX,F.zn,F.kL,x.i3,I.Q9,F.Zu],pipes:[u.Ov],encapsulation:2}),_})();var K=i(18505),Pe=i(92431);let pe=class{constructor(f,C,j,V){this.data=f,this.dialogRef=C,this.service=j,this.notification=V,this.maidClearedStatus="Maid_Left_The_Country",this.housemaidRecordId=this.data.housemaidRecordId}onSave(){this.service.confirmHousemaidRemoval(this.housemaidRecordId,this.maidClearedStatus).pipe((0,K.b)(()=>{this.dialogRef.close({cleared:!0}),this.notification.notifySuccess("Has been cleared successfully.")})).subscribe()}ngOnDestroy(){}};pe.\u0275fac=function(f){return new(f||pe)(n.Y36(se.WI),n.Y36(se.so),n.Y36(D),n.Y36(B.zg))},pe.\u0275cmp=n.Xpm({type:pe,selectors:[["ng-component"]],decls:18,vars:1,consts:[["cc-std-dialog","",1,"py-2"],["cc-dialog-title",""],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close",""],[1,"mb-1"],[1,"d-flex","flex-column","ml-3",3,"ngModel","ngModelChange"],["value","Maid_Left_The_Country"],["value","Maid_Found_A_Sponsor"],[1,"d-flex","gap-2"],["cc-flat-button","","color","accent","type","button",3,"click"],["type","button","cc-flat-button","","cc-dialog-close",""]],template:function(f,C){1&f&&(n.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),n._uU(3,"Maid Cleared"),n.qZA(),n._UZ(4,"a",2),n.qZA(),n.TgZ(5,"cc-dialog-content")(6,"h5",3),n._uU(7,"What is the maid status?"),n.qZA(),n.TgZ(8,"cc-radio-group",4),n.NdJ("ngModelChange",function(V){return C.maidClearedStatus=V}),n.TgZ(9,"cc-radio-button",5),n._uU(10,"Maid left the country "),n.qZA(),n.TgZ(11,"cc-radio-button",6),n._uU(12," Maid found a sponsor "),n.qZA()()(),n.TgZ(13,"cc-dialog-actions",7)(14,"button",8),n.NdJ("click",function(){return C.onSave()}),n._uU(15," Save "),n.qZA(),n.TgZ(16,"button",9),n._uU(17,"Close"),n.qZA()()()),2&f&&(n.xp6(8),n.Q6J("ngModel",C.maidClearedStatus))},directives:[F.iK,F.Cj,F.Zb,L.uu,F.fX,F.zn,F.kL,Pe.u6,g.JJ,g.On,Pe.UF,F.Zu],encapsulation:2,changeDetection:0}),pe=(0,ie.gn)([t.kG],pe);const he={search:{search:{},params:{page:0,size:20,sort:""}}};var U=i(65620);const ce=(0,U.PH)("[FOLLOWUP_CANCELED_MAIDS] update Search Fields",(0,U.Ky)()),ue=(0,U.PH)("[FOLLOWUP_CANCELED_MAIDS] reset Search Fields"),_e="followup-canceled-maids",$=(0,U.Lq)(he,(0,U.on)(ce,(_,{payload:f})=>Object.assign(Object.assign({},_),{search:Object.assign(Object.assign({},_.search),{search:Object.assign(Object.assign({},_.search.search),f.search),params:f.params})})),(0,U.on)(ue,_=>Object.assign(Object.assign({},_),{search:Object.assign({},he.search)}))),ae=(0,U.ZF)(_e),ee=(0,U.P1)(ae,_=>_.search);let M=(()=>{class _ extends t.il{constructor(C){super(C),this.search$=this.store.select(ee)}updateSearchState(C){this.store.dispatch(ce({payload:C}))}resetSearchState(){this.store.dispatch(ue())}pickListsCodes(){return[]}resetState(){}}return _.\u0275fac=function(C){return new(C||_)(n.LFG(U.yh))},_.\u0275prov=n.Yz7({token:_,factory:_.\u0275fac,providedIn:"root"}),_})();var ve,Te=i(77579),me=i(54004),ge=i(82722),Ae=i(8188);class le{constructor(f,C,j){this.fb=f,this.store=C,this.picklist=j,this.search=new n.vpe,this.reset=new n.vpe,ve.set(this,new Te.x),this.typesOptions=[{id:"Normal",text:"Maids.cc"},{id:"MAID_VISA",text:"Maids.visa"}],this.nationalities=this.picklist.getPicklist({code:"nationalities",page:0,pageSize:100}).pipe((0,me.U)(V=>V.map(te=>({id:te.id,text:te.label})))),this.form=this.fb.group({housemaidType:"",maidName:"",nationalityId:""})}ngOnInit(){this.store.search$.pipe((0,K.b)(f=>{var C,j;return this.form.patchValue({housemaidType:null===(C=f.search)||void 0===C?void 0:C.housemaidType,maidName:null===(j=f.search)||void 0===j?void 0:j.maidName,nationalityId:null==f?void 0:f.search.nationalityId})}),(0,ge.R)((0,ie.Q_)(this,ve,"f"))).subscribe()}onSearch(){if(this.form.value){const f=this.filterTruthyProps(this.form.value);this.search.emit(f)}}onReset(){this.reset.emit(),this.form.reset()}ngOnDestroy(){(0,ie.Q_)(this,ve,"f").next(),(0,ie.Q_)(this,ve,"f").complete()}filterTruthyProps(f){return Object.fromEntries(Object.entries(f).filter(([C,j])=>j))}}ve=new WeakMap,le.\u0275fac=function(f){return new(f||le)(n.Y36(g.qu),n.Y36(M),n.Y36(Ae.Ab))},le.\u0275cmp=n.Xpm({type:le,selectors:[["search-filter"]],outputs:{search:"search",reset:"reset"},decls:22,vars:5,consts:[[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[2,"margin-right","2px"],[1,"row",3,"formGroup"],[1,"col-md-6"],["formControlName","maidName","label","Name:","placeholder","Enter Maid's Name",3,"keyup.enter"],["label","Select nationality","formControlName","nationalityId",3,"data"],["label","Select Type","formControlName","housemaidType",3,"data"],[1,"col-md-6","d-flex","justify-content-center","gap-2","py-2"],["cc-raised-button","","color","accent","type","button",2,"display","block","height","fit-content","padding-block","0.2rem","padding-inline","2rem",3,"click"],["cc-raised-button","","type","button",2,"display","block","height","fit-content","padding-block",".2rem","padding-inline","2rem","background-color","#808080","color","#fff",3,"click"]],template:function(f,C){1&f&&(n.TgZ(0,"cc-accordion",0)(1,"cc-panel",1)(2,"cc-panel-title",2)(3,"cc-icon",3),n._uU(4,"filter_alt"),n.qZA(),n.TgZ(5,"span"),n._uU(6,"Filter"),n.qZA()(),n.TgZ(7,"cc-panel-body")(8,"div")(9,"form",4)(10,"div",5)(11,"cc-input",6),n.NdJ("keyup.enter",function(){return C.onSearch()}),n.qZA()(),n.TgZ(12,"div",5),n._UZ(13,"cc-select",7),n.ALo(14,"async"),n.qZA(),n.TgZ(15,"div",5),n._UZ(16,"cc-select",8),n.qZA(),n.TgZ(17,"div",9)(18,"button",10),n.NdJ("click",function(){return C.onSearch()}),n._uU(19," Search "),n.qZA(),n.TgZ(20,"button",11),n.NdJ("click",function(){return C.onReset()}),n._uU(21," Reset "),n.qZA()()()()()()()),2&f&&(n.xp6(9),n.Q6J("formGroup",C.form),n.xp6(4),n.Q6J("data",n.lcZ(14,3,C.nationalities)),n.xp6(3),n.Q6J("data",C.typesOptions))},directives:[O.I,O.CW,O.LL,I.Q9,O.G9,g._Y,g.JL,g.sg,c.G,g.JJ,g.u,l.jB,L.uu],pipes:[u.Ov],encapsulation:2,changeDetection:0});var re=i(43277);function Se(_,f){if(1&_){const C=n.EpF();n.TgZ(0,"button",9),n.NdJ("click",function(){const te=n.CHM(C).rowData;return n.oxw(2).openHousemaidNumbersDialog(te.housemaid.name,te.housemaid.id)}),n._uU(1," view "),n.qZA()}}function ye(_,f){if(1&_){const C=n.EpF();n.TgZ(0,"div",11)(1,"cc-menu",12)(2,"button",13),n.NdJ("click",function(){n.CHM(C);const V=n.oxw().rowData;return n.oxw(2).openMaidClearedForm(V.id)}),n._uU(3," Maid Cleared "),n.qZA()()()}if(2&_){const C=n.oxw(3);n.xp6(1),n.Q6J("ccTriggerButton",C.mainButton)}}function be(_,f){1&_&&n.YNc(0,ye,4,1,"div",10),2&_&&n.Q6J("ngIf",!f.rowData.contactedUs)}const fe=function(){return[]},S=function(){return[10,20,30,40,50]},T=function(_,f){return{"-":_,actions:f}};function m(_,f){if(1&_){const C=n.EpF();n.ynx(0),n.TgZ(1,"cc-datagrid",5),n.NdJ("page",function(V){return n.CHM(C),n.oxw().handleNextPage(V)}),n.qZA(),n.YNc(2,Se,2,0,"ng-template",6,7,n.W1O),n.YNc(4,be,1,1,"ng-template",6,8,n.W1O),n.BQk()}if(2&_){const C=f.ngIf,j=n.MAs(3),V=n.MAs(5),te=n.oxw();n.xp6(1),n.Q6J("loading",!C)("data",C.content||n.DdM(11,fe))("columns",te.gridCols)("length",C.totalElements)("pageOnFront",!1)("pageIndex",C.number)("pageSize",C.size)("pageSizeOptions",n.DdM(12,S))("cellTemplate",n.WLB(13,T,j,V)),n.xp6(1),n.Q6J("ccGridCell",C.content||n.DdM(16,fe)),n.xp6(2),n.Q6J("ccGridCell",null!=C?C.content:n.DdM(17,fe))}}const R=function(_,f){return{"red-btn":_,"black-btn":f}};let N=class{constructor(f,C,j,V){this.service=f,this.storeService=C,this.dialog=j,this.cdr=V,this.gridCols=[{field:"actions",header:"actions"},{field:"housemaid.name",header:"Name"},{field:"housemaid.nationality.label",header:"Nationality"},{field:"-",header:"Phone Number"},{field:"overstayFees",header:"Overstay fees"},{field:"housemaid.dateOfTermination",header:"Termination date"},{field:"creationDate",header:"Create date"}],this.mainButton={icon:"menu",type:"icon",color:"primary"},this.type="existMaids"}ngOnInit(){this.storeService.search$.subscribe(f=>this.search=f),this.loadMaids()}handleNextPage(f){this.storeService.updateSearchState({params:{page:f.pageIndex,size:f.pageSize}}),this.loadMaids()}onSearch(f){this.storeService.updateSearchState({search:f,params:he.search.params}),this.loadMaids()}onReset(){this.storeService.resetSearchState(),this.loadMaids()}loadMaids(){this.data$=this.storeService.search$.pipe((0,w.w)(f=>"removedMaids"===this.type?this.service.getRemovedMaids(f):this.service.getExistMaids(f))),this.cdr.detectChanges()}switchMaidType(f){this.type=f,this.loadMaids()}openHousemaidNumbersDialog(f,C){this.dialog.originalOpen(e,{width:"50%",data:{housemaidId:C,housemaidName:f}})}openMaidClearedForm(f){this.dialog.originalOpen(pe,{id:"maid-cleared",data:{housemaidRecordId:f}}).afterClosed().subscribe(()=>this.loadMaids())}};N.\u0275fac=function(f){return new(f||N)(n.Y36(D),n.Y36(M),n.Y36(F.uY),n.Y36(n.sBO))},N.\u0275cmp=n.Xpm({type:N,selectors:[["ng-component"]],decls:8,vars:11,consts:[[1,"action-group","d-flex","gap-2","my-5"],["id","canceled","cc-raised-button","","type","button",3,"ngClass","click"],["id","removed","cc-raised-button","","type","button",3,"ngClass","click"],[3,"search","reset"],[4,"ngIf"],[1,"my-2",3,"loading","data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","cellTemplate","page"],[3,"ccGridCell"],["numberTmp",""],["maidClearedTmp",""],["cc-flat-button","",2,"background-color","green","color","white",3,"click"],["class","row justify-content-center align-items-center",4,"ngIf"],[1,"row","justify-content-center","align-items-center"],[2,"font-size","20px","font-weight","900",3,"ccTriggerButton"],["cc-button","","cc-menu-item","",3,"click"]],template:function(f,C){1&f&&(n.TgZ(0,"div",0)(1,"button",1),n.NdJ("click",function(){return C.switchMaidType("existMaids")}),n._uU(2," Canceled Maids Under Our Sponsorship "),n.qZA(),n.TgZ(3,"button",2),n.NdJ("click",function(){return C.switchMaidType("removedMaids")}),n._uU(4," Removed From GDRFA Report Maids "),n.qZA()(),n.TgZ(5,"search-filter",3),n.NdJ("search",function(V){return C.onSearch(V)})("reset",function(){return C.onReset()}),n.qZA(),n.YNc(6,m,6,18,"ng-container",4),n.ALo(7,"async")),2&f&&(n.xp6(1),n.Q6J("ngClass",n.WLB(5,R,"existMaids"===C.type,"removedMaids"===C.type)),n.xp6(2),n.Q6J("ngClass",n.WLB(8,R,"removedMaids"===C.type,"existMaids"===C.type)),n.xp6(3),n.Q6J("ngIf",n.lcZ(7,3,C.data$)))},directives:[L.uu,u.mk,le,u.O5,y.Ge,y.VC,re.OL,re.Y],pipes:[u.Ov],encapsulation:2,changeDetection:0}),N=(0,ie.gn)([t.kG],N);let J=(()=>{class _{}return _.\u0275fac=function(C){return new(C||_)},_.\u0275mod=n.oAB({type:_}),_.\u0275inj=n.cJS({imports:[[U.Aw.forFeature(_e,$)],U.Aw]}),_})();var z=i(1402);const H=[{path:"",component:N,data:{label:" Follow up with canceled maids "}}];let A=(()=>{class _{}return _.\u0275fac=function(C){return new(C||_)},_.\u0275mod=n.oAB({type:_}),_.\u0275inj=n.cJS({providers:[D],imports:[[u.ez,g.UX,g.u5,t.gZ,z.Bz.forChild(H),J,t.n_,k.pS,y.Gz,l.lK,L.S6,F.I8,O.yU,I.L,o.To,c.f,y.Gz,re.v9,Pe.XD,x.Iq],z.Bz]}),_})()},30808:(Ce,G,i)=>{"use strict";i.d(G,{j:()=>fe});var u=i(97582),g=i(88476),k=i(48966),L=i(54004),t=i(5e3),y=i(76593),o=i(82599),c=i(93075),l=i(26523),O=i(65868);let F=(()=>{class S{constructor(m,R,N){this.store=m,this.dialogRef=R,this.data=N,this.selectedProcess="",this.processList=[]}ngOnInit(){this.store.loadRoboticProcessList(""),this.store.roboticProcessList$.pipe((0,L.U)(m=>m.map(R=>({id:R.id,text:R.name})))).subscribe({next:m=>{this.processList=m}})}onSave(){this.store.editPcProcess(this.data.pcId,this.data.processId,+this.selectedProcess),setTimeout(()=>{this.dialogRef.close()},100)}}return S.\u0275fac=function(m){return new(m||S)(t.Y36(y.O),t.Y36(k.so),t.Y36(k.WI))},S.\u0275cmp=t.Xpm({type:S,selectors:[["ng-component"]],decls:14,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-close-button","","cc-dialog-close",""],["form","ngForm"],["label","processes","name","process",3,"data","ngModel","required","ngModelChange"],["cc-flat-button","","cc-dialog-close","",1,"px-5"],["cc-raised-button","","color","primary",1,"px-5",3,"disabled","click"]],template:function(m,R){if(1&m&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3,"Edit PC Process"),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"cc-dialog-content")(6,"form",null,4)(8,"cc-select",5),t.NdJ("ngModelChange",function(J){return R.selectedProcess=J}),t.qZA()()(),t.TgZ(9,"cc-dialog-actions")(10,"button",6),t._uU(11,"Close"),t.qZA(),t.TgZ(12,"button",7),t.NdJ("click",function(){return R.onSave()}),t._uU(13," Save "),t.qZA()()()),2&m){const N=t.MAs(7);t.xp6(2),t.Q6J("align","center"),t.xp6(6),t.Q6J("data",R.processList)("ngModel",R.selectedProcess)("required",!0),t.xp6(4),t.Q6J("disabled",!N.valid)}},directives:[o.iK,o.Cj,o.Zb,o.fX,o.zn,o.kL,c._Y,c.JL,c.F,l.jB,c.JJ,c.On,c.Q7,o.Zu,O.uu],encapsulation:2,changeDetection:0}),S})();var I=i(61135),ie=i(78372),w=i(18505),se=i(43687),n=i(62764),q=i(69808),de=i(4882);function D(S,T){if(1&S&&(t.ynx(0),t.TgZ(1,"form",18),t._UZ(2,"cc-checkbox",19),t.qZA(),t.BQk()),2&S){const m=t.oxw(2);t.xp6(1),t.Q6J("formGroup",m.form)}}function B(S,T){if(1&S&&(t.ynx(0),t._uU(1),t.BQk()),2&S){const m=t.oxw().$implicit;t.xp6(1),t.Oqu(m.header)}}function x(S,T){if(1&S&&(t.YNc(0,D,3,1,"ng-container",17),t.YNc(1,B,2,1,"ng-container",17)),2&S){const m=T.$implicit;t.Q6J("ngIf","Select All"===m.header),t.xp6(1),t.Q6J("ngIf","Select All"!==m.header)}}function Y(S,T){if(1&S){const m=t.EpF();t.TgZ(0,"cc-checkbox",20),t.NdJ("ngModelChange",function(N){const z=t.CHM(m).$implicit;return t.oxw().onSelectProcess(N,z.id)}),t.qZA()}if(2&S){const m=T.$implicit,R=t.oxw();t.Q6J("ngModel",R.selectedPcProcessIds.includes(m.id))("name",m.name)}}const e=function(S){return{select:S}};let K=class{constructor(T,m,R,N,J){this.store=T,this.dialogRef=m,this.cdr=R,this.fb=N,this.data=J,this.search$=new I.X(""),this.cols=[{field:"select",header:"Select All"},{field:"name",header:"Name"},{field:"portal",header:"Folder"}],this.processList=[],this.selectedPcProcessIds=[],this.canReset=!1,this.hasSearched=!1,this.form=this.fb.group({selectAll:""}),this.searchForm=this.fb.group({search:""})}ngOnInit(){var T;this.search$.pipe((0,ie.b)(300),(0,w.b)(m=>this.store.loadRoboticProcessList(m))).subscribe(),this.store.loadRoboticProcessList(""),this.store.roboticProcessList$.subscribe({next:m=>{this.processList=m,this.cdr.detectChanges()}}),null===(T=this.form.get("selectAll"))||void 0===T||T.valueChanges.subscribe({next:m=>{this.selectedPcProcessIds=m?this.processList.map(R=>R.id):[],this.cdr.detectChanges()}}),this.searchForm.valueChanges.subscribe({next:()=>{const m=Object.values(this.searchForm.value).some(R=>!!R);this.canReset=m,this.cdr.detectChanges()}})}onSelectProcess(T,m){this.selectedPcProcessIds=T?[...this.selectedPcProcessIds,m]:this.selectedPcProcessIds.filter(N=>N!==m),this.form.patchValue({selectAll:this.selectedPcProcessIds.length===this.processList.length},{emitEvent:!1}),this.cdr.detectChanges()}onSearch(){const T=this.searchForm.get("search").value||"";this.search$.next(T),this.hasSearched=!0,this.canReset=!!T,this.cdr.detectChanges()}onReset(){this.search$.next(""),this.searchForm.reset(),this.hasSearched=!1,this.canReset=!1,this.cdr.detectChanges()}onSave(){this.store.addPcProcesses(this.data.pcId,this.selectedPcProcessIds),setTimeout(()=>{this.dialogRef.close()},100)}ngOnDestroy(){this.store.resetSearchState()}};function Pe(S,T){if(1&S&&(t.ynx(0),t.TgZ(1,"form",15),t._UZ(2,"cc-checkbox",16),t.qZA(),t.BQk()),2&S){const m=t.oxw(2);t.xp6(1),t.Q6J("formGroup",m.form)}}function pe(S,T){if(1&S&&(t.ynx(0),t._uU(1),t.BQk()),2&S){const m=t.oxw().$implicit;t.xp6(1),t.Oqu(m.header)}}function he(S,T){if(1&S&&(t.YNc(0,Pe,3,1,"ng-container",14),t.YNc(1,pe,2,1,"ng-container",14)),2&S){const m=T.$implicit;t.Q6J("ngIf","Select All"===m.header),t.xp6(1),t.Q6J("ngIf","Select All"!==m.header)}}function U(S,T){if(1&S){const m=t.EpF();t.TgZ(0,"cc-checkbox",17),t.NdJ("ngModelChange",function(N){const z=t.CHM(m).$implicit;return t.oxw().onSelectProcess(N,z.id)}),t.qZA()}if(2&S){const m=T.$implicit,R=t.oxw();t.Q6J("ngModel",R.selectedPcProcessIds.includes(m.id))("name",m.name)}}K.\u0275fac=function(T){return new(T||K)(t.Y36(y.O),t.Y36(k.so),t.Y36(t.sBO),t.Y36(c.qu),t.Y36(k.WI))},K.\u0275cmp=t.Xpm({type:K,selectors:[["ng-component"]],decls:25,vars:14,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-close-button","","cc-dialog-close",""],[1,"col-md-12","py-2","d-flex","flex-row","align-items-center",3,"formGroup"],["label","Search By Name","formControlName","search",1,"col-md-6"],[1,"d-flex","gap-2"],["cc-raised-button","","color","accent",3,"disabled","click"],["cc-raised-button","","color","warn",3,"disabled","click"],[3,"data","columns","loading","showPaginator","headerTemplate","cellTemplate"],["ccGridHeader",""],["headerTmp",""],["processListSelectForm","ngForm"],[3,"ccGridCell"],["select",""],["cc-flat-button","","cc-dialog-close","",1,"px-5"],["cc-raised-button","","color","primary",1,"px-5",3,"disabled","click"],[4,"ngIf"],[3,"formGroup"],["formControlName","selectAll",1,"pc_process_checkbox","custom-checkbox"],[1,"pc_process_checkbox",3,"ngModel","name","ngModelChange"]],template:function(T,m){if(1&T&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3,"Add PC Process"),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"cc-dialog-content")(6,"form",4),t._UZ(7,"cc-input",5),t.TgZ(8,"div",6)(9,"button",7),t.NdJ("click",function(){return m.onSearch()}),t._uU(10," Search "),t.qZA(),t.TgZ(11,"button",8),t.NdJ("click",function(){return m.onReset()}),t._uU(12," Reset "),t.qZA()()(),t._UZ(13,"cc-datagrid",9),t.YNc(14,x,2,2,"ng-template",10,11,t.W1O),t.TgZ(16,"form",null,12),t.YNc(18,Y,1,2,"ng-template",13,14,t.W1O),t.qZA()(),t.TgZ(20,"cc-dialog-actions")(21,"button",15),t._uU(22,"Close"),t.qZA(),t.TgZ(23,"button",16),t.NdJ("click",function(){return m.onSave()}),t._uU(24," Save "),t.qZA()()()),2&T){const R=t.MAs(15),N=t.MAs(19);t.xp6(2),t.Q6J("align","center"),t.xp6(4),t.Q6J("formGroup",m.searchForm),t.xp6(3),t.Q6J("disabled",!m.canReset),t.xp6(2),t.Q6J("disabled",!m.canReset&&!m.hasSearched),t.xp6(2),t.Q6J("data",m.processList)("columns",m.cols)("loading",!1)("showPaginator",!1)("headerTemplate",R)("cellTemplate",t.VKq(12,e,N)),t.xp6(5),t.Q6J("ccGridCell",m.processList),t.xp6(5),t.Q6J("disabled",0===m.selectedPcProcessIds.length)}},directives:[o.iK,o.Cj,o.Zb,o.fX,o.zn,o.kL,c._Y,c.JL,c.sg,se.G,c.JJ,c.u,O.uu,n.Ge,n.st,q.O5,de.E,c.F,n.VC,c.On,o.Zu],encapsulation:2,changeDetection:0}),K=(0,u.gn)([g.kG],K);const ce=function(){return[]},ue=function(S){return{select:S}};let _e=(()=>{class S{constructor(m,R,N,J,z){this.store=m,this.dialog=R,this.cdr=N,this.fb=J,this.data=z,this.cols=[{field:"select",header:"Select All"},{field:"name",header:"Name"},{field:"portal",header:"Folder"}],this.rpaPc=null,this.selectedPcProcessIds=[],this.form=this.fb.group({selectAll:""})}ngOnInit(){var m;this.store.getPcWithProcesses(this.data.pcId),this.store.pcWithProcess$.subscribe({next:N=>{this.selectedPcProcessIds=[],this.rpaPc=N,this.cdr.markForCheck()}}),null===(m=this.form.get("selectAll"))||void 0===m||m.valueChanges.subscribe({next:N=>{this.selectedPcProcessIds=[],this.selectedPcProcessIds=N?this.rpaPc.processes.map(J=>J.id):[],this.cdr.detectChanges()}})}onSelectionChange(m){}onSelectProcess(m,R){var N,J,z,H,A;this.selectedPcProcessIds=m?[...this.selectedPcProcessIds,R]:this.selectedPcProcessIds.filter(_=>_!==R),(null===(N=this.rpaPc)||void 0===N?void 0:N.processes)&&this.selectedPcProcessIds.length<(null===(z=null===(J=this.rpaPc)||void 0===J?void 0:J.processes)||void 0===z?void 0:z.length)&&this.form.patchValue({selectAll:!1},{emitEvent:!1}),(null===(H=this.rpaPc)||void 0===H?void 0:H.processes)&&this.selectedPcProcessIds.length===(null===(A=this.rpaPc)||void 0===A?void 0:A.processes.length)&&this.form.patchValue({selectAll:!0},{emitEvent:!1}),this.cdr.detectChanges()}onAdd(){this.dialog.originalOpen(K,{panelClass:["col-md-8"],data:{pcId:this.data.pcId}})}onDelete(){this.store.deletePcProcesses(this.data.pcId,this.selectedPcProcessIds)}onEdit(m){this.dialog.originalOpen(F,{panelClass:["col-md-5"],data:{pcId:this.data.pcId,processId:m}})}}return S.\u0275fac=function(m){return new(m||S)(t.Y36(y.O),t.Y36(o.uY),t.Y36(t.sBO),t.Y36(c.qu),t.Y36(k.WI))},S.\u0275cmp=t.Xpm({type:S,selectors:[["ng-component"]],decls:20,vars:13,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-close","","cc-dialog-close-button",""],[3,"columns","data","loading","showPaginator","cellTemplate","headerTemplate"],["ccGridHeader",""],["headerTmp",""],["processListSelectForm","ngForm"],[3,"ccGridCell"],["select",""],[1,"py-2"],["cc-flat-button","","cc-dialog-close","",1,"px-5"],["cc-flat-button","","color","accent",1,"px-5",3,"disabled","click"],["cc-raised-button","","color","primary",1,"px-5",3,"click"],[4,"ngIf"],[3,"formGroup"],["formControlName","selectAll",1,""],[1,"pc_process_checkbox",3,"ngModel","name","ngModelChange"]],template:function(m,R){if(1&m&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3,"PC Processes"),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"cc-dialog-content"),t._UZ(6,"cc-datagrid",4),t.YNc(7,he,2,2,"ng-template",5,6,t.W1O),t.TgZ(9,"form",null,7),t.YNc(11,U,1,2,"ng-template",8,9,t.W1O),t.qZA()(),t.TgZ(13,"cc-dialog-actions",10)(14,"button",11),t._uU(15,"Cancel"),t.qZA(),t.TgZ(16,"button",12),t.NdJ("click",function(){return R.onDelete()}),t._uU(17," Delete "),t.qZA(),t.TgZ(18,"button",13),t.NdJ("click",function(){return R.onAdd()}),t._uU(19," Add "),t.qZA()()()),2&m){const N=t.MAs(8),J=t.MAs(12);t.xp6(2),t.Q6J("align","center"),t.xp6(4),t.Q6J("columns",R.cols)("data",(null==R.rpaPc?null:R.rpaPc.processes)||t.DdM(9,ce))("loading",!1)("showPaginator",!1)("cellTemplate",t.VKq(10,ue,J))("headerTemplate",N),t.xp6(5),t.Q6J("ccGridCell",(null==R.rpaPc?null:R.rpaPc.processes)||t.DdM(12,ce)),t.xp6(5),t.Q6J("disabled",0===R.selectedPcProcessIds.length)}},directives:[o.iK,o.Cj,o.Zb,o.zn,o.fX,o.kL,n.Ge,n.st,q.O5,c._Y,c.JL,c.sg,de.E,c.JJ,c.u,c.F,n.VC,c.On,o.Zu,O.uu],encapsulation:2,changeDetection:0}),S})();var $=i(32269),ae=i(30923),ee=i(21799);let M=class{constructor(T,m,R,N,J,z){this.fb=T,this.state=m,this.apiService=R,this.notification=N,this.dialogRef=J,this.data=z,this.form=this.fb.group({name:"",value:""})}onAdd(){const T=Object.assign(Object.assign({},this.form.value),{pc:{id:this.data.pcId}});this.apiService.createConfigParam(T).subscribe({next:()=>{this.state.refreshData(),this.dialogRef.close(),this.notification.notifySuccess("Added Successfully.")}})}};M.\u0275fac=function(T){return new(T||M)(t.Y36(c.qu),t.Y36($.s),t.Y36(ae.a),t.Y36(ee.zg),t.Y36(k.so),t.Y36(k.WI))},M.\u0275cmp=t.Xpm({type:M,selectors:[["ng-component"]],decls:15,vars:4,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close-button","","cc-dialog-close",""],["cc-dialog-content",""],[3,"formGroup"],["label","Parameter Name","formControlName","name",3,"required"],["label","Parameter Value","formControlName","value",3,"required"],["cc-dialog-actions",""],[1,"d-flex","gap-2"],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"]],template:function(T,m){1&T&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3,"Add New Parameter"),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"div",4)(6,"form",5),t._UZ(7,"cc-input",6)(8,"cc-input",7),t.qZA()(),t.TgZ(9,"div",8)(10,"div",9)(11,"button",10),t._uU(12,"Cancel"),t.qZA(),t.TgZ(13,"button",11),t.NdJ("click",function(){return m.onAdd()}),t._uU(14,"Add"),t.qZA()()()()),2&T&&(t.xp6(6),t.Q6J("formGroup",m.form),t.xp6(1),t.Q6J("required",!0),t.xp6(1),t.Q6J("required",!0),t.xp6(5),t.Q6J("disabled",!m.form.valid))},directives:[o.iK,o.Cj,o.Zb,o.fX,o.zn,o.kL,c._Y,c.JL,c.sg,se.G,c.JJ,c.u,c.Q7,o.Zu,O.uu],encapsulation:2,changeDetection:0}),M=(0,u.gn)([g.kG],M);var Te=i(63900);let me=(()=>{class S{constructor(m,R,N,J,z,H){this.fb=m,this.state=R,this.apiService=N,this.notification=J,this.dialogRef=z,this.data=H,this.form=this.fb.group({name:"",value:""})}ngOnInit(){this.form.setValue({name:this.data.configParam.name,value:this.data.configParam.value})}onEdit(){const m=Object.assign(Object.assign({},this.form.value),{id:this.data.configParam.id,pc:{id:this.data.pcId}});this.apiService.updateConfigParam(m).subscribe({next:()=>{this.state.refreshData(),this.dialogRef.close(),this.notification.notifySuccess("Updated Successfully.")}})}}return S.\u0275fac=function(m){return new(m||S)(t.Y36(c.qu),t.Y36($.s),t.Y36(ae.a),t.Y36(ee.zg),t.Y36(k.so),t.Y36(k.WI))},S.\u0275cmp=t.Xpm({type:S,selectors:[["ng-component"]],decls:15,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close-button","","cc-dialog-close",""],["cc-dialog-content",""],[3,"formGroup"],["label","Parameter Name","formControlName","name",3,"required"],["label","Parameter Value","formControlName","value",3,"required"],["cc-dialog-actions",""],[1,"d-flex","gap-2"],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"]],template:function(m,R){1&m&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"div",4)(6,"form",5),t._UZ(7,"cc-input",6)(8,"cc-input",7),t.qZA()(),t.TgZ(9,"div",8)(10,"div",9)(11,"button",10),t._uU(12,"Cancel"),t.qZA(),t.TgZ(13,"button",11),t.NdJ("click",function(){return R.onEdit()}),t._uU(14," Add "),t.qZA()()()()),2&m&&(t.xp6(3),t.Oqu("Edit "+R.data.configParam.name),t.xp6(3),t.Q6J("formGroup",R.form),t.xp6(1),t.Q6J("required",!0),t.xp6(1),t.Q6J("required",!0),t.xp6(5),t.Q6J("disabled",!R.form.valid))},directives:[o.iK,o.Cj,o.Zb,o.fX,o.zn,o.kL,c._Y,c.JL,c.sg,se.G,c.JJ,c.u,c.Q7,o.Zu,O.uu],encapsulation:2,changeDetection:0}),S})();function ge(S,T){if(1&S){const m=t.EpF();t.TgZ(0,"div",10)(1,"button",11),t.NdJ("click",function(){const J=t.CHM(m).$implicit;return t.oxw().onEdit(J)}),t._uU(2," Edit "),t.qZA(),t.TgZ(3,"button",12),t.NdJ("click",function(){const J=t.CHM(m).$implicit;return t.oxw().onDelete(J.id,J.name)}),t._uU(4," Delete "),t.qZA()()}}const Ae=function(S){return{actionTmp:S}};let ve=(()=>{class S{constructor(m,R,N,J,z,H){this.apiService=m,this.notification=R,this.state=N,this.dialog=J,this.cdr=z,this.data=H,this.gridCols=[{field:"name",header:"Name",width:"33.3%"},{field:"value",header:"Value",width:"33.3%"},{field:"actionTmp",header:"Actions",width:"33.3%"}]}ngOnInit(){this.state.refreshData$.pipe((0,Te.w)(()=>this.apiService.getRpaPcProcesses(this.data.pcId))).subscribe({next:m=>{this.pcConfig=m,this.cdr.markForCheck()}})}onEdit(m){this.dialog.originalOpen(me,{panelClass:["col-md-5"],data:{pcId:this.data.pcId,pcName:this.data.pcName,configParam:m}})}onDelete(m,R){this.dialog.confirm(`Delete ${R}`,`Are you sure that you want to delete the parameter: ${R}`,()=>{this.apiService.deleteConfigParam(m).subscribe({next:()=>{this.state.refreshData(),this.notification.notifySuccess("Deleted Successfully.")}})},()=>{},"Confirm","Cancel")}onAdd(){this.dialog.originalOpen(M,{panelClass:["col-md-5"],data:{pcId:this.data.pcId,pcName:this.data.pcName}})}}return S.\u0275fac=function(m){return new(m||S)(t.Y36(ae.a),t.Y36(ee.zg),t.Y36($.s),t.Y36(o.uY),t.Y36(t.sBO),t.Y36(k.WI))},S.\u0275cmp=t.Xpm({type:S,selectors:[["ng-component"]],decls:12,vars:9,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],[1,"d-flex","justify-content-end","my-2"],["cc-raised-button","","color","warn",1,"px-5",3,"click"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["actionTmp",""],[1,"d-flex","flex-column","flex-md-row","justify-content-center","gap-2"],["cc-flat-button","","color","accent",1,"px-4",3,"click"],["cc-flat-button","","color","warn",1,"px-4",3,"click"]],template:function(m,R){if(1&m&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3),t.qZA(),t._UZ(4,"a",3),t.qZA(),t.TgZ(5,"div",4)(6,"div",5)(7,"button",6),t.NdJ("click",function(){return R.onAdd()}),t._uU(8," Add "),t.qZA()(),t._UZ(9,"cc-datagrid",7),t.YNc(10,ge,5,0,"ng-template",8,9,t.W1O),t.qZA()()),2&m){const N=t.MAs(11);t.xp6(3),t.Oqu(R.data.pcName+" Parameter"),t.xp6(6),t.Q6J("columns",R.gridCols)("data",R.pcConfig.parameters)("showPaginator",!1)("loading",!1)("cellTemplate",t.VKq(7,Ae,N)),t.xp6(1),t.Q6J("ccGridCell",R.pcConfig.parameters)}},directives:[o.iK,o.Cj,o.Zb,o.zn,o.fX,o.kL,O.uu,n.Ge,n.VC],encapsulation:2,changeDetection:0}),S})();var le=i(77871),re=i(34378),Se=i(45834);function ye(S,T){if(1&S){const m=t.EpF();t.TgZ(0,"div",14)(1,"button",15),t.NdJ("click",function(){const J=t.CHM(m).$implicit;return t.oxw().openConfigParametersDialog(J.id,J.name)}),t._uU(2," Config Parameters "),t.qZA(),t.TgZ(3,"button",16),t.NdJ("click",function(){const J=t.CHM(m).$implicit;return t.oxw().viewPcProcesses(J.id)}),t._uU(4," View "),t.qZA()()}}const be=function(S){return{operations:S}};let fe=class{constructor(T,m,R,N,J){this.store=T,this.cdr=m,this.fb=R,this.dialog=N,this.dataService=J,this.cols=[{field:"operations",header:"Actions",width:"33.3%"},{field:"name",header:"Name",width:"33.3%"},{field:"processesCount",header:"Processes Count",width:"33.3%",formatter:z=>z.processes.length}],this.vm={content:[],number:0,size:0,totalElements:0,totalPages:0},this.canReset=!1,this.hasSearched=!1,this.roboticProcesses$=this.dataService.roboticProcesses$,this.form=this.fb.group({pcName:"",processes:""})}ngOnInit(){this.store.search$.subscribe(T=>{this.store.getAllPcs(T)}),this.store.AllPcs$.subscribe({next:T=>{this.vm=T,this.cdr.markForCheck()}}),this.form.valueChanges.subscribe(()=>{this.canReset=Object.values(this.form.value).some(T=>!!T),this.cdr.detectChanges()})}viewPcProcesses(T){this.dialog.originalOpen(_e,{panelClass:["col-md-10"],data:{pcId:T}})}handleNextPage(T){this.store.updateSearchState({params:{page:T.pageIndex,size:T.pageSize}})}onSearch(){const T=this.form.value.pcName,m=(this.form.value.processes||[]).join(","),R=Object.assign(Object.assign({},this.store.initialSearchVal),{search:{pcName:T,processes:m}});this.store.updateSearchState(R),this.hasSearched=!0,this.canReset=!!T||!!m,this.cdr.detectChanges()}onReset(){this.form.reset(),this.store.resetSearchState(),this.hasSearched=!1,this.canReset=!1,this.cdr.detectChanges()}openConfigParametersDialog(T,m){this.dialog.originalOpen(ve,{panelClass:["col-md-8"],data:{pcId:T,pcName:m}})}ngOnDestroy(){this.store.resetSearchState()}};fe.\u0275fac=function(T){return new(T||fe)(t.Y36(y.O),t.Y36(t.sBO),t.Y36(c.qu),t.Y36(o.uY),t.Y36(le.X))},fe.\u0275cmp=t.Xpm({type:fe,selectors:[["app-pc-processes"]],decls:25,vars:16,consts:[[1,"py-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center"],[1,"d-flex","flex-column","flex-md-row","justify-content-center",3,"formGroup"],[1,"col-md-6"],["label","PC Name","formControlName","pcName"],["label","Process Name","formControlName","processes",3,"multiple","data"],[1,"d-flex","col-md-12","justify-content-center","gap-2","py-1"],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"],["cc-raised-button","","color","warn",1,"px-5",3,"disabled","click"],[1,"mb-4"],[3,"columns","data","pageIndex","pageSize","length","cellTemplate","page"],[3,"ccGridCell"],["operations",""],[1,"d-flex","flex-column","flex-md-row","justify-content-center","gap-2"],["cc-flat-button","","color","warn",3,"click"],["cc-flat-button","","color","accent",3,"click"]],template:function(T,m){if(1&T&&(t.TgZ(0,"div",0)(1,"cc-accordion")(2,"cc-panel",1)(3,"cc-panel-title",2)(4,"cc-icon"),t._uU(5,"filter_alt"),t.qZA(),t.TgZ(6,"span"),t._uU(7,"Filters"),t.qZA()(),t.TgZ(8,"cc-panel-body")(9,"form",3)(10,"div",4),t._UZ(11,"cc-input",5),t.qZA(),t.TgZ(12,"div",4),t._UZ(13,"cc-select",6),t.ALo(14,"async"),t.qZA()(),t.TgZ(15,"div",7)(16,"button",8),t.NdJ("click",function(){return m.onSearch()}),t._uU(17," Search "),t.qZA(),t.TgZ(18,"button",9),t.NdJ("click",function(){return m.onReset()}),t._uU(19," Reset "),t.qZA()()()()(),t._UZ(20,"div",10),t.ynx(21),t.TgZ(22,"cc-datagrid",11),t.NdJ("page",function(N){return m.handleNextPage(N)}),t.qZA(),t.YNc(23,ye,5,0,"ng-template",12,13,t.W1O),t.BQk(),t.qZA()),2&T){const R=t.MAs(24);t.xp6(9),t.Q6J("formGroup",m.form),t.xp6(4),t.Q6J("multiple",!0)("data",t.lcZ(14,12,m.roboticProcesses$)),t.xp6(3),t.Q6J("disabled",!m.canReset),t.xp6(2),t.Q6J("disabled",!m.canReset&&!m.hasSearched),t.xp6(4),t.Q6J("columns",m.cols)("data",m.vm.content)("pageIndex",m.vm.number)("pageSize",m.vm.size)("length",m.vm.totalElements)("cellTemplate",t.VKq(14,be,R)),t.xp6(1),t.Q6J("ccGridCell",m.vm.content)}},directives:[re.I,re.CW,re.LL,Se.Q9,re.G9,c._Y,c.JL,c.sg,se.G,c.JJ,c.u,l.jB,O.uu,n.Ge,n.VC],pipes:[q.Ov],encapsulation:2,changeDetection:0}),fe=(0,u.gn)([g.kG],fe)},22960:(Ce,G,i)=>{"use strict";i.r(G),i.d(G,{RpaControllerModule:()=>kt});var u=i(30923),g=i(76593),k=i(88476),L=i(62764),t=i(65868),y=i(43277),o=i(45834),c=i(82599),l=i(69808),O=i(43687),F=i(65620),I=i(26991),ie=i(4288),w=i(90738),se=i(95577),n=i(54004),q=i(70262),de=i(39646),D=i(24351),B=i(60515),x=i(18505),Y=i(63900),e=i(5e3),K=i(21799);let Pe=(()=>{class a{constructor(s,p,P){this.action$=s,this.service=p,this.notification=P,this.loadProcessList$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.Au),(0,se.z)(()=>this.service.getProcessList().pipe((0,n.U)(b=>w.Us({payload:b})),(0,q.K)(b=>(0,de.of)(w.GP({error:b.message}))))))),this.loadWorkingDays$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.g3),(0,se.z)(({code:b})=>this.service.getWorkingDaysList(b).pipe((0,n.U)(Z=>w.YG({payload:Z})))))),this.updateWorkingDays$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.jt),(0,D.b)(({code:b,workingDays:Z})=>this.service.updateWorkingTimes(b,Z).pipe((0,n.U)(()=>(this.notification.notifySuccess("Saved Successfully"),w.a_())))))),this.updateProcessesState$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.b2),(0,D.b)(({state:b})=>this.service.updateProcessesState(b).pipe((0,n.U)(()=>{this.notification.notifySuccess("Status Updated Successfully")}),(0,q.K)(()=>(this.notification.notifyError("Failed to Update"),B.E))))),{dispatch:!1}),this.updateProcessState$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.yF),(0,se.z)(({processId:b,state:Z})=>this.service.updateProcessState(b,Z).pipe((0,n.U)(()=>(this.notification.notifySuccess("related processor stopped successfully."),w.yr({payload:!0}))),(0,q.K)(()=>(this.notification.notifyError("Failed to Update"),B.E))))),{dispatch:!0}),this.updateProcessIncludedMaids$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.Bi),(0,D.b)(({processId:b,state:Z})=>this.service.updateProcessIncludedMaidsState(b,Z).pipe((0,x.b)(()=>{this.notification.notifySuccess("Updated Successfully.")}),(0,q.K)(()=>(this.notification.notifyError("Failed to Update"),B.E))))),{dispatch:!1}),this.loadDefaultWorkingDays$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.NH),(0,se.z)(()=>this.service.getDefaultWorkingDaysList().pipe((0,n.U)(b=>w.sD({payload:b})),(0,q.K)(()=>B.E))))),this.updateDefaultWorkingDays$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.Do),(0,D.b)(({workingDays:b})=>this.service.updateDefaultWorkingTimes(b).pipe((0,n.U)(()=>(this.notification.notifySuccess("Saved Successfully."),w.yU())))))),this.loadRpaPcList$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.lH),(0,Y.w)(()=>this.service.getRpaPcList().pipe((0,n.U)(b=>w.ox({payload:b})))))),this.LoadRoboticProcessList$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.nd),(0,Y.w)(({pcName:b})=>this.service.getRoboticProcessList(b).pipe((0,n.U)(Z=>w.AX({payload:Z})))))),this.getAllPcs$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.bn),(0,Y.w)(({search:b})=>this.service.getAllPc(b).pipe((0,n.U)(Z=>w.ZM({payload:Z})))))),this.getPcProcesses$=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.$S),(0,Y.w)(({pcId:b})=>this.service.getPcProcesses(b).pipe((0,n.U)(Z=>w.Ir({payload:Z})))))),this.addPcProcesses=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.Ef),(0,Y.w)(({pcId:b,processes:Z})=>this.service.addPcProcesses(b,Z).pipe((0,n.U)(W=>(this.notification.notifySuccess("Done Successfully."),w.$$())),(0,Y.w)(W=>[W,w.$S({pcId:b})]))))),this.deletePcProcesses=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.MA),(0,Y.w)(({pcId:b,processes:Z})=>this.service.deletePcProcesses(b,Z).pipe((0,n.U)(W=>(this.notification.notifySuccess("Done Successfully."),w.KL())),(0,Y.w)(W=>[W,w.$S({pcId:b})]))))),this.editPcProcess=(0,I.GW)(()=>this.action$.pipe((0,I.l4)(w.nQ),(0,Y.w)(({pcId:b,oldProcessId:Z,newProcessId:W})=>this.service.editPcProcess(b,Z,W).pipe((0,n.U)(oe=>(this.notification.notifySuccess("Done Successfully."),w.yS())),(0,Y.w)(oe=>[oe,w.$S({pcId:b})])))))}}return a.\u0275fac=function(s){return new(s||a)(e.LFG(I.eX),e.LFG(u.a),e.LFG(K.zg))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})(),pe=(()=>{class a{}return a.\u0275fac=function(s){return new(s||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({imports:[[F.Aw.forFeature(ie.J,ie.I),I.sQ.forFeature([Pe])],F.Aw,I.sQ]}),a})();var he=i(1402),U=i(97582),ce=i(50727),ue=i(43604),_e=i(42199),$=i(48966),ae=i(85185),ee=i(26523),M=i(93075);function Te(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"button",16),e.NdJ("click",function(){const b=e.CHM(s).$implicit;return e.oxw().onRemove(b.requestId,b.name)}),e._uU(1," Remove "),e.qZA()}}const me=function(){return[]},ge=function(){return[10,20,30,40,50]},Ae=function(a){return{remove:a}},ve=[{field:"remove",header:"Actions"},{field:"name",header:"Maid Name",class:"col-10 text-center"}];let le=class{constructor(r,s,p,P,b,Z){this.service=r,this.store=s,this.notification=p,this.dialog=P,this.cdr=b,this.data=Z,this.gridCols=ve,this.excludedMaidsAPI=ue.b.excludedMaidList,this.searchInput="",this.requestId=0,this.requestType="",this.fetchExcludedMaidsList=W=>this.service.fetchExcludedMaids(this.requestType,W).pipe((0,n.U)(oe=>oe.content.map(Re=>({id:Re.id,text:Re.name})))),this.processorId=this.data.processorId,this.processorName=this.data.processorName,this.requestType=this.data.requestType}ngOnInit(){this.loadProcessExcludedMaids(_e.E.search),this.store.search$.pipe((0,x.b)(r=>this.search=r)).subscribe()}onRemove(r,s){this.dialog.confirm("",`Are you sure you want to remove ${s} ?`,()=>this.removeMaid(r),()=>{})}removeMaid(r){this.service.deleteExcludedMaid(this.processorId,r).pipe((0,x.b)(()=>{this.notification.notifySuccess("Removed Successfully"),this.loadProcessExcludedMaids(this.search)})).subscribe()}excludeMaid(){this.service.excludeMaid(this.processorId,this.requestId).pipe((0,x.b)(()=>{this.requestId=0,this.notification.notifySuccess("Added Successfully."),this.store.resetSearchState(),this.loadProcessExcludedMaids(this.search)})).subscribe()}onSearch(r){const s={params:{page:0,size:this.search.params.size},search:{housemaidName:r}};this.loadProcessExcludedMaids(s),this.store.updateSearchState(s),this.cdr.detectChanges()}handleNextPage(r){const s={params:{page:r.pageIndex,size:r.pageSize}};this.store.updateSearchState(Object.assign({},s)),this.loadProcessExcludedMaids(s)}loadProcessExcludedMaids(r){this.service.getProcessExcludedMaids(this.processorId,r).subscribe(s=>{this.processExcludedMaids=s,this.cdr.detectChanges()})}ngOnDestroy(){}};le.\u0275fac=function(r){return new(r||le)(e.Y36(u.a),e.Y36(g.O),e.Y36(K.zg),e.Y36(c.uY),e.Y36(e.sBO),e.Y36($.WI))},le.\u0275cmp=e.Xpm({type:le,selectors:[["ng-component"]],decls:25,vars:19,consts:[["cc-std-dialog",""],[1,"d-flex","justify-content-center","position-relative"],["cc-dialog-title","",1,"col-10","text-center"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close","",2,"position","absolute","right","5px"],[1,"d-flex","flex-column","flex-md-row","align-items-center","justify-content-center","mb-2",2,"gap","0 1rem"],[1,"d-flex","flex-column","col-12","col-md-8"],["label","Select maids",3,"ngModel","lazyPageFetcher","ngModelChange"],["type","button","cc-flat-button","","color","accent",1,"col-8","col-md-1",2,"text-align","center","font-size","16px","font-weight","500",3,"disabled","click"],[1,"d-flex","flex-column","flex-md-row","justify-content-center","align-items-center","mb-4",2,"gap","0 1rem"],["ngModel","","label","Maid\u2019s Name","placeholder","Maid name to search",1,"col-12","col-md-8",3,"ngModel","ngModelChange"],["name","ngModel"],["type","button","cc-flat-button","","color","accent",1,"col-8","col-md-1",2,"text-align","center","font-size","16px","font-weight","500",3,"click"],[3,"data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","cellTemplate","page"],[3,"ccGridCell"],["removeBtnTmp",""],["cc-raised-button","","color","accent","cc-dialog-close",""],["type","button","cc-flat-button","","color","accent",3,"click"]],template:function(r,s){if(1&r){const p=e.EpF();e.TgZ(0,"div",0)(1,"cc-dialog-header",1)(2,"h1",2),e._uU(3),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"cc-dialog-content")(6,"div",4)(7,"div",5)(8,"cc-label"),e._uU(9,"Type maid's name to add her to the exclude list"),e.qZA(),e.TgZ(10,"cc-select",6),e.NdJ("ngModelChange",function(b){return s.requestId=b}),e.qZA()(),e.TgZ(11,"button",7),e.NdJ("click",function(){return s.excludeMaid()}),e._uU(12," Add "),e.qZA()(),e.TgZ(13,"div",8)(14,"cc-input",9,10),e.NdJ("ngModelChange",function(b){return s.searchInput=b}),e.qZA(),e.TgZ(16,"button",11),e.NdJ("click",function(){e.CHM(p);const b=e.MAs(15);return s.onSearch(b.value)}),e._uU(17," Search "),e.qZA()(),e.ynx(18),e.TgZ(19,"cc-datagrid",12),e.NdJ("page",function(b){return s.handleNextPage(b)}),e.qZA(),e.YNc(20,Te,2,0,"ng-template",13,14,e.W1O),e.BQk(),e.qZA(),e.TgZ(22,"cc-dialog-actions")(23,"button",15),e._uU(24,"Close"),e.qZA()()()}if(2&r){const p=e.MAs(21);e.xp6(3),e.hij("Exclude maids from processor (",s.processorName,")"),e.xp6(7),e.Q6J("ngModel",s.requestId)("lazyPageFetcher",s.fetchExcludedMaidsList),e.xp6(1),e.Q6J("disabled",!s.requestId),e.xp6(3),e.Q6J("ngModel",s.searchInput),e.xp6(5),e.Q6J("data",(null==s.processExcludedMaids?null:s.processExcludedMaids.content)||e.DdM(14,me))("columns",s.gridCols)("length",(null==s.processExcludedMaids?null:s.processExcludedMaids.totalElements)||0)("pageOnFront",!1)("pageIndex",(null==s.processExcludedMaids?null:s.processExcludedMaids.number)||0)("pageSize",(null==s.processExcludedMaids?null:s.processExcludedMaids.size)||0)("pageSizeOptions",e.DdM(15,ge))("cellTemplate",e.VKq(16,Ae,p)),e.xp6(1),e.Q6J("ccGridCell",(null==s.processExcludedMaids?null:s.processExcludedMaids.content)||e.DdM(18,me))}},directives:[c.iK,c.Cj,c.Zb,t.uu,c.fX,c.zn,c.kL,ae.k_,ee.jB,M.JJ,M.On,O.G,L.Ge,L.VC,c.Zu],encapsulation:2,changeDetection:0}),le=(0,U.gn)([k.kG],le);var be,re=i(77579),Se=i(92431),ye=i(467);const S=function(){return{acceptedFiles:".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"}};function T(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",10)(1,"div",14)(2,"cc-label"),e._uU(3,"Included Maids Sheet"),e.qZA()(),e.TgZ(4,"cc-file-uploader",21),e.NdJ("ngModelChange",function(P){return e.CHM(s),e.oxw().excelFile=P}),e.qZA(),e.TgZ(5,"div",14)(6,"button",22),e.NdJ("click",function(){return e.CHM(s),e.oxw().addExcel("above")}),e._uU(7," Add Above "),e.qZA(),e.TgZ(8,"button",22),e.NdJ("click",function(){return e.CHM(s),e.oxw().addExcel("below")}),e._uU(9," Add Below "),e.qZA()()()}if(2&a){const s=e.oxw();e.xp6(4),e.Q6J("ngModel",s.excelFile)("dropzoneConfig",e.DdM(4,S)),e.xp6(2),e.Q6J("disabled",""==s.excelFile||null==s.excelFile||null==s.excelFile),e.xp6(2),e.Q6J("disabled",""==s.excelFile||null==s.excelFile||null==s.excelFile)}}function m(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",10)(1,"div",11)(2,"cc-label"),e._uU(3,"Type maid's name to add her to the included list"),e.qZA(),e.TgZ(4,"cc-select",23),e.NdJ("ngModelChange",function(P){return e.CHM(s),e.oxw().requestIds=P}),e.qZA()(),e.TgZ(5,"div",14)(6,"button",22),e.NdJ("click",function(){return e.CHM(s),e.oxw().add("above")}),e._uU(7," Add Above "),e.qZA(),e.TgZ(8,"button",22),e.NdJ("click",function(){return e.CHM(s),e.oxw().add("below")}),e._uU(9," Add Below "),e.qZA()()()}if(2&a){const s=e.oxw();e.xp6(4),e.Q6J("ngModel",s.requestIds)("lazyPageFetcher",s.fetchMaidsList)("multiple",!0),e.xp6(2),e.Q6J("disabled",null==s.requestIds||0==s.requestIds.length),e.xp6(2),e.Q6J("disabled",null==s.requestIds||0==s.requestIds.length)}}function R(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",24)(1,"div",25)(2,"button",26),e.NdJ("click",function(){return e.CHM(s),e.oxw().clearList()}),e._uU(3," Clear List "),e.qZA()()()}}function N(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"button",27),e.NdJ("click",function(){const b=e.CHM(s).$implicit;return e.oxw().onRemove(b.requestId,b.name)}),e._uU(1," Remove "),e.qZA()}}const J=function(){return[]},z=function(){return[10,20,30,40,50]},H=function(a){return{remove:a}},A=[{field:"remove",header:"Actions"},{field:"name",header:"Maid Name",class:"col-10 text-center"}];let _=class{constructor(r,s,p,P,b,Z){this.service=r,this.store=s,this.notification=p,this.dialog=P,this.cdr=b,this.data=Z,be.set(this,new re.x),this.gridCols=A,this.searchInput="",this.requestId=0,this.requestIds=[],this.requestType="",this.excel=!0,this.excelFile="",this.fetchMaidsList=W=>this.service.fetchExcludedMaids(this.requestType,W).pipe((0,n.U)(oe=>oe.content.map(Re=>({id:Re.id,text:Re.name})))),this.processorId=this.data.processorId,this.processorName=this.data.processorName,this.requestType=this.data.requestType}ngOnInit(){this.loadProcessIncludedMaids(_e.E.search),this.store.search$.pipe((0,x.b)(r=>this.search=r)).subscribe()}onRemove(r,s){this.dialog.confirm("",`Are you sure you want to remove ${s} ?`,()=>this.removeMaid(r),()=>{})}removeMaid(r){this.service.deleteIncludedMaid(this.processorId,r).pipe((0,x.b)(()=>{this.notification.notifySuccess("Removed Successfully"),this.loadProcessIncludedMaids(this.search)})).subscribe()}addExcel(r){this.service.includeMaidsFromExcel(this.processorId,this.excelFile[0].id,"above"==r).pipe((0,x.b)(()=>{this.requestId=0,this.notification.notifySuccess("Added Successfully"),this.excelFile="",this.store.resetSearchState(),this.loadProcessIncludedMaids(this.search)})).subscribe()}add(r){this.service.includeMaids(this.processorId,this.requestIds,"above"==r).pipe((0,x.b)(()=>{this.requestId=0,this.notification.notifySuccess("Added Successfully"),this.store.resetSearchState(),this.loadProcessIncludedMaids(this.search)})).subscribe()}onSearch(r){const s={params:{page:0,size:this.search.params.size},search:{housemaidName:r}};this.loadProcessIncludedMaids(s),this.store.updateSearchState(s)}handleNextPage(r){const s={params:{page:r.pageIndex,size:r.pageSize}};this.store.updateSearchState(Object.assign({},s)),this.loadProcessIncludedMaids(s)}loadProcessIncludedMaids(r){this.service.getProcessIncludedMaids(this.processorId,r).subscribe(s=>{this.processIncludedMaids=s,this.cdr.detectChanges()})}clearList(){this.dialog.confirm("Alert","Are you sure you want to clear the included maids list?",()=>{this.service.clearIncluded(this.processorId).pipe((0,x.b)(()=>{this.requestId=0,this.notification.notifySuccess("Cleared Successfully"),this.store.resetSearchState(),this.loadProcessIncludedMaids(this.search)})).subscribe()},()=>{},"Yes","No")}ngOnDestroy(){(0,U.Q_)(this,be,"f").next(),(0,U.Q_)(this,be,"f").subscribe()}};be=new WeakMap,_.\u0275fac=function(r){return new(r||_)(e.Y36(u.a),e.Y36(g.O),e.Y36(K.zg),e.Y36(c.uY),e.Y36(e.sBO),e.Y36($.WI))},_.\u0275cmp=e.Xpm({type:_,selectors:[["ng-component"]],decls:32,vars:23,consts:[["cc-std-dialog",""],[1,"d-flex","justify-content-center","position-relative"],["cc-dialog-title","",1,"col-10","text-center"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close","",2,"position","absolute","right","5px"],[1,"row"],[1,"form-group","col-md-12"],[3,"ngModel","ngModelChange"],[3,"value"],[1,"col-md-3",3,"value"],["class","form-group col-md-12 row",4,"ngIf"],[1,"form-group","col-md-12","row"],[1,"col-md-9"],["ngModel","","label","Maid\u2019s Name","placeholder","Maid name to search",3,"ngModel","ngModelChange"],["name","ngModel"],[1,"col-md-3","align-content-center"],["cc-raised-button","","color","accent",1,"m-2",3,"click"],[3,"showToolbar","toolbarTemplate","data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","cellTemplate","page"],["toolbarTpl",""],[3,"ccGridCell"],["removeBtnTmp",""],["cc-raised-button","","color","accent","cc-dialog-close",""],["label","Included Maids Sheet","required","true","name","excelFile","required","",1,"col-md-6",3,"ngModel","dropzoneConfig","ngModelChange"],["cc-raised-button","",1,"m-2",3,"disabled","click"],["label","Select maids",3,"ngModel","lazyPageFetcher","multiple","ngModelChange"],[1,"w-100","d-flex","justify-content-end"],[1,"toolbar-actions"],["cc-raised-button","","color","accent",3,"click"],["type","button","cc-flat-button","","color","accent",3,"click"]],template:function(r,s){if(1&r){const p=e.EpF();e.TgZ(0,"div",0)(1,"cc-dialog-header",1)(2,"h1",2),e._uU(3),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"cc-dialog-content")(6,"div",4)(7,"div",5)(8,"cc-radio-group",6),e.NdJ("ngModelChange",function(b){return s.excel=b}),e.TgZ(9,"cc-radio-button",7),e._uU(10,"Upload Excel Sheet"),e.qZA(),e.TgZ(11,"cc-radio-button",8),e._uU(12,"Select Specific Maid"),e.qZA()()(),e.YNc(13,T,10,5,"div",9),e.YNc(14,m,10,5,"div",9),e.qZA(),e.TgZ(15,"div",4)(16,"div",10)(17,"div",11)(18,"cc-input",12,13),e.NdJ("ngModelChange",function(b){return s.searchInput=b}),e.qZA()(),e.TgZ(20,"div",14)(21,"button",15),e.NdJ("click",function(){e.CHM(p);const b=e.MAs(19);return s.onSearch(b.value)}),e._uU(22," Search "),e.qZA()()()(),e.ynx(23),e.TgZ(24,"cc-datagrid",16),e.NdJ("page",function(b){return s.handleNextPage(b)}),e.qZA(),e.YNc(25,R,4,0,"ng-template",null,17,e.W1O),e.YNc(27,N,2,0,"ng-template",18,19,e.W1O),e.BQk(),e.qZA(),e.TgZ(29,"cc-dialog-actions")(30,"button",20),e._uU(31,"Close"),e.qZA()()()}if(2&r){const p=e.MAs(26),P=e.MAs(28);e.xp6(3),e.hij(" Included maids from processor (",s.processorName,") "),e.xp6(5),e.Q6J("ngModel",s.excel),e.xp6(1),e.Q6J("value",!0),e.xp6(2),e.Q6J("value",!1),e.xp6(2),e.Q6J("ngIf",s.excel),e.xp6(1),e.Q6J("ngIf",!s.excel),e.xp6(4),e.Q6J("ngModel",s.searchInput),e.xp6(6),e.Q6J("showToolbar",!0)("toolbarTemplate",p)("data",(null==s.processIncludedMaids?null:s.processIncludedMaids.content)||e.DdM(18,J))("columns",s.gridCols)("length",(null==s.processIncludedMaids?null:s.processIncludedMaids.totalElements)||0)("pageOnFront",!1)("pageIndex",(null==s.processIncludedMaids?null:s.processIncludedMaids.number)||0)("pageSize",(null==s.processIncludedMaids?null:s.processIncludedMaids.size)||0)("pageSizeOptions",e.DdM(19,z))("cellTemplate",e.VKq(20,H,P)),e.xp6(3),e.Q6J("ccGridCell",(null==s.processIncludedMaids?null:s.processIncludedMaids.content)||e.DdM(22,J))}},directives:[c.iK,c.Cj,c.Zb,t.uu,c.fX,c.zn,c.kL,Se.u6,M.JJ,M.On,Se.UF,l.O5,ae.k_,ye.U2,M.Q7,ee.jB,O.G,L.Ge,L.VC,c.Zu],encapsulation:2,changeDetection:0}),_=(0,U.gn)([k.kG],_);var V,f=i(82722),C=i(27122),j=i(28172);class te{constructor(r,s){this.fb=r,this.store=s,this.delete=new e.vpe,this.add=new e.vpe,V.set(this,new re.x),this.workingHour={fromTime:"",toTime:""},this.form=r.group({fromTime:"",toTime:""})}ngOnInit(){this.store.workingHour$.pipe((0,f.R)((0,U.Q_)(this,V,"f")),(0,n.U)(r=>this.workingHour=r[this.index]),(0,x.b)(r=>this.form.patchValue({fromTime:this.convertToTime(r.fromTime,"HH:mm"),toTime:this.convertToTime(r.toTime,"HH:mm")},{emitEvent:!1}))).subscribe()}onDelete(){this.delete.emit()}onAddDailyWorkingHour(){this.add.emit()}updateFromWorkingHour(r){console.log(r);const s={fromTime:this.convertToTime(r,"HH:mm:00"),toTime:this.convertToTime(this.workingHour.toTime,"HH:mm:00")};this.store.updateWorkingHours(this.index,s)}updateToWorkingHour(r){console.log(r);const s={fromTime:this.convertToTime(this.workingHour.fromTime,"HH:mm:00"),toTime:this.convertToTime(r,"HH:mm:00")};this.store.updateWorkingHours(this.index,s)}ngOnDestroy(){(0,U.Q_)(this,V,"f").next(),(0,U.Q_)(this,V,"f").unsubscribe()}convertToTime(r,s){return C.ou.fromISO(r).toFormat(s)}}V=new WeakMap,te.\u0275fac=function(r){return new(r||te)(e.Y36(M.qu),e.Y36(g.O))},te.\u0275cmp=e.Xpm({type:te,selectors:[["daily-working-form"]],inputs:{index:"index"},outputs:{delete:"delete",add:"add"},decls:8,vars:5,consts:[[3,"formGroup"],[2,"display","flex","flex-direction","column","width","100%"],[1,"row","d-flex","flex-column","flex-md-row","justify-content-around"],["formControlName","fromTime",1,"col-md-6",3,"format","label","timeSet"],["formControlName","toTime",1,"col-md-6",3,"format","label","timeSet"],["cc-icon-button","","color","warn",2,"font-size","30px","align-self","flex-end",3,"click"]],template:function(r,s){1&r&&(e.TgZ(0,"form",0)(1,"div",1)(2,"div",2)(3,"cc-timepicker",3),e.NdJ("timeSet",function(P){return s.updateFromWorkingHour(P)}),e.qZA(),e.TgZ(4,"cc-timepicker",4),e.NdJ("timeSet",function(P){return s.updateToWorkingHour(P)}),e.qZA()(),e.TgZ(5,"button",5),e.NdJ("click",function(){return s.onDelete()}),e.TgZ(6,"cc-icon"),e._uU(7,"delete"),e.qZA()()()()),2&r&&(e.Q6J("formGroup",s.form),e.xp6(3),e.Q6J("format",24)("label","From Time"),e.xp6(1),e.Q6J("format",24)("label","To Time"))},directives:[M._Y,M.JL,M.sg,j.u5,M.JJ,M.u,t.uu,o.Q9],encapsulation:2,changeDetection:0});var Ne,Ee=i(58015),Ze=i(4882);function Be(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",35)(1,"daily-working-form",36),e.NdJ("delete",function(){const b=e.CHM(s).index;return e.oxw(2).deleteWorkingHours(b)}),e.qZA()()}if(2&a){const s=r.index;e.xp6(1),e.Q6J("index",s)}}function Ke(a,r){1&a&&(e.TgZ(0,"div")(1,"p",37),e._uU(2," Please select at least one day. "),e.qZA()())}const Xe=function(a){return[a]},Qe=function(a,r){return[a,r]};function Me(a,r){if(1&a){const s=e.EpF();e.ynx(0),e.TgZ(1,"div",1)(2,"cc-dialog-header",2)(3,"h1",3),e._uU(4),e.qZA(),e._UZ(5,"a",4),e.qZA(),e.TgZ(6,"cc-dialog-content")(7,"form",5),e.ALo(8,"async"),e.TgZ(9,"div",6)(10,"cc-label",7),e._uU(11,"Daily working hours:"),e.qZA(),e.TgZ(12,"div",8)(13,"div",9)(14,"button",10),e.NdJ("click",function(){return e.CHM(s),e.oxw().addNewWorkingHours()}),e.TgZ(15,"cc-icon",11),e._uU(16,"add"),e.qZA()()(),e.YNc(17,Be,2,1,"div",12),e.qZA()(),e.TgZ(18,"div",13)(19,"cc-label",7),e._uU(20,"Working days:"),e.qZA(),e.TgZ(21,"div",14)(22,"cc-checkbox",15),e._uU(23,"Sunday"),e.qZA(),e.TgZ(24,"cc-checkbox",16),e._uU(25,"Monday"),e.qZA(),e.TgZ(26,"cc-checkbox",17),e._uU(27,"Tuesday"),e.qZA(),e.TgZ(28,"cc-checkbox",18),e._uU(29,"Wednesday"),e.qZA(),e.TgZ(30,"cc-checkbox",19),e._uU(31,"Thursday"),e.qZA(),e.TgZ(32,"cc-checkbox",20),e._uU(33,"Friday"),e.qZA(),e.TgZ(34,"cc-checkbox",21),e._uU(35,"Saturday"),e.qZA()(),e.YNc(36,Ke,3,0,"div",0),e.qZA(),e.TgZ(37,"div",22)(38,"div",23)(39,"cc-label",24),e._uU(40," Process snooze period: "),e.qZA(),e.TgZ(41,"div",25),e._UZ(42,"cc-input",26)(43,"cc-select",27),e.qZA()(),e.TgZ(44,"div",23)(45,"cc-label",24),e._uU(46," Recheck snooze period: "),e.qZA(),e.TgZ(47,"div",25),e._UZ(48,"cc-input",28)(49,"cc-select",29),e.qZA()(),e.TgZ(50,"div",23)(51,"cc-label",24),e._uU(52," Pause period between maids: "),e.qZA(),e.TgZ(53,"div",25),e._UZ(54,"cc-input",30)(55,"cc-select",31),e.qZA()()()()(),e.TgZ(56,"cc-dialog-actions",32)(57,"button",33),e._uU(58,"Close"),e.qZA(),e.TgZ(59,"button",34),e.NdJ("click",function(){return e.CHM(s),e.oxw().onCreateWorkingTime()}),e._uU(60," Save "),e.qZA()()(),e.BQk()}if(2&a){const s=e.oxw();e.xp6(4),e.Oqu("Working Days/Hours"),e.xp6(3),e.Q6J("formGroup",s.form)("ccConnectForm",e.lcZ(8,14,s.workingDays$)),e.xp6(10),e.Q6J("ngForOf",s.form.get("workingHours").controls),e.xp6(1),e.Q6J("formGroup",s.days)("ccValidateBy",e.VKq(16,Xe,s.daysValidator)),e.xp6(18),e.Q6J("ngIf",s.days.hasError("no-days-selected")),e.xp6(6),e.Q6J("ccValidateBy",e.WLB(18,Qe,s.periodValidator,s.negativeValidator)),e.xp6(1),e.Q6J("data",s.unitsOptions),e.xp6(5),e.Q6J("ccValidateBy",e.WLB(21,Qe,s.periodValidator,s.negativeValidator)),e.xp6(1),e.Q6J("data",s.unitsOptions),e.xp6(5),e.Q6J("ccValidateBy",e.WLB(24,Qe,s.periodValidator,s.negativeValidator)),e.xp6(1),e.Q6J("data",s.unitsOptions),e.xp6(4),e.Q6J("disabled",!s.form.valid||!s.days.valid)}}const Fe=a=>["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].some(p=>!0===a.get(p).value)?null:{"no-days-selected":!0},Ve=a=>/^-?\d+$/.test(a.value)?null:{"not-a-number":"value is not a number"},Ye=a=>a.value<0?{"negative-number":"value should not be negative"}:null;class xe{constructor(r,s,p,P){this.store=r,this.fb=s,this.dialogRef=p,this.data=P,Ne.set(this,new re.x),this.periodValidator=Ve,this.negativeValidator=Ye,this.daysValidator=Fe,this.unitsOptions=["Second","Minute","Hour"].map(b=>({id:b,text:b})),this.processCode=this.data.code,this.form=s.group({}),this.days=this.fb.group({Sunday:!1,Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1})}ngOnInit(){this.store.loadWorkingDays(this.processCode),this.workingDays$=this.store.workingDays$.pipe((0,x.b)(r=>this.days.patchValue({Sunday:r.days.includes("Sunday"),Monday:r.days.includes("Monday"),Tuesday:r.days.includes("Tuesday"),Wednesday:r.days.includes("Wednesday"),Thursday:r.days.includes("Thursday"),Friday:r.days.includes("Friday"),Saturday:r.days.includes("Saturday")})))}addNewWorkingHours(){this.store.addNewWorkingHours()}deleteWorkingHours(r){this.store.deleteWorkingHours(r)}onCreateWorkingTime(){let r;this.form.valid&&(r=Object.assign(Object.assign({},this.form.value),{days:this.getDays()}),this.store.updateWorkingDays(this.processCode,r)),this.store.isWorkingHoursUpdated.pipe((0,x.b)(s=>{s&&this.dialogRef.close()}),(0,f.R)((0,U.Q_)(this,Ne,"f"))).subscribe()}getDays(){const r=this.filterTruthyProps(this.days.value);return Object.keys(r).join(",")}ngOnDestroy(){(0,U.Q_)(this,Ne,"f").next(),(0,U.Q_)(this,Ne,"f").unsubscribe()}filterTruthyProps(r){return Object.fromEntries(Object.entries(r).filter(([s,p])=>p))}}var ke;function et(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",35)(1,"daily-working-form",36),e.NdJ("delete",function(){const b=e.CHM(s).index;return e.oxw(2).deleteWorkingHours(b)}),e.qZA()()}if(2&a){const s=r.index;e.xp6(1),e.Q6J("index",s)}}function tt(a,r){1&a&&(e.TgZ(0,"div")(1,"p",37),e._uU(2," Please select at least one day. "),e.qZA()())}Ne=new WeakMap,xe.\u0275fac=function(r){return new(r||xe)(e.Y36(g.O),e.Y36(M.qu),e.Y36($.so),e.Y36($.WI))},xe.\u0275cmp=e.Xpm({type:xe,selectors:[["ng-component"]],decls:2,vars:3,consts:[[4,"ngIf"],["cc-std-dialog",""],[1,"d-flex","justify-content-center","position-relative"],["cc-dialog-title","",1,"col-10","text-center"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close","",2,"position","absolute","right","5px"],[3,"formGroup","ccConnectForm"],[1,"d-flex","flex-cloumn","flex-md-row","py-5"],[1,"col-4"],[1,"d-flex","flex-column","col-6","position-relative"],[1,""],["type","button",1,"rounded-circle","d-flex","align-items-center","justify-content-center","float-right",2,"width","fit-content","width","35px","height","35px","margin","0 auto","color","#fff","appearance","none","border","none","outline","none","background","red","position","absolute","right","-30px","top","-20px",3,"click"],[2,"font-size","36px"],["formArrayName","workingHours","class","d-flex flex-column align-items-center",4,"ngFor","ngForOf"],[1,"d-flex",3,"formGroup","ccValidateBy"],[1,"d-flex","flex-column","gap-1"],["formControlName","Sunday"],["formControlName","Monday"],["formControlName","Tuesday"],["formControlName","Wednesday"],["formControlName","Thursday"],["formControlName","Friday"],["formControlName","Saturday"],[1,"col-12",2,"width","100%","display","flex","flex-direction","column","gap","1.5rem 0","margin-block","3.5rem","place-content","center","place-items","center"],[1,"col-12","d-flex","gap-1","px-0"],[1,"col-md-4","px-0"],[1,"row","d-flex","col-md-6","justify-content-center","align-items-center","px-0"],["type","number","label","period","formControlName","snoozePeriod",1,"col-md-6",3,"ccValidateBy"],["label","select unit","formControlName","snoozePeriodUnit",1,"col-md-6",3,"data"],["label","period","type","number","formControlName","recheckPeriod",1,"col-md-6",3,"ccValidateBy"],["label","select unit","formControlName","recheckPeriodUnit",1,"col-md-6",3,"data"],["label","period","type","number","formControlName","pausePeriodBetweenMaids",1,"col-md-6",3,"ccValidateBy"],["label","select unit","formControlName","pausePeriodBetweenMaidsUnit",1,"col-md-6",3,"data"],[2,"gap","0 0.5rem"],["cc-raised-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"],["formArrayName","workingHours",1,"d-flex","flex-column","align-items-center"],[3,"index","delete"],[2,"color","rgba(255, 0, 0, 0.927)"]],template:function(r,s){1&r&&(e.YNc(0,Me,61,27,"ng-container",0),e.ALo(1,"async")),2&r&&e.Q6J("ngIf",e.lcZ(1,1,s.workingDays$))},directives:[l.O5,c.iK,c.Cj,c.Zb,t.uu,c.fX,c.zn,c.kL,M._Y,M.JL,M.sg,k.Ls,ae.k_,o.Q9,l.sg,M.CE,te,Ee.KE,Ze.E,M.JJ,M.u,O.G,ee.jB,c.Zu],pipes:[l.Ov],encapsulation:2,changeDetection:0});const ot=function(a){return[a]},ze=function(a,r){return[a,r]};function st(a,r){if(1&a){const s=e.EpF();e.ynx(0),e.TgZ(1,"div",1)(2,"cc-dialog-header",2)(3,"h1",3),e._uU(4),e.qZA(),e._UZ(5,"a",4),e.qZA(),e.TgZ(6,"cc-dialog-content")(7,"form",5)(8,"div",6)(9,"cc-label",7),e._uU(10,"Daily working hours:"),e.qZA(),e.TgZ(11,"div",8)(12,"div",9)(13,"button",10),e.NdJ("click",function(){return e.CHM(s),e.oxw().addNewWorkingHours()}),e.TgZ(14,"cc-icon",11),e._uU(15,"add"),e.qZA()()(),e.YNc(16,et,2,1,"div",12),e.qZA()(),e.TgZ(17,"div",13)(18,"cc-label",7),e._uU(19,"Working days:"),e.qZA(),e.TgZ(20,"div",14)(21,"cc-checkbox",15),e._uU(22,"Sunday"),e.qZA(),e.TgZ(23,"cc-checkbox",16),e._uU(24,"Monday"),e.qZA(),e.TgZ(25,"cc-checkbox",17),e._uU(26,"Tuesday"),e.qZA(),e.TgZ(27,"cc-checkbox",18),e._uU(28,"Wednesday"),e.qZA(),e.TgZ(29,"cc-checkbox",19),e._uU(30,"Thursday"),e.qZA(),e.TgZ(31,"cc-checkbox",20),e._uU(32,"Friday"),e.qZA(),e.TgZ(33,"cc-checkbox",21),e._uU(34,"Saturday"),e.qZA()(),e.YNc(35,tt,3,0,"div",0),e.qZA(),e.TgZ(36,"div",22)(37,"div",23)(38,"cc-label",24),e._uU(39," Process snooze period: "),e.qZA(),e.TgZ(40,"div",25),e._UZ(41,"cc-input",26)(42,"cc-select",27),e.qZA()(),e.TgZ(43,"div",23)(44,"cc-label",24),e._uU(45," Recheck snooze period: "),e.qZA(),e.TgZ(46,"div",25),e._UZ(47,"cc-input",28)(48,"cc-select",29),e.qZA()(),e.TgZ(49,"div",23)(50,"cc-label",24),e._uU(51," Pause period between maids: "),e.qZA(),e.TgZ(52,"div",25),e._UZ(53,"cc-input",30)(54,"cc-select",31),e.qZA()()()()(),e.TgZ(55,"cc-dialog-actions",32)(56,"button",33),e._uU(57,"Close"),e.qZA(),e.TgZ(58,"button",34),e.NdJ("click",function(){return e.CHM(s),e.oxw().onCreateDefault()}),e._uU(59," save "),e.qZA()()(),e.BQk()}if(2&a){const s=r.ngIf,p=e.oxw();e.xp6(4),e.Oqu("Default Working Days/Hours"),e.xp6(3),e.Q6J("formGroup",p.form)("ccConnectForm",s),e.xp6(9),e.Q6J("ngForOf",p.form.get("workingHours").controls),e.xp6(1),e.Q6J("formGroup",p.days)("ccValidateBy",e.VKq(14,ot,p.daysValidator)),e.xp6(18),e.Q6J("ngIf",p.days.hasError("no-days-selected")),e.xp6(6),e.Q6J("ccValidateBy",e.WLB(16,ze,p.periodValidator,p.negativeValidator)),e.xp6(1),e.Q6J("data",p.unitsOptions),e.xp6(5),e.Q6J("ccValidateBy",e.WLB(19,ze,p.periodValidator,p.negativeValidator)),e.xp6(1),e.Q6J("data",p.unitsOptions),e.xp6(5),e.Q6J("ccValidateBy",e.WLB(22,ze,p.periodValidator,p.negativeValidator)),e.xp6(1),e.Q6J("data",p.unitsOptions),e.xp6(4),e.Q6J("disabled",!p.form.valid||!p.days.valid)}}const rt=a=>["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"].some(p=>!0===a.get(p).value)?null:{"no-days-selected":!0},nt=a=>/^-?\d+$/.test(a.value)?null:{"not-a-number":"value is not a number"},it=a=>a.value<0?{"negative-number":"value should not be negative"}:null;class Le{constructor(r,s,p){this.store=r,this.fb=s,this.dialogRef=p,ke.set(this,new re.x),this.periodValidator=nt,this.negativeValidator=it,this.daysValidator=rt,this.unitsOptions=["Second","Minute","Hour"].map(P=>({id:P,text:P})),this.form=s.group({}),this.days=this.fb.group({Sunday:!1,Monday:!1,Tuesday:!1,Wednesday:!1,Thursday:!1,Friday:!1,Saturday:!1}),this.workingDays$=this.store.workingDays$.pipe((0,x.b)(P=>{var b,Z,W,oe,Re,He,gt;P&&this.days.patchValue({Sunday:null===(b=P.days)||void 0===b?void 0:b.includes("Sunday"),Monday:null===(Z=P.days)||void 0===Z?void 0:Z.includes("Monday"),Tuesday:null===(W=P.days)||void 0===W?void 0:W.includes("Tuesday"),Wednesday:null===(oe=P.days)||void 0===oe?void 0:oe.includes("Wednesday"),Thursday:null===(Re=P.days)||void 0===Re?void 0:Re.includes("Thursday"),Friday:null===(He=P.days)||void 0===He?void 0:He.includes("Friday"),saturday:null===(gt=P.days)||void 0===gt?void 0:gt.includes("saturday")})}))}ngOnInit(){this.store.loadDefaultWorkingDays()}addNewWorkingHours(){this.store.addNewWorkingHours()}deleteWorkingHours(r){this.store.deleteWorkingHours(r)}onCreateDefault(){let r;this.form.valid&&(r=Object.assign(Object.assign({},this.form.value),{days:this.getDays()}),this.store.updateDefaultWorkingDays(r)),this.store.isWorkingHoursUpdated.pipe((0,x.b)(s=>{s&&this.dialogRef.close()}),(0,f.R)((0,U.Q_)(this,ke,"f"))).subscribe()}ngOnDestroy(){(0,U.Q_)(this,ke,"f").next(),(0,U.Q_)(this,ke,"f").complete()}getDays(){const r=this.filterTruthyProps(this.days.value);return Object.keys(r).join(",")}filterTruthyProps(r){return Object.fromEntries(Object.entries(r).filter(([s,p])=>p))}}ke=new WeakMap,Le.\u0275fac=function(r){return new(r||Le)(e.Y36(g.O),e.Y36(M.qu),e.Y36($.so))},Le.\u0275cmp=e.Xpm({type:Le,selectors:[["ng-component"]],decls:2,vars:3,consts:[[4,"ngIf"],["cc-std-dialog",""],[1,"d-flex","justify-content-center","position-relative"],["cc-dialog-title","",1,"col-10","text-center"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close","",2,"position","absolute","right","5px"],[3,"formGroup","ccConnectForm"],[1,"d-flex","flex-cloumn","flex-md-row","py-5"],[1,"col-4"],[1,"d-flex","flex-column","col-6","position-relative"],[1,""],["type","button",1,"rounded-circle","d-flex","align-items-center","justify-content-center","float-right",2,"width","fit-content","width","35px","height","35px","margin","0 auto","color","#fff","appearance","none","border","none","outline","none","background","red","position","absolute","right","-30px","top","-20px",3,"click"],[2,"font-size","36px"],["formArrayName","workingHours","class","d-flex flex-column align-items-center",4,"ngFor","ngForOf"],[1,"d-flex",3,"formGroup","ccValidateBy"],[1,"d-flex","flex-column","gap-1"],["formControlName","Sunday"],["formControlName","Monday"],["formControlName","Tuesday"],["formControlName","Wednesday"],["formControlName","Thursday"],["formControlName","Friday"],["formControlName","Saturday"],[1,"col-12",2,"width","100%","display","flex","flex-direction","column","gap","1.5rem 0","margin-block","3.5rem","place-content","center","place-items","center"],[1,"col-12","d-flex","gap-1","px-0"],[1,"col-md-4","px-0"],[1,"row","d-flex","col-md-6","justify-content-center","align-items-center","px-0"],["type","number","label","period","formControlName","snoozePeriod",1,"col-md-6",3,"ccValidateBy"],["label","select unit","formControlName","snoozePeriodUnit",1,"col-md-6",3,"data"],["label","period","type","number","formControlName","recheckPeriod",1,"col-md-6",3,"ccValidateBy"],["label","select unit","formControlName","recheckPeriodUnit",1,"col-md-6",3,"data"],["label","period","type","number","formControlName","pausePeriodBetweenMaids",1,"col-md-6",3,"ccValidateBy"],["label","select unit","formControlName","pausePeriodBetweenMaidsUnit",1,"col-md-6",3,"data"],[2,"gap","0 0.5rem"],["cc-raised-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"],["formArrayName","workingHours",1,"d-flex","flex-column","align-items-center"],[3,"index","delete"],[2,"color","rgba(255, 0, 0, 0.927)"]],template:function(r,s){1&r&&(e.YNc(0,st,60,25,"ng-container",0),e.ALo(1,"async")),2&r&&e.Q6J("ngIf",e.lcZ(1,1,s.workingDays$))},directives:[l.O5,c.iK,c.Cj,c.Zb,t.uu,c.fX,c.zn,c.kL,M._Y,M.JL,M.sg,k.Ls,ae.k_,o.Q9,l.sg,M.CE,te,Ee.KE,Ze.E,M.JJ,M.u,O.G,ee.jB,c.Zu],pipes:[l.Ov],encapsulation:2,changeDetection:0});var We=i(79136);const ct=["form"];function at(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"cc-slide-toggle",16,17),e.NdJ("change",function(){const b=e.CHM(s).$implicit,Z=e.MAs(1);return e.oxw(2).updateProcessStatus(b.id,Z.value)}),e.TgZ(2,"span",6),e._uU(3,"On/Off"),e.qZA()()}if(2&a){const s=r.$implicit;e.MGl("name","processStatus+",r.index,""),e.Q6J("ngModel",s.active)}}function lt(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"cc-slide-toggle",18,19),e.NdJ("change",function(){const b=e.CHM(s).$implicit,Z=e.MAs(1);return e.oxw(2).updateProcessIncludedMaidsStatus(b.id,Z.value)}),e.TgZ(2,"span",6),e._uU(3,"On/Off"),e.qZA()()}if(2&a){const s=r.$implicit;e.Q6J("ngModel",s.includedMaidsOnly)("checked",s.includedMaidsOnly)}}function dt(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",20)(1,"cc-menu",21)(2,"button",22),e.NdJ("click",function(){const b=e.CHM(s).rowData;return e.oxw(2).openExcludeMaidsDialog(b.id,b.name,b.requestType)}),e._uU(3," Exclude Maids "),e.qZA(),e.TgZ(4,"button",22),e.NdJ("click",function(){const b=e.CHM(s).rowData;return e.oxw(2).openIncludeMaidsDialog(b.id,b.name,b.requestType)}),e._uU(5," Included Maids "),e.qZA(),e.TgZ(6,"button",22),e.NdJ("click",function(){const b=e.CHM(s).rowData;return e.oxw(2).openControlPeriodsDialog(b.code)}),e._uU(7," Control Periods "),e.qZA(),e.TgZ(8,"button",22),e.NdJ("click",function(){return e.CHM(s),e.oxw(2).onRunProcessor()}),e._uU(9," Run Processor For 1 Time "),e.qZA()()()}if(2&a){const s=e.oxw(2);e.xp6(1),e.Q6J("ccTriggerButton",s.mainButton)}}const Oe=function(a,r,s){return{active:a,includedMaidsOnly:r,operations:s}};function pt(a,r){if(1&a&&(e.ynx(0),e._UZ(1,"cc-datagrid",10),e.TgZ(2,"form",null,11),e.YNc(4,at,4,2,"ng-template",12,13,e.W1O),e.qZA(),e.YNc(6,lt,4,2,"ng-template",12,14,e.W1O),e.YNc(8,dt,10,1,"ng-template",12,15,e.W1O),e.BQk()),2&a){const s=r.ngIf,p=e.MAs(5),P=e.MAs(7),b=e.MAs(9),Z=e.oxw();e.xp6(1),e.Q6J("columns",Z.gridCols)("data",s)("showPaginator",!1)("loading",!1)("cellTemplate",e.kEZ(8,Oe,p,P,b)),e.xp6(3),e.Q6J("ccGridCell",s),e.xp6(2),e.Q6J("ccGridCell",s),e.xp6(2),e.Q6J("ccGridCell",s)}}const ut=[{field:"operations",header:"Operations"},{field:"name",header:"Processor name"},{field:"excludedCount",header:"Excluded maids"},{field:"includedMaidsOnly",header:"Included maids only"},{field:"active",header:"Processor status"}];let h=class{constructor(r,s){this.store=r,this.dialog=s,this.gridCols=ut,this.mainButton={icon:"menu",type:"icon",color:"primary"},this.isProcessesToggled=!1}ngOnInit(){this.store.loadProcessList(),this.processList$=this.store.processList$.pipe((0,x.b)(r=>{this.isAllProcessActive=r.every(s=>s.active)}))}ngAfterViewInit(){this.form.valueChanges.pipe((0,x.b)(r=>{const s=Object.values(r);this.isAllProcessActive=s.every(p=>!!p)})).subscribe()}updateProcessesStatus(r){this.store.updateProcessesStatus({active:r,module:"visa"}),Object.keys(this.form.controls).forEach(p=>{var P;return null===(P=this.form.control.get(p))||void 0===P?void 0:P.setValue(r)}),this.form.control.updateValueAndValidity()}updateProcessStatus(r,s){this.store.updateProcessStatus(r,{active:s,module:"visa"})}updateProcessIncludedMaidsStatus(r,s){this.store.updateProcessIncludeMaidsStatus(r,{active:s})}openExcludeMaidsDialog(r,s,p){this.dialog.originalOpen(le,{width:"100%",restoreFocus:!0,data:{processorId:r,processorName:s,requestType:p}})}openIncludeMaidsDialog(r,s,p){this.dialog.originalOpen(_,{width:"100%",restoreFocus:!0,data:{processorId:r,processorName:s,requestType:p}})}openControlPeriodsDialog(r){this.dialog.originalOpen(xe,{width:"75%",restoreFocus:!0,data:{code:r}})}openDefaultWorkingHoursForm(){this.dialog.originalOpen(Le,{width:"75%",restoreFocus:!0})}onRunProcessor(){alert("not implemented yet")}};h.\u0275fac=function(r){return new(r||h)(e.Y36(g.O),e.Y36(c.uY))},h.\u0275cmp=e.Xpm({type:h,selectors:[["rpa-processors"]],viewQuery:function(r,s){if(1&r&&e.Gf(ct,5),2&r){let p;e.iGM(p=e.CRH())&&(s.form=p.first)}},decls:17,vars:7,consts:[["cc-std-dialog",""],[2,"position","relative","padding-block","1rem"],["cc-dialog-title","",2,"position","absolute","transform","translateX(-50%)","left","50%"],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close","",2,"font-weight","900","font-size","18px","position","absolute","right","0.2rem"],[1,"rpa-processor-actions"],["ngModel","",3,"ngModel","labelPosition","checked","ngModelChange"],[2,"color","black !important"],["type","button","cc-raised-button","","color","accent",3,"click"],[4,"ngIf"],["cc-raised-button","","color","accent","cc-dialog-close",""],[1,"my-2",3,"columns","data","showPaginator","loading","cellTemplate"],["form","ngForm"],[3,"ccGridCell"],["statusTmp",""],["includedMaidsTmp",""],["operationsTmp",""],["ngModel","",3,"ngModel","name","change"],["isActive","ngModel"],["ngModel","",3,"ngModel","checked","change"],["includedMaids","ngModel"],[1,"row","justify-content-center","align-items-center"],[2,"font-size","20px","font-weight","900",3,"ccTriggerButton"],["cc-button","","cc-menu-item","",3,"click"]],template:function(r,s){1&r&&(e.TgZ(0,"div",0)(1,"cc-dialog-header",1)(2,"h1",2),e._uU(3),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4)(6,"cc-slide-toggle",5),e.NdJ("ngModelChange",function(P){return s.isAllProcessActive=P})("ngModelChange",function(P){return s.updateProcessesStatus(P)}),e.TgZ(7,"span",6),e._uU(8,"Turn ON/OFF all processors"),e.qZA()(),e.TgZ(9,"button",7),e.NdJ("click",function(){return s.openDefaultWorkingHoursForm()}),e._uU(10," RPA Working Days/Hours Default Values "),e.qZA()(),e.TgZ(11,"cc-dialog-content"),e.YNc(12,pt,10,12,"ng-container",8),e.ALo(13,"async"),e.qZA(),e.TgZ(14,"cc-dialog-actions")(15,"button",9),e._uU(16,"Cancel"),e.qZA()()()),2&r&&(e.xp6(3),e.hij(" "," RPA Processors "," "),e.xp6(3),e.Q6J("ngModel",s.isAllProcessActive)("labelPosition","before")("checked",s.isAllProcessActive),e.xp6(6),e.Q6J("ngIf",e.lcZ(13,5,s.processList$)))},directives:[c.iK,c.Cj,c.Zb,t.uu,c.fX,c.zn,We.I,M.JJ,M.On,c.kL,l.O5,L.Ge,M._Y,M.JL,M.F,L.VC,y.OL,y.Y,c.Zu],pipes:[l.Ov],encapsulation:2,changeDetection:0}),h=(0,U.gn)([k.kG],h);let v=(()=>{class a{constructor(s,p,P,b){this.mediaService=s,this.notification=p,this.dialogRef=P,this.data=b,this.title=this.data.attachment.name,this.uuid=this.data.attachment.uuid,this.downloadLink=[ue.b.download,this.uuid].join("/")}onDownload(){this.mediaService.downloadFile(["public","download",this.uuid].join("/")).pipe((0,x.b)(()=>{this.dialogRef.close(),this.notification.notifyInfo("Downloading the file...")})).subscribe()}}return a.\u0275fac=function(s){return new(s||a)(e.Y36(K.yJ),e.Y36(K.zg),e.Y36($.so),e.Y36($.WI))},a.\u0275cmp=e.Xpm({type:a,selectors:[["ng-component"]],decls:13,vars:3,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["role","button","cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],[1,"d-block","w-100","h-100",2,"object-fit","contain",3,"src","alt"],["cc-dialog-actions",""],["cc-raised-button","","color","accent",3,"click"],[2,"font-size","36px"]],template:function(s,p){1&s&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4),e._UZ(6,"img",5),e.qZA(),e.TgZ(7,"div",6)(8,"button",7),e.NdJ("click",function(){return p.onDownload()}),e.TgZ(9,"cc-icon",8),e._uU(10,"download"),e.qZA(),e.TgZ(11,"span"),e._uU(12,"Download"),e.qZA()()()()),2&s&&(e.xp6(3),e.Oqu(p.title),e.xp6(3),e.Q6J("src",p.downloadLink,e.LSH)("alt",p.title))},directives:[c.iK,c.Cj,c.Zb,c.zn,c.fX,c.kL,c.Zu,t.uu,o.Q9],encapsulation:2}),a})();var E,d=i(92340);function Q(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"button",10),e.NdJ("click",function(){const b=e.CHM(s).ngIf;return e.oxw(3).openPreviewScreenshotDialog(b)}),e._uU(1," Download Error Screenshot "),e.qZA()}}function ne(a,r){if(1&a){const s=e.EpF();e.ynx(0),e.TgZ(1,"button",11),e.NdJ("click",function(){e.CHM(s);const P=e.oxw().$implicit;return e.oxw(2).toggleRelatedRPAProcessor(P.process)}),e._uU(2," Stop related RPA processor "),e.qZA(),e.BQk()}}function X(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"cc-menu",6)(1,"div",7),e.YNc(2,Q,2,0,"button",8),e.TgZ(3,"button",9),e.NdJ("click",function(){const b=e.CHM(s).$implicit;return e.oxw(2).navToRelatedVpStepPage(b.url)}),e._uU(4," Go to related VP step "),e.qZA(),e.YNc(5,ne,3,0,"ng-container",0),e.qZA()()}if(2&a){const s=r.$implicit,p=e.oxw(2);e.Q6J("ccTriggerButton",p.mainMenu),e.xp6(2),e.Q6J("ngIf",s.attachments[0]),e.xp6(3),e.Q6J("ngIf",s.process.active)}}const Je=function(){return[]},mt=function(a){return{actions:a}};function ft(a,r){if(1&a){const s=e.EpF();e.ynx(0),e.TgZ(1,"div",1)(2,"button",2),e.NdJ("click",function(){return e.CHM(s),e.oxw().openRpaProcessorsDialog()}),e._uU(3," RPA Processors "),e.qZA(),e.TgZ(4,"button",2),e.NdJ("click",function(){return e.CHM(s),e.oxw().navToErrorReportPage()}),e._uU(5," Show Error Report "),e.qZA(),e.TgZ(6,"button",2),e.NdJ("click",function(){return e.CHM(s),e.oxw().navToParameterPage()}),e._uU(7," RPA Parameters "),e.qZA(),e.TgZ(8,"button",2),e.NdJ("click",function(){return e.CHM(s),e.oxw().navToProcessesReport()}),e._uU(9," Processes Report "),e.qZA(),e.TgZ(10,"button",2),e.NdJ("click",function(){return e.CHM(s),e.oxw().navToPcProcesses()}),e._uU(11," Manage Pc Processes "),e.qZA()(),e.TgZ(12,"cc-datagrid",3),e.NdJ("page",function(P){return e.CHM(s),e.oxw().handleNextPage(P)}),e.qZA(),e.YNc(13,X,6,3,"ng-template",4,5,e.W1O),e.BQk()}if(2&a){const s=e.MAs(14),p=e.oxw();e.xp6(12),e.Q6J("data",p.errorList.content||e.DdM(7,Je))("columns",p.gridCols)("length",p.errorList.totalElements)("pageIndex",p.errorList.number)("pageSize",p.errorList.size)("cellTemplate",e.VKq(8,mt,s)),e.xp6(1),e.Q6J("ccGridCell",p.errorList.content)}}const _t=[{field:"actions",header:"Actions"},{field:"process.name",header:"VP Step name"},{field:"name",header:"Maid name"},{field:"creationDate",header:"Error date/time"}];class $e{constructor(r,s,p,P,b){this.store=r,this.service=s,this.router=p,this.dialog=P,this.cdr=b,E.set(this,new ce.w0),this.search=this.store.initialSearchVal,this.gridCols=_t,this.baseDownloadUrl=ue.b.download,this.mainMenu={icon:"menu",type:"icon",color:"primary"}}ngOnInit(){this.loadErrorsList(this.search),this.store.loadProcessList(),this.processList$=this.store.processList$;const r=this.store.search$.pipe((0,x.b)(s=>this.search=s)).subscribe();(0,U.Q_)(this,E,"f").add(r)}handleNextPage(r){const s={params:{page:r.pageIndex,size:r.pageSize}};this.store.updateSearchState(s),this.loadErrorsList(s)}toggleRelatedRPAProcessor(r){this.store.updateProcessStatus(r.id,{active:!1,module:r.module}),this.store.processStateIsUpdated.subscribe(s=>{s&&this.loadErrorsList(this.search)})}openRpaProcessorsDialog(){this.dialog.originalOpen(h,{width:"80%"})}openPreviewScreenshotDialog(r){this.dialog.originalOpen(v,{panelClass:["col-md-10"],data:{attachment:r}})}navToProcessesReport(){this.router.navigateByUrl(["visa","v2","rpa","rpa-controller","processes-report"].join("/"))}navToParameterPage(){window.open(d.N.production&&!d.N.newErp?"main.html#!/visa/rpa/parameters":"visa/rpa/parameters","__blank")}navToErrorReportPage(){window.open(d.N.production&&!d.N.newErp?"main.html#!/visa/rpa/errors-report":"visa/rpa/errors-report","__blank")}navToRelatedVpStepPage(r){console.log(r),window.open(d.N.production&&!d.N.newErp?"main.html#!/"+r:r,"__blank")}navToPcProcesses(){window.open(d.N.production&&!d.N.newErp?"main.html#!/visa/v2/rpa/rpa-controller/pc-processes":"visa/v2/rpa/rpa-controller/pc-processes","__blank")}ngOnDestroy(){(0,U.Q_)(this,E,"f").unsubscribe()}loadErrorsList(r){this.service.getErrorList(r).subscribe(s=>{this.errorList=s,this.cdr.detectChanges()})}}E=new WeakMap,$e.\u0275fac=function(r){return new(r||$e)(e.Y36(g.O),e.Y36(u.a),e.Y36(he.F0),e.Y36(c.uY),e.Y36(e.sBO))},$e.\u0275cmp=e.Xpm({type:$e,selectors:[["app-rpa-controller"]],decls:1,vars:1,consts:[[4,"ngIf"],[1,"btn-group","d-flex",2,"margin-block","2.6rem 1.5rem","gap","0 0.5rem"],["cc-raised-button","","color","accent",3,"click"],[1,"my-2",3,"data","columns","length","pageIndex","pageSize","cellTemplate","page"],[3,"ccGridCell"],["actionsRef",""],[2,"font-size","20px","font-weight","900",3,"ccTriggerButton"],[1,"action-group","d-flex","flex-column","gap-1","px-3","py-2"],["cc-menu-item","","cc-flat-button","","title","Download Error screenshot",3,"click",4,"ngIf"],["cc-menu-item","","cc-flat-button","","title","Go to related VP step",3,"click"],["cc-menu-item","","cc-flat-button","","title","Download Error screenshot",3,"click"],["cc-menu-item","","cc-flat-button","","title","Stop related RPA processor",3,"click"]],template:function(r,s){1&r&&e.YNc(0,ft,15,10,"ng-container",0),2&r&&e.Q6J("ngIf",s.errorList)},directives:[l.O5,t.uu,L.Ge,L.VC,y.OL,y.Y],encapsulation:2,changeDetection:0});var Ge=i(34378);let je=class{constructor(r,s,p){this.fb=r,this.store=s,this.cdr=p,this.search=new e.vpe,this.reset=new e.vpe,this.canReset=!1,this.hasSearched=!1,this.form=this.fb.group({roboticProcess:"",rpaPc:""})}ngOnInit(){this.store.loadRpaPcList(),this.store.loadRoboticProcessList(""),this.rpaPcList$=this.store.rpaPcList$.pipe((0,n.U)(r=>r.map(s=>({id:s.id,text:s.name})))),this.roboticProcessList$=this.store.roboticProcessList$.pipe((0,n.U)(r=>r.map(s=>({id:s.id,text:s.name})))),this.form.valueChanges.subscribe({next:r=>{this.canReset=Object.values(r).some(s=>!!s),this.cdr.detectChanges()}})}onSubmit(){const r=this.filterTruthyProps(this.form.value);this.search.emit(r),this.hasSearched=!0,this.cdr.detectChanges()}onReset(){this.hasSearched&&this.canReset&&this.reset.emit(),this.form.reset(),this.hasSearched=!1,this.canReset=!1}ngOnDestroy(){}filterTruthyProps(r){return Object.fromEntries(Object.entries(r).filter(([s,p])=>p))}};function vt(a,r){1&a&&e._UZ(0,"div",9)}function bt(a,r){if(1&a&&(e.TgZ(0,"div",7),e.YNc(1,vt,1,0,"div",8),e.qZA()),2&a){const s=r.$implicit;e.xp6(1),e.Q6J("ngIf",s.showRedAlert)}}je.\u0275fac=function(r){return new(r||je)(e.Y36(M.qu),e.Y36(g.O),e.Y36(e.sBO))},je.\u0275cmp=e.Xpm({type:je,selectors:[["process-report-filter"]],outputs:{search:"search",reset:"reset"},decls:22,vars:8,consts:[[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center"],[1,"d-flex","flex-column",3,"formGroup","ngSubmit"],[1,"d-flex"],[1,"col-md-6"],["label","Robotic Process","formControlName","roboticProcess",3,"data"],["label","RPA PC","formControlName","RpaPc",3,"data"],[1,"col-md-4","d-flex","justify-content-center","gap-2","py-3","mx-auto"],["cc-raised-button","","color","accent","type","submit",2,"padding","0.15rem 1.8rem"],["cc-raised-button","","id","reset-btn",2,"padding","0.15rem 1.8rem","background-color","#808080","color","#fff",3,"disabled","click"]],template:function(r,s){1&r&&(e.TgZ(0,"cc-accordion",0)(1,"cc-panel",1)(2,"cc-panel-title",2)(3,"cc-icon"),e._uU(4,"filter_alt"),e.qZA(),e.TgZ(5,"span"),e._uU(6,"Filters"),e.qZA()(),e.TgZ(7,"cc-panel-body")(8,"div")(9,"form",3),e.NdJ("ngSubmit",function(){return s.onSubmit()}),e.TgZ(10,"div",4)(11,"div",5),e._UZ(12,"cc-select",6),e.ALo(13,"async"),e.qZA(),e.TgZ(14,"div",5),e._UZ(15,"cc-select",7),e.ALo(16,"async"),e.qZA()(),e.TgZ(17,"div",8)(18,"button",9),e._uU(19," Search "),e.qZA(),e.TgZ(20,"button",10),e.NdJ("click",function(){return s.onReset()}),e._uU(21," Reset "),e.qZA()()()()()()()),2&r&&(e.xp6(9),e.Q6J("formGroup",s.form),e.xp6(3),e.Q6J("data",e.lcZ(13,4,s.roboticProcessList$)),e.xp6(3),e.Q6J("data",e.lcZ(16,6,s.rpaPcList$)),e.xp6(5),e.Q6J("disabled",!s.canReset&&!s.hasSearched))},directives:[Ge.I,Ge.CW,Ge.LL,o.Q9,Ge.G9,M._Y,M.JL,M.sg,ee.jB,M.JJ,M.u,t.uu],pipes:[l.Ov],encapsulation:2}),je=(0,U.gn)([k.kG],je);const Ct=function(){return[]},Pt=function(a){return{showRedAlert:a}};function yt(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",3)(1,"cc-datagrid",4),e.NdJ("page",function(P){return e.CHM(s),e.oxw().handleNextPage(P)}),e.qZA(),e.YNc(2,bt,2,1,"ng-template",5,6,e.W1O),e.qZA()}if(2&a){const s=r.ngIf,p=e.MAs(3),P=e.oxw();e.xp6(1),e.Q6J("columns",P.gridCols)("data",s.content||e.DdM(7,Ct))("pageIndex",s.number)("pageSize",s.size)("length",s.totalElements)("cellTemplate",e.VKq(8,Pt,p)),e.xp6(1),e.Q6J("ccGridCell",s.content)}}const Rt=[{field:"processName",header:"ProcessName"},{field:"housemaidName",header:"Housemaid Name"},{field:"startDate",header:"Start Date"},{field:"endDate",header:"End Date"},{field:"creationDate",header:"Creation Date"},{field:"pcName",header:"PC Name",width:"100px"},{field:"status",header:"Status",width:"100px"},{field:"otherInfo",header:"Info",width:"35ch"},{field:"showRedAlert",header:"Alert",width:"100px"}];let Ie=class{constructor(r,s,p){this.store=r,this.service=s,this.cdr=p,this.gridCols=Rt,this.search=this.store.initialSearchVal}ngOnInit(){this.loadProcessesReport(this.search)}onSearch(r){const s={params:{page:0,size:20},search:r};this.store.updateSearchState(s),this.loadProcessesReport(s)}onReset(){this.store.resetSearchState(),this.loadProcessesReport(this.store.initialSearchVal)}handleNextPage(r){const s={params:{page:r.pageIndex,size:r.pageSize}};this.store.updateSearchState(s),this.loadProcessesReport(s)}loadProcessesReport(r){this.processesReport$=this.service.getProcessesReportList(r),this.cdr.detectChanges()}};Ie.\u0275fac=function(r){return new(r||Ie)(e.Y36(g.O),e.Y36(u.a),e.Y36(e.sBO))},Ie.\u0275cmp=e.Xpm({type:Ie,selectors:[["ng-component"]],decls:4,vars:3,consts:[[1,"py-2"],[3,"search","reset"],["class","processesReport",4,"ngIf"],[1,"processesReport"],[3,"columns","data","pageIndex","pageSize","length","cellTemplate","page"],[3,"ccGridCell"],["showRedAlert",""],[1,"red-alert-container"],["class","vibrate",4,"ngIf"],[1,"vibrate"]],template:function(r,s){1&r&&(e._UZ(0,"div",0),e.TgZ(1,"process-report-filter",1),e.NdJ("search",function(P){return s.onSearch(P)})("reset",function(){return s.onReset()}),e.qZA(),e.YNc(2,yt,4,10,"div",2),e.ALo(3,"async")),2&r&&(e.xp6(2),e.Q6J("ngIf",e.lcZ(3,1,s.processesReport$)))},directives:[je,l.O5,L.Ge,L.VC],pipes:[l.Ov],encapsulation:2,changeDetection:0}),Ie=(0,U.gn)([k.kG],Ie);var qe=i(32269),ht=i(8188);let we=class{constructor(r,s,p,P,b,Z){this.apiService=r,this.stateService=s,this.fb=p,this.picklist=P,this.dialogRef=b,this.notification=Z,this.form=this.fb.group({nationality:"",allAfricans:!1,replacementNationalities:"",AfricansIncluded:!1}),this.nationalitiesPageFetcher=W=>this.picklist.getPicklist({code:"nationalities",page:W.page,pageSize:W.size,search:W.searchString}).pipe((0,n.U)(oe=>oe.map(Re=>({id:Re.id,text:Re.label}))))}allAfricansChecked(r){r.checked&&this.form.controls.nationality.setValue("")}ngOnInit(){}onSave(){const{nationality:r,allAfricans:s,replacementNationalities:p,AfricansIncluded:P}=this.form.value;let b=p;(""==p||!p)&&(b=[]);let Z={allAfricans:s,replacementNationalities:b.map(W=>{if(W)return{nationality:{id:W}}})};if(p.length>0){const W=null==p?void 0:p.map(oe=>{if(oe)return{nationality:{id:oe}}});Z=Object.assign(Object.assign({},Z),{replacementNationalities:W})}s||(Z=Object.assign(Object.assign({},Z),{nationality:{id:r}})),P&&(Z.replacementNationalities=[...Z.replacementNationalities||[],{nationality:"",allAfricans:!0}]),this.apiService.addNationalityReplacementRule(Z).subscribe({next:()=>{this.dialogRef.close(),this.notification.notifySuccess("Added successfully."),this.stateService.refreshData()}})}};we.\u0275fac=function(r){return new(r||we)(e.Y36(u.a),e.Y36(qe.s),e.Y36(M.qu),e.Y36(ht.Ab),e.Y36($.so),e.Y36(K.zg))},we.\u0275cmp=e.Xpm({type:we,selectors:[["ng-component"]],decls:20,vars:9,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-content",""],[3,"formGroup"],[1,"col-12","d-flex","align-items-center"],[1,"col-6"],["label","Nationality of the new maid","formControlName","nationality",3,"lazyPageFetcher","required","disabled"],["formControlName","allAfricans",1,"col-2","float-right",3,"change"],[1,"col-12","d-flex","justify-content-around","align-items-center"],["label","Nationalities to replace","formControlName","replacementNationalities",1,"col-10",3,"lazyPageFetcher","multiple","required"],["formControlName","AfricansIncluded",1,"col-2"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close","",1,"px-4"],["cc-raised-button","","color","accent",1,"px-4",3,"disabled","click"]],template:function(r,s){1&r&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Add Nationality Replacement"),e.qZA()(),e.TgZ(4,"div",3)(5,"form",4)(6,"div",5)(7,"div",6),e._UZ(8,"cc-select",7),e.qZA(),e.TgZ(9,"cc-checkbox",8),e.NdJ("change",function(P){return s.allAfricansChecked(P)}),e._uU(10,"Africans"),e.qZA()(),e.TgZ(11,"div",9),e._UZ(12,"cc-select",10),e.TgZ(13,"cc-checkbox",11),e._uU(14,"Africans"),e.qZA()()()(),e.TgZ(15,"div",12)(16,"button",13),e._uU(17,"Cancel"),e.qZA(),e.TgZ(18,"button",14),e.NdJ("click",function(){return s.onSave()}),e._uU(19," Save "),e.qZA()()()),2&r&&(e.xp6(2),e.Q6J("align","center"),e.xp6(3),e.Q6J("formGroup",s.form),e.xp6(3),e.Q6J("lazyPageFetcher",s.nationalitiesPageFetcher)("required",!s.form.value.allAfricans)("disabled",s.form.value.allAfricans),e.xp6(4),e.Q6J("lazyPageFetcher",s.nationalitiesPageFetcher)("multiple",!0)("required",!s.form.value.AfricansIncluded),e.xp6(6),e.Q6J("disabled",!s.form.valid))},directives:[c.iK,c.Cj,c.Zb,c.kL,M._Y,M.JL,M.sg,ee.jB,M.JJ,M.u,M.Q7,Ze.E,c.Zu,t.uu,c.zn],encapsulation:2,changeDetection:0}),we=(0,U.gn)([k.kG],we);let De=class{constructor(r,s,p,P,b,Z,W){this.apiService=r,this.stateService=s,this.fb=p,this.picklist=P,this.dialogRef=b,this.notification=Z,this.data=W,this.replacementNationalitiesIds=[],this.form=this.fb.group({nationality:"",allAfricans:!1,replacementNationalities:"",AfricansIncluded:!1}),this.nationalitiesPageFetcher=oe=>this.picklist.getPicklist({code:"nationalities",page:oe.page,pageSize:oe.size,search:oe.searchString}).pipe((0,n.U)(Re=>Re.map(He=>({id:He.id,text:He.label})))),this.extraNationalitiesToReplace=this.data.rule.replacementNationalities.map(oe=>({id:oe.nationality.id,text:oe.nationality.label})).filter(oe=>oe.text),this.extraNationality=[{id:this.data.rule.nationality.id,text:this.data.rule.nationality.label}]}allAfricansChecked(r){r.checked&&this.form.controls.nationality.setValue("")}ngOnInit(){this.populateFormField(),console.log(this.data.rule)}onSave(){const{nationality:r,allAfricans:s,replacementNationalities:p,AfricansIncluded:P}=this.form.value;let b=p;(""==p||!p)&&(b=[]);let Z={id:this.data.rule.id,allAfricans:s,replacementNationalities:b.map(W=>{if(W)return{nationality:{id:W}}})};s||(Z=Object.assign(Object.assign({},Z),{nationality:{id:r}})),Z=Object.assign(Object.assign({},Z),s?{nationality:""}:{nationality:{id:r}}),P&&(Z.replacementNationalities=[...Z.replacementNationalities||[],{nationality:"",allAfricans:!0}]),this.apiService.updateNationalityReplacementRule(Z).subscribe({next:()=>{this.dialogRef.close(),this.notification.notifySuccess("Updated successfully."),this.stateService.refreshData()}})}populateFormField(){var r;const s=(null===(r=this.data.rule.nationality)||void 0===r?void 0:r.id)||"",p=this.data.rule.allAfricans,P=this.data.rule.replacementNationalities.filter(Z=>Z.nationality.label).map(Z=>Z.nationality.id),b=this.data.rule.replacementNationalities.some(Z=>Z.allAfricans);this.form.patchValue({nationality:s,allAfricans:p,replacementNationalities:P,AfricansIncluded:b})}};function Tt(a,r){if(1&a&&(e.TgZ(0,"div")(1,"span",8),e._uU(2),e.qZA()()),2&a){const s=r.$implicit;e.xp6(2),e.Oqu(s.allAfricans?"Africans":s.nationality.label)}}function xt(a,r){if(1&a&&(e.TgZ(0,"span",11),e._uU(1),e.qZA()),2&a){const s=r.$implicit;e.xp6(1),e.hij(" ",s.allAfricans?"Africans":s.nationality.label," ")}}function St(a,r){if(1&a&&(e.TgZ(0,"div",9),e.YNc(1,xt,2,1,"span",10),e.qZA()),2&a){const s=r.$implicit;e.xp6(1),e.Q6J("ngForOf",s.replacementNationalities)}}function At(a,r){if(1&a){const s=e.EpF();e.TgZ(0,"div",12)(1,"button",13),e.NdJ("click",function(){const b=e.CHM(s).$implicit;return e.oxw().openEditDialog(b)}),e.TgZ(2,"cc-icon"),e._uU(3,"edit"),e.qZA()(),e.TgZ(4,"button",13),e.NdJ("click",function(){const b=e.CHM(s).$implicit;return e.oxw().onDelete(b)}),e.TgZ(5,"cc-icon",14),e._uU(6,"delete"),e.qZA()()()}}De.\u0275fac=function(r){return new(r||De)(e.Y36(u.a),e.Y36(qe.s),e.Y36(M.qu),e.Y36(ht.Ab),e.Y36($.so),e.Y36(K.zg),e.Y36($.WI))},De.\u0275cmp=e.Xpm({type:De,selectors:[["ng-component"]],decls:20,vars:11,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-content",""],[3,"formGroup"],[1,"col-12","d-flex","align-items-center"],[1,"col-6"],["label","Nationality of the new maid","formControlName","nationality",3,"lazyPageFetcher","modelOptions","disabled","required"],["formControlName","allAfricans",1,"col-2","float-right",3,"change"],[1,"col-12","d-flex","justify-content-around","align-items-center"],["label","Nationalities to replace","formControlName","replacementNationalities",1,"col-10",3,"lazyPageFetcher","modelOptions","multiple","required"],["formControlName","AfricansIncluded",1,"col-2"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close","",1,"px-4"],["cc-raised-button","","color","accent",1,"px-4",3,"disabled","click"]],template:function(r,s){1&r&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Edit Nationality Replacement"),e.qZA()(),e.TgZ(4,"div",3)(5,"form",4)(6,"div",5)(7,"div",6),e._UZ(8,"cc-select",7),e.qZA(),e.TgZ(9,"cc-checkbox",8),e.NdJ("change",function(P){return s.allAfricansChecked(P)}),e._uU(10,"Africans"),e.qZA()(),e.TgZ(11,"div",9),e._UZ(12,"cc-select",10),e.TgZ(13,"cc-checkbox",11),e._uU(14,"Africans"),e.qZA()()()(),e.TgZ(15,"div",12)(16,"button",13),e._uU(17,"Cancel"),e.qZA(),e.TgZ(18,"button",14),e.NdJ("click",function(){return s.onSave()}),e._uU(19," Save "),e.qZA()()()),2&r&&(e.xp6(2),e.Q6J("align","center"),e.xp6(3),e.Q6J("formGroup",s.form),e.xp6(3),e.Q6J("lazyPageFetcher",s.nationalitiesPageFetcher)("modelOptions",s.extraNationality)("disabled",s.form.value.allAfricans)("required",!s.form.value.allAfricans),e.xp6(4),e.Q6J("lazyPageFetcher",s.nationalitiesPageFetcher)("modelOptions",s.extraNationalitiesToReplace)("multiple",!0)("required",!s.form.value.AfricansIncluded),e.xp6(6),e.Q6J("disabled",!s.form.valid))},directives:[c.iK,c.Cj,c.Zb,c.kL,M._Y,M.JL,M.sg,ee.jB,M.JJ,M.u,M.Q7,Ze.E,c.Zu,t.uu,c.zn],encapsulation:2,changeDetection:0}),De=(0,U.gn)([k.kG],De);const Et=function(a,r,s){return{nationality:a,replacementNationalities:r,operations:s}};let Ue=class{constructor(r,s,p,P){this.apiService=r,this.cdRef=s,this.stateService=p,this.dialog=P,this.vm=[],this.gridCols=[{field:"nationality",header:"Nationality",width:"20ch"},{field:"replacementNationalities",header:"Nationalities to Replace",width:"75%"},{field:"operations",header:"Actions"}]}ngOnInit(){this.stateService.refreshData$.pipe((0,Y.w)(()=>this.apiService.getAllNationalityReplacementRules)).subscribe(r=>{this.vm=r,this.cdRef.detectChanges()})}openAddDialog(){this.dialog.originalOpen(we,{panelClass:["col-md-8"]})}openEditDialog(r){this.dialog.originalOpen(De,{panelClass:["col-md-8"],data:{rule:r}})}onDelete(r){this.dialog.confirm(`Are you sure you want to delete replacement rule for ${r.nationality.label||"Africans"}`,"",()=>this.apiService.deleteReplacementRule(r.id).subscribe({next:()=>{this.stateService.refreshData()}}))}};Ue.\u0275fac=function(r){return new(r||Ue)(e.Y36(u.a),e.Y36(e.sBO),e.Y36(qe.s),e.Y36(c.uY))},Ue.\u0275cmp=e.Xpm({type:Ue,selectors:[["app-replacement-rules"]],decls:12,vars:12,consts:[[1,"py-4"],[3,"columns","data","showPaginator","loading","cellTemplate"],[1,"col-md-12","d-flex","justify-content-center","py-2"],["cc-raised-button","","color","accent",3,"click"],[3,"ccGridCell"],["nationality",""],["replacementNationalities",""],["operations",""],[2,"font-weight","500"],[1,"d-flex","flex-row","flex-wrap","gap-2","justify-content-center","align-items-center","col-10","mx-auto","py-2"],["class","px-4 py-1","style","\n          border-radius: 5px;\n          background-color: #80808059;\n          font-weight: 500;\n          text-transform: capitalize;\n        ",4,"ngFor","ngForOf"],[1,"px-4","py-1",2,"border-radius","5px","background-color","#80808059","font-weight","500","text-transform","capitalize"],[1,"d-flex","gap-2"],["cc-icon-button","",2,"cursor","pointer",3,"click"],["color","accent"]],template:function(r,s){if(1&r&&(e._UZ(0,"div",0),e.ynx(1),e._UZ(2,"cc-datagrid",1),e.TgZ(3,"div",2)(4,"button",3),e.NdJ("click",function(){return s.openAddDialog()}),e._uU(5," Add New Nationality Rule "),e.qZA()(),e.YNc(6,Tt,3,1,"ng-template",4,5,e.W1O),e.YNc(8,St,2,1,"ng-template",4,6,e.W1O),e.YNc(10,At,7,0,"ng-template",4,7,e.W1O),e.BQk()),2&r){const p=e.MAs(7),P=e.MAs(9),b=e.MAs(11);e.xp6(2),e.Q6J("columns",s.gridCols)("data",s.vm)("showPaginator",!1)("loading",!1)("cellTemplate",e.kEZ(8,Et,p,P,b)),e.xp6(4),e.Q6J("ccGridCell",s.vm),e.xp6(2),e.Q6J("ccGridCell",s.vm),e.xp6(2),e.Q6J("ccGridCell",s.vm)}},directives:[L.Ge,t.uu,L.VC,l.sg,o.Q9],encapsulation:2,changeDetection:0}),Ue=(0,U.gn)([k.kG],Ue);var Zt=i(30808);const Mt=[{path:"",component:$e,data:{pageCode:"visa_RPAController"}},{path:"processes-report",component:Ie,data:{label:"Processes Report",pageCode:"visa_RPAProcessesReport"}},{path:"replacement-rules",component:Ue,data:{label:"Replacement rules",pageCode:"ReplacmentRules"}},{path:"pc-processes",component:Zt.j,data:{label:"PC Processes management",pageCode:"visa_RPA_PC_Processes"}}];let Ot=(()=>{class a{}return a.\u0275fac=function(s){return new(s||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({imports:[[he.Bz.forChild(Mt)],he.Bz]}),a})();var Nt=i(11523),Ft=i(77871);let kt=(()=>{class a{}return a.\u0275fac=function(s){return new(s||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({providers:[u.a,g.O,qe.s,Ft.X],imports:[[l.ez,M.u5,Se.XD,M.UX,pe,Ot,ye.sJ,k.n_,L.Gz,t.S6,O.f,c.I8,o.L,We.B,y.v9,j.H5,ee.lK,Ze.$,Nt.SZ,k.er,Ee.YV,ae.C6,Ge.yU,$.Is]]}),a})()},30923:(Ce,G,i)=>{"use strict";i.d(G,{a:()=>t});var u=i(40520),g=i(43604),k=i(54004),L=i(5e3);let t=(()=>{class y{constructor(c){this.http=c,this.getAllNationalityReplacementRules=this.http.get(g.b.nationalityReplacement.list),this.deleteReplacementRule=l=>this.http.delete(g.b.nationalityReplacement.delete(l)),this.addNationalityReplacementRule=l=>this.http.post(g.b.nationalityReplacement.add,l),this.updateNationalityReplacementRule=l=>this.http.post(g.b.nationalityReplacement.update,l),this.getAllPc=l=>{var O,F;return this.http.get(g.b.pcProcesses.getAllPcs,{params:{page:l.params.page||0,size:l.params.size||20,search:(null===(O=null==l?void 0:l.search)||void 0===O?void 0:O.pcName)||"",processes:(null===(F=null==l?void 0:l.search)||void 0===F?void 0:F.processes)||""}})},this.getAllPcData=l=>this.http.get(g.b.pcProcesses.getAllPcs,{params:{page:l.page,size:l.size,search:l.searchString||""}}).pipe((0,k.U)(O=>O.content)),this.getPcProcesses=l=>this.http.get(g.b.pcProcesses.getPcWithProcesses(l)),this.addPcProcesses=(l,O)=>this.http.post(g.b.pcProcesses.addPcProcesses(l),O),this.deletePcProcesses=(l,O)=>this.http.post(g.b.pcProcesses.deletePcProcesses(l),O),this.editPcProcess=(l,O,F)=>this.http.post(g.b.pcProcesses.editPcProcess(l),{},{params:{oldProcessId:O,newProcessId:F}}),this.createConfigParam=l=>this.http.post(g.b.pcProcesses.createConfigParam(),l),this.updateConfigParam=l=>this.http.post(g.b.pcProcesses.updateConfigParam(),l),this.getConfigParamById=l=>this.http.get(g.b.pcProcesses.getConfigParamById(l)),this.getRpaPcProcesses=l=>this.http.get(g.b.pcProcesses.getRpaPcProcesses(l)),this.getRoboticProcesses=this.http.get(g.b.processList),this.deleteConfigParam=l=>this.http.delete(g.b.pcProcesses.deleteConfigParam(l))}getErrorList(c){return this.http.post(g.b.rpaControllerErrorList,["UNDER_PROCESS","PENDING"],{params:new u.LE({fromObject:this.getParamsObj(c)})})}getProcessList(){return this.http.get(g.b.rpaControllerProcessList,{})}updateProcessesState(c){return this.http.put(`${g.b.processes}/status/toggle`,c)}updateProcessState(c,l){return this.http.put(`${g.b.processes}/${c}/status/toggle`,l)}updateProcessIncludedMaidsState(c,l){return this.http.put(`${g.b.processes}/${c}/includedMaidsOnly/toggle`,l)}getProcessExcludedMaids(c,l){return this.http.get(`${g.b.processes}/${c}/getRoboticProcessExcludedUI`,{params:new u.LE({fromObject:this.getParamsObj(l)})})}getProcessIncludedMaids(c,l){return this.http.get(`${g.b.processes}/${c}/getRoboticProcessIncludedUI`,{params:new u.LE({fromObject:this.getParamsObj(l)})})}getProcessesReportList(c){return this.http.get(g.b.rpaProcessesReport,{params:new u.LE({fromObject:this.getParamsObj(c)})})}createRpaWorkingDaysTime(c,l){return this.http.post(`${g.b.createWorkingTime}`,l,{params:{code:c}})}fetchExcludedMaids(c,l){return this.http.get(`${g.b.excludedMaidList}/${c}`,{params:new u.LE({fromObject:{page:l.page,size:l.size,name:l.searchString||""}})})}fetchIncludedMaids(c,l){return this.http.get(`${g.b.includedMaidList}/${c}`,{params:new u.LE({fromObject:{page:l.page,size:l.size,name:l.searchString||""}})})}getWorkingDaysList(c){return this.http.get(g.b.workingTimesList,{params:{code:c}}).pipe((0,k.U)(l=>this.mapToWorkingDays(l[0])))}getDefaultWorkingDaysList(){return this.http.get(g.b.workingTimesList).pipe((0,k.U)(c=>this.mapToWorkingDays(c[0])))}updateWorkingTimes(c,l){return this.http.post(g.b.createWorkingTime,l,{params:{code:c}})}updateDefaultWorkingTimes(c){return this.http.post(g.b.createWorkingTime,c)}excludeMaid(c,l){return this.http.put(`${g.b.processes}/${c}/request/excludedNew`,[l])}includeMaid(c,l){return this.http.put(`${g.b.processes}/${c}/request/includedNew`,[l])}includeMaids(c,l,O){return this.http.post(`${g.b.processes}/${c}/request/IncludeRequestsAboveOrBelow?orderAbove=${O}`,l)}includeMaidsFromExcel(c,l,O){return this.http.post(`${g.b.processes}/${c}/request/includedNewFromExcel?orderAbove=${O}&attachment=${l}`,{})}clearIncluded(c){return this.http.delete(`${g.b.processes}/${c}/request/clearIncludedRequests`)}deleteExcludedMaid(c,l){return this.http.delete(`${g.b.processes}/${c}/request/excludedDelete`,{body:[l]})}deleteIncludedMaid(c,l){return this.http.delete(`${g.b.processes}/${c}/request/includedDelete`,{body:[l]})}getRpaPcList(){return this.http.get(g.b.getRpaPcList)}getRoboticProcessList(c){return this.http.get(g.b.getRoboticProcessList,{params:{search:c||""}})}mapToWorkingDays(c){var l;return{days:c.days,snoozePeriod:c.snoozePeriod,snoozePeriodUnit:c.snoozePeriodUnit,recheckPeriod:c.recheckPeriod,recheckPeriodUnit:c.recheckPeriodUnit,pausePeriodBetweenMaids:c.pausePeriodBetweenMaids,pausePeriodBetweenMaidsUnit:c.pausePeriodBetweenMaidsUnit,workingHours:null===(l=c.workingHours)||void 0===l?void 0:l.map(O=>({fromTime:O.fromTimeString,toTime:O.toTimeString}))}}getParamsObj(c){return Object.assign({page:c.params.page,size:c.params.size},c.search)}}return y.\u0275fac=function(c){return new(c||y)(L.LFG(u.eN))},y.\u0275prov=L.Yz7({token:y,factory:y.\u0275fac}),y})()},77871:(Ce,G,i)=>{"use strict";i.d(G,{X:()=>L});var u=i(54004),g=i(5e3),k=i(30923);let L=(()=>{class t{constructor(o){this.apiService=o,this.roboticProcesses$=this.apiService.getRoboticProcesses.pipe((0,u.U)(c=>c.map(l=>({id:l.id,text:l.name}))))}}return t.\u0275fac=function(o){return new(o||t)(g.LFG(k.a))},t.\u0275prov=g.Yz7({token:t,factory:t.\u0275fac}),t})()},32269:(Ce,G,i)=>{"use strict";i.d(G,{s:()=>k});var u=i(61135),g=i(5e3);let k=(()=>{class L{constructor(){this.refreshDataSub$=new u.X(0),this.refreshData$=this.refreshDataSub$.asObservable()}refreshData(){this.refreshDataSub$.next(0)}}return L.\u0275fac=function(y){return new(y||L)},L.\u0275prov=g.Yz7({token:L,factory:L.\u0275fac}),L})()},76593:(Ce,G,i)=>{"use strict";i.d(G,{O:()=>de});var u=i(88476),g=i(65620),k=i(42199),L=i(4288);const t=(0,g.ZF)(L.J),y=(0,g.P1)(t,D=>D.search),o=(0,g.P1)(t,D=>D.processList),c=(0,g.P1)(t,D=>D.workingDaysForm),l=(0,g.P1)(t,D=>{var B,x;return null!==(x=null===(B=D.workingDaysForm)||void 0===B?void 0:B.workingHours)&&void 0!==x?x:k.N}),O=(0,g.P1)(t,D=>D.isWorkingHoursUpdated),F=(0,g.P1)(t,D=>D.processStateIsUpdated),I=(0,g.P1)(t,D=>D.roboticProcessList),ie=(0,g.P1)(t,D=>D.rpaPcList),w=(0,g.P1)(t,D=>D.rpaPcs),se=(0,g.P1)(t,D=>D.rpaPc);var n=i(90738),q=i(5e3);let de=(()=>{class D extends u.il{constructor(x){super(x),this.initialSearchVal=k.E.search,this.search$=x.select(y),this.processList$=x.select(o),this.workingDays$=x.select(c),this.isWorkingHoursUpdated=x.select(O),this.workingHour$=x.select(l),this.processStateIsUpdated=x.select(F),this.roboticProcessList$=x.select(I),this.rpaPcList$=x.select(ie),this.AllPcs$=x.select(w),this.pcWithProcess$=x.select(se)}loadProcessList(){return this.store.dispatch(n.Au())}loadRoboticProcessList(x){this.store.dispatch(n.nd({pcName:x}))}loadRpaPcList(){this.store.dispatch(n.lH())}loadWorkingDays(x){return this.store.dispatch(n.g3({code:x}))}addNewWorkingHours(){this.store.dispatch(n.qJ())}updateWorkingDays(x,Y){this.store.dispatch(n.jt({code:x,workingDays:Y}))}deleteWorkingHours(x){this.store.dispatch(n.sI({index:x}))}updateWorkingHours(x,Y){this.store.dispatch(n.qI({index:x,payload:Y}))}updateSearchState(x){this.store.dispatch(n.np({payload:x}))}resetSearchState(){this.store.dispatch(n.v2())}updateProcessStatus(x,Y){this.store.dispatch(n.yF({processId:x,state:Y}))}updateProcessesStatus(x){this.store.dispatch(n.b2({state:x}))}updateProcessIncludeMaidsStatus(x,Y){this.store.dispatch(n.Bi({processId:x,state:Y}))}loadDefaultWorkingDays(){this.store.dispatch(n.NH())}updateDefaultWorkingDays(x){this.store.dispatch(n.Do({workingDays:x}))}getAllPcs(x){this.store.dispatch(n.bn({search:x}))}getPcWithProcesses(x){this.store.dispatch(n.$S({pcId:x}))}addPcProcesses(x,Y){this.store.dispatch(n.Ef({pcId:x,processes:Y}))}deletePcProcesses(x,Y){this.store.dispatch(n.MA({pcId:x,processes:Y}))}editPcProcess(x,Y,e){this.store.dispatch(n.nQ({pcId:x,oldProcessId:Y,newProcessId:e}))}pickListsCodes(){return[]}resetState(){}}return D.\u0275fac=function(x){return new(x||D)(q.LFG(g.yh))},D.\u0275prov=q.Yz7({token:D,factory:D.\u0275fac}),D})()},42199:(Ce,G,i)=>{"use strict";i.d(G,{E:()=>u,N:()=>g});const u={search:{search:{},params:{page:0,size:20,sort:""}},processList:[],workingDaysForm:{days:"",snoozePeriod:0,recheckPeriod:0,pausePeriodBetweenMaids:0,snoozePeriodUnit:"",recheckPeriodUnit:"",pausePeriodBetweenMaidsUnit:"",workingHours:[]},error:"",isWorkingHoursUpdated:!1,processStateIsUpdated:!1,rpaPcList:[],roboticProcessList:[],rpaPcs:{content:[],number:0,size:0,totalElements:0,totalPages:0},rpaPc:null},g={fromTime:"00:00:00",toTime:"00:00:00"}},90738:(Ce,G,i)=>{"use strict";i.d(G,{$$:()=>ae,$S:()=>ue,AX:()=>Pe,Au:()=>c,Bi:()=>o,Do:()=>Y,Ef:()=>$,GP:()=>O,Ir:()=>_e,KL:()=>M,MA:()=>ee,NH:()=>B,Us:()=>l,YG:()=>I,Ym:()=>n,ZM:()=>ce,a_:()=>se,b2:()=>y,bn:()=>U,g3:()=>F,jt:()=>w,lH:()=>pe,nQ:()=>Te,nd:()=>K,np:()=>g,oA:()=>ge,ox:()=>he,qI:()=>de,qJ:()=>q,sD:()=>x,sI:()=>D,v2:()=>k,yF:()=>L,yS:()=>me,yU:()=>e,yr:()=>t});var u=i(65620);const g=(0,u.PH)("[RPA_CONTROLLER | STORE SERVICE | Update State] update Search Field Config",(0,u.Ky)()),k=(0,u.PH)("[RPA_CONTROLLER | STORE SERVICE] reset Search Field Config"),L=(0,u.PH)("[RPA_CONTROLLER | SERVICE | toggle_process_state]",(0,u.Ky)()),t=(0,u.PH)("[RPA_CONTROLLER | SERVICE | toggle_process_state success]",(0,u.Ky)()),y=(0,u.PH)("[RPA_CONTROLLER | SERVICE | toggle_processes_state]",(0,u.Ky)()),o=(0,u.PH)("[RPA_CONTROLLER | SERVICE | toggle_process_included_maids_state]",(0,u.Ky)()),c=(0,u.PH)("[RPA_CONTROLLER | SERVICE | load_process_list]"),l=(0,u.PH)("[RPA_CONTROLLER | SERVICE | load_process_list success]",(0,u.Ky)()),O=(0,u.PH)("[RPA_CONTROLLER | SERVICE | load_process_list failure]",(0,u.Ky)()),F=(0,u.PH)("RPA_CONTROLLER | SERVICE | get working days",(0,u.Ky)()),I=(0,u.PH)("RPA_CONTROLLER | SERVICE | get working days success",(0,u.Ky)()),w=((0,u.PH)("RPA_CONTROLLER | SERVICE | get default working days"),(0,u.PH)("RPA_CONTROLLER | SERVICE | update working days ",(0,u.Ky)())),se=(0,u.PH)("RPA_CONTROLLER | SERVICE | update working days success"),n=(0,u.PH)("RPA_CONTROLLER | STORE SERVICE | update working days",(0,u.Ky)()),q=(0,u.PH)("RPA_CONTROLLER | STORE SERVICE | add Working Hour"),de=(0,u.PH)("RPA_CONTROLLER | STORE SERVICE | update Working Hour",(0,u.Ky)()),D=(0,u.PH)("RPA_CONTROLLER | STORE SERVICE | delete Working Hour",(0,u.Ky)()),B=(0,u.PH)("RPA_CONTROLLER | SERVICE | get default working days"),x=(0,u.PH)("RPA_CONTROLLER | SERVICE | get default working days success",(0,u.Ky)()),Y=(0,u.PH)("RPA_CONTROLLER | SERVICE | update default working days",(0,u.Ky)()),e=(0,u.PH)("RPA_CONTROLLER | SERVICE | update default working days success"),K=(0,u.PH)("RPA_CONTROLLER | SERVICE | get robotic process list",(0,u.Ky)()),Pe=(0,u.PH)("RPA_CONTROLLER | SERVICE | get robotic process list success",(0,u.Ky)()),pe=(0,u.PH)("RPA_CONTROLLER | SERVICE |get Rpa Pc List"),he=(0,u.PH)("RPA_CONTROLLER | SERVICE |get Rpa Pc List success",(0,u.Ky)()),U=(0,u.PH)("RPA_CONTROLLER | SERVICE | get all rpa pcs",(0,u.Ky)()),ce=(0,u.PH)("RPA_CONTROLLER | SERVICE | get all rpa pcs success",(0,u.Ky)()),ue=(0,u.PH)("RPA_CONTROLLER | SERVICE | get pc with processes",(0,u.Ky)()),_e=(0,u.PH)("RPA_CONTROLLER | SERVICE | get pc with processes success",(0,u.Ky)()),$=(0,u.PH)("RPA_CONTROLLER | SERVICE | add pc process",(0,u.Ky)()),ae=(0,u.PH)("RPA_CONTROLLER | SERVICE | add pc process success"),ee=(0,u.PH)("RPA_CONTROLLER | SERVICE | delete pc process",(0,u.Ky)()),M=(0,u.PH)("RPA_CONTROLLER | SERVICE | delete pc process success"),Te=(0,u.PH)("RPA_CONTROLLER | SERVICE | edit pc process",(0,u.Ky)()),me=(0,u.PH)("RPA_CONTROLLER | SERVICE | edit pc process success"),ge=(0,u.PH)("[RPA_CONTROLLER | RESET] reset rpa_controller state")},4288:(Ce,G,i)=>{"use strict";i.d(G,{I:()=>t,J:()=>L});var u=i(65620),g=i(90738),k=i(42199);const L="rpa-controller",t=(0,u.Lq)(k.E,(0,u.on)(g.np,(y,{payload:o})=>Object.assign(Object.assign({},y),{search:Object.assign(Object.assign({},y.search),{search:o.search,params:o.params})})),(0,u.on)(g.Au,y=>Object.assign({},y)),(0,u.on)(g.Us,(y,{payload:o})=>Object.assign(Object.assign({},y),{processList:o,error:""})),(0,u.on)(g.GP,(y,{error:o})=>Object.assign(Object.assign({},y),{error:o})),(0,u.on)(g.g3,y=>Object.assign({},y)),(0,u.on)(g.YG,(y,{payload:o})=>Object.assign(Object.assign({},y),{workingDaysForm:Object.assign({},o)})),(0,u.on)(g.jt,y=>Object.assign(Object.assign({},y),{isWorkingHoursUpdated:!1})),(0,u.on)(g.a_,y=>Object.assign(Object.assign({},y),{isWorkingHoursUpdated:!0})),(0,u.on)(g.Ym,(y,{payload:o})=>Object.assign(Object.assign({},y),{workingDaysForm:Object.assign({},o)})),(0,u.on)(g.qJ,y=>Object.assign(Object.assign({},y),{workingDaysForm:Object.assign(Object.assign({},y.workingDaysForm),{workingHours:[...y.workingDaysForm.workingHours,k.N]})})),(0,u.on)(g.qI,(y,{index:o,payload:c})=>Object.assign(Object.assign({},y),{workingDaysForm:Object.assign(Object.assign({},y.workingDaysForm),{workingHours:y.workingDaysForm.workingHours.map((l,O)=>O===o?c:l)})})),(0,u.on)(g.sI,(y,{index:o})=>Object.assign(Object.assign({},y),{workingDaysForm:Object.assign(Object.assign({},y.workingDaysForm),{workingHours:y.workingDaysForm.workingHours.filter((c,l)=>l!==o)})})),(0,u.on)(g.NH,y=>Object.assign(Object.assign({},y),{workingDaysForm:k.E.workingDaysForm})),(0,u.on)(g.sD,(y,{payload:o})=>Object.assign(Object.assign({},y),{workingDaysForm:{days:o.days||"",snoozePeriod:o.snoozePeriod||0,snoozePeriodUnit:o.snoozePeriodUnit||"",recheckPeriod:o.recheckPeriod||0,recheckPeriodUnit:o.recheckPeriodUnit||"",pausePeriodBetweenMaids:o.pausePeriodBetweenMaids||0,pausePeriodBetweenMaidsUnit:o.pausePeriodBetweenMaidsUnit||"",workingHours:o.workingHours||[]}})),(0,u.on)(g.Do,y=>Object.assign(Object.assign({},y),{isWorkingHoursUpdated:!1})),(0,u.on)(g.yU,y=>Object.assign(Object.assign({},y),{isWorkingHoursUpdated:!0})),(0,u.on)(g.yF,y=>Object.assign(Object.assign({},y),{processStateIsUpdated:!1})),(0,u.on)(g.yr,(y,{payload:o})=>Object.assign(Object.assign({},y),{processStateIsUpdated:o})),(0,u.on)(g.ox,(y,{payload:o})=>Object.assign(Object.assign({},y),{rpaPcList:o})),(0,u.on)(g.AX,(y,{payload:o})=>Object.assign(Object.assign({},y),{roboticProcessList:o})),(0,u.on)(g.ZM,(y,{payload:o})=>Object.assign(Object.assign({},y),{rpaPcs:o})),(0,u.on)(g.$S,y=>Object.assign(Object.assign({},y),{rpaPc:k.E.rpaPc})),(0,u.on)(g.Ir,(y,{payload:o})=>Object.assign(Object.assign({},y),{rpaPc:o})),(0,u.on)(g.oA,y=>Object.assign({},k.E)),(0,u.on)(g.v2,y=>Object.assign(Object.assign({},y),{search:k.E.search})))},13608:(Ce,G,i)=>{"use strict";i.r(G),i.d(G,{RPAErrorResolverModule:()=>ut});var u=i(69808),g=i(93075),k=i(88476),L=i(40520),t=i(43604),y=i(8188),o=i(5e3);let c=(()=>{class h{constructor(d){this.http=d,this.resolveAllErrors=E=>this.http.post(t.b.resolveAllErrors,E)}getRPAErrorList$(d){return this.http.post(t.b.errorList,["UNDER_PROCESS","PENDING"],{headers:d.search?(new L.WM).set("searchFilter",JSON.stringify(d.search)):new L.WM,context:(new L.qT).set(y.hG,!0),params:new L.LE({fromObject:this.getParamsObj(d)})})}getRPAProcessesList$(){return this.http.get(t.b.processList,{context:(new L.qT).set(y.hG,!0)})}getModuleList$(){return this.http.get(t.b.moduleList)}updateErrorStatus$(d,E){return this.http.post(`${t.b.updateErrorState}/${d}`,E,{context:(new L.qT).set(y.hG,!0),observe:"response"})}getParamsObj(d){return Object.assign({page:d.params.page,size:d.params.size},d.search)}}return h.\u0275fac=function(d){return new(d||h)(o.LFG(L.eN))},h.\u0275prov=o.Yz7({token:h,factory:h.\u0275fac}),h})();var l=i(65620),O=i(26991);const F=(0,l.PH)("[RPA_Error | INIT] get error list"),I=(0,l.PH)("[RPA_Error | EFFECT] get error list success",(0,l.Ky)()),ie=(0,l.PH)("[RPA_Error | EFFECT] get error list failure"),w=(0,l.PH)("[RPA_Error | INIT] get rpa_process list"),se=(0,l.PH)("[RPA_Error | EFFECT] get rpa_process_list success",(0,l.Ky)()),n=(0,l.PH)("[RPA_Error | EFFECT] get rpa_process_list Failure",(0,l.Ky)()),q=(0,l.PH)("[RPA_Error | INIT] get module list"),de=(0,l.PH)("[RPA_Error | EFFECT] get module list success",(0,l.Ky)()),D=(0,l.PH)("[RPA_Error | EFFECT] get module list failure",(0,l.Ky)()),B=(0,l.PH)("[RPA_Error | INIT] update error status",(0,l.Ky)()),x=(0,l.PH)("[RPA_Error | EFFECT] update error status success",(0,l.Ky)()),Y=(0,l.PH)("[RPA_Error | EFFECT] update error status failure",(0,l.Ky)()),e=(0,l.PH)("[RPA_Error | STORE SERVICE | Update State] update Search Fields",(0,l.Ky)()),K=(0,l.PH)("[RPA_Error | STORE SERVICE | Update State] reset Search Fields"),Pe=(0,l.PH)("[RPA_Error | Store] Resolve All Errors",(0,l.Ky)()),pe=(0,l.PH)("[RPA_Error | Store] Resolve All Errors success"),he=(0,l.PH)("[RPA_Error | RESET] reset rpa-error_resolver state"),U={moduleList:{content:[],number:0,size:20,totalElements:0,totalPages:0},rpaProcessesList:[],search:{search:{},params:{page:0,size:20,sort:""}},errorStateUpdated:!1,error:"",reload:!1},ce="rpa-error-resolver",ue=(0,l.Lq)(U,(0,l.on)(F,h=>Object.assign({},h)),(0,l.on)(I,(h,{reload:v})=>Object.assign(Object.assign({},h),{reload:v})),(0,l.on)(w,h=>Object.assign({},h)),(0,l.on)(se,(h,{payload:v})=>Object.assign(Object.assign({},h),{rpaProcessesList:[...v],error:""})),(0,l.on)(n,(h,{error:v})=>Object.assign(Object.assign({},h),{error:v})),(0,l.on)(q,h=>Object.assign({},h)),(0,l.on)(de,(h,{payload:v})=>Object.assign(Object.assign({},h),{moduleList:Object.assign({},v),error:""})),(0,l.on)(D,(h,{error:v})=>Object.assign(Object.assign({},h),{error:v})),(0,l.on)(B,(h,{})=>Object.assign(Object.assign({},h),{errorStateUpdated:!1,error:""})),(0,l.on)(x,(h,{isUpdated:v})=>Object.assign(Object.assign({},h),{errorStateUpdated:v,error:""})),(0,l.on)(Y,(h,{error:v})=>Object.assign(Object.assign({},h),{errorStateUpdated:U.errorStateUpdated,error:v})),(0,l.on)(e,(h,{payload:v})=>Object.assign(Object.assign({},h),{search:Object.assign(Object.assign({},h.search),{search:v.search,params:v.params})})),(0,l.on)(K,h=>Object.assign(Object.assign({},h),{search:Object.assign({},U.search)})),(0,l.on)(he,h=>Object.assign({},U)));var _e=i(63900),$=i(54004),ae=i(70262),ee=i(39646),M=i(95577),Te=i(61135);let me=(()=>{class h{constructor(){this.reloadSub=new Te.X(0),this.reload$=this.reloadSub.asObservable()}reloadData(){this.reloadSub.next(1)}}return h.\u0275fac=function(d){return new(d||h)},h.\u0275prov=o.Yz7({token:h,factory:h.\u0275fac,providedIn:"root"}),h})();var ge=i(21799);let Ae=(()=>{class h{constructor(d,E,Q,ne){this.action$=d,this.service=E,this.uiService=Q,this.notification=ne,this.getRpaProcessList$=(0,O.GW)(()=>this.action$.pipe((0,O.l4)(w),(0,_e.w)(()=>this.service.getRPAProcessesList$().pipe((0,$.U)(X=>se({payload:X})),(0,ae.K)(X=>(0,ee.of)(n({error:X.message}))))))),this.getModuleList$=(0,O.GW)(()=>this.action$.pipe((0,O.l4)(q),(0,_e.w)(()=>this.service.getModuleList$().pipe((0,$.U)(X=>de({payload:X})),(0,ae.K)(X=>(0,ee.of)(D({error:X.message}))))))),this.updateErrorStatus$=(0,O.GW)(()=>this.action$.pipe((0,O.l4)(B),(0,M.z)(({rpa_error_id:X,status:Je})=>this.service.updateErrorStatus$(X,Je).pipe((0,$.U)(()=>(this.notification.notifySuccess("Done successfully"),x({isUpdated:!0}))),(0,ae.K)(mt=>(this.notification.notifyError("An error occurred. Please try again."),(0,ee.of)(Y({error:mt.message})))))))),this.getErrorList=(0,O.GW)(()=>this.action$.pipe((0,O.l4)(F),(0,$.U)(()=>I({reload:!0})),(0,ae.K)(()=>(0,ee.of)(ie())))),this.resolveAllErrors$=(0,O.GW)(()=>this.action$.pipe((0,O.l4)(Pe),(0,_e.w)(({errorsToResolve:X})=>this.service.resolveAllErrors(X).pipe((0,$.U)(()=>(this.notification.notifySuccess("Done Successfully."),this.uiService.reloadData(),pe()))))))}}return h.\u0275fac=function(d){return new(d||h)(o.LFG(O.eX),o.LFG(c),o.LFG(me),o.LFG(ge.zg))},h.\u0275prov=o.Yz7({token:h,factory:h.\u0275fac}),h})(),ve=(()=>{class h{}return h.\u0275fac=function(d){return new(d||h)},h.\u0275mod=o.oAB({type:h}),h.\u0275inj=o.cJS({imports:[[l.Aw.forFeature(ce,ue),O.sQ.forFeature([Ae])],l.Aw,O.sQ]}),h})();var le=i(1402);const re=(0,l.ZF)(ce),Se=(0,l.P1)(re,h=>h.rpaProcessesList),ye=(0,l.P1)(re,h=>h.moduleList),be=(0,l.P1)(re,h=>h.search),fe=(0,l.P1)(re,h=>h.errorStateUpdated),S=(0,l.P1)(re,h=>h.error),T=(0,l.P1)(re,h=>h.reload);let m=(()=>{class h extends k.il{constructor(d){super(d),this.initialSearch=U.search,this.errorProcessList$=this.store.select(Se),this.moduleList$=this.store.select(ye),this.search$=this.store.select(be),this.errorStateUpdated$=this.store.select(fe),this.error$=this.store.select(S),this.reload$=this.store.select(T),this.getErrorProcessList=()=>this.store.dispatch(w()),this.getModuleList=()=>this.store.dispatch(q()),this.updateErrorStatus=(E,Q)=>this.store.dispatch(B({rpa_error_id:E,status:Q})),this.resetSearch=()=>this.store.dispatch(K()),this.updateSearch=E=>this.store.dispatch(e({payload:E})),this.resolveAllErrors=E=>this.store.dispatch(Pe({errorsToResolve:E}))}pickListsCodes(){return["user_status","rpa_error_type"]}resetState(){this.store.dispatch(he())}}return h.\u0275fac=function(d){return new(d||h)(o.LFG(l.yh))},h.\u0275prov=o.Yz7({token:h,factory:h.\u0275fac}),h})();var R=i(88087),N=i(62764),J=i(26523),z=i(65868),H=i(82599),A=i(34378),_=i(45834),f=i(28172),C=i(43687),j=i(97582),V=i(77579),te=i(18505),Ee=i(82722),Ze=i(48966);let Ne=(()=>{class h{constructor(d,E){this.data=d,this.notification=E,this.attachmentBaseUrl=t.b.download,this.attachment=d.attachment||void 0}onDownload(){this.notification.notifyInfo("Downloading The File...")}}return h.\u0275fac=function(d){return new(d||h)(o.Y36(Ze.WI),o.Y36(ge.zg))},h.\u0275cmp=o.Xpm({type:h,selectors:[["app-doc-preview"]],decls:12,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-title",""],["role","button","type","button","cc-icon-button","","cc-dialog-close-button","","cc-dialog-close",""],["alt","",2,"width","100%","height","100%",3,"src"],["role","button","type","button","cc-raised-button","","color","primary",1,"text-decoration-none",3,"disabled","href","download","click"],["cc-raised-button","","cc-dialog-close",""]],template:function(d,E){1&d&&(o.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),o._uU(3),o.qZA(),o._UZ(4,"a",2),o.qZA(),o.TgZ(5,"cc-dialog-content"),o._UZ(6,"img",3),o.qZA(),o.TgZ(7,"cc-dialog-actions")(8,"a",4),o.NdJ("click",function(){return E.onDownload()}),o._uU(9," Download "),o.qZA(),o.TgZ(10,"button",5),o._uU(11,"Close"),o.qZA()()()),2&d&&(o.xp6(3),o.Oqu(null==E.attachment?null:E.attachment.name),o.xp6(3),o.Q6J("src",E.attachmentBaseUrl+"/"+(null==E.attachment?null:E.attachment.uuid),o.LSH),o.xp6(2),o.Q6J("disabled",!E.attachment)("href",E.attachmentBaseUrl+"/"+(null==E.attachment?null:E.attachment.uuid),o.LSH)("download",null==E.attachment?null:E.attachment.name))},directives:[H.iK,H.Cj,H.Zb,z.uu,H.fX,H.zn,H.kL,H.Zu],styles:[""],changeDetection:0}),h})(),Be=(()=>{class h{constructor(d){this.data=d}ngOnInit(){}}return h.\u0275fac=function(d){return new(d||h)(o.Y36(Ze.WI))},h.\u0275cmp=o.Xpm({type:h,selectors:[["app-exception-msg-preview"]],decls:11,vars:1,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close","","cc-dialog-close-button",""],[2,"width","100%","max-width","100%","word-break","normal","font-size","16px","font-weight","500"],["cc-flat-button","","color","primary","cc-dialog-close",""]],template:function(d,E){1&d&&(o.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),o._uU(3,"Exception Message"),o.qZA(),o._UZ(4,"a",3),o.qZA(),o.TgZ(5,"cc-dialog-content")(6,"p",4),o._uU(7),o.qZA()(),o.TgZ(8,"cc-dialog-actions")(9,"button",5),o._uU(10,"Close"),o.qZA()()()),2&d&&(o.xp6(7),o.Oqu(E.data.message))},directives:[H.iK,H.Cj,H.Zb,H.zn,H.fX,H.kL,H.Zu,z.uu],encapsulation:2}),h})(),Ke=(()=>{class h{constructor(d,E,Q,ne,X){this.store=d,this.apiService=E,this.notifications=Q,this.dialogRef=ne,this.data=X,this.errorTypeForAll={}}ngOnInit(){this.store.picklistSelectors("rpa_error_type").pipe((0,$.U)(d=>({id:d.id,text:d.text,data:d})),(0,te.b)(({data:d})=>this.errorTypesData=d)).subscribe()}onResolve(){const d=this.data.errorsToResolve.map(E=>({id:E.id,status:"SOLVED",type:this.errorTypeForAll.code}));this.store.resolveAllErrors(d),setTimeout(()=>{this.dialogRef.close()},200)}}return h.\u0275fac=function(d){return new(d||h)(o.Y36(m),o.Y36(c),o.Y36(ge.zg),o.Y36(Ze.so),o.Y36(Ze.WI))},h.\u0275cmp=o.Xpm({type:h,selectors:[["app-resolve-all-error"]],decls:13,vars:6,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["form","ngForm"],["name","resolveAll","label","select error type",3,"ngModel","emitFullSelectOption","data","required","ngModelChange"],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","primary",3,"disabled","click"]],template:function(d,E){if(1&d&&(o.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),o._uU(3,"Resolve All"),o.qZA()(),o.TgZ(4,"cc-dialog-content")(5,"form",null,3)(7,"cc-select",4),o.NdJ("ngModelChange",function(ne){return E.errorTypeForAll=ne}),o.qZA()()(),o.TgZ(8,"cc-dialog-actions")(9,"button",5),o._uU(10,"Cancel"),o.qZA(),o.TgZ(11,"button",6),o.NdJ("click",function(){return E.onResolve()}),o._uU(12," Resolve "),o.qZA()()()),2&d){const Q=o.MAs(6);o.xp6(2),o.Q6J("align","center"),o.xp6(5),o.Q6J("ngModel",E.errorTypeForAll)("emitFullSelectOption",!0)("data",E.errorTypesData)("required",!0),o.xp6(4),o.Q6J("disabled",!Q.valid)}},directives:[H.iK,H.Cj,H.Zb,H.kL,g._Y,g.JL,g.F,J.jB,g.JJ,g.On,g.Q7,H.Zu,z.uu,H.zn],encapsulation:2}),h})();var Me,Xe=i(15439),Qe=i(85185);class Fe{constructor(v,d){this.fb=v,this.store=d,this.search=new o.vpe,this.reset=new o.vpe,Me.set(this,new V.x),this.form=this.fb.group({from:"",to:"",moduleCode:"",maidName:"",processId:""})}ngOnInit(){this.store.getModuleList(),this.store.getErrorProcessList(),this.syncFormWithSearchState(),this.loadModuleTypeOptions(),this.loadProcessTypeOptions()}onSubmit(){const v=Object.assign(Object.assign({},this.form.value),{from:this.formatDate(this.form.value.from),to:this.formatDate(this.form.value.to)}),d=this.filterTruthyProps(v);this.search.emit(d)}onReset(){this.store.resetSearch(),this.reset.emit()}ngOnDestroy(){(0,j.Q_)(this,Me,"f").next(),(0,j.Q_)(this,Me,"f").unsubscribe()}syncFormWithSearchState(){this.store.search$.pipe((0,te.b)(v=>{this.form.patchValue({from:v.search.from,to:v.search.to,moduleCode:v.search.moduleCode,maidName:v.search.maidName,processId:v.search.processId})}),(0,Ee.R)((0,j.Q_)(this,Me,"f"))).subscribe()}loadModuleTypeOptions(){this.store.moduleList$.pipe((0,$.U)(v=>v.content.map(d=>({id:d.code,text:d.name,data:d}))),(0,te.b)(v=>this.moduleTypesData=v),(0,Ee.R)((0,j.Q_)(this,Me,"f"))).subscribe()}loadProcessTypeOptions(){this.store.errorProcessList$.pipe((0,$.U)(v=>v.map(d=>({id:d.id,text:d.name,data:d}))),(0,te.b)(v=>this.processTypesData=v),(0,Ee.R)((0,j.Q_)(this,Me,"f"))).subscribe()}filterTruthyProps(v){return Object.fromEntries(Object.entries(v).filter(([d,E])=>E))}formatDate(v){return v?Xe(new Date(v)).format("YYYY-MM-DD HH:mm:00"):""}}Me=new WeakMap,Fe.\u0275fac=function(v){return new(v||Fe)(o.Y36(g.qu),o.Y36(m))},Fe.\u0275cmp=o.Xpm({type:Fe,selectors:[["app-filter"]],outputs:{search:"search",reset:"reset"},decls:29,vars:3,consts:[[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[2,"margin-right","2px"],[1,"row",3,"formGroup","ngSubmit"],[1,"col-md-4"],["formControlName","from","color","primary"],["formControlName","to","color","primary"],["label","Select Module","formControlName","moduleCode",3,"data"],["formControlName","maidName","label","Maid\u2019s Name:","placeholder","Enter Maid's Name"],["label","Select Process","formControlName","processId",3,"data"],[1,"col-md-4","d-flex","justify-content-center","gap-2","py-2"],["cc-raised-button","","color","accent","type","submit",2,"display","block","height","fit-content","padding-block","0.2rem","padding-inline","2rem"],["cc-raised-button","",2,"display","block","height","fit-content","padding-block",".2rem","padding-inline","2rem","background-color","#808080","color","#fff",3,"click"]],template:function(v,d){1&v&&(o.TgZ(0,"cc-accordion",0)(1,"cc-panel",1)(2,"cc-panel-title",2)(3,"cc-icon",3),o._uU(4,"filter_alt"),o.qZA(),o.TgZ(5,"span"),o._uU(6,"Filter"),o.qZA()(),o.TgZ(7,"cc-panel-body")(8,"div")(9,"form",4),o.NdJ("ngSubmit",function(){return d.onSubmit()}),o.TgZ(10,"div",5)(11,"cc-datetimepicker",6)(12,"cc-label"),o._uU(13,"From Date"),o.qZA()()(),o.TgZ(14,"div",5)(15,"cc-datetimepicker",7)(16,"cc-label"),o._uU(17,"To Date"),o.qZA()()(),o.TgZ(18,"div",5),o._UZ(19,"cc-select",8),o.qZA(),o.TgZ(20,"div",5),o._UZ(21,"cc-input",9),o.qZA(),o.TgZ(22,"div",5),o._UZ(23,"cc-select",10),o.qZA(),o.TgZ(24,"div",11)(25,"button",12),o._uU(26," Search "),o.qZA(),o.TgZ(27,"button",13),o.NdJ("click",function(){return d.onReset()}),o._uU(28," Reset "),o.qZA()()()()()()()),2&v&&(o.xp6(9),o.Q6J("formGroup",d.form),o.xp6(10),o.Q6J("data",d.moduleTypesData),o.xp6(4),o.Q6J("data",d.processTypesData))},directives:[A.I,A.CW,A.LL,_.Q9,A.G9,g._Y,g.JL,g.sg,f.ZC,g.JJ,g.u,Qe.k_,J.jB,C.G,z.uu],styles:[""],changeDetection:0});var xe,Ve=i(4882),Ye=i(43277);const ke=["selectAllTpl"];function et(h,v){if(1&h){const d=o.EpF();o.TgZ(0,"cc-select",10),o.NdJ("ngModelChange",function(Q){return o.CHM(d).$implicit.errorType=Q}),o.qZA()}if(2&h){const d=v.$implicit,E=o.oxw();o.Q6J("ngModel",d.errorType)("emitFullSelectOption",!0)("data",E.errorTypesData)}}function tt(h,v){if(1&h){const d=o.EpF();o.TgZ(0,"form",null,11)(2,"cc-checkbox",12),o.NdJ("change",function(Q){const X=o.CHM(d).$implicit;return o.oxw().onSelectError(Q,X)}),o.qZA()()}if(2&h){const d=v.$implicit,E=o.oxw();o.xp6(2),o.Q6J("ngModel",E.getErrorIsSelected(d.id)&&E.selectAll)("name",d.id)}}function ot(h,v){if(1&h){const d=o.EpF();o.TgZ(0,"button",19),o.NdJ("click",function(){o.CHM(d);const Q=o.oxw().rowData;return o.oxw().updateErrorStatus(Q)}),o._uU(1," On It "),o.qZA()}}function ze(h,v){if(1&h){const d=o.EpF();o.TgZ(0,"button",20),o.NdJ("click",function(){o.CHM(d);const Q=o.oxw().rowData;return o.oxw().updateErrorStatus(Q)}),o._uU(1," Resolve "),o.qZA()}}function st(h,v){if(1&h){const d=o.EpF();o.TgZ(0,"cc-menu",13)(1,"div",14)(2,"button",15),o.NdJ("click",function(){const ne=o.CHM(d).rowData;return o.oxw().openPreviewDialog(ne)}),o._uU(3," Preview Error Screenshot "),o.qZA(),o.YNc(4,ot,2,0,"button",16),o.YNc(5,ze,2,0,"button",17),o.TgZ(6,"button",18),o.NdJ("click",function(){const ne=o.CHM(d).rowData;return o.oxw().openExceptionMsgPreview(ne.stacktrace)}),o._uU(7," Preview the exception message "),o.qZA()()()}if(2&h){const d=v.rowData,E=o.oxw();o.Q6J("ccTriggerButton",E.mainMenu),o.xp6(4),o.Q6J("ngIf","PENDING"===d.status),o.xp6(1),o.Q6J("ngIf","PENDING"!==d.status)}}function rt(h,v){if(1&h){const d=o.EpF();o.ynx(0),o.TgZ(1,"cc-checkbox",22,23),o.NdJ("ngModelChange",function(Q){return o.CHM(d),o.oxw(2).selectAll=Q})("ngModelChange",function(Q){return o.CHM(d),o.oxw(2).handleErrorSelection(Q)}),o.qZA(),o.BQk()}if(2&h){const d=o.oxw(2);o.xp6(1),o.Q6J("ngModel",d.selectAll)}}function nt(h,v){if(1&h&&(o.ynx(0),o._uU(1),o.BQk()),2&h){const d=o.oxw().$implicit;o.xp6(1),o.hij(" ",d.header," ")}}function it(h,v){if(1&h&&(o.YNc(0,rt,3,1,"ng-container",21),o.YNc(1,nt,2,1,"ng-container",21)),2&h){const d=v.$implicit;o.Q6J("ngIf","Select All"===d.header),o.xp6(1),o.Q6J("ngIf","Select All"!==d.header)}}function Le(h,v){if(1&h){const d=o.EpF();o.TgZ(0,"button",24),o.NdJ("click",function(){return o.CHM(d),o.oxw().openResolveAllErrorDialog()}),o._uU(1," Resolve All "),o.qZA()}if(2&h){const d=o.oxw();o.Q6J("disabled",d.errorsToResolve.length<2)}}const We=function(){return[]},ct=function(){return[2,3,4,20,30,40,50]},at=function(h,v,d){return{operations:h,errorType:v,selectAll:d}},lt=function(h){return{selectAll:h}},dt=[{field:"operations",header:"Actions"},{field:"selectAll",header:"Select All"},{field:"process.name",header:"Process Name"},{field:"name",header:"Client/Maid Name"},{field:"stage",header:"Stage",width:"15ch"},{field:"creationDate",header:"Error date/time",width:"150px"},{field:"endUserSuggestion",header:"End user suggestion"},{field:"errorType",header:"Error type",width:"200px"}];let Oe=class{constructor(v,d,E,Q,ne,X){this.store=v,this.service=d,this.uiService=E,this.notification=Q,this.dialog=ne,this.cdr=X,xe.set(this,new V.x),this.mainMenu={icon:"menu",type:"icon",color:"primary"},this.errorsToResolve=[],this.errorTypeForAll="",this.selectAll=!1,this.gridCols=dt}ngOnInit(){this.uiService.reload$.subscribe(()=>{this.loadErrorList(this.store.initialSearch),this.errorsToResolve=[],this.selectAll=!1}),this.store.errorStateUpdated$.pipe((0,te.b)(v=>{!0===v&&this.loadErrorList(this.search)}),(0,Ee.R)((0,j.Q_)(this,xe,"f"))).subscribe(),this.store.search$.pipe((0,te.b)(v=>this.search=v),(0,Ee.R)((0,j.Q_)(this,xe,"f"))).subscribe(),this.store.picklistSelectors("rpa_error_type").pipe((0,$.U)(v=>({id:v.id,text:v.text,data:v})),(0,te.b)(({data:v})=>this.errorTypesData=v)).subscribe()}handleNextPage(v){const d={search:Object.assign({},this.search.search),params:{page:v.pageIndex,size:v.pageSize}};this.store.updateSearch(d),this.loadErrorList(d)}onSearch(v){const d={params:this.store.initialSearch.params,search:v};this.store.updateSearch(d),this.loadErrorList(d)}onResetSearchForm(){this.loadErrorList(this.store.initialSearch)}updateErrorStatus(v){"UNDER_PROCESS"!==v.status||v.errorType.code?this.store.updateErrorStatus(`${v.id}`,"PENDING"===v.status?{status:"UNDER_PROCESS"}:{status:"SOLVED",type:v.errorType.code}):this.notification.notifyInfo("Please select the error type.")}openResolveAllErrorDialog(){this.dialog.originalOpen(Ke,{panelClass:["col-md-6"],data:{errorsToResolve:this.errorsToResolve}})}openPreviewDialog(v){console.log(v.attachments),this.dialog.originalOpen(Ne,{width:"75%",data:{attachment:v.attachments[0]}})}openExceptionMsgPreview(v){this.dialog.originalOpen(Be,{panelClass:["col-md-8"],data:{message:v}})}getErrorIsSelected(v){return!!this.errorsToResolve.find(d=>d.id===v)}handleErrorSelection(v){this.errorList.content.forEach(d=>{const E=this.errorsToResolve.some(Q=>Q.id===d.id);v&&!E?this.addToErrorsToResolve(d):!v&&E&&this.removeFromErrorsToResolve(d)})}onSelectError({checked:v},d){v?this.addToErrorsToResolve(d):this.removeFromErrorsToResolve(d),this.SelectAllControl.control.patchValue(this.errorsToResolve.length===this.errorList.content.length,{emitEvent:!1,emitModelToViewChange:!0,emitViewToModelChange:!1})}addToErrorsToResolve(v){this.errorsToResolve=[...this.errorsToResolve,"PENDING"===v.status?{id:v.id,status:"UNDER_PROCESS"}:{id:v.id,status:"SOLVED"}],this.cdr.detectChanges()}removeFromErrorsToResolve(v){this.errorsToResolve=this.errorsToResolve.filter(d=>d.id!==v.id)}loadErrorList(v){this.errorList$=this.service.getRPAErrorList$(v),this.errorList$.subscribe(d=>{this.errorList=d,this.selectAll=!1,this.errorsToResolve=[],this.cdr.detectChanges()})}ngOnDestroy(){(0,j.Q_)(this,xe,"f").next(),(0,j.Q_)(this,xe,"f").unsubscribe()}};xe=new WeakMap,Oe.\u0275fac=function(v){return new(v||Oe)(o.Y36(m),o.Y36(c),o.Y36(me),o.Y36(ge.zg),o.Y36(H.uY),o.Y36(o.sBO))},Oe.\u0275cmp=o.Xpm({type:Oe,selectors:[["rpa-error-list"]],viewQuery:function(v,d){if(1&v&&o.Gf(ke,5),2&v){let E;o.iGM(E=o.CRH())&&(d.SelectAllControl=E.first)}},decls:14,vars:21,consts:[[3,"search","reset"],[1,"my-2",3,"data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","cellTemplate","showSummary","summaryTemplate","headerTemplate","page"],["as","","row",""],["errorTypeTmp",""],["selectAllTmp",""],[3,"ccGridCell"],["actionsRef",""],["ccGridHeader",""],["headerTpl",""],["selectAllFooter",""],["ngModel","","label","select error type",3,"ngModel","emitFullSelectOption","data","ngModelChange"],["form","ngForm"],[3,"ngModel","name","change"],[2,"font-size","20px","font-weight","900",3,"ccTriggerButton"],[1,"action-group","d-flex","flex-column","gap-1","px-3","py-2"],["cc-menu-item","","cc-flat-button","","color","primary","role","button","label","Preview error screenshot",3,"click"],["cc-menu-item","","type","button","cc-flat-button","","label","On It","color","accent","title","On it",3,"click",4,"ngIf"],["cc-menu-item","","style","background-color: green","cc-flat-button","","color","accent","title","Resolve",3,"click",4,"ngIf"],["cc-menu-item","","cc-flat-button","","color","accent","title","Preview the exception message",3,"click"],["cc-menu-item","","type","button","cc-flat-button","","label","On It","color","accent","title","On it",3,"click"],["cc-menu-item","","cc-flat-button","","color","accent","title","Resolve",2,"background-color","green",3,"click"],[4,"ngIf"],["color","warn",1,"custom-checkbox",3,"ngModel","ngModelChange"],["selectAllTpl","ngModel"],["cc-flat-button","","color","primary","cc-flat-button","","color","accent","title","Resolve All",3,"disabled","click"]],template:function(v,d){if(1&v&&(o.ynx(0),o.TgZ(1,"app-filter",0),o.NdJ("search",function(Q){return d.onSearch(Q)})("reset",function(){return d.onResetSearchForm()}),o.qZA(),o.TgZ(2,"cc-datagrid",1),o.NdJ("page",function(Q){return d.handleNextPage(Q)}),o._uU(3," > "),o.qZA(),o.YNc(4,et,1,3,"ng-template",2,3,o.W1O),o.YNc(6,tt,3,2,"ng-template",null,4,o.W1O),o.YNc(8,st,8,3,"ng-template",5,6,o.W1O),o.YNc(10,it,2,2,"ng-template",7,8,o.W1O),o.YNc(12,Le,2,1,"ng-template",null,9,o.W1O),o.BQk()),2&v){const E=o.MAs(5),Q=o.MAs(7),ne=o.MAs(9),X=o.MAs(11),Je=o.MAs(13);o.xp6(2),o.Q6J("data",null!=d.errorList?d.errorList.content:o.DdM(12,We))("columns",d.gridCols)("length",null!=d.errorList?d.errorList.totalElements:0)("pageOnFront",!1)("pageIndex",null!=d.errorList?d.errorList.number:0)("pageSize",null!=d.errorList?d.errorList.size:0)("pageSizeOptions",o.DdM(13,ct))("cellTemplate",o.kEZ(14,at,ne,E,Q))("showSummary",!0)("summaryTemplate",o.VKq(18,lt,Je))("headerTemplate",X),o.xp6(6),o.Q6J("ccGridCell",null!=d.errorList?d.errorList.content:o.DdM(20,We))}},directives:[Fe,N.Ge,J.jB,g.JJ,g.On,g._Y,g.JL,g.F,Ve.E,N.VC,Ye.OL,z.uu,Ye.Y,u.O5,N.st],styles:[""],changeDetection:0}),Oe=(0,j.gn)([k.kG],Oe);const pt=[{path:"",component:Oe,data:{label:" RPA Errors Resolver "}}];let ut=(()=>{class h{}return h.\u0275fac=function(d){return new(d||h)},h.\u0275mod=o.oAB({type:h}),h.\u0275inj=o.cJS({providers:[c,m],imports:[[u.ez,g.UX,g.u5,le.Bz.forChild(pt),ve,k.gZ,k.n_,R.pS,N.Gz,J.lK,z.S6,H.I8,A.yU,_.L,f.To,C.f,Ve.$,Ye.v9]]}),h})()},90821:(Ce,G,i)=>{"use strict";i.r(G),i.d(G,{RpaErrorsReportModule:()=>H});var u=i(97582),g=i(50727),k=i(18505),L=i(43604),t=i(5e3),y=i(88476),o=i(65620);const c=(0,o.PH)("[RPA_Error | INIT] get rpa_process_list"),l=(0,o.PH)("[RPA_Error | EFFECT] get rpa_process_list success",(0,o.Ky)()),O=(0,o.PH)("[RPA_Error | EFFECT] get rpa_process_list Failure"),F=(0,o.PH)("[RPA_Error | STORE SERVICE | Update State] update Search Field Config",(0,o.Ky)()),I=(0,o.PH)("[RPA_Error | STORE SERVICE | Update State] reset Search Fields"),ie=(0,o.PH)("[RPA_Error | RESET] reset rpa-error_resolver state"),w={rpaProcessList:[],search:{search:{},params:{page:0,size:20,sort:""}}},se="rpa-error-report",n=(0,o.Lq)(w,(0,o.on)(c,A=>Object.assign({},A)),(0,o.on)(l,(A,{payload:_})=>Object.assign(Object.assign({},A),{rpaProcessList:[..._],error:""})),(0,o.on)(O,A=>Object.assign({},A)),(0,o.on)(F,(A,{payload:_})=>Object.assign(Object.assign({},A),{search:Object.assign(Object.assign({},A.search),{search:_.search,params:_.params})})),(0,o.on)(I,A=>Object.assign(Object.assign({},A),{search:Object.assign({},w.search)})),(0,o.on)(ie,A=>Object.assign({},w))),q=(0,o.ZF)(se),de=(0,o.P1)(q,A=>A.rpaProcessList),D=(0,o.P1)(q,A=>A.search);let B=(()=>{class A extends y.il{constructor(f){super(f),this.initialSearchVal=w.search,this.processList$=f.select(de),this.search$=f.select(D)}loadProcessList(){this.store.dispatch(c())}updateSearchState(f){this.store.dispatch(F({payload:f}))}resetSearch(){this.store.dispatch(I())}pickListsCodes(){return[]}resetState(){}}return A.\u0275fac=function(f){return new(f||A)(t.LFG(o.yh))},A.\u0275prov=t.Yz7({token:A,factory:A.\u0275fac}),A})();var x=i(40520),Y=i(8188);let e=(()=>{class A{constructor(f){this.http=f,this.getErrorList=C=>this.http.post(L.b.rpaErrorList,["UNDER_PROCESS","PENDING","SOLVED"],{headers:{pageCode:"Visa_RPAErrorsReport"},context:(new x.qT).set(Y.hG,!0),params:new x.LE({fromObject:this.getParamsObj(C)})}),this.getProcessList=()=>this.http.get(L.b.processList,{headers:{pageCode:"Visa_RPAErrorsReport"}})}getParamsObj(f){return Object.assign({page:f.params.page,size:f.params.size},f.search)}}return A.\u0275fac=function(f){return new(f||A)(t.LFG(x.eN))},A.\u0275prov=t.Yz7({token:A,factory:A.\u0275fac}),A})();var me,K=i(21799),Pe=i(77579),pe=i(82722),he=i(54004),U=i(15439),ce=i(93075),ue=i(34378),_e=i(45834),$=i(28172),ae=i(85185),ee=i(43687),M=i(26523),Te=i(65868);class ge{constructor(_,f){this.fb=_,this.store=f,this.search=new t.vpe,this.reset=new t.vpe,me.set(this,new Pe.x),this.form=this.fb.group({from:"",to:"",moduleCode:"",maidName:"",processId:""})}ngOnInit(){this.store.loadProcessList(),this.syncFormWithSearchState(),this.loadProcessTypeOptions()}onSubmit(){const _=Object.assign(Object.assign({},this.form.value),{from:this.formatDate(this.form.value.from),to:this.formatDate(this.form.value.to)}),f=this.filterTruthyProps(_);this.search.emit(f)}onReset(){this.store.resetSearch(),this.reset.emit()}ngOnDestroy(){(0,u.Q_)(this,me,"f").next(),(0,u.Q_)(this,me,"f").unsubscribe()}syncFormWithSearchState(){this.store.search$.pipe((0,k.b)(_=>{this.form.patchValue({from:_.search.from,to:_.search.to,moduleCode:_.search.moduleCode,maidName:_.search.maidName,processId:_.search.processId})}),(0,pe.R)((0,u.Q_)(this,me,"f"))).subscribe()}loadProcessTypeOptions(){this.store.processList$.pipe((0,he.U)(_=>_.map(f=>({id:f.id,text:f.name,data:f}))),(0,k.b)(_=>this.processTypesData=_),(0,pe.R)((0,u.Q_)(this,me,"f"))).subscribe()}filterTruthyProps(_){return Object.fromEntries(Object.entries(_).filter(([f,C])=>C))}formatDate(_){return _?U(new Date(_)).format("YYYY-MM-DD HH:mm:00"):""}}me=new WeakMap,ge.\u0275fac=function(_){return new(_||ge)(t.Y36(ce.qu),t.Y36(B))},ge.\u0275cmp=t.Xpm({type:ge,selectors:[["app-filter"]],outputs:{search:"search",reset:"reset"},decls:28,vars:4,consts:[[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center"],[2,"position","relative","left","-4px"],[1,"d-flex","flex-column",3,"formGroup","ngSubmit"],[1,"row"],[1,"col-md-6"],["formControlName","from",3,"color"],["formControlName","to",3,"color"],["formControlName","maidName","label","Maid\u2019s Name:","placeholder","Enter Maid's Name"],["label","Select Process","formControlName","processId",3,"data"],[1,"col-md-4","d-flex","justify-content-center","gap-2","py-3","mx-auto"],["cc-raised-button","","color","accent","type","submit",2,"padding","0.15rem 1.8rem"],["cc-raised-button","","id","reset-btn",2,"padding","0.15rem 1.8rem","background-color","#808080","color","#fff",3,"click"]],template:function(_,f){1&_&&(t.TgZ(0,"cc-accordion",0)(1,"cc-panel",1)(2,"cc-panel-title",2)(3,"cc-icon",3),t._uU(4,"filter_alt"),t.qZA(),t.TgZ(5,"span"),t._uU(6,"Filters"),t.qZA()(),t.TgZ(7,"cc-panel-body")(8,"div")(9,"form",4),t.NdJ("ngSubmit",function(){return f.onSubmit()}),t.TgZ(10,"div",5)(11,"div",6)(12,"cc-datetimepicker",7)(13,"cc-label"),t._uU(14,"From Date"),t.qZA()()(),t.TgZ(15,"div",6)(16,"cc-datetimepicker",8)(17,"cc-label"),t._uU(18,"To Date"),t.qZA()()(),t.TgZ(19,"div",6),t._UZ(20,"cc-input",9),t.qZA(),t.TgZ(21,"div",6),t._UZ(22,"cc-select",10),t.qZA()(),t.TgZ(23,"div",11)(24,"button",12),t._uU(25," Search "),t.qZA(),t.TgZ(26,"button",13),t.NdJ("click",function(){return f.onReset()}),t._uU(27," Reset "),t.qZA()()()()()()()),2&_&&(t.xp6(9),t.Q6J("formGroup",f.form),t.xp6(3),t.Q6J("color","primary"),t.xp6(4),t.Q6J("color","primary"),t.xp6(6),t.Q6J("data",f.processTypesData))},directives:[ue.I,ue.CW,ue.LL,_e.Q9,ue.G9,ce._Y,ce.JL,ce.sg,$.ZC,ce.JJ,ce.u,ae.k_,ee.G,M.jB,Te.uu],styles:[""],changeDetection:0});var ve,Ae=i(62764);const le=function(){return[10,20,30,40,50]},Se=[{field:"process.name",header:"Process Name"},{field:"name",header:"Maid Name"},{field:"errorType.name",header:"Error type",width:"200px"},{field:"creationDate",header:"Error date/time",width:"200px"},{field:"status",header:"Status",formatter:A=>`<span>${function re(A){return A[0].toUpperCase()+A.slice(1).toLowerCase()}(A.status)}</span>`}];class ye{constructor(_,f,C,j){this.store=_,this.service=f,this.mediaService=C,this.cdr=j,ve.set(this,new g.w0),this.errorList={content:[],number:0,size:0,totalElements:0,totalPages:0},this.gridCols=Se}ngOnInit(){this.loadErrorList(this.store.initialSearchVal);const f=this.store.search$.pipe((0,k.b)(C=>this.search=C)).subscribe();(0,u.Q_)(this,ve,"f").add(f)}handleNextPage(_){const f={search:Object.assign({},this.search.search),params:{page:_.pageIndex,size:_.pageSize}};this.store.updateSearchState(Object.assign({},f)),this.loadErrorList(f)}onSearch(_){const f={params:this.store.initialSearchVal.params,search:_};this.store.updateSearchState(f),this.loadErrorList(f)}onResetSearchForm(){this.loadErrorList(this.store.initialSearchVal)}downloadExcelFile(){this.mediaService.downloadFile(L.b.rpaExportExcel)}loadErrorList(_){this.service.getErrorList(_).subscribe(f=>{this.errorList=f,this.cdr.detectChanges()})}ngOnDestroy(){(0,u.Q_)(this,ve,"f").unsubscribe()}}ve=new WeakMap,ye.\u0275fac=function(_){return new(_||ye)(t.Y36(B),t.Y36(e),t.Y36(K.yJ),t.Y36(t.sBO))},ye.\u0275cmp=t.Xpm({type:ye,selectors:[["app-rpa-errors-report"]],decls:6,vars:10,consts:[[3,"search","reset"],[2,"width","100%","height","fit-content","padding-block","2rem","margin-block","1.1rem"],["id","btn","cc-flat-button","",2,"float","right","text-decoration","none","background-color","green","color","white",3,"click"],[1,"my-2",3,"data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","columnMenuDisplayOnHover","columnMenuButtonIcon","page"]],template:function(_,f){1&_&&(t.ynx(0),t.TgZ(1,"app-filter",0),t.NdJ("search",function(j){return f.onSearch(j)})("reset",function(){return f.onResetSearchForm()}),t.qZA(),t.TgZ(2,"div",1)(3,"button",2),t.NdJ("click",function(){return f.downloadExcelFile()}),t._uU(4," Export Excel "),t.qZA()(),t.TgZ(5,"cc-datagrid",3),t.NdJ("page",function(j){return f.handleNextPage(j)}),t.qZA(),t.BQk()),2&_&&(t.xp6(5),t.Q6J("data",f.errorList.content)("columns",f.gridCols)("length",f.errorList.totalElements)("pageOnFront",!1)("pageIndex",f.errorList.number)("pageSize",f.errorList.size)("pageSizeOptions",t.DdM(9,le))("columnMenuDisplayOnHover",!1)("columnMenuButtonIcon","settings"))},directives:[ge,Te.uu,Ae.Ge],styles:[""]});var be=i(26991),fe=i(95577),S=i(70262),T=i(39646);let m=(()=>{class A{constructor(f,C){this.action$=f,this.service=C,this.getRpaProcessList$=(0,be.GW)(()=>this.action$.pipe((0,be.l4)(c),(0,fe.z)(()=>this.service.getProcessList().pipe((0,he.U)(j=>l({payload:j})),(0,S.K)(j=>(console.error(j.message),(0,T.of)(O())))))))}}return A.\u0275fac=function(f){return new(f||A)(t.LFG(be.eX),t.LFG(e))},A.\u0275prov=t.Yz7({token:A,factory:A.\u0275fac}),A})(),R=(()=>{class A{}return A.\u0275fac=function(f){return new(f||A)},A.\u0275mod=t.oAB({type:A}),A.\u0275inj=t.cJS({imports:[[o.Aw.forFeature(se,n),be.sQ.forFeature([m])],o.Aw,be.sQ]}),A})();var N=i(69808),J=i(1402);const z=[{path:"",component:ye}];let H=(()=>{class A{}return A.\u0275fac=function(f){return new(f||A)},A.\u0275mod=t.oAB({type:A}),A.\u0275inj=t.cJS({providers:[e,B],imports:[[N.ez,ce.UX,ce.u5,J.Bz.forChild(z),R,y.gZ,y.n_,Ae.Gz,M.lK,Te.S6,ue.yU,_e.L,$.To,ee.f]]}),A})()},95747:(Ce,G,i)=>{"use strict";i.r(G),i.d(G,{RPAModule:()=>O});var u=i(1402),g=i(30808),k=i(5e3);const L=[{path:"rpa-controller",loadChildren:()=>Promise.resolve().then(i.bind(i,22960)).then(F=>F.RpaControllerModule),data:{pageCode:"visa_RPAController"}},{path:"errors-report",loadChildren:()=>Promise.resolve().then(i.bind(i,90821)).then(F=>F.RpaErrorsReportModule),data:{label:" RPA Errors Report ",pageCode:"visa_RPAErrorsReport"}},{path:"errors-resolver",loadChildren:()=>Promise.resolve().then(i.bind(i,13608)).then(F=>F.RPAErrorResolverModule),data:{label:" RPA Errors Resolver ",pageCode:"visa_RPAErrorsResolver"}},{path:"followup-canceled-maids",loadChildren:()=>Promise.resolve().then(i.bind(i,51316)).then(F=>F.FollowupCanceledMaidsModule),data:{label:" Follow up with canceled maids ",pageCode:"visa_RPA_FollowupWithCanceledMaids"}},{path:"visa-prioritize",loadChildren:()=>Promise.all([i.e("vendors-node_modules_maids_cc-lib_fesm2015_maids-cc-lib-tabs_mjs"),i.e("src_app_modules_rpa-visa-prioritize_rpa-visa-prioritize_module_ts")]).then(i.bind(i,76734)).then(F=>F.RpaVisaPrioritizeModule),data:{label:" Visa Prioritize",pageCode:"visa_RpaVisaPrioritize"}},{path:"visa-prioritize",loadChildren:()=>Promise.all([i.e("vendors-node_modules_maids_cc-lib_fesm2015_maids-cc-lib-tabs_mjs"),i.e("src_app_modules_rpa-visa-prioritize_rpa-visa-prioritize_module_ts")]).then(i.bind(i,76734)).then(F=>F.RpaVisaPrioritizeModule),data:{label:" Visa Prioritize",pageCode:"visa_RpaVisaPrioritize"}},{path:"pc-processes",component:g.j,data:{label:"PC Processes management",pageCode:"visa_RPA_PC_Processes"}}];let t=(()=>{class F{}return F.\u0275fac=function(ie){return new(ie||F)},F.\u0275mod=k.oAB({type:F}),F.\u0275inj=k.cJS({imports:[[u.Bz.forChild(L)],u.Bz]}),F})();var y=i(22960),o=i(13608),c=i(51316),l=i(90821);let O=(()=>{class F{}return F.\u0275fac=function(ie){return new(ie||F)},F.\u0275mod=k.oAB({type:F}),F.\u0275inj=k.cJS({imports:[[t,y.RpaControllerModule,o.RPAErrorResolverModule,c.FollowupCanceledMaidsModule,l.RpaErrorsReportModule]]}),F})()},46700:(Ce,G,i)=>{var u={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function g(L){var t=k(L);return i(t)}function k(L){if(!i.o(u,L)){var t=new Error("Cannot find module '"+L+"'");throw t.code="MODULE_NOT_FOUND",t}return u[L]}g.keys=function(){return Object.keys(u)},g.resolve=k,Ce.exports=g,g.id=46700}}]);
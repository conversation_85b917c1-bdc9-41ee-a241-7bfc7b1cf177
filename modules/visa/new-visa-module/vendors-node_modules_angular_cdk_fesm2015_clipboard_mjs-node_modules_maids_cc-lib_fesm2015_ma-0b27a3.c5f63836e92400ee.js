"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["vendors-node_modules_angular_cdk_fesm2015_clipboard_mjs-node_modules_maids_cc-lib_fesm2015_ma-0b27a3"],{69287:(M,f,s)=>{s.d(f,{Iq:()=>T,TU:()=>m,i3:()=>x});var t=s(69808),u=s(5e3);class g{constructor(a,o){this._document=o;const _=this._textarea=this._document.createElement("textarea"),r=_.style;r.position="fixed",r.top=r.opacity="0",r.left="-999em",_.setAttribute("aria-hidden","true"),_.value=a,this._document.body.appendChild(_)}copy(){const a=this._textarea;let o=!1;try{if(a){const _=this._document.activeElement;a.select(),a.setSelectionRange(0,a.value.length),o=this._document.execCommand("copy"),_&&_.focus()}}catch(_){}return o}destroy(){const a=this._textarea;a&&(a.remove(),this._textarea=void 0)}}let m=(()=>{class c{constructor(o){this._document=o}copy(o){const _=this.beginCopy(o),r=_.copy();return _.destroy(),r}beginCopy(o){return new g(o,this._document)}}return c.\u0275fac=function(o){return new(o||c)(u.LFG(t.K0))},c.\u0275prov=u.Yz7({token:c,factory:c.\u0275fac,providedIn:"root"}),c})();const l=new u.OlP("CDK_COPY_TO_CLIPBOARD_CONFIG");let x=(()=>{class c{constructor(o,_,r){this._clipboard=o,this._ngZone=_,this.text="",this.attempts=1,this.copied=new u.vpe,this._pending=new Set,r&&null!=r.attempts&&(this.attempts=r.attempts)}copy(o=this.attempts){if(o>1){let _=o;const r=this._clipboard.beginCopy(this.text);this._pending.add(r);const d=()=>{const C=r.copy();C||!--_||this._destroyed?(this._currentTimeout=null,this._pending.delete(r),r.destroy(),this.copied.emit(C)):this._currentTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(d,1))};d()}else this.copied.emit(this._clipboard.copy(this.text))}ngOnDestroy(){this._currentTimeout&&clearTimeout(this._currentTimeout),this._pending.forEach(o=>o.destroy()),this._pending.clear(),this._destroyed=!0}}return c.\u0275fac=function(o){return new(o||c)(u.Y36(m),u.Y36(u.R0b),u.Y36(l,8))},c.\u0275dir=u.lG2({type:c,selectors:[["","cdkCopyToClipboard",""]],hostBindings:function(o,_){1&o&&u.NdJ("click",function(){return _.copy()})},inputs:{text:["cdkCopyToClipboard","text"],attempts:["cdkCopyToClipboardAttempts","attempts"]},outputs:{copied:"cdkCopyToClipboardCopied"}}),c})(),T=(()=>{class c{}return c.\u0275fac=function(o){return new(o||c)},c.\u0275mod=u.oAB({type:c}),c.\u0275inj=u.cJS({}),c})()},43277:(M,f,s)=>{s.d(f,{OL:()=>D,Y:()=>Q,v9:()=>F});var t=s(5e3),u=s(12462),g=s(47423),m=s(25245),l=s(69808);function x(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function T(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",7),t.YNc(2,x,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function c(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function a(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",9),t.YNc(2,c,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function o(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function _(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",10),t.YNc(2,o,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function r(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function d(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",11),t.YNc(2,r,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function C(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function b(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",12),t.YNc(2,C,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function y(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function h(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",13),t.YNc(2,y,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function O(n,i){if(1&n&&(t.TgZ(0,"mat-icon"),t._uU(1),t.qZA()),2&n){const e=t.oxw(2).$implicit;t.xp6(1),t.Oqu(e.icon)}}function w(n,i){if(1&n&&(t.ynx(0),t.TgZ(1,"button",14),t.YNc(2,O,2,1,"mat-icon",8),t.TgZ(3,"span"),t._uU(4),t.qZA()(),t.BQk()),2&n){const e=t.oxw().$implicit;t.oxw();const p=t.MAs(2);t.xp6(1),t.Q6J("color",e.color)("matMenuTriggerFor",p),t.xp6(1),t.Q6J("ngIf",e.icon),t.xp6(2),t.Oqu(e.text)}}function A(n,i){1&n&&(t.ynx(0,4),t.YNc(1,T,5,4,"ng-container",5),t.YNc(2,a,5,4,"ng-container",5),t.YNc(3,_,5,4,"ng-container",5),t.YNc(4,d,5,4,"ng-container",5),t.YNc(5,b,5,4,"ng-container",5),t.YNc(6,h,5,4,"ng-container",5),t.YNc(7,w,5,4,"ng-container",6),t.BQk()),2&n&&(t.Q6J("ngSwitch",i.$implicit.type),t.xp6(1),t.Q6J("ngSwitchCase","raised"),t.xp6(1),t.Q6J("ngSwitchCase","flat"),t.xp6(1),t.Q6J("ngSwitchCase","stroked"),t.xp6(1),t.Q6J("ngSwitchCase","icon"),t.xp6(1),t.Q6J("ngSwitchCase","fab"),t.xp6(1),t.Q6J("ngSwitchCase","mini-fab"))}const Z=function(n,i){return{$implicit:n,btn:i}},v=["*"];let D=(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(e){return new(e||n)},n.\u0275cmp=t.Xpm({type:n,selectors:[["cc-menu"]],inputs:{btn:["ccTriggerButton","btn"]},ngContentSelectors:v,decls:6,vars:5,consts:[[3,"ngTemplateOutlet","ngTemplateOutletContext"],[1,""],["menu","matMenu"],["btnDefaultTpl",""],[3,"ngSwitch"],[4,"ngSwitchCase"],[4,"ngSwitchDefault"],["mat-raised-button","","type","button",3,"color","matMenuTriggerFor"],[4,"ngIf"],["mat-flat-button","","type","button",3,"color","matMenuTriggerFor"],["mat-stroked-button","","type","button",3,"color","matMenuTriggerFor"],["mat-icon-button","","type","button",3,"color","matMenuTriggerFor"],["mat-fab","","type","button",3,"color","matMenuTriggerFor"],["mat-mini-fab","","type","button",3,"color","matMenuTriggerFor"],["mat-button","",3,"color","matMenuTriggerFor"]],template:function(e,p){if(1&e&&(t.F$t(),t.GkF(0,0),t.TgZ(1,"mat-menu",1,2),t.Hsn(3),t.qZA(),t.YNc(4,A,8,7,"ng-template",null,3,t.W1O)),2&e){const J=t.MAs(5);t.Q6J("ngTemplateOutlet",J)("ngTemplateOutletContext",t.WLB(2,Z,p.btn,p.btn))}},directives:[u.VK,g.lW,m.Hw,l.tP,l.RF,l.n9,u.p6,l.O5,l.ED],styles:[""]}),n})(),Q=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275dir=t.lG2({type:n,selectors:[["","cc-menu-item",""]],hostAttrs:[1,"cc-menu-item"]}),n})(),F=(()=>{class n{}return n.\u0275fac=function(e){return new(e||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[[l.ez,g.ot,m.Ps,u.Tx]]}),n})()}}]);
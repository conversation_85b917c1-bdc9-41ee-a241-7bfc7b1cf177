"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["vendors-node_modules_maids_cc-lib_fesm2015_maids-cc-lib-textarea_mjs"],{42002:(J,D,s)=>{s.d(D,{Qf:()=>K,l2:()=>Y});var f=s(69808),e=s(5e3),c=s(93075),m=s(67322),h=s(98833),p=s(88476),x=s(85185),C=s(58015),g=s(63191),b=s(77579),v=s(54968),M=s(50727),d=s(82722),T=s(95698),E=s(71884),I=s(74533);const O=["*"],A=["autosize"],R=["input"];function w(n,l){}function P(n,l){if(1&n&&(e.TgZ(0,"mat-form-field",4,5)(2,"mat-label"),e.YNc(3,w,0,0,"ng-template",6),e.qZA(),e.TgZ(4,"cc-matinput-proxy",7,8),e.Hsn(6),e.qZA(),e.TgZ(7,"mat-error"),e._UZ(8,"cc-validation-message",9),e.qZA()()),2&n){const t=e.oxw(),i=e.MAs(5);let o;e.xp6(3),e.Q6J("ngTemplateOutlet",i),e.xp6(1),e.Q6J("ccInputImp",t.textareaDirective)("required",null==t._required?null:t._required.required),e.xp6(4),e.Q6J("control",null!==(o=null==t.controlDir?null:t.controlDir.control)&&void 0!==o?o:t.__internalControl)}}function y(n,l){}function F(n,l){if(1&n){const t=e.EpF();e.TgZ(0,"textarea",12,13),e.NdJ("blur",function(){return e.CHM(t),e.oxw(2).touch()}),e.qZA()}if(2&n){const t=e.oxw(2);let i;e.Q6J("cdkTextareaAutosize",t.enabled)("cdkAutosizeMinRows",t.minRows+"")("cdkAutosizeMaxRows",t.maxRows+"")("formControl",t.__internalControl)("placeholder",t.placeholder)("required",null!==(i=null==t._required?null:t._required.required)&&void 0!==i&&i)}}function z(n,l){if(1&n){const t=e.EpF();e.TgZ(0,"textarea",14,15),e.NdJ("blur",function(){return e.CHM(t),e.oxw(2).touch()}),e.qZA()}if(2&n){const t=e.oxw(2);let i,o;e.Q6J("formControl",null!==(i=null==t.controlDir?null:t.controlDir.control)&&void 0!==i?i:t.__internalControl)("placeholder",t.placeholder)("required",null!==(o=null==t._required?null:t._required.required)&&void 0!==o&&o)}}function B(n,l){if(1&n&&(e.TgZ(0,"mat-form-field",4)(1,"mat-label"),e.YNc(2,y,0,0,"ng-template",6),e.qZA(),e.YNc(3,F,3,6,"textarea",10),e.YNc(4,z,2,3,"ng-template",null,11,e.W1O),e.TgZ(6,"mat-error"),e._UZ(7,"cc-validation-message",9),e.qZA()()),2&n){const t=e.MAs(5),i=e.oxw(),o=e.MAs(5);let a;e.xp6(2),e.Q6J("ngTemplateOutlet",o),e.xp6(1),e.Q6J("ngIf",i.enabled)("ngIfElse",t),e.xp6(4),e.Q6J("control",null!==(a=null==i.controlDir?null:i.controlDir.control)&&void 0!==a?a:i.__internalControl)}}function N(n,l){1&n&&e.Hsn(0,1,["*ngIf","fieldLabelDir; else stringLabelTpl"])}function k(n,l){if(1&n&&e._uU(0),2&n){const t=e.oxw(2);e.Oqu(t.label)}}function U(n,l){if(1&n&&(e.YNc(0,N,1,0,"ng-content",16),e.YNc(1,k,1,1,"ng-template",null,17,e.W1O)),2&n){const t=e.MAs(2),i=e.oxw();e.Q6J("ngIf",i.fieldLabelDir)("ngIfElse",t)}}const q=[[["textarea"]],[["cc-label"]]],L=["textarea","cc-label"];let W=(()=>{class n{constructor(t,i,o){this._cc_parent=t,this._matFormField=i,this._ngControl=o,this._implementationChanged$=new b.x,this._handleTextareaChanged=a=>{this._implementation=a,this._implementation&&(this._matFormField&&this._implementation.ccSetMatFormfield(this._matFormField),this._ngControl&&this._implementation.ccSetNgControl(this._ngControl)),Object.setPrototypeOf(this,a),(0,v.R)(a.elementRef.nativeElement,"input").pipe((0,d.R)(this._implementationChanged$)).subscribe(_=>this._cc_parent._signalView2Model(a.value)),this._cc_parent.model2view$.pipe((0,d.R)(this._implementationChanged$)).subscribe(_=>a.value=_),this._cc_parent.disabledState$.pipe((0,d.R)(this._implementationChanged$)).subscribe(_=>a.disabled=_),(0,v.R)(a.elementRef.nativeElement,"blur").pipe((0,d.R)(this._implementationChanged$)).subscribe(()=>this._cc_parent.touch())}}set required(t){this._implementation&&(this._implementation.required=(0,g.Ig)(t))}set ccInputImp(t){this._implementationChanged$.next(t),t?this._handleTextareaChanged(t):this._implementationRemoved()}ngOnDestroy(){this._implementationChanged$.next(),this._implementationChanged$.complete()}get elementRef(){var t;return null===(t=this._implementation)||void 0===t?void 0:t.elementRef}_implementationRemoved(){this._implementation=void 0,Object.setPrototypeOf(this,n)}}return n.\u0275fac=function(t){return new(t||n)(e.Y36(p.Hr),e.Y36(m.KE),e.Y36(c.a5,8))},n.\u0275cmp=e.Xpm({type:n,selectors:[["cc-matinput-proxy"]],inputs:{required:"required",ccInputImp:"ccInputImp"},features:[e._Bn([{provide:m.Eo,useExisting:n}])],ngContentSelectors:O,decls:1,vars:0,template:function(t,i){1&t&&(e.F$t(),e.Hsn(0))},styles:["[_nghost-%COMP%]{display:contents}"],changeDetection:0}),n})(),Z=(()=>{class n extends h.Nt{get elementRef(){return this._elementRef}ccSetMatFormfield(t){this._formField=t,this._isInFormField=!0}ccSetNgControl(t){t&&(this.ngControl=t)}}return n.\u0275fac=function(){let l;return function(i){return(l||(l=e.n5z(n)))(i||n)}}(),n.\u0275dir=e.lG2({type:n,selectors:[["textarea","cc-native-textarea",""]],exportAs:["ccNativeTextarea"],features:[e.qOj]}),n})(),K=(()=>{class n extends p.Hr{constructor(t,i,o,a,r){super(t),this._cdRef=i,this.ngZone=o,this.controlDir=a,this._required=r,this._disabledInput=null,this.label="",this._enabled=!1,this._minRows=2,this._maxRows=0,this.__internalControl=new c.NI,this._subscriptions=new M.w0,a?a.valueAccessor=this:this.__internalControl=new c.NI}set textareaDisabled(t){this._disabledInput=(0,g.Ig)(t)}get inputElement(){var t,i;return null!==(t=this._viewChildInputElement)&&void 0!==t?t:null===(i=this.textareaDirective)||void 0===i?void 0:i.elementRef}triggerResize(){this.ngZone.onStable.pipe((0,T.q)(1)).subscribe(()=>this.autosize.resizeToFitContent(!0))}set enabled(t){t=(0,g.Ig)(t),this._enabled=t,this._cdRef.markForCheck()}get enabled(){return this._enabled}set minRows(t){this._minRows=t,this._cdRef.markForCheck()}get minRows(){return this._minRows}set maxRows(t){this._maxRows=t,this._cdRef.markForCheck()}get maxRows(){return this._maxRows}ngAfterViewInit(){var t,i,o,a;if(null===(t=this.__internalControl)||void 0===t||t.valueChanges.pipe((0,d.R)(this._destroy$)).subscribe(r=>{this._signalView2Model(r),this.enabled&&this.triggerResize(),this._cdRef.markForCheck()}),this.model2view$.subscribe(r=>{this.__internalControl.setValue(r,{emitEvent:!1}),this.__internalControl.updateValueAndValidity({onlySelf:!0,emitEvent:!1}),this.enabled&&this.triggerResize(),this._cdRef.markForCheck()}),this.__internalControl.valueChanges.pipe((0,d.R)(this._destroy$)).subscribe(r=>this._signalView2Model(r)),this.disabledState$.subscribe(r=>this.__internalControl[r?"disable":"enable"]({emitEvent:!1})),this.touched$.pipe((0,d.R)(this._destroy$),(0,T.q)(1)).subscribe(()=>this._cdRef.markForCheck()),null===(i=this._viewChildTextarea)||void 0===i||i.forEach(r=>{var _;r.nativeElement.disabled=null!==(_=this._disabledInput)&&void 0!==_&&_,this._cdRef.markForCheck()}),null===(o=this.controlDir)||void 0===o?void 0:o.control){const r=this.controlDir.control.markAsTouched;this.controlDir.control.markAsTouched=_=>{var u;let $=null==r?void 0:r.call(null===(u=this.controlDir)||void 0===u?void 0:u.control,_);return this._cdRef.detectChanges(),$}}if(null===(a=this.controlDir)||void 0===a?void 0:a.control){const r=this.controlDir.control.markAsUntouched;this.controlDir.control.markAsUntouched=_=>{var u;return setTimeout(()=>{this._cdRef.detectChanges()}),null==r?void 0:r.call(null===(u=this.controlDir)||void 0===u?void 0:u.control,_)}}}ngOnInit(){var t,i;this.controlDir&&(this.__internalControl.setValidators(this.controlDir.validator),this.__internalControl.setAsyncValidators(this.controlDir.asyncValidator)),this._subscriptions.add(null===(i=null===(t=this.controlDir)||void 0===t?void 0:t.control)||void 0===i?void 0:i.valueChanges.pipe((0,E.x)()).subscribe(o=>{this.__internalControl.setValue(o,{emitViewToModelChange:!1})}))}ngOnChanges(t){var i;t.textareaDisabled&&(null===(i=this._viewChildTextarea)||void 0===i||i.forEach(o=>{o.nativeElement.disabled=t.textareaDisabled.currentValue,this._cdRef.markForCheck()}))}ngOnDestroy(){super.ngOnDestroy(),this._subscriptions.unsubscribe()}}return n.\u0275fac=function(t){return new(t||n)(e.Y36(e.zs3),e.Y36(e.sBO),e.Y36(e.R0b),e.Y36(c.a5,10),e.Y36(c.Q7,10))},n.\u0275cmp=e.Xpm({type:n,selectors:[["cc-textarea"]],contentQueries:function(t,i,o){if(1&t&&(e.Suo(o,x.k_,5),e.Suo(o,Z,5)),2&t){let a;e.iGM(a=e.CRH())&&(i.fieldLabelDir=a.first),e.iGM(a=e.CRH())&&(i.textareaDirective=a.first)}},viewQuery:function(t,i){if(1&t&&(e.Gf(A,5),e.Gf(R,5,e.SBq),e.Gf(h.Nt,5),e.Gf(R,5,e.SBq)),2&t){let o;e.iGM(o=e.CRH())&&(i.autosize=o.first),e.iGM(o=e.CRH())&&(i._viewChildInputElement=o.first),e.iGM(o=e.CRH())&&(i.inputField=o.first),e.iGM(o=e.CRH())&&(i._viewChildTextarea=o)}},inputs:{textareaDisabled:["disabled","textareaDisabled"],label:"label",type:"type",enabled:["ccTextareaAutosize","enabled"],minRows:["ccAutosizeMinRows","minRows"],maxRows:["ccAutosizeMaxRows","maxRows"]},features:[e._Bn([{provide:p.Hr,useExisting:n}]),e.qOj,e.TTD],ngContentSelectors:L,decls:6,vars:2,consts:[["formFieldWrapper","",1,"w-100"],["class","w-100",4,"ngIf","ngIfElse"],["defaultImplementation",""],["fieldLabelTemplate",""],[1,"w-100"],["matFormField",""],[3,"ngTemplateOutlet"],[3,"ccInputImp","required"],["matInputBridge",""],[3,"control"],["matInput","",3,"cdkTextareaAutosize","cdkAutosizeMinRows","cdkAutosizeMaxRows","formControl","placeholder","required","blur",4,"ngIf","ngIfElse"],["wihoutAutoResize",""],["matInput","",3,"cdkTextareaAutosize","cdkAutosizeMinRows","cdkAutosizeMaxRows","formControl","placeholder","required","blur"],["input","","autosize","cdkTextareaAutosize"],["matInput","",3,"formControl","placeholder","required","blur"],["input",""],[4,"ngIf","ngIfElse"],["stringLabelTpl",""]],template:function(t,i){if(1&t&&(e.F$t(q),e.TgZ(0,"div",0),e.YNc(1,P,9,4,"mat-form-field",1),e.YNc(2,B,8,4,"ng-template",null,2,e.W1O),e.qZA(),e.YNc(4,U,3,2,"ng-template",null,3,e.W1O)),2&t){const o=e.MAs(3);e.xp6(1),e.Q6J("ngIf",i.textareaDirective)("ngIfElse",o)}},directives:[m.KE,W,C.Oq,p.cb,f.O5,m.hX,f.tP,m.TO,h.Nt,I.IC,c.Fj,c.JJ,c.oH,c.Q7],styles:[""],changeDetection:0}),n})(),Y=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[[f.ez,c.UX,C.iv,h.c,m.lN,p.hq,C.YV],x.C6]}),n})()}}]);
(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_quota-management_quota-management_module_ts-node_modules_moment_locale_sync_r-7ca6e8"],{7505:(C,v,n)=>{"use strict";n.r(v),n.d(v,{QuotaManagementModule:()=>at});var p=n(69808),r=n(93075),f=n(65868),h=n(54657),g=n(62764),A=n(45834),M=n(88087),l=n(69202),x=n(88476),J=n(34378),I=n(57902),_=n(92431),D=n(26523),O=n(42002),P=n(29690),S=n(82599),B=n(90824),q=n(43687),G=n(4882),Y=n(28172),w=n(1402);const U={paginationInfo:{last:!1,totalElements:0,totalPages:0,size:6,number:0,first:!0}};var T=n(15439),t=n(5e3),Z=n(8188),y=n(54004),j=n(43604),E=n(40520);let z=(()=>{class s{constructor(e,a,i){this._api=e,this.picklistService=a,this._http=i}getStatistics(){return this._http.get(j.b.statistics).pipe((0,y.U)(a=>a))}getQuotaList(){return this._http.get(j.b.quotaList).pipe((0,y.U)(a=>a))}deleteQuota(e){return this._http.delete(j.b.deleteQuota+"/"+e)}createQuota(e){return this._http.post(j.b.createQuota,e)}getPicklistData(e,a){return this.picklistService.getPicklist({code:e,page:a.page,pageSize:100,search:a.searchString}).pipe((0,y.U)(i=>i.map(u=>({id:u.id,text:u.label}))))}}return s.\u0275fac=function(e){return new(e||s)(t.LFG(Z.JV),t.LFG(Z.Ab),t.LFG(E.eN))},s.\u0275prov=t.Yz7({token:s,factory:s.\u0275fac,providedIn:"root"}),s})();var k=n(46507),F=n(15861),N=n(21799);const L=["formGroupDirective"];function R(s,c){if(1&s){const e=t.EpF();t.TgZ(0,"button",22),t.NdJ("click",function(){t.CHM(e);const i=t.oxw().$implicit;return t.oxw().deleteQuota(i.id)}),t._uU(1,"Delete"),t.qZA()}}function H(s,c){if(1&s&&(t.TgZ(0,"div",20),t.YNc(1,R,2,0,"button",21),t.qZA()),2&s){const e=c.$implicit;t.xp6(1),t.Q6J("ngIf",e.id)}}const b=function(){return[]},V=function(s){return{actions:s}};let W=(()=>{class s{constructor(e,a,i,o,u){this.service=e,this.cdr=a,this._dialog=i,this.fb=o,this.notificationService=u,this.gridCols=[{field:"actions",header:"Actions"},{field:"type",header:"Type"},{field:"job",header:"Job",formatter:d=>d.job.label},{field:"gender",header:"Gender"},{field:"number",header:"Number"}],this.formValue={gender:"",job:"",quantity:"",type:""},this.jobs=d=>this.service.getPicklistData("worker_types",d),this.typeOptions=[{id:"housemaid",text:"Housemaid"},{id:"officeStaff",text:"OfficeStaff"}],this.genderOptions=[{id:"Male",text:"Male"},{id:"Female",text:"Female"}],this.rowStyleFormatter={color:(d,m)=>({condition:"Total"==d.type,value:"#E40000"}),"font-weight":(d,m)=>({condition:"Total"==d.type,value:"bold"})}}ngOnInit(){this.quotaForm=this.fb.group({type:new r.NI(""),job:new r.NI(""),gender:new r.NI(""),quantity:new r.NI("")}),this.loadData()}loadData(){this.service.getQuotaList().subscribe(e=>{this.quotaList=e;let a=0;e.forEach(i=>{a+=i.number}),e.push({type:"Total",job:"",gender:"",number:a}),this.cdr.detectChanges()})}deleteQuota(e){var a=this;this._dialog.confirm("Delete Item, are you sure?","",()=>{var i;this.service.deleteQuota(e).subscribe({next:(i=(0,F.Z)(function*(){a.notificationService.notifySuccess("Deleted Successfully",2e3),a.loadData()}),function(){return i.apply(this,arguments)}),error:i=>{this.notificationService.notifyError(i.message)}})},()=>{})}addQuota(){var i,e=this;this.service.createQuota({gender:this.quotaForm.value.gender,job:{id:this.quotaForm.value.job},number:this.quotaForm.value.quantity,type:this.quotaForm.value.type}).subscribe({next:(i=(0,F.Z)(function*(){e.notificationService.notifySuccess("Quota Added Successfully",2e3),e.loadData()}),function(){return i.apply(this,arguments)}),error:i=>{let o=JSON.parse(i);this.notificationService.notifyError(o.message)}})}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(z),t.Y36(t.sBO),t.Y36(S.uY),t.Y36(r.qu),t.Y36(N.zg))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-quota-quantities"]],viewQuery:function(e,a){if(1&e&&t.Gf(L,5),2&e){let i;t.iGM(i=t.CRH())&&(a.formGroupDirective=i.first)}},features:[t._Bn([])],decls:32,vars:21,consts:[[1,"justify-content-center","light_grey"],[1,"row","justify-content-center","align-items-center"],[1,"col-sm-12"],[1,"bold"],[3,"formGroup"],["formGroupDirective","ngForm"],[1,"row"],[1,"col-lg-2","col-md-2","col-sm-12"],[1,"form-group"],["label","Select Type","name","type","formControlName","type",1,"col-md-8",3,"data","required"],[1,"col-lg-3","col-md-3","col-sm-12"],["label","Select Job","name","job","formControlName","job",1,"col-md-8",3,"lazyPageFetcher","required"],["label","Select Gender","name","gender","formControlName","gender",1,"col-md-8",3,"data","required"],["label","Quantity","type","number","name","quantity","formControlName","quantity",1,"col-md-8",3,"required"],[1,"col-lg-2","col-md-2","col-sm-12","d-flex"],["cc-stroked-button","",3,"disabled","click"],[1,"col-md-12","col-sm-12"],[2,"max-height","10vh",3,"data","columns","stickyHeader","showPaginator","cellTemplate","rowStyleFormatter"],[3,"ccGridCell"],["actionsTmplAll",""],[1,"d-flex","gap-2"],["cc-stroked-button","","color","warn",3,"click",4,"ngIf"],["cc-stroked-button","","color","warn",3,"click"]],template:function(e,a){if(1&e&&(t.TgZ(0,"cc-card")(1,"cc-card-header",0)(2,"cc-card-title")(3,"div",1)(4,"div",2)(5,"div",3),t._uU(6," Quota Request Quantities "),t.qZA()()()()(),t.TgZ(7,"cc-card-content")(8,"form",4,5)(10,"div",6)(11,"div",7)(12,"div",8),t._UZ(13,"cc-select",9),t.qZA()(),t.TgZ(14,"div",10)(15,"div",8),t._UZ(16,"cc-select",11),t.qZA()(),t.TgZ(17,"div",7)(18,"div",8),t._UZ(19,"cc-select",12),t.qZA()(),t.TgZ(20,"div",10)(21,"div",8),t._UZ(22,"cc-input",13),t.qZA()(),t.TgZ(23,"div",14)(24,"div",8)(25,"button",15),t.NdJ("click",function(){return a.addQuota()}),t._uU(26,"Add"),t.qZA()()()()(),t.TgZ(27,"div",6)(28,"div",16),t._UZ(29,"cc-datagrid",17),t.YNc(30,H,2,1,"ng-template",18,19,t.W1O),t.qZA()()()()),2&e){const i=t.MAs(31);t.xp6(8),t.Q6J("formGroup",a.quotaForm),t.xp6(5),t.Q6J("data",a.typeOptions||t.DdM(16,b))("required",!0),t.xp6(3),t.Q6J("lazyPageFetcher",a.jobs)("required",!0),t.xp6(3),t.Q6J("data",a.genderOptions||t.DdM(17,b))("required",!0),t.xp6(3),t.Q6J("required",!0),t.xp6(3),t.Q6J("disabled",!a.quotaForm.valid),t.xp6(4),t.Q6J("data",a.quotaList||t.DdM(18,b))("columns",a.gridCols)("stickyHeader",!0)("showPaginator",!1)("cellTemplate",t.VKq(19,V,i))("rowStyleFormatter",a.rowStyleFormatter),t.xp6(1),t.Q6J("ccGridCell",a.quotaList)}},directives:[l.Dt,l.oJ,l.K9,l.uw,r._Y,r.JL,r.sg,D.jB,r.JJ,r.u,r.Q7,q.G,f.uu,g.Ge,g.VC,p.O5],styles:["cc-select[_ngcontent-%COMP%], cc-input[_ngcontent-%COMP%], cc-label[_ngcontent-%COMP%], cc-button[_ngcontent-%COMP%]{padding:0!important;margin:0!important}.d-flex[_ngcontent-%COMP%]{align-items:center}.bold[_ngcontent-%COMP%]{font-weight:700!important}"],changeDetection:0}),s})();function K(s,c){if(1&s&&(t.TgZ(0,"p",13),t._uU(1),t.qZA()),2&s){const e=t.oxw();t.xp6(1),t.hij("New quota is requested on: ",e.getDate(e.statistics.newQuotaRequestedAt||""),"")}}function $(s,c){if(1&s&&(t.TgZ(0,"p",14),t._uU(1),t.qZA()),2&s){const e=t.oxw();t.xp6(1),t.hij("Expected Date to finish the current quota: ",e.getDate(e.statistics.currentQuotaExpectedFinishDate||""),"")}}const X=function(){return[]},tt=[{path:"",children:[{path:"",component:(()=>{class s{constructor(e,a){this.service=e,this.cdr=a,this.requestedQuotaQuantities=[],this.requestedQuotaQuantitiesWithAppendedRows=[],this.gridMaxItems=6,this.page=0,this.requestedQuotaQuantitiesPagination=U,this.gridCols=[{field:"job",header:"Job"},{field:"gender",header:"Gender"},{field:"quantity",header:"Number"},{field:"creationDate",header:"Date",type:"date",date:{format:"yyyy-LL-dd",locale:"en"}}],this.lineChartType="line",this.rowStyleFormatter={color:(i,o)=>({condition:"Total"==i.job,value:"#E40000"}),"font-weight":(i,o)=>({condition:"Total"==i.job,value:"bold"})}}ngOnInit(){this.fetchStatistics()}fetchStatistics(){this.service.getStatistics().subscribe(e=>{var a,i,o,u;this.statistics=e,this.lineChartOptions={responsive:!0,interaction:{mode:"index",intersect:!1},scales:{x:{title:{display:!0,text:"Date"}},y:{title:{display:!0,text:"Total Quota Quantities"}}},plugins:{tooltip:{callbacks:{footer:function(d){var m;return 1===d[0].datasetIndex?`R\xb2: ${null===(m=null==e?void 0:e.graph)||void 0===m?void 0:m.rSquared.toFixed(2)}`:""}}}}},this.lineChartData={labels:null===(a=null==e?void 0:e.graph)||void 0===a?void 0:a.xAxisValues,datasets:[{label:"Quota Changes",data:null===(i=null==e?void 0:e.graph)||void 0===i?void 0:i.observedData,borderColor:"blue",fill:!1,pointBackgroundColor:"blue",pointBorderColor:"blue",pointBorderWidth:1},{label:`Trendline for quota R\xb2 = ${null===(o=null==e?void 0:e.graph)||void 0===o?void 0:o.rSquared.toFixed(2)}`,data:null===(u=null==e?void 0:e.graph)||void 0===u?void 0:u.trendlineData,borderColor:"red",fill:!1,pointBackgroundColor:"red",pointBorderColor:"red",pointBorderWidth:1}]},this.requestedQuotaQuantitiesWithAppendedRows=this.appendSumRowAfterGroup(this.statistics.requestedQuotaQuantities),this.paginate(0),this.cdr.markForCheck()})}paginate(e){this.requestedQuotaQuantities=this.requestedQuotaQuantitiesWithAppendedRows.slice(e*this.gridMaxItems,(e+1)*this.gridMaxItems),this.requestedQuotaQuantitiesPagination.paginationInfo={last:(e+1)*this.gridMaxItems+1>this.statistics.requestedQuotaQuantities.length,totalElements:this.statistics.requestedQuotaQuantities.length,totalPages:Math.ceil(this.statistics.requestedQuotaQuantities.length/this.gridMaxItems),size:this.gridMaxItems,number:e,first:0===e}}appendSumRowAfterGroup(e){const a=this.calculateSumByDate(e),i=[];let o=null;return e.forEach((u,d)=>{const m=u.creationDate.split(" ")[0];null!==o&&o!==m&&i.push({total:!0,quantity:a[o],job:"Total",creationDate:o+" 00:00:00"}),i.push(u),e.length-1===d&&i.push({total:!0,quantity:a[m],job:"Total",creationDate:m+" 00:00:00"}),o=m}),i}calculateSumByDate(e){const a={};return e.forEach(i=>{const o=i.creationDate.split(" ")[0];a[o]||(a[o]=0),a[o]+=i.quantity}),a}getDate(e){return e?T(e).format("DD/MM/YYYY"):T().format("DD/MM/YYYY")}fetchQuotaList(){this.service.getQuotaList().subscribe(e=>{this.quotaList=e,this.cdr.detectChanges()})}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(z),t.Y36(t.sBO))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-quota-mgt"]],features:[t._Bn([])],decls:20,vars:16,consts:[[1,"my-5"],[1,"justify-content-center","light_grey"],[1,"row","justify-content-center","align-items-center"],[1,"col-sm-12"],[1,"bold"],[1,"row"],[1,"col-md-6","col-sm-12"],[2,"max-height","10vh",3,"data","columns","stickyHeader","length","pageSize","pageIndex","showColumnMenuButton","showColumnMenuHeader","rowStyleFormatter","page"],[1,"my-2"],["class","w3-margin-0 w3-padding text-success",4,"ngIf"],["class","w3-margin-0 w3-padding ",4,"ngIf"],[3,"data","options","type","width"],[1,"mt-2"],[1,"w3-margin-0","w3-padding","text-success"],[1,"w3-margin-0","w3-padding"]],template:function(e,a){1&e&&(t.TgZ(0,"div",0)(1,"cc-card")(2,"cc-card-header",1)(3,"cc-card-title")(4,"div",2)(5,"div",3)(6,"div",4),t._uU(7," Quota Status "),t.qZA()()()()(),t.TgZ(8,"cc-card-content")(9,"div",5)(10,"div",6)(11,"cc-datagrid",7),t.NdJ("page",function(o){return a.paginate(o.pageIndex)}),t.qZA(),t.TgZ(12,"div",8),t.YNc(13,K,2,1,"p",9),t.YNc(14,$,2,1,"p",10),t.qZA()(),t.TgZ(15,"div",6),t._UZ(16,"cc-chart",11),t.qZA()()(),t._UZ(17,"cc-card-footer"),t.qZA(),t.TgZ(18,"div",12),t._UZ(19,"app-quota-quantities"),t.qZA()()),2&e&&(t.xp6(11),t.Q6J("data",a.requestedQuotaQuantities||t.DdM(15,X))("columns",a.gridCols)("stickyHeader",!0)("length",a.requestedQuotaQuantitiesPagination.paginationInfo.totalElements)("pageSize",a.requestedQuotaQuantitiesPagination.paginationInfo.size)("pageIndex",a.requestedQuotaQuantitiesPagination.paginationInfo.number)("showColumnMenuButton",!0)("showColumnMenuHeader",!1)("rowStyleFormatter",a.rowStyleFormatter),t.xp6(2),t.Q6J("ngIf",null==a.statistics?null:a.statistics.newQuotaRequestedAt),t.xp6(1),t.Q6J("ngIf",null==a.statistics?null:a.statistics.currentQuotaExpectedFinishDate),t.xp6(2),t.Q6J("data",a.lineChartData)("options",a.lineChartOptions)("type",a.lineChartType)("width","100%"))},directives:[l.Dt,l.oJ,l.K9,l.uw,g.Ge,p.O5,k.x,l.uC,W],styles:[".bold[_ngcontent-%COMP%]{font-weight:700!important}"],changeDetection:0}),s})(),data:{label:"Quota Management"}}],data:{label:"VP Manager"}}];let et=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({imports:[[w.Bz.forChild(tt)],w.Bz]}),s})(),at=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({providers:[],imports:[[p.ez,B.nE,S.I8,P.J,O.l2,D.lK,q.f,_.XD,J.yU,I.A,p.ez,r.u5,r.UX,h.JC,f.S6,M.pS,g.Gz,x.n_,A.L,M.pS,l.Ev,x.er,G.$,Y.Bp,l.Ev,k.o,et]]}),s})()},46700:(C,v,n)=>{var p={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function r(h){var g=f(h);return n(g)}function f(h){if(!n.o(p,h)){var g=new Error("Cannot find module '"+h+"'");throw g.code="MODULE_NOT_FOUND",g}return p[h]}r.keys=function(){return Object.keys(p)},r.resolve=f,C.exports=r,r.id=46700}}]);
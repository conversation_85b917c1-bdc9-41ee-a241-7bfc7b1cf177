(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_maids-transfer_maids-transfer_module_ts-node_modules_moment_locale_sync_recursive_"],{19791:(C,b,a)=>{"use strict";a.r(b),a.d(b,{MaidsTransferModule:()=>Y});var d=a(69808),u=a(1402),m=a(48966),l=a(77579),t=a(5e3),p=a(40520),S=a(8188),h=a(43604),g=a(21799);let v=(()=>{class s{constructor(e,n,o){this._api=e,this._http=n,this._notificationService=o}getTransferMaids$(e){return this._http.get(`${this._api}/${h.b.maidsToTransfer}`,{params:new p.LE(e?{fromObject:{search:e.search||"",page:e.params.page||0,size:e.params.size||30}}:{fromObject:{page:0,size:30,search:""}})})}getHousemaidNumbers$(e){return this._http.get(`${h.b.housemaidNumbers}/${e}`)}getOfficeStaff$(e){return this._http.get(`${this._api}/${h.b.officeStaff}`,{params:new p.LE(e?{fromObject:Object.assign(Object.assign({},e.params),{_type:"query",_:1718272798384})}:{fromObject:{page:0,size:50,search:"",_type:"query",_:1718272798384}})})}transferMaid$(e,n){return this._http.get(`${this._api}/${h.b.applyTransfer}/${e}/${n}`,{observe:"response"})}}return s.\u0275fac=function(e){return new(e||s)(t.LFG(S.JV),t.LFG(p.eN),t.LFG(g.zg))},s.\u0275prov=t.Yz7({token:s,factory:s.\u0275fac,providedIn:"root"}),s})();var f=a(82599),j=a(65868),T=a(45834);function Z(s,r){if(1&s){const e=t.EpF();t.ynx(0),t.TgZ(1,"div",1)(2,"cc-dialog-header")(3,"h4",2),t._uU(4),t.qZA(),t.TgZ(5,"a",3),t.NdJ("click",function(){return t.CHM(e),t.oxw().close()}),t.TgZ(6,"cc-icon"),t._uU(7,"close"),t.qZA()()(),t.TgZ(8,"cc-dialog-content",4)(9,"div",5)(10,"div",6)(11,"p"),t._uU(12,"WhatsApp Number:"),t.qZA()(),t.TgZ(13,"div",7)(14,"p"),t._uU(15),t.qZA(),t.TgZ(16,"button",8),t.NdJ("click",function(){const i=t.CHM(e).ngIf;return t.oxw().copy(i.normalizedWhatsAppPhoneNumber)}),t.TgZ(17,"cc-icon"),t._uU(18,"content_copy"),t.qZA()()()(),t.TgZ(19,"div",5)(20,"div",6)(21,"p"),t._uU(22,"UAE Number:"),t.qZA()(),t.TgZ(23,"div",7)(24,"p"),t._uU(25),t.qZA(),t.TgZ(26,"button",8),t.NdJ("click",function(){const i=t.CHM(e).ngIf;return t.oxw().copy(i.normalizedPhoneNumber)}),t.TgZ(27,"cc-icon"),t._uU(28,"content_copy"),t.qZA()()()()(),t.TgZ(29,"cc-dialog-actions")(30,"button",9),t.NdJ("click",function(){return t.CHM(e),t.oxw().close()}),t._uU(31,"Close"),t.qZA()()(),t.BQk()}if(2&s){const e=r.ngIf,n=t.oxw();t.xp6(4),t.hij("",n.data.name," Numbers"),t.xp6(11),t.Oqu(e.normalizedWhatsAppPhoneNumber),t.xp6(10),t.Oqu(e.normalizedPhoneNumber)}}let $=(()=>{class s{constructor(e,n,o,i){this.data=e,this._transferService=n,this._notificationService=o,this._ccDialogRef=i,this.destroy$=new l.x,this.numbers$=this._transferService.getHousemaidNumbers$(this.data.maidId)}ngOnInit(){}copy(e){navigator.clipboard.writeText(e).then(()=>{this._notificationService.notifySuccess("Copied to clipboard successfully")})}close(){this._ccDialogRef.close()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(m.WI),t.Y36(v),t.Y36(g.zg),t.Y36(m.so))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-phone-dialog"]],decls:2,vars:3,consts:[[4,"ngIf"],["cc-std-dialog",""],["cc-dialog-title","",1,"text-center","my-4"],["role","button","type","button","cc-icon-button","",3,"click"],[1,"px-4"],[1,"row"],[1,"col-md-4"],[1,"col-md-8","d-flex","justify-content-between"],["cc-icon-button","","color","accent",3,"click"],["cc-flat-button","","color","basic",3,"click"]],template:function(e,n){1&e&&(t.YNc(0,Z,32,3,"ng-container",0),t.ALo(1,"async")),2&e&&t.Q6J("ngIf",t.lcZ(1,1,n.numbers$))},directives:[d.O5,f.iK,f.Cj,f.Zb,j.uu,T.Q9,f.kL,f.Zu],pipes:[d.Ov],styles:[""]}),s})();var D=a(54004),L=a(82722),N=a(18505),O=a(70262),c=a(93075),k=a(26523);const J=function(){return[]};let A=(()=>{class s{constructor(e,n,o,i,_,B){this.data=e,this._transferService=n,this._formBuilder=o,this._ccDialog=i,this._ccDialogRef=_,this._notificationService=B,this.destroy$=new l.x,this.transferForm=this._formBuilder.group({officeStaffId:""}),this.officeStaff$=this._transferService.getOfficeStaff$().pipe((0,D.U)(E=>E.content.map(M=>({id:M.id,text:M.name}))))}ngOnInit(){}confirmTransfer(){this._ccDialog.confirm("Warning","Are you sure you want to transfer maid's visa to this office staff?",()=>{this._transferService.transferMaid$(this.data.maidId,this.transferForm.value.officeStaffId).pipe((0,L.R)(this.destroy$)).pipe((0,N.b)(e=>{200===e.status?this._notificationService.notifySuccess("Transferred successfully"):this._notificationService.notifyError(`Unexpected status code: ${e.status}`)}),(0,O.K)(e=>{throw this._notificationService.notifyError(`Error occurred: ${e}`),e})).subscribe(()=>this.cancel())})}cancel(){this._ccDialogRef.close()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(m.WI),t.Y36(v),t.Y36(c.qu),t.Y36(f.uY),t.Y36(m.so),t.Y36(g.zg))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-transfer-dialog"]],decls:12,vars:7,consts:[[1,"dialog-title"],[1,"my-3"],[3,"formGroup"],["label","Office staff name","formControlName","officeStaffId",1,"col-md-6",3,"required","data"],[1,"d-flex","justify-content-end","gap-2"],["cc-flat-button","","color","basic",3,"click"],["cc-flat-button","","color","basic",1,"accent-text",3,"disabled","click"]],template:function(e,n){if(1&e&&(t.TgZ(0,"h3",0),t._uU(1,"Transfer to office staff"),t.qZA(),t.TgZ(2,"p",1),t._uU(3," Please click on the related office staff to transfer maid's visa to\n"),t.qZA(),t.TgZ(4,"form",2),t._UZ(5,"cc-select",3),t.ALo(6,"async"),t.TgZ(7,"div",4)(8,"button",5),t.NdJ("click",function(){return n.cancel()}),t._uU(9,"Cancel"),t.qZA(),t.TgZ(10,"button",6),t.NdJ("click",function(){return n.confirmTransfer()}),t._uU(11," Transfer "),t.qZA()()()),2&e){let o;t.xp6(4),t.Q6J("formGroup",n.transferForm),t.xp6(1),t.Q6J("required",!0)("data",null!==(o=t.lcZ(6,4,n.officeStaff$))&&void 0!==o?o:t.DdM(6,J)),t.xp6(5),t.Q6J("disabled",n.transferForm.invalid)}},directives:[c._Y,c.JL,c.sg,k.jB,c.JJ,c.u,c.Q7,j.uu],pipes:[d.Ov],styles:[".dialog-title[_ngcontent-%COMP%]{font-weight:300;font-size:24px}.accent-text[_ngcontent-%COMP%]{color:#92000a}"]}),s})();var x=a(43687),y=a(62764);function w(s,r){if(1&s){const e=t.EpF();t.TgZ(0,"button",6),t.NdJ("click",function(){const i=t.CHM(e).$implicit;return t.oxw().openPhoneDialog(i.id,i.name)}),t._uU(1," View "),t.qZA()}}const z=function(){return[]},F=function(s){return{phone:s}},P=[{path:"",children:[{path:"",component:(()=>{class s{constructor(e,n,o){this._formBuilder=e,this._ccDialog=n,this._transferService=o,this.searchForm=this._formBuilder.group({search:""}),this.transferCols=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"stroked",text:"Transfer Visa",color:"basic",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.openTansferDialog(i.id)}]}},{field:"name",header:"Name"},{field:"phone",header:"Phone Number"},{field:"nationality.label",header:"Nationality"},{field:"initialLocation.label",header:"Location"}]}ngOnInit(){this.dataList$=this._transferService.getTransferMaids$(),this.dataList$.subscribe(e=>this.dataList=e)}search(){this.dataList$=this._transferService.getTransferMaids$({search:this.searchForm.value.search,params:{}}),this.dataList$.subscribe(e=>this.dataList=e)}getNextPage(e){this._transferService.getTransferMaids$({search:this.searchForm.value.search,params:{page:e.pageIndex,size:e.pageSize}}),this.dataList$.subscribe(n=>this.dataList=n)}openPhoneDialog(e,n){this._ccDialog.originalOpen($,{width:"600px",data:{maidId:e,name:n}})}openTansferDialog(e){this._ccDialog.originalOpen(A,{width:"600px",data:{maidId:e}})}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(c.qu),t.Y36(f.uY),t.Y36(v))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-maids-transfer"]],decls:8,vars:16,consts:[[3,"formGroup","submit"],["formControlName","search","label","Search","placeholder","Search",1,"col-md-6","offset-md-3","col-lg-4","offset-lg-4","px-3","mt-3"],[1,"input-icon-suffix",3,"click"],[1,"d-flex","w-100",3,"columns","data","stickyHeader","columnMenuButtonIcon","showPaginator","length","pageOnFront","pageIndex","pageSize","cellTemplate","page"],[3,"ccGridCell"],["phoneTpl",""],["cc-stroked-button","",3,"click"]],template:function(e,n){if(1&e&&(t.TgZ(0,"form",0),t.NdJ("submit",function(){return n.search()}),t.TgZ(1,"cc-input",1)(2,"cc-icon",2),t.NdJ("click",function(){return n.search()}),t._uU(3,"search"),t.qZA()()(),t.ynx(4),t.TgZ(5,"cc-datagrid",3),t.NdJ("page",function(i){return n.getNextPage(i)}),t.qZA(),t.YNc(6,w,2,0,"ng-template",4,5,t.W1O),t.BQk()),2&e){const o=t.MAs(7);t.Q6J("formGroup",n.searchForm),t.xp6(5),t.Q6J("columns",n.transferCols)("data",null!=n.dataList?n.dataList.content:t.DdM(12,z))("stickyHeader",!0)("columnMenuButtonIcon","settings")("showPaginator",!0)("length",null!=n.dataList?n.dataList.totalElements:0)("pageOnFront",!1)("pageIndex",null!=n.dataList?n.dataList.number:0)("pageSize",null!=n.dataList?n.dataList.size:0)("cellTemplate",t.VKq(13,F,o)),t.xp6(1),t.Q6J("ccGridCell",null!=n.dataList?n.dataList.content:t.DdM(15,z))}},directives:[c._Y,c.JL,c.sg,x.G,c.JJ,c.u,T.Q9,y.Ge,y.VC,j.uu],styles:[""]}),s})(),data:{pageCode:"MaidsTransferredToOfficeStaff"}}]}];let U=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({imports:[[u.Bz.forChild(P)],u.Bz]}),s})();var I=a(88476);let Y=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({imports:[[d.ez,U,x.f,y.Gz,f.I8,I.n_.forFeature(),k.lK,T.L,c.UX,j.S6,g.Bw]]}),s})()},46700:(C,b,a)=>{var d={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function u(l){var t=m(l);return a(t)}function m(l){if(!a.o(d,l)){var t=new Error("Cannot find module '"+l+"'");throw t.code="MODULE_NOT_FOUND",t}return d[l]}u.keys=function(){return Object.keys(d)},u.resolve=m,C.exports=u,u.id=46700}}]);
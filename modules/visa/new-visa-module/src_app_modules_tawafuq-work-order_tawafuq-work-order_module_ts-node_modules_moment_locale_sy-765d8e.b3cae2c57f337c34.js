(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_tawafuq-work-order_tawafuq-work-order_module_ts-node_modules_moment_locale_sy-765d8e"],{21426:(L,D,n)=>{"use strict";n.r(D),n.d(D,{TawafuqWorkOrderModule:()=>ge});var p=n(69808),h=n(97582),e=n(5e3),u=n(54004),f=n(40520),c=n(43604);let g=(()=>{class a{constructor(t){this._http=t,this.doneTask=(o,i)=>this._http.post(c.b["tawafuq-work-order"].complete(o,i),{id:o}),this.closePossibleComplaint=o=>this._http.post(c.b["tawafuq-work-order"].closePossibleComplaint(o),{}),this.reopenComplaint=(o,i,r)=>this._http.post(c.b["tawafuq-work-order"].reopenComplaint(o),{notes:i},{params:{taskName:r}}),this.approvePayment=(o,i,r)=>this._http.post(c.b["tawafuq-work-order"].approvePayment(o),{notes:i},{params:{taskName:r}}),this.getHousemaidWithTawafuqComplaints=()=>this._http.get(c.b["tawafuq-work-order"].getHousemaidWithTawafuqComplaints,{params:{size:20}}),this.getHousemaidList=o=>this._http.get(c.b["tawafuq-work-order"].getHousemaidList,{params:{page:o.page,size:o.size||100,search:o.searchString||""}}),this.getPossibleComplaints=()=>this._http.get(c.b["tawafuq-work-order"].getPossibleComplaints,{params:{size:20}}),this.getTawafuqProTodos=o=>this._http.get(c.b["tawafuq-work-order"].getProTodos,{params:{type:o,size:50}}),this.getActiveTodos=(o,i="")=>this._http.post(c.b["tawafuq-work-order"].getActiveTodos,[],{params:{type:o,page:0,size:50,date:i}}),this.addTranslationFees=(o,i,r)=>this._http.post(c.b["tawafuq-work-order"].addTranslationFees(o),{},{params:{service:i,fees:r}}),this.getTranslationServices=()=>this._http.get(c.b["tawafuq-work-order"].getTranslationServices)}toggleMarker(t,o){let i=new f.LE;return o&&(i=i.set("remove",o)),this._http.post(c.b["tawafuq-work-order"]["toggle-marker"](t),{},{params:i})}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(f.eN))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})(),y=(()=>{class a{constructor(t){this.apiService=t,this.housemaidList$=o=>this.apiService.getHousemaidList(o).pipe((0,u.U)(i=>i.content.map(r=>({id:r.id,text:r.label})))),this.translationServiceOptions$=this.apiService.getTranslationServices().pipe((0,u.U)(o=>o.map(i=>({id:i,text:i}))))}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(g))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})();var k=n(61135);const U={params:{page:0,size:50}};let A=(()=>{class a{constructor(){this.reloadDataSub$=new k.X(0),this.searchStateSub$=new k.X({}),this.reloadData$=this.reloadDataSub$.asObservable(),this.searchState$=this.searchStateSub$.asObservable()}reloadData(){this.reloadDataSub$.next(1)}updateSearchState(t,o){console.log(o);const i=this.searchStateSub$.value,r=Object.assign(Object.assign({},i[t]),o);this.searchStateSub$.next(Object.assign(Object.assign({},i),{[t]:r}))}getSearchStateByKey$(t){return this.searchStateSub$.pipe((0,u.U)(o=>(console.log(Object.keys(o)),Object.keys(o).includes(t)?o[t]:U)))}resetSearchState(){const t=Object.fromEntries(Object.keys(this.searchStateSub$.value).map(o=>[o,Object.assign({},U)]));this.searchStateSub$.next(t)}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})();var O=n(48966),l=n(82599),E=n(26523),d=n(93075),S=n(65868);let Q=(()=>{class a{constructor(t,o,i,r){this.dataService=t,this.apiService=o,this.stateService=i,this.dialogRef=r,this.housemaidOpts$=this.dataService.housemaidList$}onAdd(t){this.apiService.toggleMarker(t).subscribe({next:()=>{this.stateService.reloadData(),this.dialogRef.close()}})}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(y),e.Y36(g),e.Y36(A),e.Y36(O.so))},a.\u0275cmp=e.Xpm({type:a,selectors:[["ng-component"]],decls:13,vars:3,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],["ngModel","",3,"lazyPageFetcher","required"],["housemaid","ngModel"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"]],template:function(t,o){if(1&t){const i=e.EpF();e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Add Maid To All Tawafuq Complaints"),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4),e._UZ(6,"cc-select",5,6),e.qZA(),e.TgZ(8,"div",7)(9,"button",8),e._uU(10,"Cancel"),e.qZA(),e.TgZ(11,"button",9),e.NdJ("click",function(){e.CHM(i);const m=e.MAs(7);return o.onAdd(m.control.value)}),e._uU(12," Add "),e.qZA()()()}if(2&t){const i=e.MAs(7);e.xp6(6),e.Q6J("lazyPageFetcher",o.housemaidOpts$)("required",!0),e.xp6(5),e.Q6J("disabled",!i.control.value)}},directives:[l.iK,l.Cj,l.Zb,l.zn,l.fX,l.kL,E.jB,d.JJ,d.On,d.Q7,l.Zu,S.uu],encapsulation:2,changeDetection:0}),a})();var F=n(88476),P=n(63900),Y=n(92340);const B={baseAPI:Y.N.apiBase,isVPAdmin:!1,autoRefresh:!1,MODULES:[{module:"VISA",processTaskFilter:{tawafuqManagerWorkflow:["ADD_COURT_RESULT","COLLECT_MONEY_FROM_MAID","PAY_COURT_FEES","PAY_COURT_VERDICT","PAY_LAWYER_FEES","PAY_THE_MAID","SET_COURT_DATE","WAITING_FOR_VERDICT_EXECUTION"]}}],ROW_ACTIONS:{tawafuqManagerWorkflow:{WAITING_FOR_VERDICT_EXECUTION:[{label:"Done",code:"done_task"}],ADD_COURT_RESULT:[{label:"View Task",code:"view_task"}],COLLECT_MONEY_FROM_MAID:[{label:"View Task",code:"view_task"}],PAY_COURT_FEES:[{label:"View Task",code:"view_task"}],PAY_COURT_VERDICT:[{label:"View Task",code:"view_task"}],PAY_LAWYER_FEES:[{label:"View Task",code:"view_task"}],PAY_THE_MAID:[{label:"View Task",code:"view_task"}],SET_COURT_DATE:[{label:"View Task",code:"view_task"}]}},TABLE_HEADERS:{allHeaders:{taskLabel:{label:"To-Do",type:"html",sortProp:"taskLabel",value:function(a){return a.taskLabel}},housemaidName:"Maid Name",courtNumber:"Court Number",raisedBy:"Who raised the case",creationDate:"Creation Date"},shownHeaders:["taskLabel","housemaidName","courtNumber","raisedBy","creationDate"]},ADD_COMPLAINT_RESULT_COLS:[{header:"Maid\u2019s Name",field:"housemaidName"},{header:"Court number",field:"courtNumber"},{header:"Who raised the case",field:"whoComplained"},{header:"Creation Date",field:"creationDate",formatter:a=>a.creationDate.split(" ")[0]}],COURT_DATA_GRID_COLS:[{header:"Maid\u2019s Name",field:"housemaidName"},{header:"Court number",field:"courtNumber"},{header:"Who raised the case",field:"whoComplained"},{header:"Creation Date",field:"creationDate",formatter:a=>a.creationDate.split(" ")[0]}],POSSIBLE_COMPLAINTS_COLS:[{header:"actions",field:"operations"},{header:"Maid\u2019s Name",field:"housemaidName"},{header:"Start date",field:"startDate",formatter:a=>{var s;return null===(s=a.startDate)||void 0===s?void 0:s.split(" ")[0]},width:"15ch"},{header:"Resignation date",field:"resignationDate",formatter:a=>{var s;return null===(s=a.resignationDate)||void 0===s?void 0:s.split(" ")[0]}},{header:"Cancellation Mode",field:"cancellationMode"},{header:"Contract type",field:"contractType"},{header:"Basic salary",field:"basicSalary"},{header:"Total salary",field:"totalSalary"},{header:"Maid\u2019s demand",field:"maidDemand"},{header:"Notes",field:"notes"}],ALL_TAWAFUQ_COMPLAINTS_COLS:[{header:"actions",field:"operations"},{header:"Maid\u2019s Name",field:"housemaid.name"},{header:"Date",field:"creationDate"},{header:"Added by",field:"creator.fullName"}]};var V=n(50727),H=n(70262),G=n(60515);let z=(()=>{class a{constructor(t,o){this.stateService=t,this.apiService=o,this.subscriptions=new V.w0}loadTawafuqComplaints(){return this.createDataStream(()=>this.apiService.getHousemaidWithTawafuqComplaints())}loadCourtData(){return this.createDataStream(()=>this.apiService.getActiveTodos("SEND_FILES_TO_COURT"))}loadComplaintResults(){return this.createDataStream(()=>this.apiService.getTawafuqProTodos("ADD_COMPLAINT_RESULT"))}loadPossibleComplaints(){return this.createDataStream(()=>this.apiService.getPossibleComplaints())}createDataStream(t){return this.stateService.reloadData$.pipe((0,P.w)(()=>t()),(0,H.K)(()=>G.E))}ngOnDestroy(){this.subscriptions.unsubscribe()}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(A),e.LFG(g))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})();var R=n(21799),w=n(1402),x=n(45834),W=n(463),I=n(62764),b=n(34378);const X=["*"];let K=(()=>{class a{constructor(){this.title=""}ngOnInit(){}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.Xpm({type:a,selectors:[["tawafuq-folding-panel"]],inputs:{title:"title"},ngContentSelectors:X,decls:8,vars:1,consts:[[1,"my-4"],[1,"py-1",2,"font-size","16px","font-weight","500"]],template:function(t,o){1&t&&(e.F$t(),e.TgZ(0,"div",0)(1,"cc-accordion")(2,"cc-panel")(3,"cc-panel-title")(4,"p",1),e._uU(5),e.qZA()(),e.TgZ(6,"cc-panel-body"),e.Hsn(7),e.qZA()()()()),2&t&&(e.xp6(5),e.hij(" ",o.title," "))},directives:[b.I,b.CW,b.LL,b.G9],encapsulation:2}),a})();function ee(a,s){if(1&a){const t=e.EpF();e.TgZ(0,"button",20),e.NdJ("click",function(){const r=e.CHM(t).$implicit;return e.oxw().closeComplaintByHousemaid(r)}),e._uU(1," Close "),e.qZA()}}function te(a,s){if(1&a){const t=e.EpF();e.TgZ(0,"button",20),e.NdJ("click",function(){const r=e.CHM(t).$implicit;return e.oxw().closePossibleComplaint(r)}),e._uU(1," Close "),e.qZA()}}const J=function(a){return{operations:a}},j={content:[],number:0,size:0,totalElements:0,totalPages:0};let T=class{constructor(s,t,o,i,r,m,M){this.apiService=s,this.stateService=t,this.dataFetchService=o,this.notification=i,this.router=r,this.dialog=m,this.cdRef=M,this.config=B,this.allTawafuqComplaints=j,this.addComplaintResultData=j,this.possibleComplaints=j,this.courtData=j,this.rowStyleFormatter={"background-color":(Z,Te)=>({condition:Z.appointmentPassed,value:"#ff0000"}),color:(Z,Te)=>({condition:Z.appointmentPassed,value:"#fff"})}}ngOnInit(){this.stateService.reloadData$.pipe((0,P.w)(()=>this.dataFetchService.loadTawafuqComplaints())).subscribe(s=>{this.allTawafuqComplaints=s,this.cdRef.markForCheck()}),this.dataFetchService.loadCourtData().subscribe(s=>{this.courtData=s,this.cdRef.markForCheck()}),this.dataFetchService.loadComplaintResults().subscribe(s=>{this.addComplaintResultData=s,this.cdRef.markForCheck()}),this.stateService.reloadData$.pipe((0,P.w)(()=>this.dataFetchService.loadPossibleComplaints())).subscribe(s=>{this.possibleComplaints=s,this.cdRef.markForCheck()})}viewTask(s){this.router.navigate(["visa","v2","tawafuq-work-order",s.id],{queryParams:{type:s.taskName,maid:s.housemaidName}})}handleAction(s){switch(s.data.actionCode){case"view_task":this.viewTask(s.data.data);break;case"done_task":this.dialog.alert("Are you sure?","",()=>this.doneTask(s.data.id,"WAITING_FOR_VERDICT_EXECUTION"))}}doneTask(s,t){this.apiService.doneTask(s,t).subscribe({next:()=>{this.notification.notifySuccess("Done Successfully."),this.stateService.reloadData()}})}closePossibleComplaint(s){this.apiService.closePossibleComplaint(s.id).subscribe({next:()=>{this.stateService.resetSearchState(),this.stateService.reloadData()}})}closeComplaintByHousemaid(s){this.apiService.toggleMarker(s.housemaid.id,!0).subscribe({next:()=>{this.stateService.reloadData(),this.stateService.resetSearchState()}})}openAddMaidDialog(){this.dialog.originalOpen(Q,{panelClass:["col-md-6"]})}handleNextPage(s,t){this.stateService.updateSearchState(t,{params:{page:s.pageIndex,size:s.pageSize}})}scrollToTop(){const s=document.getElementById("scrollableContainer");null==s||s.scrollTo({top:0})}};T.\u0275fac=function(s){return new(s||T)(e.Y36(g),e.Y36(A),e.Y36(z),e.Y36(R.zg),e.Y36(w.F0),e.Y36(l.uY),e.Y36(e.sBO))},T.\u0275cmp=e.Xpm({type:T,selectors:[["app-tawafuq-work-order"]],decls:36,vars:37,consts:[["id","scrollableContainer",1,"px-4","my-5",2,"height","100vh","overflow-y","scroll"],["id","scroll_up",2,"width","fit-content","position","fixed","bottom","10px","right","42px","z-index","9999999"],["cc-raised-button","",2,"background","#000","color","#fff","border-radius","50% !important","height","61px",3,"click"],[2,"font-size","36px"],[1,"mb-3"],[1,"col-md-12",2,"z-index","999999"],[2,"z-index","999999",3,"hideSearch","baseAPI","modules","autoRefresh","rowActions","tableHeaders","isVPAdmin","actionMode","onRowAction"],["id","scroll_up_show",1,"mb-3","mt-5"],[1,"col-md-12","px-0","py-2"],["cc-raised-button","","color","primary",3,"click"],[3,"columns","data","pageIndex","pageSize","length","cellTemplate","page"],[3,"ccGridCell"],["closeComplaintByMaidTmp",""],[1,"mb-3","mt-5"],["title","Submit the Court File"],[3,"columns","data","pageIndex","pageSize","length","page"],["title","Add Complaint Result"],[3,"columns","data","pageIndex","pageSize","length","rowStyleFormatter","page"],["title","Possible complaints"],["closePossibleComplaintTmp",""],["cc-flat-button","",3,"click"]],template:function(s,t){if(1&s&&(e.TgZ(0,"div",0)(1,"div",1)(2,"button",2),e.NdJ("click",function(){return t.scrollToTop()}),e.TgZ(3,"cc-icon",3),e._uU(4,"arrow_upward"),e.qZA()()(),e.TgZ(5,"div",4)(6,"h4"),e._uU(7,"Tawafuq manager work order"),e.qZA()(),e.TgZ(8,"div",5)(9,"cc-workflow-single-task",6),e.NdJ("onRowAction",function(i){return t.handleAction(i)}),e.qZA()(),e.TgZ(10,"div",7)(11,"h4"),e._uU(12,"All Tawafuq Complaints"),e.qZA()(),e.TgZ(13,"div",8)(14,"button",9),e.NdJ("click",function(){return t.openAddMaidDialog()}),e._uU(15," Add Maid "),e.qZA()(),e.ynx(16),e.TgZ(17,"cc-datagrid",10),e.NdJ("page",function(i){return t.handleNextPage(i,"allTawafuqComplaints")}),e.qZA(),e.YNc(18,ee,2,0,"ng-template",11,12,e.W1O),e.BQk(),e.TgZ(20,"div",13)(21,"h4"),e._uU(22,"Tawafuq tasks follow up"),e.qZA()(),e.ynx(23),e.TgZ(24,"tawafuq-folding-panel",14)(25,"cc-datagrid",15),e.NdJ("page",function(i){return t.handleNextPage(i,"court")}),e.qZA()(),e.BQk(),e.ynx(26),e.TgZ(27,"tawafuq-folding-panel",16)(28,"cc-datagrid",17),e.NdJ("page",function(i){return t.handleNextPage(i,"addComplaintResult")}),e.qZA()(),e.BQk(),e.ynx(29),e.TgZ(30,"tawafuq-folding-panel",18),e.ynx(31),e.TgZ(32,"cc-datagrid",10),e.NdJ("page",function(i){return t.handleNextPage(i,"possibleComplaints")}),e.qZA(),e.YNc(33,te,2,0,"ng-template",11,19,e.W1O),e._uU(35," > "),e.BQk(),e.qZA(),e.BQk(),e.qZA()),2&s){const o=e.MAs(19),i=e.MAs(34);e.xp6(9),e.Q6J("hideSearch",!0)("baseAPI",t.config.baseAPI)("modules",t.config.MODULES)("autoRefresh",t.config.autoRefresh)("rowActions",t.config.ROW_ACTIONS)("tableHeaders",t.config.TABLE_HEADERS)("isVPAdmin",t.config.isVPAdmin)("actionMode","menu"),e.xp6(8),e.Q6J("columns",t.config.ALL_TAWAFUQ_COMPLAINTS_COLS)("data",t.allTawafuqComplaints.content)("pageIndex",t.allTawafuqComplaints.number)("pageSize",t.allTawafuqComplaints.size)("length",t.allTawafuqComplaints.totalElements)("cellTemplate",e.VKq(33,J,o)),e.xp6(1),e.Q6J("ccGridCell",t.allTawafuqComplaints.content),e.xp6(7),e.Q6J("columns",t.config.COURT_DATA_GRID_COLS)("data",t.courtData.content)("pageIndex",t.courtData.number)("pageSize",t.courtData.size)("length",t.courtData.totalElements),e.xp6(3),e.Q6J("columns",t.config.ADD_COMPLAINT_RESULT_COLS)("data",t.addComplaintResultData.content)("pageIndex",t.addComplaintResultData.number)("pageSize",t.addComplaintResultData.size)("length",t.addComplaintResultData.totalElements)("rowStyleFormatter",t.rowStyleFormatter),e.xp6(4),e.Q6J("columns",t.config.POSSIBLE_COMPLAINTS_COLS)("data",t.possibleComplaints.content)("pageIndex",t.possibleComplaints.number)("pageSize",t.possibleComplaints.size)("length",t.possibleComplaints.totalElements)("cellTemplate",e.VKq(35,J,i)),e.xp6(1),e.Q6J("ccGridCell",t.possibleComplaints.content)}},directives:[S.uu,x.Q9,W.aM,I.Ge,I.VC,K],encapsulation:2,changeDetection:0}),T=(0,h.gn)([F.kG],T);var ae=n(35684),se=n(71884),$=n(11523);let _=class{constructor(s,t,o,i,r,m){this.formBuilder=s,this.dataService=t,this.apiService=o,this.notification=i,this.dialogRef=r,this.data=m,this.translateServiceOptions$=this.dataService.translationServiceOptions$,this.form=s.group({service:"",fees:""})}ngOnInit(){}addTranslationFees(){const{service:s,fees:t}=this.form.value;this.apiService.addTranslationFees(this.data.id,s,t).subscribe({next:o=>{this.notification.notifySuccess("Translation Fees Added Successfully"),this.dialogRef.close()}})}};_.\u0275fac=function(s){return new(s||_)(e.Y36(d.qu),e.Y36(y),e.Y36(g),e.Y36(R.zg),e.Y36(O.so),e.Y36(O.WI))},_.\u0275cmp=e.Xpm({type:_,selectors:[["ng-component"]],decls:17,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],[1,"px-4",3,"formGroup"],["label","Translation Service","formControlName","service",3,"data"],["label","Translation fees amount","formControlName","fees"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close","",1,"px-4"],["cc-raised-button","","color","accent",1,"px-4",3,"click"]],template:function(s,t){1&s&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Pay Translation Fees"),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4)(6,"form",5)(7,"h4"),e._uU(8),e.qZA(),e._UZ(9,"cc-select",6),e.ALo(10,"async"),e._UZ(11,"cc-amount-input",7),e.qZA()(),e.TgZ(12,"div",8)(13,"button",9),e._uU(14,"Cancel"),e.qZA(),e.TgZ(15,"button",10),e.NdJ("click",function(){return t.addTranslationFees()}),e._uU(16," Save "),e.qZA()()()),2&s&&(e.xp6(6),e.Q6J("formGroup",t.form),e.xp6(2),e.hij(" Please add the court fees amount for the case of (",t.data.maidName,") when the announcement is received "),e.xp6(1),e.Q6J("data",e.lcZ(10,3,t.translateServiceOptions$)))},directives:[l.iK,l.Cj,l.Zb,l.zn,l.fX,l.kL,d._Y,d.JL,d.sg,E.jB,d.JJ,d.u,$.Fi,l.Zu,S.uu],pipes:[p.Ov],encapsulation:2,changeDetection:0}),_=(0,h.gn)([F.kG],_);var N=n(42113),q=n(69202);function oe(a,s){1&a&&(e.TgZ(0,"h2"),e._uU(1,"Set Court date"),e.qZA())}function ie(a,s){1&a&&(e.TgZ(0,"h2"),e._uU(1,"Pay lawyer fees"),e.qZA())}function ne(a,s){1&a&&(e.TgZ(0,"h2"),e._uU(1,"Add Court result"),e.qZA())}function re(a,s){if(1&a&&(e.TgZ(0,"h2"),e._uU(1),e.qZA()),2&a){const t=e.oxw();e.xp6(1),e.hij("Pay to The Maid (",t.maid,")")}}function le(a,s){if(1&a&&(e.TgZ(0,"h2"),e._uU(1),e.qZA()),2&a){const t=e.oxw();e.xp6(1),e.hij(" The Maid (",t.maid,") will pay the company ")}}function ce(a,s){1&a&&(e.TgZ(0,"h2"),e._uU(1,"Pay court fees"),e.qZA())}function de(a,s){if(1&a&&(e.TgZ(0,"h2"),e._uU(1),e.qZA()),2&a){const t=e.oxw();e.xp6(1),e.hij(" Pay Court Verdict for (",t.maid,") ")}}function ue(a,s){if(1&a){const t=e.EpF();e.TgZ(0,"button",8),e.NdJ("click",function(){return e.CHM(t),e.oxw().$implicit.handler()}),e._uU(1),e.qZA()}if(2&a){const t=e.oxw().$implicit;e.xp6(1),e.hij(" ",t.label," ")}}function pe(a,s){if(1&a&&(e.ynx(0),e.YNc(1,ue,2,1,"button",7),e.BQk()),2&a){const t=s.$implicit;e.xp6(1),e.Q6J("ngIf",t.isVisible)}}function me(a,s){if(1&a&&e.YNc(0,pe,2,1,"ng-container",6),2&a){const t=e.oxw();e.Q6J("ngForOf",t.todoActions)}}var v=(()=>{return(a=v||(v={})).SET_COURT_DATE="SET_COURT_DATE",a.PAY_LAWYER_FEES="PAY_LAWYER_FEES",a.ADD_COURT_RESULT="ADD_COURT_RESULT",a.PAY_THE_MAID="PAY_THE_MAID",a.COLLECT_MONEY_FROM_MAID="COLLECT_MONEY_FROM_MAID",a.PAY_COURT_FEES="PAY_COURT_FEES",a.PAY_COURT_VERDICT="PAY_COURT_VERDICT",v;var a})();let C=class{constructor(s,t,o,i,r,m,M){this.router=s,this.route=t,this.dialog=o,this.cdRef=i,this.apiService=r,this.taskFormService=m,this.notification=M,this.BASE_URL=Y.N.apiBase,this.NAVIGATION_BASE=["visa","v2","tawafuq-work-order"],this.id="",this.type="",this.maid="",this.hide=!1,this.taskId$=new k.X(""),this.stepId$=new k.X(""),this.actions=[],this.todoActions=[]}ngOnInit(){this.initializeRouteParams(),this.setupTaskForm(),this.setupActions()}onTaskSaved(){setTimeout(()=>{this.taskFormService.refetchTaskForm()},500)}onAllTasksDone(){this.navigateToMainPage()}onCancel(){this.navigateToMainPage()}initializeRouteParams(){var s,t,o;this.id=null!==(s=this.getRouteParam("id"))&&void 0!==s?s:"",this.type=null!==(t=this.getQueryParam("type"))&&void 0!==t?t:"",this.maid=null!==(o=this.getQueryParam("maid"))&&void 0!==o?o:"",this.hide=Object.values(v).includes(this.type),this.taskId$.next(this.id),this.stepId$.next(this.type),this.actions=this.isPaymentRelatedType()?["Cancel"]:["Cancel","Done"],this.cdRef.detectChanges()}setupTaskForm(){this.taskFormService.taskForm.pipe((0,ae.T)(1)).subscribe(s=>{var t;this.taskForm=s,null===(t=s.get("payMaid"))||void 0===t||t.valueChanges.pipe((0,se.x)()).subscribe(o=>{var i;this.taskFormService.updateSavePermission(!1),this.taskFormService.emitSaveRequest({saveOnly:!0,skipValidation:!0});let r={};"no"===o&&(r={payMaid:"no",agreedAmount:this.taskForm.value.agreedAmount,id:this.id}),"yes"===o&&(r={payMaid:"yes",courtDate:this.taskForm.value.courtDate,courtNumber1:this.taskForm.value.courtNumber1,courtNumber2:this.taskForm.value.courtNumber2,hireLawyer:this.taskForm.value.hireLawyer,attachments:null!==(i=this.taskForm.value.attachments)&&void 0!==i?i:[],id:this.id}),this.taskFormService.updateSavePayloadData(r),this.taskFormService.updateSavePermission(!0)})})}setupActions(){this.todoActions=[{label:"Pay translation fees",isVisible:this.type===v.PAY_COURT_FEES,handler:()=>this.openTranslationFeesDialog()},{label:"Approved",isVisible:this.isPaymentRelatedType(),handler:()=>this.handleApproval()},{label:"Re-complaint",isVisible:this.isPaymentRelatedType(),handler:()=>this.handleReComplaint()}]}openTranslationFeesDialog(){this.dialog.originalOpen(_,{panelClass:"col-md-8",data:{id:this.id,maidName:this.maid}})}handleApproval(){const{notes:s}=this.taskForm.value;this.apiService.approvePayment(this.id,s,this.type).subscribe({next:()=>{this.notification.notifySuccess("Approved Successfully"),this.navigateToMainPage()}})}handleReComplaint(){const{notes:s}=this.taskForm.value;this.apiService.reopenComplaint(this.id,s,this.type).subscribe({next:()=>{this.notification.notifySuccess("Re-Complained Successfully"),this.navigateToMainPage()}})}isPaymentRelatedType(){return this.type===v.PAY_THE_MAID||this.type===v.COLLECT_MONEY_FROM_MAID}getRouteParam(s){return this.route.snapshot.paramMap.get(s)}getQueryParam(s){return this.route.snapshot.queryParamMap.get(s)}navigateToMainPage(){this.router.navigateByUrl(this.NAVIGATION_BASE.join("/"))}};C.\u0275fac=function(s){return new(s||C)(e.Y36(w.F0),e.Y36(w.gz),e.Y36(l.uY),e.Y36(e.sBO),e.Y36(g),e.Y36(N.XI),e.Y36(R.zg))},C.\u0275cmp=e.Xpm({type:C,selectors:[["app-todo-details"]],decls:17,vars:18,consts:[[1,"p-4"],[1,"py-2"],[4,"ngIf"],[1,"mt-5"],[3,"baseUrl","entityName","moduleName","stepID","taskID","actions","actionsTemplateRef","taskSaved","allTasksDone","cancel"],["actionsTmp",""],[4,"ngFor","ngForOf"],["cc-raised-button","","class","px-4",3,"click",4,"ngIf"],["cc-raised-button","",1,"px-4",3,"click"]],template:function(s,t){if(1&s&&(e.TgZ(0,"div",0)(1,"cc-card")(2,"cc-card-content")(3,"div",1),e.YNc(4,oe,2,0,"h2",2),e.YNc(5,ie,2,0,"h2",2),e.YNc(6,ne,2,0,"h2",2),e.YNc(7,re,2,1,"h2",2),e.YNc(8,le,2,1,"h2",2),e.YNc(9,ce,2,0,"h2",2),e.YNc(10,de,2,1,"h2",2),e.qZA(),e.TgZ(11,"div",3)(12,"cc-workflow-task-form",4),e.NdJ("taskSaved",function(){return t.onTaskSaved()})("allTasksDone",function(){return t.onAllTasksDone()})("cancel",function(){return t.onCancel()}),e.ALo(13,"async"),e.ALo(14,"async"),e.qZA(),e.YNc(15,me,1,1,"ng-template",null,5,e.W1O),e.qZA()()()()),2&s){const o=e.MAs(16);e.xp6(4),e.Q6J("ngIf","SET_COURT_DATE"==t.type),e.xp6(1),e.Q6J("ngIf","PAY_LAWYER_FEES"==t.type),e.xp6(1),e.Q6J("ngIf","ADD_COURT_RESULT"==t.type),e.xp6(1),e.Q6J("ngIf","PAY_THE_MAID"==t.type),e.xp6(1),e.Q6J("ngIf","COLLECT_MONEY_FROM_MAID"==t.type),e.xp6(1),e.Q6J("ngIf","PAY_COURT_FEES"==t.type),e.xp6(1),e.Q6J("ngIf","PAY_COURT_VERDICT"==t.type),e.xp6(2),e.Q6J("baseUrl",t.BASE_URL)("entityName","tawafuqManager")("moduleName","visa")("stepID",e.lcZ(13,14,t.stepId$))("taskID",e.lcZ(14,16,t.taskId$))("actions",t.actions)("actionsTemplateRef",o)}},directives:[q.Dt,q.uw,p.O5,N.y9,p.sg,S.uu],pipes:[p.Ov],encapsulation:2,changeDetection:0}),C=(0,h.gn)([F.kG],C);var he=n(8188);const fe=[{path:"",component:T},{path:":id",component:C,data:{label:"Tawafuq Manager Todo Details"}}];let ge=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({providers:[g,A,y,z],imports:[[p.ez,d.u5,d.UX,w.Bz.forChild(fe),I.Gz,b.yU,l.I8,W.ZN,S.S6,he.aS,E.lK,x.L,N.SP,$.SZ,q.Ev]]}),a})()},46700:(L,D,n)=>{var p={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function h(u){var f=e(u);return n(f)}function e(u){if(!n.o(p,u)){var f=new Error("Cannot find module '"+u+"'");throw f.code="MODULE_NOT_FOUND",f}return p[u]}h.keys=function(){return Object.keys(p)},h.resolve=e,L.exports=h,h.id=46700}}]);
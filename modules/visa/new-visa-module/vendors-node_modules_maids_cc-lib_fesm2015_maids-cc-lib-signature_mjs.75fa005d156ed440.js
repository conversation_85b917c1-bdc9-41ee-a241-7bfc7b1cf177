(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["vendors-node_modules_maids_cc-lib_fesm2015_maids-cc-lib-signature_mjs"],{58259:function(r,l){var o;!function(i,s){"use strict";"object"==typeof r.exports?r.exports=i.document?s(i,!0):function(u){if(!u.document)throw new Error("jQuery requires a window with a document");return s(u)}:s(i)}("undefined"!=typeof window?window:this,function(i,s){"use strict";var u=[],a=Object.getPrototypeOf,p=u.slice,v=u.flat?function(e){return u.flat.call(e)}:function(e){return u.concat.apply([],e)},y=u.push,g=u.indexOf,D={},b=D.toString,x=D.hasOwnProperty,w=x.toString,L=w.call(Object),R={},S=function(t){return"function"==typeof t&&"number"!=typeof t.nodeType&&"function"!=typeof t.item},F=function(t){return null!=t&&t===t.window},E=i.document,U={type:!0,src:!0,nonce:!0,noModule:!0};function P(e,t,c){var h,_,m=(c=c||E).createElement("script");if(m.text=e,t)for(h in U)(_=t[h]||t.getAttribute&&t.getAttribute(h))&&m.setAttribute(h,_);c.head.appendChild(m).parentNode.removeChild(m)}function V(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?D[b.call(e)]||"object":typeof e}var X="3.7.1",ce=/HTML$/i,d=function(e,t){return new d.fn.init(e,t)};function he(e){var t=!!e&&"length"in e&&e.length,c=V(e);return!S(e)&&!F(e)&&("array"===c||0===t||"number"==typeof t&&t>0&&t-1 in e)}function ae(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}d.fn=d.prototype={jquery:X,constructor:d,length:0,toArray:function(){return p.call(this)},get:function(e){return null==e?p.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=d.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return d.each(this,e)},map:function(e){return this.pushStack(d.map(this,function(t,c){return e.call(t,c,t)}))},slice:function(){return this.pushStack(p.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(d.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(d.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,c=+e+(e<0?t:0);return this.pushStack(c>=0&&c<t?[this[c]]:[])},end:function(){return this.prevObject||this.constructor()},push:y,sort:u.sort,splice:u.splice},d.extend=d.fn.extend=function(){var e,t,c,h,_,m,M=arguments[0]||{},W=1,N=arguments.length,K=!1;for("boolean"==typeof M&&(K=M,M=arguments[W]||{},W++),"object"!=typeof M&&!S(M)&&(M={}),W===N&&(M=this,W--);W<N;W++)if(null!=(e=arguments[W]))for(t in e)h=e[t],"__proto__"!==t&&M!==h&&(K&&h&&(d.isPlainObject(h)||(_=Array.isArray(h)))?(c=M[t],m=_&&!Array.isArray(c)?[]:_||d.isPlainObject(c)?c:{},_=!1,M[t]=d.extend(K,m,h)):void 0!==h&&(M[t]=h));return M},d.extend({expando:"jQuery"+(X+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,c;return!(!e||"[object Object]"!==b.call(e)||(t=a(e))&&("function"!=typeof(c=x.call(t,"constructor")&&t.constructor)||w.call(c)!==L))},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},globalEval:function(e,t,c){P(e,{nonce:t&&t.nonce},c)},each:function(e,t){var c,h=0;if(he(e))for(c=e.length;h<c&&!1!==t.call(e[h],h,e[h]);h++);else for(h in e)if(!1===t.call(e[h],h,e[h]))break;return e},text:function(e){var t,c="",h=0,_=e.nodeType;if(!_)for(;t=e[h++];)c+=d.text(t);return 1===_||11===_?e.textContent:9===_?e.documentElement.textContent:3===_||4===_?e.nodeValue:c},makeArray:function(e,t){var c=t||[];return null!=e&&(he(Object(e))?d.merge(c,"string"==typeof e?[e]:e):y.call(c,e)),c},inArray:function(e,t,c){return null==t?-1:g.call(t,e,c)},isXMLDoc:function(e){var c=e&&(e.ownerDocument||e).documentElement;return!ce.test(e&&e.namespaceURI||c&&c.nodeName||"HTML")},merge:function(e,t){for(var c=+t.length,h=0,_=e.length;h<c;h++)e[_++]=t[h];return e.length=_,e},grep:function(e,t,c){for(var _=[],m=0,M=e.length,W=!c;m<M;m++)!t(e[m],m)!==W&&_.push(e[m]);return _},map:function(e,t,c){var h,_,m=0,M=[];if(he(e))for(h=e.length;m<h;m++)null!=(_=t(e[m],m,c))&&M.push(_);else for(m in e)null!=(_=t(e[m],m,c))&&M.push(_);return v(M)},guid:1,support:R}),"function"==typeof Symbol&&(d.fn[Symbol.iterator]=u[Symbol.iterator]),d.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){D["[object "+t+"]"]=t.toLowerCase()});var I=u.pop,Q=u.sort,ue=u.splice,ee="[\\x20\\t\\r\\n\\f]",Le=new RegExp("^"+ee+"+|((?:^|[^\\\\])(?:\\\\.)*)"+ee+"+$","g");d.contains=function(e,t){var c=t&&t.parentNode;return e===c||!(!c||1!==c.nodeType||!(e.contains?e.contains(c):e.compareDocumentPosition&&16&e.compareDocumentPosition(c)))};var xe=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\x80-\uFFFF\w-]/g;function Ae(e,t){return t?"\0"===e?"\ufffd":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e}d.escapeSelector=function(e){return(e+"").replace(xe,Ae)};var Pe=E,Ke=y;!function(){var e,t,c,h,_,M,W,N,K,J,m=Ke,re=d.expando,Y=0,se=0,Ce=cn(),Re=cn(),Oe=cn(),nt=cn(),Je=function($,H){return $===H&&(_=!0),0},bt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",vt="(?:\\\\[\\da-fA-F]{1,6}"+ee+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",ke="\\["+ee+"*("+vt+")(?:"+ee+"*([*^$|!~]?=)"+ee+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+vt+"))|)"+ee+"*\\]",Bt=":("+vt+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+ke+")*)|.*)\\)|)",Be=new RegExp(ee+"+","g"),Ge=new RegExp("^"+ee+"*,"+ee+"*"),Zt=new RegExp("^"+ee+"*([>+~]|"+ee+")"+ee+"*"),In=new RegExp(ee+"|>"),yt=new RegExp(Bt),Qt=new RegExp("^"+vt+"$"),xt={ID:new RegExp("^#("+vt+")"),CLASS:new RegExp("^\\.("+vt+")"),TAG:new RegExp("^("+vt+"|[*])"),ATTR:new RegExp("^"+ke),PSEUDO:new RegExp("^"+Bt),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+ee+"*(even|odd|(([+-]|)(\\d*)n|)"+ee+"*(?:([+-]|)"+ee+"*(\\d+)|))"+ee+"*\\)|)","i"),bool:new RegExp("^(?:"+bt+")$","i"),needsContext:new RegExp("^"+ee+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+ee+"*((?:-\\d)?\\d*)"+ee+"*\\)|)(?=[^-]|$)","i")},At=/^(?:input|select|textarea|button)$/i,$t=/^h\d$/i,ft=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,kn=/[+~]/,Et=new RegExp("\\\\[\\da-fA-F]{1,6}"+ee+"?|\\\\([^\\r\\n\\f])","g"),Ct=function($,H){var z="0x"+$.slice(1)-65536;return H||(z<0?String.fromCharCode(z+65536):String.fromCharCode(z>>10|55296,1023&z|56320))},Fr=function(){It()},Hr=fn(function($){return!0===$.disabled&&ae($,"fieldset")},{dir:"parentNode",next:"legend"});try{m.apply(u=p.call(Pe.childNodes),Pe.childNodes)}catch($){m={apply:function(H,z){Ke.apply(H,p.call(z))},call:function(H){Ke.apply(H,p.call(arguments,1))}}}function We($,H,z,Z){var ne,le,_e,ve,me,$e,Me,De=H&&H.ownerDocument,Ie=H?H.nodeType:9;if(z=z||[],"string"!=typeof $||!$||1!==Ie&&9!==Ie&&11!==Ie)return z;if(!Z&&(It(H),H=H||M,N)){if(11!==Ie&&(me=ft.exec($)))if(ne=me[1]){if(9===Ie){if(!(_e=H.getElementById(ne)))return z;if(_e.id===ne)return m.call(z,_e),z}else if(De&&(_e=De.getElementById(ne))&&We.contains(H,_e)&&_e.id===ne)return m.call(z,_e),z}else{if(me[2])return m.apply(z,H.getElementsByTagName($)),z;if((ne=me[3])&&H.getElementsByClassName)return m.apply(z,H.getElementsByClassName(ne)),z}if(!(nt[$+" "]||K&&K.test($))){if(Me=$,De=H,1===Ie&&(In.test($)||Zt.test($))){for(((De=kn.test($)&&Ln(H.parentNode)||H)!=H||!R.scope)&&((ve=H.getAttribute("id"))?ve=d.escapeSelector(ve):H.setAttribute("id",ve=re)),le=($e=Jt($)).length;le--;)$e[le]=(ve?"#"+ve:":scope")+" "+ln($e[le]);Me=$e.join(",")}try{return m.apply(z,De.querySelectorAll(Me)),z}catch(Te){nt($,!0)}finally{ve===re&&H.removeAttribute("id")}}}return fr($.replace(Le,"$1"),H,z,Z)}function cn(){var $=[];return function H(z,Z){return $.push(z+" ")>t.cacheLength&&delete H[$.shift()],H[z+" "]=Z}}function mt($){return $[re]=!0,$}function Ht($){var H=M.createElement("fieldset");try{return!!$(H)}catch(z){return!1}finally{H.parentNode&&H.parentNode.removeChild(H),H=null}}function Kr($){return function(H){return ae(H,"input")&&H.type===$}}function Vr($){return function(H){return(ae(H,"input")||ae(H,"button"))&&H.type===$}}function cr($){return function(H){return"form"in H?H.parentNode&&!1===H.disabled?"label"in H?"label"in H.parentNode?H.parentNode.disabled===$:H.disabled===$:H.isDisabled===$||H.isDisabled!==!$&&Hr(H)===$:H.disabled===$:"label"in H&&H.disabled===$}}function jt($){return mt(function(H){return H=+H,mt(function(z,Z){for(var ne,le=$([],z.length,H),_e=le.length;_e--;)z[ne=le[_e]]&&(z[ne]=!(Z[ne]=z[ne]))})})}function Ln($){return $&&void 0!==$.getElementsByTagName&&$}function It($){var H,z=$?$.ownerDocument||$:Pe;return z==M||9!==z.nodeType||!z.documentElement||(W=(M=z).documentElement,N=!d.isXMLDoc(M),J=W.matches||W.webkitMatchesSelector||W.msMatchesSelector,W.msMatchesSelector&&Pe!=M&&(H=M.defaultView)&&H.top!==H&&H.addEventListener("unload",Fr),R.getById=Ht(function(Z){return W.appendChild(Z).id=d.expando,!M.getElementsByName||!M.getElementsByName(d.expando).length}),R.disconnectedMatch=Ht(function(Z){return J.call(Z,"*")}),R.scope=Ht(function(){return M.querySelectorAll(":scope")}),R.cssHas=Ht(function(){try{return M.querySelector(":has(*,:jqfake)"),!1}catch(Z){return!0}}),R.getById?(t.filter.ID=function(Z){var ne=Z.replace(Et,Ct);return function(le){return le.getAttribute("id")===ne}},t.find.ID=function(Z,ne){if(void 0!==ne.getElementById&&N){var le=ne.getElementById(Z);return le?[le]:[]}}):(t.filter.ID=function(Z){var ne=Z.replace(Et,Ct);return function(le){var _e=void 0!==le.getAttributeNode&&le.getAttributeNode("id");return _e&&_e.value===ne}},t.find.ID=function(Z,ne){if(void 0!==ne.getElementById&&N){var le,_e,ve,me=ne.getElementById(Z);if(me){if((le=me.getAttributeNode("id"))&&le.value===Z)return[me];for(ve=ne.getElementsByName(Z),_e=0;me=ve[_e++];)if((le=me.getAttributeNode("id"))&&le.value===Z)return[me]}return[]}}),t.find.TAG=function(Z,ne){return void 0!==ne.getElementsByTagName?ne.getElementsByTagName(Z):ne.querySelectorAll(Z)},t.find.CLASS=function(Z,ne){if(void 0!==ne.getElementsByClassName&&N)return ne.getElementsByClassName(Z)},K=[],Ht(function(Z){var ne;W.appendChild(Z).innerHTML="<a id='"+re+"' href='' disabled='disabled'></a><select id='"+re+"-\r\\' disabled='disabled'><option selected=''></option></select>",Z.querySelectorAll("[selected]").length||K.push("\\["+ee+"*(?:value|"+bt+")"),Z.querySelectorAll("[id~="+re+"-]").length||K.push("~="),Z.querySelectorAll("a#"+re+"+*").length||K.push(".#.+[+~]"),Z.querySelectorAll(":checked").length||K.push(":checked"),(ne=M.createElement("input")).setAttribute("type","hidden"),Z.appendChild(ne).setAttribute("name","D"),W.appendChild(Z).disabled=!0,2!==Z.querySelectorAll(":disabled").length&&K.push(":enabled",":disabled"),(ne=M.createElement("input")).setAttribute("name",""),Z.appendChild(ne),Z.querySelectorAll("[name='']").length||K.push("\\["+ee+"*name"+ee+"*="+ee+"*(?:''|\"\")")}),R.cssHas||K.push(":has"),K=K.length&&new RegExp(K.join("|")),Je=function(Z,ne){if(Z===ne)return _=!0,0;var le=!Z.compareDocumentPosition-!ne.compareDocumentPosition;return le||(1&(le=(Z.ownerDocument||Z)==(ne.ownerDocument||ne)?Z.compareDocumentPosition(ne):1)||!R.sortDetached&&ne.compareDocumentPosition(Z)===le?Z===M||Z.ownerDocument==Pe&&We.contains(Pe,Z)?-1:ne===M||ne.ownerDocument==Pe&&We.contains(Pe,ne)?1:h?g.call(h,Z)-g.call(h,ne):0:4&le?-1:1)}),M}for(e in We.matches=function($,H){return We($,null,null,H)},We.matchesSelector=function($,H){if(It($),N&&!nt[H+" "]&&(!K||!K.test(H)))try{var z=J.call($,H);if(z||R.disconnectedMatch||$.document&&11!==$.document.nodeType)return z}catch(Z){nt(H,!0)}return We(H,M,null,[$]).length>0},We.contains=function($,H){return($.ownerDocument||$)!=M&&It($),d.contains($,H)},We.attr=function($,H){($.ownerDocument||$)!=M&&It($);var z=t.attrHandle[H.toLowerCase()],Z=z&&x.call(t.attrHandle,H.toLowerCase())?z($,H,!N):void 0;return void 0!==Z?Z:$.getAttribute(H)},We.error=function($){throw new Error("Syntax error, unrecognized expression: "+$)},d.uniqueSort=function($){var H,z=[],Z=0,ne=0;if(_=!R.sortStable,h=!R.sortStable&&p.call($,0),Q.call($,Je),_){for(;H=$[ne++];)H===$[ne]&&(Z=z.push(ne));for(;Z--;)ue.call($,z[Z],1)}return h=null,$},d.fn.uniqueSort=function(){return this.pushStack(d.uniqueSort(p.apply(this)))},(t=d.expr={cacheLength:50,createPseudo:mt,match:xt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function($){return $[1]=$[1].replace(Et,Ct),$[3]=($[3]||$[4]||$[5]||"").replace(Et,Ct),"~="===$[2]&&($[3]=" "+$[3]+" "),$.slice(0,4)},CHILD:function($){return $[1]=$[1].toLowerCase(),"nth"===$[1].slice(0,3)?($[3]||We.error($[0]),$[4]=+($[4]?$[5]+($[6]||1):2*("even"===$[3]||"odd"===$[3])),$[5]=+($[7]+$[8]||"odd"===$[3])):$[3]&&We.error($[0]),$},PSEUDO:function($){var H,z=!$[6]&&$[2];return xt.CHILD.test($[0])?null:($[3]?$[2]=$[4]||$[5]||"":z&&yt.test(z)&&(H=Jt(z,!0))&&(H=z.indexOf(")",z.length-H)-z.length)&&($[0]=$[0].slice(0,H),$[2]=z.slice(0,H)),$.slice(0,3))}},filter:{TAG:function($){var H=$.replace(Et,Ct).toLowerCase();return"*"===$?function(){return!0}:function(z){return ae(z,H)}},CLASS:function($){var H=Ce[$+" "];return H||(H=new RegExp("(^|"+ee+")"+$+"("+ee+"|$)"))&&Ce($,function(z){return H.test("string"==typeof z.className&&z.className||void 0!==z.getAttribute&&z.getAttribute("class")||"")})},ATTR:function($,H,z){return function(Z){var ne=We.attr(Z,$);return null==ne?"!="===H:!H||(ne+="","="===H?ne===z:"!="===H?ne!==z:"^="===H?z&&0===ne.indexOf(z):"*="===H?z&&ne.indexOf(z)>-1:"$="===H?z&&ne.slice(-z.length)===z:"~="===H?(" "+ne.replace(Be," ")+" ").indexOf(z)>-1:"|="===H&&(ne===z||ne.slice(0,z.length+1)===z+"-"))}},CHILD:function($,H,z,Z,ne){var le="nth"!==$.slice(0,3),_e="last"!==$.slice(-4),ve="of-type"===H;return 1===Z&&0===ne?function(me){return!!me.parentNode}:function(me,$e,Me){var De,Ie,Te,Ve,st,it=le!==_e?"nextSibling":"previousSibling",dt=me.parentNode,Tt=ve&&me.nodeName.toLowerCase(),qt=!Me&&!ve,at=!1;if(dt){if(le){for(;it;){for(Te=me;Te=Te[it];)if(ve?ae(Te,Tt):1===Te.nodeType)return!1;st=it="only"===$&&!st&&"nextSibling"}return!0}if(st=[_e?dt.firstChild:dt.lastChild],_e&&qt){for(at=(Ve=(De=(Ie=dt[re]||(dt[re]={}))[$]||[])[0]===Y&&De[1])&&De[2],Te=Ve&&dt.childNodes[Ve];Te=++Ve&&Te&&Te[it]||(at=Ve=0)||st.pop();)if(1===Te.nodeType&&++at&&Te===me){Ie[$]=[Y,Ve,at];break}}else if(qt&&(at=Ve=(De=(Ie=me[re]||(me[re]={}))[$]||[])[0]===Y&&De[1]),!1===at)for(;(Te=++Ve&&Te&&Te[it]||(at=Ve=0)||st.pop())&&(!(ve?ae(Te,Tt):1===Te.nodeType)||!++at||(qt&&((Ie=Te[re]||(Te[re]={}))[$]=[Y,at]),Te!==me)););return(at-=ne)===Z||at%Z==0&&at/Z>=0}}},PSEUDO:function($,H){var z,Z=t.pseudos[$]||t.setFilters[$.toLowerCase()]||We.error("unsupported pseudo: "+$);return Z[re]?Z(H):Z.length>1?(z=[$,$,"",H],t.setFilters.hasOwnProperty($.toLowerCase())?mt(function(ne,le){for(var _e,ve=Z(ne,H),me=ve.length;me--;)ne[_e=g.call(ne,ve[me])]=!(le[_e]=ve[me])}):function(ne){return Z(ne,0,z)}):Z}},pseudos:{not:mt(function($){var H=[],z=[],Z=jn($.replace(Le,"$1"));return Z[re]?mt(function(ne,le,_e,ve){for(var me,$e=Z(ne,null,ve,[]),Me=ne.length;Me--;)(me=$e[Me])&&(ne[Me]=!(le[Me]=me))}):function(ne,le,_e){return H[0]=ne,Z(H,null,_e,z),H[0]=null,!z.pop()}}),has:mt(function($){return function(H){return We($,H).length>0}}),contains:mt(function($){return $=$.replace(Et,Ct),function(H){return(H.textContent||d.text(H)).indexOf($)>-1}}),lang:mt(function($){return Qt.test($||"")||We.error("unsupported lang: "+$),$=$.replace(Et,Ct).toLowerCase(),function(H){var z;do{if(z=N?H.lang:H.getAttribute("xml:lang")||H.getAttribute("lang"))return(z=z.toLowerCase())===$||0===z.indexOf($+"-")}while((H=H.parentNode)&&1===H.nodeType);return!1}}),target:function($){var H=i.location&&i.location.hash;return H&&H.slice(1)===$.id},root:function($){return $===W},focus:function($){return $===function qr(){try{return M.activeElement}catch($){}}()&&M.hasFocus()&&!!($.type||$.href||~$.tabIndex)},enabled:cr(!1),disabled:cr(!0),checked:function($){return ae($,"input")&&!!$.checked||ae($,"option")&&!!$.selected},selected:function($){return!0===$.selected},empty:function($){for($=$.firstChild;$;$=$.nextSibling)if($.nodeType<6)return!1;return!0},parent:function($){return!t.pseudos.empty($)},header:function($){return $t.test($.nodeName)},input:function($){return At.test($.nodeName)},button:function($){return ae($,"input")&&"button"===$.type||ae($,"button")},text:function($){var H;return ae($,"input")&&"text"===$.type&&(null==(H=$.getAttribute("type"))||"text"===H.toLowerCase())},first:jt(function(){return[0]}),last:jt(function($,H){return[H-1]}),eq:jt(function($,H,z){return[z<0?z+H:z]}),even:jt(function($,H){for(var z=0;z<H;z+=2)$.push(z);return $}),odd:jt(function($,H){for(var z=1;z<H;z+=2)$.push(z);return $}),lt:jt(function($,H,z){var Z;for(Z=z<0?z+H:z>H?H:z;--Z>=0;)$.push(Z);return $}),gt:jt(function($,H,z){for(var Z=z<0?z+H:z;++Z<H;)$.push(Z);return $})}}).pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[e]=Kr(e);for(e in{submit:!0,reset:!0})t.pseudos[e]=Vr(e);function lr(){}function Jt($,H){var z,Z,ne,le,_e,ve,me,$e=Re[$+" "];if($e)return H?0:$e.slice(0);for(_e=$,ve=[],me=t.preFilter;_e;){for(le in(!z||(Z=Ge.exec(_e)))&&(Z&&(_e=_e.slice(Z[0].length)||_e),ve.push(ne=[])),z=!1,(Z=Zt.exec(_e))&&(z=Z.shift(),ne.push({value:z,type:Z[0].replace(Le," ")}),_e=_e.slice(z.length)),t.filter)(Z=xt[le].exec(_e))&&(!me[le]||(Z=me[le](Z)))&&(z=Z.shift(),ne.push({value:z,type:le,matches:Z}),_e=_e.slice(z.length));if(!z)break}return H?_e.length:_e?We.error($):Re($,ve).slice(0)}function ln($){for(var H=0,z=$.length,Z="";H<z;H++)Z+=$[H].value;return Z}function fn($,H,z){var Z=H.dir,ne=H.next,le=ne||Z,_e=z&&"parentNode"===le,ve=se++;return H.first?function(me,$e,Me){for(;me=me[Z];)if(1===me.nodeType||_e)return $(me,$e,Me);return!1}:function(me,$e,Me){var De,Ie,Te=[Y,ve];if(Me){for(;me=me[Z];)if((1===me.nodeType||_e)&&$(me,$e,Me))return!0}else for(;me=me[Z];)if(1===me.nodeType||_e)if(Ie=me[re]||(me[re]={}),ne&&ae(me,ne))me=me[Z]||me;else{if((De=Ie[le])&&De[0]===Y&&De[1]===ve)return Te[2]=De[2];if(Ie[le]=Te,Te[2]=$(me,$e,Me))return!0}return!1}}function Rn($){return $.length>1?function(H,z,Z){for(var ne=$.length;ne--;)if(!$[ne](H,z,Z))return!1;return!0}:$[0]}function dn($,H,z,Z,ne){for(var le,_e=[],ve=0,me=$.length,$e=null!=H;ve<me;ve++)(le=$[ve])&&(!z||z(le,Z,ne))&&(_e.push(le),$e&&H.push(ve));return _e}function Sn($,H,z,Z,ne,le){return Z&&!Z[re]&&(Z=Sn(Z)),ne&&!ne[re]&&(ne=Sn(ne,le)),mt(function(_e,ve,me,$e){var Me,De,Ie,Te,Ve=[],st=[],it=ve.length,dt=_e||function zr($,H,z){for(var Z=0,ne=H.length;Z<ne;Z++)We($,H[Z],z);return z}(H||"*",me.nodeType?[me]:me,[]),Tt=!$||!_e&&H?dt:dn(dt,Ve,$,me,$e);if(z?z(Tt,Te=ne||(_e?$:it||Z)?[]:ve,me,$e):Te=Tt,Z)for(Me=dn(Te,st),Z(Me,[],me,$e),De=Me.length;De--;)(Ie=Me[De])&&(Te[st[De]]=!(Tt[st[De]]=Ie));if(_e){if(ne||$){if(ne){for(Me=[],De=Te.length;De--;)(Ie=Te[De])&&Me.push(Tt[De]=Ie);ne(null,Te=[],Me,$e)}for(De=Te.length;De--;)(Ie=Te[De])&&(Me=ne?g.call(_e,Ie):Ve[De])>-1&&(_e[Me]=!(ve[Me]=Ie))}}else Te=dn(Te===ve?Te.splice(it,Te.length):Te),ne?ne(null,ve,Te,$e):m.apply(ve,Te)})}function Bn($){for(var H,z,Z,ne=$.length,le=t.relative[$[0].type],_e=le||t.relative[" "],ve=le?1:0,me=fn(function(De){return De===H},_e,!0),$e=fn(function(De){return g.call(H,De)>-1},_e,!0),Me=[function(De,Ie,Te){var Ve=!le&&(Te||Ie!=c)||((H=Ie).nodeType?me(De,Ie,Te):$e(De,Ie,Te));return H=null,Ve}];ve<ne;ve++)if(z=t.relative[$[ve].type])Me=[fn(Rn(Me),z)];else{if((z=t.filter[$[ve].type].apply(null,$[ve].matches))[re]){for(Z=++ve;Z<ne&&!t.relative[$[Z].type];Z++);return Sn(ve>1&&Rn(Me),ve>1&&ln($.slice(0,ve-1).concat({value:" "===$[ve-2].type?"*":""})).replace(Le,"$1"),z,ve<Z&&Bn($.slice(ve,Z)),Z<ne&&Bn($=$.slice(Z)),Z<ne&&ln($))}Me.push(z)}return Rn(Me)}function jn($,H){var z,Z=[],ne=[],le=Oe[$+" "];if(!le){for(H||(H=Jt($)),z=H.length;z--;)(le=Bn(H[z]))[re]?Z.push(le):ne.push(le);le=Oe($,function Yr($,H){var z=H.length>0,Z=$.length>0,ne=function(le,_e,ve,me,$e){var Me,De,Ie,Te=0,Ve="0",st=le&&[],it=[],dt=c,Tt=le||Z&&t.find.TAG("*",$e),qt=Y+=null==dt?1:Math.random()||.1,at=Tt.length;for($e&&(c=_e==M||_e||$e);Ve!==at&&null!=(Me=Tt[Ve]);Ve++){if(Z&&Me){for(De=0,!_e&&Me.ownerDocument!=M&&(It(Me),ve=!N);Ie=$[De++];)if(Ie(Me,_e||M,ve)){m.call(me,Me);break}$e&&(Y=qt)}z&&((Me=!Ie&&Me)&&Te--,le&&st.push(Me))}if(Te+=Ve,z&&Ve!==Te){for(De=0;Ie=H[De++];)Ie(st,it,_e,ve);if(le){if(Te>0)for(;Ve--;)st[Ve]||it[Ve]||(it[Ve]=I.call(me));it=dn(it)}m.apply(me,it),$e&&!le&&it.length>0&&Te+H.length>1&&d.uniqueSort(me)}return $e&&(Y=qt,c=dt),st};return z?mt(ne):ne}(ne,Z)),le.selector=$}return le}function fr($,H,z,Z){var ne,le,_e,ve,me,$e="function"==typeof $&&$,Me=!Z&&Jt($=$e.selector||$);if(z=z||[],1===Me.length){if((le=Me[0]=Me[0].slice(0)).length>2&&"ID"===(_e=le[0]).type&&9===H.nodeType&&N&&t.relative[le[1].type]){if(!(H=(t.find.ID(_e.matches[0].replace(Et,Ct),H)||[])[0]))return z;$e&&(H=H.parentNode),$=$.slice(le.shift().value.length)}for(ne=xt.needsContext.test($)?0:le.length;ne--&&!t.relative[ve=(_e=le[ne]).type];)if((me=t.find[ve])&&(Z=me(_e.matches[0].replace(Et,Ct),kn.test(le[0].type)&&Ln(H.parentNode)||H))){if(le.splice(ne,1),!($=Z.length&&ln(le)))return m.apply(z,Z),z;break}}return($e||jn($,Me))(Z,H,!N,z,!H||kn.test($)&&Ln(H.parentNode)||H),z}lr.prototype=t.filters=t.pseudos,t.setFilters=new lr,R.sortStable=re.split("").sort(Je).join("")===re,It(),R.sortDetached=Ht(function($){return 1&$.compareDocumentPosition(M.createElement("fieldset"))}),d.find=We,d.expr[":"]=d.expr.pseudos,d.unique=d.uniqueSort,We.compile=jn,We.select=fr,We.setDocument=It,We.tokenize=Jt,We.escape=d.escapeSelector,We.getText=d.text,We.isXML=d.isXMLDoc,We.selectors=d.expr,We.support=d.support,We.uniqueSort=d.uniqueSort}();var te=function(e,t,c){for(var h=[],_=void 0!==c;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(_&&d(e).is(c))break;h.push(e)}return h},fe=function(e,t){for(var c=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&c.push(e);return c},ge=d.expr.match.needsContext,be=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function Se(e,t,c){return S(t)?d.grep(e,function(h,_){return!!t.call(h,_,h)!==c}):t.nodeType?d.grep(e,function(h){return h===t!==c}):"string"!=typeof t?d.grep(e,function(h){return g.call(t,h)>-1!==c}):d.filter(t,e,c)}d.filter=function(e,t,c){var h=t[0];return c&&(e=":not("+e+")"),1===t.length&&1===h.nodeType?d.find.matchesSelector(h,e)?[h]:[]:d.find.matches(e,d.grep(t,function(_){return 1===_.nodeType}))},d.fn.extend({find:function(e){var t,c,h=this.length,_=this;if("string"!=typeof e)return this.pushStack(d(e).filter(function(){for(t=0;t<h;t++)if(d.contains(_[t],this))return!0}));for(c=this.pushStack([]),t=0;t<h;t++)d.find(e,_[t],c);return h>1?d.uniqueSort(c):c},filter:function(e){return this.pushStack(Se(this,e||[],!1))},not:function(e){return this.pushStack(Se(this,e||[],!0))},is:function(e){return!!Se(this,"string"==typeof e&&ge.test(e)?d(e):e||[],!1).length}});var we,pe=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/,He=d.fn.init=function(e,t,c){var h,_;if(!e)return this;if(c=c||we,"string"==typeof e){if(!(h="<"===e[0]&&">"===e[e.length-1]&&e.length>=3?[null,e,null]:pe.exec(e))||!h[1]&&t)return!t||t.jquery?(t||c).find(e):this.constructor(t).find(e);if(h[1]){if(d.merge(this,d.parseHTML(h[1],(t=t instanceof d?t[0]:t)&&t.nodeType?t.ownerDocument||t:E,!0)),be.test(h[1])&&d.isPlainObject(t))for(h in t)S(this[h])?this[h](t[h]):this.attr(h,t[h]);return this}return(_=E.getElementById(h[2]))&&(this[0]=_,this.length=1),this}return e.nodeType?(this[0]=e,this.length=1,this):S(e)?void 0!==c.ready?c.ready(e):e(d):d.makeArray(e,this)};He.prototype=d.fn,we=d(E);var Ne=/^(?:parents|prev(?:Until|All))/,Ee={children:!0,contents:!0,next:!0,prev:!0};function je(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}d.fn.extend({has:function(e){var t=d(e,this),c=t.length;return this.filter(function(){for(var h=0;h<c;h++)if(d.contains(this,t[h]))return!0})},closest:function(e,t){var c,h=0,_=this.length,m=[],M="string"!=typeof e&&d(e);if(!ge.test(e))for(;h<_;h++)for(c=this[h];c&&c!==t;c=c.parentNode)if(c.nodeType<11&&(M?M.index(c)>-1:1===c.nodeType&&d.find.matchesSelector(c,e))){m.push(c);break}return this.pushStack(m.length>1?d.uniqueSort(m):m)},index:function(e){return e?"string"==typeof e?g.call(d(e),this[0]):g.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(d.uniqueSort(d.merge(this.get(),d(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),d.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return te(e,"parentNode")},parentsUntil:function(e,t,c){return te(e,"parentNode",c)},next:function(e){return je(e,"nextSibling")},prev:function(e){return je(e,"previousSibling")},nextAll:function(e){return te(e,"nextSibling")},prevAll:function(e){return te(e,"previousSibling")},nextUntil:function(e,t,c){return te(e,"nextSibling",c)},prevUntil:function(e,t,c){return te(e,"previousSibling",c)},siblings:function(e){return fe((e.parentNode||{}).firstChild,e)},children:function(e){return fe(e.firstChild)},contents:function(e){return null!=e.contentDocument&&a(e.contentDocument)?e.contentDocument:(ae(e,"template")&&(e=e.content||e),d.merge([],e.childNodes))}},function(e,t){d.fn[e]=function(c,h){var _=d.map(this,t,c);return"Until"!==e.slice(-5)&&(h=c),h&&"string"==typeof h&&(_=d.filter(h,_)),this.length>1&&(Ee[e]||d.uniqueSort(_),Ne.test(e)&&_.reverse()),this.pushStack(_)}});var qe=/[^\x20\t\r\n\f]+/g;function Ue(e){return e}function et(e){throw e}function Dt(e,t,c,h){var _;try{e&&S(_=e.promise)?_.call(e).done(t).fail(c):e&&S(_=e.then)?_.call(e,t,c):t.apply(void 0,[e].slice(h))}catch(m){c.apply(void 0,[m])}}d.Callbacks=function(e){e="string"==typeof e?function ze(e){var t={};return d.each(e.match(qe)||[],function(c,h){t[h]=!0}),t}(e):d.extend({},e);var t,c,h,_,m=[],M=[],W=-1,N=function(){for(_=_||e.once,h=t=!0;M.length;W=-1)for(c=M.shift();++W<m.length;)!1===m[W].apply(c[0],c[1])&&e.stopOnFalse&&(W=m.length,c=!1);e.memory||(c=!1),t=!1,_&&(m=c?[]:"")},K={add:function(){return m&&(c&&!t&&(W=m.length-1,M.push(c)),function J(re){d.each(re,function(Y,se){S(se)?(!e.unique||!K.has(se))&&m.push(se):se&&se.length&&"string"!==V(se)&&J(se)})}(arguments),c&&!t&&N()),this},remove:function(){return d.each(arguments,function(J,re){for(var Y;(Y=d.inArray(re,m,Y))>-1;)m.splice(Y,1),Y<=W&&W--}),this},has:function(J){return J?d.inArray(J,m)>-1:m.length>0},empty:function(){return m&&(m=[]),this},disable:function(){return _=M=[],m=c="",this},disabled:function(){return!m},lock:function(){return _=M=[],!c&&!t&&(m=c=""),this},locked:function(){return!!_},fireWith:function(J,re){return _||(re=[J,(re=re||[]).slice?re.slice():re],M.push(re),t||N()),this},fire:function(){return K.fireWith(this,arguments),this},fired:function(){return!!h}};return K},d.extend({Deferred:function(e){var t=[["notify","progress",d.Callbacks("memory"),d.Callbacks("memory"),2],["resolve","done",d.Callbacks("once memory"),d.Callbacks("once memory"),0,"resolved"],["reject","fail",d.Callbacks("once memory"),d.Callbacks("once memory"),1,"rejected"]],c="pending",h={state:function(){return c},always:function(){return _.done(arguments).fail(arguments),this},catch:function(m){return h.then(null,m)},pipe:function(){var m=arguments;return d.Deferred(function(M){d.each(t,function(W,N){var K=S(m[N[4]])&&m[N[4]];_[N[1]](function(){var J=K&&K.apply(this,arguments);J&&S(J.promise)?J.promise().progress(M.notify).done(M.resolve).fail(M.reject):M[N[0]+"With"](this,K?[J]:arguments)})}),m=null}).promise()},then:function(m,M,W){var N=0;function K(J,re,Y,se){return function(){var Ce=this,Re=arguments,Oe=function(){var Je,bt;if(!(J<N)){if((Je=Y.apply(Ce,Re))===re.promise())throw new TypeError("Thenable self-resolution");S(bt=Je&&("object"==typeof Je||"function"==typeof Je)&&Je.then)?se?bt.call(Je,K(N,re,Ue,se),K(N,re,et,se)):(N++,bt.call(Je,K(N,re,Ue,se),K(N,re,et,se),K(N,re,Ue,re.notifyWith))):(Y!==Ue&&(Ce=void 0,Re=[Je]),(se||re.resolveWith)(Ce,Re))}},nt=se?Oe:function(){try{Oe()}catch(Je){d.Deferred.exceptionHook&&d.Deferred.exceptionHook(Je,nt.error),J+1>=N&&(Y!==et&&(Ce=void 0,Re=[Je]),re.rejectWith(Ce,Re))}};J?nt():(d.Deferred.getErrorHook?nt.error=d.Deferred.getErrorHook():d.Deferred.getStackHook&&(nt.error=d.Deferred.getStackHook()),i.setTimeout(nt))}}return d.Deferred(function(J){t[0][3].add(K(0,J,S(W)?W:Ue,J.notifyWith)),t[1][3].add(K(0,J,S(m)?m:Ue)),t[2][3].add(K(0,J,S(M)?M:et))}).promise()},promise:function(m){return null!=m?d.extend(m,h):h}},_={};return d.each(t,function(m,M){var W=M[2],N=M[5];h[M[1]]=W.add,N&&W.add(function(){c=N},t[3-m][2].disable,t[3-m][3].disable,t[0][2].lock,t[0][3].lock),W.add(M[3].fire),_[M[0]]=function(){return _[M[0]+"With"](this===_?void 0:this,arguments),this},_[M[0]+"With"]=W.fireWith}),h.promise(_),e&&e.call(_,_),_},when:function(e){var t=arguments.length,c=t,h=Array(c),_=p.call(arguments),m=d.Deferred(),M=function(W){return function(N){h[W]=this,_[W]=arguments.length>1?p.call(arguments):N,--t||m.resolveWith(h,_)}};if(t<=1&&(Dt(e,m.done(M(c)).resolve,m.reject,!t),"pending"===m.state()||S(_[c]&&_[c].then)))return m.then();for(;c--;)Dt(_[c],M(c),m.reject);return m.promise()}});var gt=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;d.Deferred.exceptionHook=function(e,t){i.console&&i.console.warn&&e&&gt.test(e.name)&&i.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},d.readyException=function(e){i.setTimeout(function(){throw e})};var Qe=d.Deferred();function ht(){E.removeEventListener("DOMContentLoaded",ht),i.removeEventListener("load",ht),d.ready()}d.fn.ready=function(e){return Qe.then(e).catch(function(t){d.readyException(t)}),this},d.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--d.readyWait:d.isReady)||(d.isReady=!0,!(!0!==e&&--d.readyWait>0)&&Qe.resolveWith(E,[d]))}}),d.ready.then=Qe.then,"complete"===E.readyState||"loading"!==E.readyState&&!E.documentElement.doScroll?i.setTimeout(d.ready):(E.addEventListener("DOMContentLoaded",ht),i.addEventListener("load",ht));var Ze=function(e,t,c,h,_,m,M){var W=0,N=e.length,K=null==c;if("object"===V(c))for(W in _=!0,c)Ze(e,t,W,c[W],!0,m,M);else if(void 0!==h&&(_=!0,S(h)||(M=!0),K&&(M?(t.call(e,h),t=null):(K=t,t=function(J,re,Y){return K.call(d(J),Y)})),t))for(;W<N;W++)t(e[W],c,M?h:h.call(e[W],W,t(e[W],c)));return _?e:K?t.call(e):N?t(e[0],c):m},ct=/^-ms-/,hn=/-([a-z])/g;function pn(e,t){return t.toUpperCase()}function lt(e){return e.replace(ct,"ms-").replace(hn,pn)}var wt=function(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType};function Pt(){this.expando=d.expando+Pt.uid++}Pt.uid=1,Pt.prototype={cache:function(e){var t=e[this.expando];return t||(t={},wt(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,c){var h,_=this.cache(e);if("string"==typeof t)_[lt(t)]=c;else for(h in t)_[lt(h)]=t[h];return _},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][lt(t)]},access:function(e,t,c){return void 0===t||t&&"string"==typeof t&&void 0===c?this.get(e,t):(this.set(e,t,c),void 0!==c?c:t)},remove:function(e,t){var c,h=e[this.expando];if(void 0!==h){if(void 0!==t)for((c=(t=Array.isArray(t)?t.map(lt):(t=lt(t))in h?[t]:t.match(qe)||[]).length);c--;)delete h[t[c]];(void 0===t||d.isEmptyObject(h))&&(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!d.isEmptyObject(t)}};var ye=new Pt,tt=new Pt,Xt=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,en=/[A-Z]/g;function tn(e,t,c){var h;if(void 0===c&&1===e.nodeType)if(h="data-"+t.replace(en,"-$&").toLowerCase(),"string"==typeof(c=e.getAttribute(h))){try{c=function _n(e){return"true"===e||"false"!==e&&("null"===e?null:e===+e+""?+e:Xt.test(e)?JSON.parse(e):e)}(c)}catch(_){}tt.set(e,t,c)}else c=void 0;return c}d.extend({hasData:function(e){return tt.hasData(e)||ye.hasData(e)},data:function(e,t,c){return tt.access(e,t,c)},removeData:function(e,t){tt.remove(e,t)},_data:function(e,t,c){return ye.access(e,t,c)},_removeData:function(e,t){ye.remove(e,t)}}),d.fn.extend({data:function(e,t){var c,h,_,m=this[0],M=m&&m.attributes;if(void 0===e){if(this.length&&(_=tt.get(m),1===m.nodeType&&!ye.get(m,"hasDataAttrs"))){for(c=M.length;c--;)M[c]&&0===(h=M[c].name).indexOf("data-")&&(h=lt(h.slice(5)),tn(m,h,_[h]));ye.set(m,"hasDataAttrs",!0)}return _}return"object"==typeof e?this.each(function(){tt.set(this,e)}):Ze(this,function(W){var N;if(m&&void 0===W)return void 0!==(N=tt.get(m,e))||void 0!==(N=tn(m,e))?N:void 0;this.each(function(){tt.set(this,e,W)})},null,t,arguments.length>1,null,!0)},removeData:function(e){return this.each(function(){tt.remove(this,e)})}}),d.extend({queue:function(e,t,c){var h;if(e)return h=ye.get(e,t=(t||"fx")+"queue"),c&&(!h||Array.isArray(c)?h=ye.access(e,t,d.makeArray(c)):h.push(c)),h||[]},dequeue:function(e,t){var c=d.queue(e,t=t||"fx"),h=c.length,_=c.shift(),m=d._queueHooks(e,t);"inprogress"===_&&(_=c.shift(),h--),_&&("fx"===t&&c.unshift("inprogress"),delete m.stop,_.call(e,function(){d.dequeue(e,t)},m)),!h&&m&&m.empty.fire()},_queueHooks:function(e,t){var c=t+"queueHooks";return ye.get(e,c)||ye.access(e,c,{empty:d.Callbacks("once memory").add(function(){ye.remove(e,[t+"queue",c])})})}}),d.fn.extend({queue:function(e,t){var c=2;return"string"!=typeof e&&(t=e,e="fx",c--),arguments.length<c?d.queue(this[0],e):void 0===t?this:this.each(function(){var h=d.queue(this,e,t);d._queueHooks(this,e),"fx"===e&&"inprogress"!==h[0]&&d.dequeue(this,e)})},dequeue:function(e){return this.each(function(){d.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){var c,h=1,_=d.Deferred(),m=this,M=this.length,W=function(){--h||_.resolveWith(m,[m])};for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";M--;)(c=ye.get(m[M],e+"queueHooks"))&&c.empty&&(h++,c.empty.add(W));return W(),_.promise(t)}});var nn=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,kt=new RegExp("^(?:([+-])=|)("+nn+")([a-z%]*)$","i"),pt=["Top","Right","Bottom","Left"],Mt=E.documentElement,Ot=function(e){return d.contains(e.ownerDocument,e)},mn={composed:!0};Mt.getRootNode&&(Ot=function(e){return d.contains(e.ownerDocument,e)||e.getRootNode(mn)===e.ownerDocument});var Nt=function(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&Ot(e)&&"none"===d.css(e,"display")};function rn(e,t,c,h){var _,m,M=20,W=h?function(){return h.cur()}:function(){return d.css(e,t,"")},N=W(),K=c&&c[3]||(d.cssNumber[t]?"":"px"),J=e.nodeType&&(d.cssNumber[t]||"px"!==K&&+N)&&kt.exec(d.css(e,t));if(J&&J[3]!==K){for(K=K||J[3],J=+(N/=2)||1;M--;)d.style(e,t,J+K),(1-m)*(1-(m=W()/N||.5))<=0&&(M=0),J/=m;d.style(e,t,(J*=2)+K),c=c||[]}return c&&(J=+J||+N||0,_=c[1]?J+(c[1]+1)*c[2]:+c[2],h&&(h.unit=K,h.start=J,h.end=_)),_}var gn={};function Nn(e){var t,c=e.ownerDocument,h=e.nodeName,_=gn[h];return _||(t=c.body.appendChild(c.createElement(h)),_=d.css(t,"display"),t.parentNode.removeChild(t),"none"===_&&(_="block"),gn[h]=_,_)}function Lt(e,t){for(var c,h,_=[],m=0,M=e.length;m<M;m++)(h=e[m]).style&&(c=h.style.display,t?("none"===c&&(_[m]=ye.get(h,"display")||null,_[m]||(h.style.display="")),""===h.style.display&&Nt(h)&&(_[m]=Nn(h))):"none"!==c&&(_[m]="none",ye.set(h,"display",c)));for(m=0;m<M;m++)null!=_[m]&&(e[m].style.display=_[m]);return e}d.fn.extend({show:function(){return Lt(this,!0)},hide:function(){return Lt(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){Nt(this)?d(this).show():d(this).hide()})}});var t,c,Wt=/^(?:checkbox|radio)$/i,bn=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,vn=/^$|^module$|\/(?:java|ecma)script/i;t=E.createDocumentFragment().appendChild(E.createElement("div")),(c=E.createElement("input")).setAttribute("type","radio"),c.setAttribute("checked","checked"),c.setAttribute("name","t"),t.appendChild(c),R.checkClone=t.cloneNode(!0).cloneNode(!0).lastChild.checked,t.innerHTML="<textarea>x</textarea>",R.noCloneChecked=!!t.cloneNode(!0).lastChild.defaultValue,t.innerHTML="<option></option>",R.option=!!t.lastChild;var ut={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function rt(e,t){var c;return c=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[],void 0===t||t&&ae(e,t)?d.merge([e],c):c}function ie(e,t){for(var c=0,h=e.length;c<h;c++)ye.set(e[c],"globalEval",!t||ye.get(t[c],"globalEval"))}ut.tbody=ut.tfoot=ut.colgroup=ut.caption=ut.thead,ut.th=ut.td,R.option||(ut.optgroup=ut.option=[1,"<select multiple='multiple'>","</select>"]);var de=/<|&#?\w+;/;function O(e,t,c,h,_){for(var m,M,W,N,K,J,re=t.createDocumentFragment(),Y=[],se=0,Ce=e.length;se<Ce;se++)if((m=e[se])||0===m)if("object"===V(m))d.merge(Y,m.nodeType?[m]:m);else if(de.test(m)){for(M=M||re.appendChild(t.createElement("div")),W=(bn.exec(m)||["",""])[1].toLowerCase(),M.innerHTML=(N=ut[W]||ut._default)[1]+d.htmlPrefilter(m)+N[2],J=N[0];J--;)M=M.lastChild;d.merge(Y,M.childNodes),(M=re.firstChild).textContent=""}else Y.push(t.createTextNode(m));for(re.textContent="",se=0;m=Y[se++];)if(h&&d.inArray(m,h)>-1)_&&_.push(m);else if(K=Ot(m),M=rt(re.appendChild(m),"script"),K&&ie(M),c)for(J=0;m=M[J++];)vn.test(m.type||"")&&c.push(m);return re}var q=/^([^.]*)(?:\.(.+)|)/;function G(){return!0}function oe(){return!1}function Fe(e,t,c,h,_,m){var M,W;if("object"==typeof t){for(W in"string"!=typeof c&&(h=h||c,c=void 0),t)Fe(e,W,c,h,t[W],m);return e}if(null==h&&null==_?(_=c,h=c=void 0):null==_&&("string"==typeof c?(_=h,h=void 0):(_=h,h=c,c=void 0)),!1===_)_=oe;else if(!_)return e;return 1===m&&(M=_,_=function(N){return d().off(N),M.apply(this,arguments)},_.guid=M.guid||(M.guid=d.guid++)),e.each(function(){d.event.add(this,t,_,h,c)})}function Ye(e,t,c){c?(ye.set(e,t,!1),d.event.add(e,t,{namespace:!1,handler:function(h){var _,m=ye.get(this,t);if(1&h.isTrigger&&this[t]){if(m)(d.event.special[t]||{}).delegateType&&h.stopPropagation();else if(m=p.call(arguments),ye.set(this,t,m),this[t](),_=ye.get(this,t),ye.set(this,t,!1),m!==_)return h.stopImmediatePropagation(),h.preventDefault(),_}else m&&(ye.set(this,t,d.event.trigger(m[0],m.slice(1),this)),h.stopPropagation(),h.isImmediatePropagationStopped=G)}})):void 0===ye.get(e,t)&&d.event.add(e,t,G)}d.event={global:{},add:function(e,t,c,h,_){var m,M,W,N,K,J,re,Y,se,Ce,Re,Oe=ye.get(e);if(wt(e))for(c.handler&&(c=(m=c).handler,_=m.selector),_&&d.find.matchesSelector(Mt,_),c.guid||(c.guid=d.guid++),(N=Oe.events)||(N=Oe.events=Object.create(null)),(M=Oe.handle)||(M=Oe.handle=function(nt){return void 0!==d&&d.event.triggered!==nt.type?d.event.dispatch.apply(e,arguments):void 0}),K=(t=(t||"").match(qe)||[""]).length;K--;)se=Re=(W=q.exec(t[K])||[])[1],Ce=(W[2]||"").split(".").sort(),se&&(re=d.event.special[se]||{},re=d.event.special[se=(_?re.delegateType:re.bindType)||se]||{},J=d.extend({type:se,origType:Re,data:h,handler:c,guid:c.guid,selector:_,needsContext:_&&d.expr.match.needsContext.test(_),namespace:Ce.join(".")},m),(Y=N[se])||((Y=N[se]=[]).delegateCount=0,(!re.setup||!1===re.setup.call(e,h,Ce,M))&&e.addEventListener&&e.addEventListener(se,M)),re.add&&(re.add.call(e,J),J.handler.guid||(J.handler.guid=c.guid)),_?Y.splice(Y.delegateCount++,0,J):Y.push(J),d.event.global[se]=!0)},remove:function(e,t,c,h,_){var m,M,W,N,K,J,re,Y,se,Ce,Re,Oe=ye.hasData(e)&&ye.get(e);if(Oe&&(N=Oe.events)){for(K=(t=(t||"").match(qe)||[""]).length;K--;)if(se=Re=(W=q.exec(t[K])||[])[1],Ce=(W[2]||"").split(".").sort(),se){for(re=d.event.special[se]||{},Y=N[se=(h?re.delegateType:re.bindType)||se]||[],W=W[2]&&new RegExp("(^|\\.)"+Ce.join("\\.(?:.*\\.|)")+"(\\.|$)"),M=m=Y.length;m--;)J=Y[m],(_||Re===J.origType)&&(!c||c.guid===J.guid)&&(!W||W.test(J.namespace))&&(!h||h===J.selector||"**"===h&&J.selector)&&(Y.splice(m,1),J.selector&&Y.delegateCount--,re.remove&&re.remove.call(e,J));M&&!Y.length&&((!re.teardown||!1===re.teardown.call(e,Ce,Oe.handle))&&d.removeEvent(e,se,Oe.handle),delete N[se])}else for(se in N)d.event.remove(e,se+t[K],c,h,!0);d.isEmptyObject(N)&&ye.remove(e,"handle events")}},dispatch:function(e){var t,c,h,_,m,M,W=new Array(arguments.length),N=d.event.fix(e),K=(ye.get(this,"events")||Object.create(null))[N.type]||[],J=d.event.special[N.type]||{};for(W[0]=N,t=1;t<arguments.length;t++)W[t]=arguments[t];if(N.delegateTarget=this,!J.preDispatch||!1!==J.preDispatch.call(this,N)){for(M=d.event.handlers.call(this,N,K),t=0;(_=M[t++])&&!N.isPropagationStopped();)for(N.currentTarget=_.elem,c=0;(m=_.handlers[c++])&&!N.isImmediatePropagationStopped();)(!N.rnamespace||!1===m.namespace||N.rnamespace.test(m.namespace))&&(N.handleObj=m,N.data=m.data,void 0!==(h=((d.event.special[m.origType]||{}).handle||m.handler).apply(_.elem,W))&&!1===(N.result=h)&&(N.preventDefault(),N.stopPropagation()));return J.postDispatch&&J.postDispatch.call(this,N),N.result}},handlers:function(e,t){var c,h,_,m,M,W=[],N=t.delegateCount,K=e.target;if(N&&K.nodeType&&!("click"===e.type&&e.button>=1))for(;K!==this;K=K.parentNode||this)if(1===K.nodeType&&("click"!==e.type||!0!==K.disabled)){for(m=[],M={},c=0;c<N;c++)void 0===M[_=(h=t[c]).selector+" "]&&(M[_]=h.needsContext?d(_,this).index(K)>-1:d.find(_,this,null,[K]).length),M[_]&&m.push(h);m.length&&W.push({elem:K,handlers:m})}return K=this,N<t.length&&W.push({elem:K,handlers:t.slice(N)}),W},addProp:function(e,t){Object.defineProperty(d.Event.prototype,e,{enumerable:!0,configurable:!0,get:S(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[e]},set:function(c){Object.defineProperty(this,e,{enumerable:!0,configurable:!0,writable:!0,value:c})}})},fix:function(e){return e[d.expando]?e:new d.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return Wt.test(t.type)&&t.click&&ae(t,"input")&&Ye(t,"click",!0),!1},trigger:function(e){var t=this||e;return Wt.test(t.type)&&t.click&&ae(t,"input")&&Ye(t,"click"),!0},_default:function(e){var t=e.target;return Wt.test(t.type)&&t.click&&ae(t,"input")&&ye.get(t,"click")||ae(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},d.removeEvent=function(e,t,c){e.removeEventListener&&e.removeEventListener(t,c)},d.Event=function(e,t){if(!(this instanceof d.Event))return new d.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?G:oe,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&d.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[d.expando]=!0},d.Event.prototype={constructor:d.Event,isDefaultPrevented:oe,isPropagationStopped:oe,isImmediatePropagationStopped:oe,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=G,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=G,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=G,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},d.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:!0},d.event.addProp),d.each({focus:"focusin",blur:"focusout"},function(e,t){function c(h){if(E.documentMode){var _=ye.get(this,"handle"),m=d.event.fix(h);m.type="focusin"===h.type?"focus":"blur",m.isSimulated=!0,_(h),m.target===m.currentTarget&&_(m)}else d.event.simulate(t,h.target,d.event.fix(h))}d.event.special[e]={setup:function(){var h;if(Ye(this,e,!0),!E.documentMode)return!1;(h=ye.get(this,t))||this.addEventListener(t,c),ye.set(this,t,(h||0)+1)},trigger:function(){return Ye(this,e),!0},teardown:function(){var h;if(!E.documentMode)return!1;(h=ye.get(this,t)-1)?ye.set(this,t,h):(this.removeEventListener(t,c),ye.remove(this,t))},_default:function(h){return ye.get(h.target,e)},delegateType:t},d.event.special[t]={setup:function(){var h=this.ownerDocument||this.document||this,_=E.documentMode?this:h,m=ye.get(_,t);m||(E.documentMode?this.addEventListener(t,c):h.addEventListener(e,c,!0)),ye.set(_,t,(m||0)+1)},teardown:function(){var h=this.ownerDocument||this.document||this,_=E.documentMode?this:h,m=ye.get(_,t)-1;m?ye.set(_,t,m):(E.documentMode?this.removeEventListener(t,c):h.removeEventListener(e,c,!0),ye.remove(_,t))}}}),d.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,t){d.event.special[e]={delegateType:t,bindType:t,handle:function(c){var h,_=this,m=c.relatedTarget,M=c.handleObj;return(!m||m!==_&&!d.contains(_,m))&&(c.type=M.origType,h=M.handler.apply(this,arguments),c.type=t),h}}}),d.fn.extend({on:function(e,t,c,h){return Fe(this,e,t,c,h)},one:function(e,t,c,h){return Fe(this,e,t,c,h,1)},off:function(e,t,c){var h,_;if(e&&e.preventDefault&&e.handleObj)return h=e.handleObj,d(e.delegateTarget).off(h.namespace?h.origType+"."+h.namespace:h.origType,h.selector,h.handler),this;if("object"==typeof e){for(_ in e)this.off(_,t,e[_]);return this}return(!1===t||"function"==typeof t)&&(c=t,t=void 0),!1===c&&(c=oe),this.each(function(){d.event.remove(this,e,c,t)})}});var Xe=/<script|<style|<link/i,Kt=/checked\s*(?:[^=]|=\s*.checked.)/i,an=/^\s*<!\[CDATA\[|\]\]>\s*$/g;function Wn(e,t){return ae(e,"table")&&ae(11!==t.nodeType?t:t.firstChild,"tr")&&d(e).children("tbody")[0]||e}function dr(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function hr(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Un(e,t){var c,h,_,M,W,N;if(1===t.nodeType){if(ye.hasData(e)&&(N=ye.get(e).events))for(_ in ye.remove(t,"handle events"),N)for(c=0,h=N[_].length;c<h;c++)d.event.add(t,_,N[_][c]);tt.hasData(e)&&(M=tt.access(e),W=d.extend({},M),tt.set(t,W))}}function pr(e,t){var c=t.nodeName.toLowerCase();"input"===c&&Wt.test(e.type)?t.checked=e.checked:("input"===c||"textarea"===c)&&(t.defaultValue=e.defaultValue)}function Ut(e,t,c,h){t=v(t);var _,m,M,W,N,K,J=0,re=e.length,Y=re-1,se=t[0],Ce=S(se);if(Ce||re>1&&"string"==typeof se&&!R.checkClone&&Kt.test(se))return e.each(function(Re){var Oe=e.eq(Re);Ce&&(t[0]=se.call(this,Re,Oe.html())),Ut(Oe,t,c,h)});if(re&&(m=(_=O(t,e[0].ownerDocument,!1,e,h)).firstChild,1===_.childNodes.length&&(_=m),m||h)){for(W=(M=d.map(rt(_,"script"),dr)).length;J<re;J++)N=_,J!==Y&&(N=d.clone(N,!0,!0),W&&d.merge(M,rt(N,"script"))),c.call(e[J],N,J);if(W)for(K=M[M.length-1].ownerDocument,d.map(M,hr),J=0;J<W;J++)vn.test((N=M[J]).type||"")&&!ye.access(N,"globalEval")&&d.contains(K,N)&&(N.src&&"module"!==(N.type||"").toLowerCase()?d._evalUrl&&!N.noModule&&d._evalUrl(N.src,{nonce:N.nonce||N.getAttribute("nonce")},K):P(N.textContent.replace(an,""),N,K))}return e}function Fn(e,t,c){for(var h,_=t?d.filter(t,e):e,m=0;null!=(h=_[m]);m++)!c&&1===h.nodeType&&d.cleanData(rt(h)),h.parentNode&&(c&&Ot(h)&&ie(rt(h,"script")),h.parentNode.removeChild(h));return e}d.extend({htmlPrefilter:function(e){return e},clone:function(e,t,c){var h,_,m,M,W=e.cloneNode(!0),N=Ot(e);if(!(R.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||d.isXMLDoc(e)))for(M=rt(W),h=0,_=(m=rt(e)).length;h<_;h++)pr(m[h],M[h]);if(t)if(c)for(m=m||rt(e),M=M||rt(W),h=0,_=m.length;h<_;h++)Un(m[h],M[h]);else Un(e,W);return(M=rt(W,"script")).length>0&&ie(M,!N&&rt(e,"script")),W},cleanData:function(e){for(var t,c,h,_=d.event.special,m=0;void 0!==(c=e[m]);m++)if(wt(c)){if(t=c[ye.expando]){if(t.events)for(h in t.events)_[h]?d.event.remove(c,h):d.removeEvent(c,h,t.handle);c[ye.expando]=void 0}c[tt.expando]&&(c[tt.expando]=void 0)}}}),d.fn.extend({detach:function(e){return Fn(this,e,!0)},remove:function(e){return Fn(this,e)},text:function(e){return Ze(this,function(t){return void 0===t?d.text(this):this.empty().each(function(){(1===this.nodeType||11===this.nodeType||9===this.nodeType)&&(this.textContent=t)})},null,e,arguments.length)},append:function(){return Ut(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||Wn(this,e).appendChild(e)})},prepend:function(){return Ut(this,arguments,function(e){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=Wn(this,e);t.insertBefore(e,t.firstChild)}})},before:function(){return Ut(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return Ut(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(d.cleanData(rt(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return d.clone(this,e,t)})},html:function(e){return Ze(this,function(t){var c=this[0]||{},h=0,_=this.length;if(void 0===t&&1===c.nodeType)return c.innerHTML;if("string"==typeof t&&!Xe.test(t)&&!ut[(bn.exec(t)||["",""])[1].toLowerCase()]){t=d.htmlPrefilter(t);try{for(;h<_;h++)1===(c=this[h]||{}).nodeType&&(d.cleanData(rt(c,!1)),c.innerHTML=t);c=0}catch(m){}}c&&this.empty().append(t)},null,e,arguments.length)},replaceWith:function(){var e=[];return Ut(this,arguments,function(t){var c=this.parentNode;d.inArray(this,e)<0&&(d.cleanData(rt(this)),c&&c.replaceChild(t,this))},e)}}),d.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){d.fn[e]=function(c){for(var h,_=[],m=d(c),M=m.length-1,W=0;W<=M;W++)h=W===M?this:this.clone(!0),d(m[W])[t](h),y.apply(_,h.get());return this.pushStack(_)}});var yn=new RegExp("^("+nn+")(?!px)[a-z%]+$","i"),xn=/^--/,on=function(e){var t=e.ownerDocument.defaultView;return(!t||!t.opener)&&(t=i),t.getComputedStyle(e)},Hn=function(e,t,c){var h,_,m={};for(_ in t)m[_]=e.style[_],e.style[_]=t[_];for(_ in h=c.call(e),t)e.style[_]=m[_];return h},_r=new RegExp(pt.join("|"),"i");function Vt(e,t,c){var h,_,m,M,W=xn.test(t),N=e.style;return(c=c||on(e))&&(M=c.getPropertyValue(t)||c[t],W&&M&&(M=M.replace(Le,"$1")||void 0),""===M&&!Ot(e)&&(M=d.style(e,t)),!R.pixelBoxStyles()&&yn.test(M)&&_r.test(t)&&(h=N.width,_=N.minWidth,m=N.maxWidth,N.minWidth=N.maxWidth=N.width=M,M=c.width,N.width=h,N.minWidth=_,N.maxWidth=m)),void 0!==M?M+"":M}function qn(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}!function(){function e(){if(K){N.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",K.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",Mt.appendChild(N).appendChild(K);var J=i.getComputedStyle(K);c="1%"!==J.top,W=12===t(J.marginLeft),K.style.right="60%",m=36===t(J.right),h=36===t(J.width),K.style.position="absolute",_=12===t(K.offsetWidth/3),Mt.removeChild(N),K=null}}function t(J){return Math.round(parseFloat(J))}var c,h,_,m,M,W,N=E.createElement("div"),K=E.createElement("div");!K.style||(K.style.backgroundClip="content-box",K.cloneNode(!0).style.backgroundClip="",R.clearCloneStyle="content-box"===K.style.backgroundClip,d.extend(R,{boxSizingReliable:function(){return e(),h},pixelBoxStyles:function(){return e(),m},pixelPosition:function(){return e(),c},reliableMarginLeft:function(){return e(),W},scrollboxSize:function(){return e(),_},reliableTrDimensions:function(){var J,re,Y,se;return null==M&&(J=E.createElement("table"),re=E.createElement("tr"),Y=E.createElement("div"),J.style.cssText="position:absolute;left:-11111px;border-collapse:separate",re.style.cssText="box-sizing:content-box;border:1px solid",re.style.height="1px",Y.style.height="9px",Y.style.display="block",Mt.appendChild(J).appendChild(re).appendChild(Y),se=i.getComputedStyle(re),M=parseInt(se.height,10)+parseInt(se.borderTopWidth,10)+parseInt(se.borderBottomWidth,10)===re.offsetHeight,Mt.removeChild(J)),M}}))}();var Kn=["Webkit","Moz","ms"],Vn=E.createElement("div").style,zn={};function Tn(e){return d.cssProps[e]||zn[e]||(e in Vn?e:zn[e]=function mr(e){for(var t=e[0].toUpperCase()+e.slice(1),c=Kn.length;c--;)if((e=Kn[c]+t)in Vn)return e}(e)||e)}var gr=/^(none|table(?!-c[ea]).+)/,br={position:"absolute",visibility:"hidden",display:"block"},Yn={letterSpacing:"0",fontWeight:"400"};function Gn(e,t,c){var h=kt.exec(t);return h?Math.max(0,h[2]-(c||0))+(h[3]||"px"):t}function Mn(e,t,c,h,_,m){var M="width"===t?1:0,W=0,N=0,K=0;if(c===(h?"border":"content"))return 0;for(;M<4;M+=2)"margin"===c&&(K+=d.css(e,c+pt[M],!0,_)),h?("content"===c&&(N-=d.css(e,"padding"+pt[M],!0,_)),"margin"!==c&&(N-=d.css(e,"border"+pt[M]+"Width",!0,_))):(N+=d.css(e,"padding"+pt[M],!0,_),"padding"!==c?N+=d.css(e,"border"+pt[M]+"Width",!0,_):W+=d.css(e,"border"+pt[M]+"Width",!0,_));return!h&&m>=0&&(N+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-m-N-W-.5))||0),N+K}function Zn(e,t,c){var h=on(e),m=(!R.boxSizingReliable()||c)&&"border-box"===d.css(e,"boxSizing",!1,h),M=m,W=Vt(e,t,h),N="offset"+t[0].toUpperCase()+t.slice(1);if(yn.test(W)){if(!c)return W;W="auto"}return(!R.boxSizingReliable()&&m||!R.reliableTrDimensions()&&ae(e,"tr")||"auto"===W||!parseFloat(W)&&"inline"===d.css(e,"display",!1,h))&&e.getClientRects().length&&(m="border-box"===d.css(e,"boxSizing",!1,h),(M=N in e)&&(W=e[N])),(W=parseFloat(W)||0)+Mn(e,t,c||(m?"border":"content"),M,h,W)+"px"}function ot(e,t,c,h,_){return new ot.prototype.init(e,t,c,h,_)}d.extend({cssHooks:{opacity:{get:function(e,t){if(t){var c=Vt(e,"opacity");return""===c?"1":c}}}},cssNumber:{animationIterationCount:!0,aspectRatio:!0,borderImageSlice:!0,columnCount:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,scale:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeMiterlimit:!0,strokeOpacity:!0},cssProps:{},style:function(e,t,c,h){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var _,m,M,W=lt(t),N=xn.test(t),K=e.style;if(N||(t=Tn(W)),M=d.cssHooks[t]||d.cssHooks[W],void 0===c)return M&&"get"in M&&void 0!==(_=M.get(e,!1,h))?_:K[t];if("string"==(m=typeof c)&&(_=kt.exec(c))&&_[1]&&(c=rn(e,t,_),m="number"),null==c||c!=c)return;"number"===m&&!N&&(c+=_&&_[3]||(d.cssNumber[W]?"":"px")),!R.clearCloneStyle&&""===c&&0===t.indexOf("background")&&(K[t]="inherit"),(!M||!("set"in M)||void 0!==(c=M.set(e,c,h)))&&(N?K.setProperty(t,c):K[t]=c)}},css:function(e,t,c,h){var _,m,M,W=lt(t);return xn.test(t)||(t=Tn(W)),(M=d.cssHooks[t]||d.cssHooks[W])&&"get"in M&&(_=M.get(e,!0,c)),void 0===_&&(_=Vt(e,t,h)),"normal"===_&&t in Yn&&(_=Yn[t]),""===c||c?(m=parseFloat(_),!0===c||isFinite(m)?m||0:_):_}}),d.each(["height","width"],function(e,t){d.cssHooks[t]={get:function(c,h,_){if(h)return!gr.test(d.css(c,"display"))||c.getClientRects().length&&c.getBoundingClientRect().width?Zn(c,t,_):Hn(c,br,function(){return Zn(c,t,_)})},set:function(c,h,_){var m,M=on(c),W=!R.scrollboxSize()&&"absolute"===M.position,K=(W||_)&&"border-box"===d.css(c,"boxSizing",!1,M),J=_?Mn(c,t,_,K,M):0;return K&&W&&(J-=Math.ceil(c["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(M[t])-Mn(c,t,"border",!1,M)-.5)),J&&(m=kt.exec(h))&&"px"!==(m[3]||"px")&&(c.style[t]=h,h=d.css(c,t)),Gn(0,h,J)}}}),d.cssHooks.marginLeft=qn(R.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Vt(e,"marginLeft"))||e.getBoundingClientRect().left-Hn(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),d.each({margin:"",padding:"",border:"Width"},function(e,t){d.cssHooks[e+t]={expand:function(c){for(var h=0,_={},m="string"==typeof c?c.split(" "):[c];h<4;h++)_[e+pt[h]+t]=m[h]||m[h-2]||m[0];return _}},"margin"!==e&&(d.cssHooks[e+t].set=Gn)}),d.fn.extend({css:function(e,t){return Ze(this,function(c,h,_){var m,M,W={},N=0;if(Array.isArray(h)){for(m=on(c),M=h.length;N<M;N++)W[h[N]]=d.css(c,h[N],!1,m);return W}return void 0!==_?d.style(c,h,_):d.css(c,h)},e,t,arguments.length>1)}}),d.Tween=ot,(ot.prototype={constructor:ot,init:function(e,t,c,h,_,m){this.elem=e,this.prop=c,this.easing=_||d.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=h,this.unit=m||(d.cssNumber[c]?"":"px")},cur:function(){var e=ot.propHooks[this.prop];return e&&e.get?e.get(this):ot.propHooks._default.get(this)},run:function(e){var t,c=ot.propHooks[this.prop];return this.pos=t=this.options.duration?d.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),c&&c.set?c.set(this):ot.propHooks._default.set(this),this}}).init.prototype=ot.prototype,(ot.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=d.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){d.fx.step[e.prop]?d.fx.step[e.prop](e):1!==e.elem.nodeType||!d.cssHooks[e.prop]&&null==e.elem.style[Tn(e.prop)]?e.elem[e.prop]=e.now:d.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=ot.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},d.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},d.fx=ot.prototype.init,d.fx.step={};var Ft,sn,vr=/^(?:toggle|show|hide)$/,yr=/queueHooks$/;function En(){sn&&(!1===E.hidden&&i.requestAnimationFrame?i.requestAnimationFrame(En):i.setTimeout(En,d.fx.interval),d.fx.tick())}function Qn(){return i.setTimeout(function(){Ft=void 0}),Ft=Date.now()}function un(e,t){var c,h=0,_={height:e};for(t=t?1:0;h<4;h+=2-t)_["margin"+(c=pt[h])]=_["padding"+c]=e;return t&&(_.opacity=_.width=e),_}function Jn(e,t,c){for(var h,_=(_t.tweeners[t]||[]).concat(_t.tweeners["*"]),m=0,M=_.length;m<M;m++)if(h=_[m].call(c,t,e))return h}function _t(e,t,c){var h,_,m=0,M=_t.prefilters.length,W=d.Deferred().always(function(){delete N.elem}),N=function(){if(_)return!1;for(var re=Ft||Qn(),Y=Math.max(0,K.startTime+K.duration-re),Ce=1-(Y/K.duration||0),Re=0,Oe=K.tweens.length;Re<Oe;Re++)K.tweens[Re].run(Ce);return W.notifyWith(e,[K,Ce,Y]),Ce<1&&Oe?Y:(Oe||W.notifyWith(e,[K,1,0]),W.resolveWith(e,[K]),!1)},K=W.promise({elem:e,props:d.extend({},t),opts:d.extend(!0,{specialEasing:{},easing:d.easing._default},c),originalProperties:t,originalOptions:c,startTime:Ft||Qn(),duration:c.duration,tweens:[],createTween:function(re,Y){var se=d.Tween(e,K.opts,re,Y,K.opts.specialEasing[re]||K.opts.easing);return K.tweens.push(se),se},stop:function(re){var Y=0,se=re?K.tweens.length:0;if(_)return this;for(_=!0;Y<se;Y++)K.tweens[Y].run(1);return re?(W.notifyWith(e,[K,1,0]),W.resolveWith(e,[K,re])):W.rejectWith(e,[K,re]),this}}),J=K.props;for(function Tr(e,t){var c,h,_,m,M;for(c in e)if(_=t[h=lt(c)],m=e[c],Array.isArray(m)&&(_=m[1],m=e[c]=m[0]),c!==h&&(e[h]=m,delete e[c]),(M=d.cssHooks[h])&&"expand"in M)for(c in m=M.expand(m),delete e[h],m)c in e||(e[c]=m[c],t[c]=_);else t[h]=_}(J,K.opts.specialEasing);m<M;m++)if(h=_t.prefilters[m].call(K,e,J,K.opts))return S(h.stop)&&(d._queueHooks(K.elem,K.opts.queue).stop=h.stop.bind(h)),h;return d.map(J,Jn,K),S(K.opts.start)&&K.opts.start.call(e,K),K.progress(K.opts.progress).done(K.opts.done,K.opts.complete).fail(K.opts.fail).always(K.opts.always),d.fx.timer(d.extend(N,{elem:e,anim:K,queue:K.opts.queue})),K}d.Animation=d.extend(_t,{tweeners:{"*":[function(e,t){var c=this.createTween(e,t);return rn(c.elem,e,kt.exec(t),c),c}]},tweener:function(e,t){S(e)?(t=e,e=["*"]):e=e.match(qe);for(var c,h=0,_=e.length;h<_;h++)(_t.tweeners[c=e[h]]=_t.tweeners[c]||[]).unshift(t)},prefilters:[function xr(e,t,c){var h,_,m,M,W,N,K,J,re="width"in t||"height"in t,Y=this,se={},Ce=e.style,Re=e.nodeType&&Nt(e),Oe=ye.get(e,"fxshow");for(h in c.queue||(null==(M=d._queueHooks(e,"fx")).unqueued&&(M.unqueued=0,W=M.empty.fire,M.empty.fire=function(){M.unqueued||W()}),M.unqueued++,Y.always(function(){Y.always(function(){M.unqueued--,d.queue(e,"fx").length||M.empty.fire()})})),t)if(vr.test(_=t[h])){if(delete t[h],m=m||"toggle"===_,_===(Re?"hide":"show")){if("show"!==_||!Oe||void 0===Oe[h])continue;Re=!0}se[h]=Oe&&Oe[h]||d.style(e,h)}if((N=!d.isEmptyObject(t))||!d.isEmptyObject(se))for(h in re&&1===e.nodeType&&(c.overflow=[Ce.overflow,Ce.overflowX,Ce.overflowY],null==(K=Oe&&Oe.display)&&(K=ye.get(e,"display")),"none"===(J=d.css(e,"display"))&&(K?J=K:(Lt([e],!0),K=e.style.display||K,J=d.css(e,"display"),Lt([e]))),("inline"===J||"inline-block"===J&&null!=K)&&"none"===d.css(e,"float")&&(N||(Y.done(function(){Ce.display=K}),null==K&&(K="none"===(J=Ce.display)?"":J)),Ce.display="inline-block")),c.overflow&&(Ce.overflow="hidden",Y.always(function(){Ce.overflow=c.overflow[0],Ce.overflowX=c.overflow[1],Ce.overflowY=c.overflow[2]})),N=!1,se)N||(Oe?"hidden"in Oe&&(Re=Oe.hidden):Oe=ye.access(e,"fxshow",{display:K}),m&&(Oe.hidden=!Re),Re&&Lt([e],!0),Y.done(function(){for(h in Re||Lt([e]),ye.remove(e,"fxshow"),se)d.style(e,h,se[h])})),N=Jn(Re?Oe[h]:0,h,Y),h in Oe||(Oe[h]=N.start,Re&&(N.end=N.start,N.start=0))}],prefilter:function(e,t){t?_t.prefilters.unshift(e):_t.prefilters.push(e)}}),d.speed=function(e,t,c){var h=e&&"object"==typeof e?d.extend({},e):{complete:c||!c&&t||S(e)&&e,duration:e,easing:c&&t||t&&!S(t)&&t};return d.fx.off?h.duration=0:"number"!=typeof h.duration&&(h.duration=h.duration in d.fx.speeds?d.fx.speeds[h.duration]:d.fx.speeds._default),(null==h.queue||!0===h.queue)&&(h.queue="fx"),h.old=h.complete,h.complete=function(){S(h.old)&&h.old.call(this),h.queue&&d.dequeue(this,h.queue)},h},d.fn.extend({fadeTo:function(e,t,c,h){return this.filter(Nt).css("opacity",0).show().end().animate({opacity:t},e,c,h)},animate:function(e,t,c,h){var _=d.isEmptyObject(e),m=d.speed(t,c,h),M=function(){var W=_t(this,d.extend({},e),m);(_||ye.get(this,"finish"))&&W.stop(!0)};return M.finish=M,_||!1===m.queue?this.each(M):this.queue(m.queue,M)},stop:function(e,t,c){var h=function(_){var m=_.stop;delete _.stop,m(c)};return"string"!=typeof e&&(c=t,t=e,e=void 0),t&&this.queue(e||"fx",[]),this.each(function(){var _=!0,m=null!=e&&e+"queueHooks",M=d.timers,W=ye.get(this);if(m)W[m]&&W[m].stop&&h(W[m]);else for(m in W)W[m]&&W[m].stop&&yr.test(m)&&h(W[m]);for(m=M.length;m--;)M[m].elem===this&&(null==e||M[m].queue===e)&&(M[m].anim.stop(c),_=!1,M.splice(m,1));(_||!c)&&d.dequeue(this,e)})},finish:function(e){return!1!==e&&(e=e||"fx"),this.each(function(){var t,c=ye.get(this),h=c[e+"queue"],_=c[e+"queueHooks"],m=d.timers,M=h?h.length:0;for(c.finish=!0,d.queue(this,e,[]),_&&_.stop&&_.stop.call(this,!0),t=m.length;t--;)m[t].elem===this&&m[t].queue===e&&(m[t].anim.stop(!0),m.splice(t,1));for(t=0;t<M;t++)h[t]&&h[t].finish&&h[t].finish.call(this);delete c.finish})}}),d.each(["toggle","show","hide"],function(e,t){var c=d.fn[t];d.fn[t]=function(h,_,m){return null==h||"boolean"==typeof h?c.apply(this,arguments):this.animate(un(t,!0),h,_,m)}}),d.each({slideDown:un("show"),slideUp:un("hide"),slideToggle:un("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){d.fn[e]=function(c,h,_){return this.animate(t,c,h,_)}}),d.timers=[],d.fx.tick=function(){var e,t=0,c=d.timers;for(Ft=Date.now();t<c.length;t++)!(e=c[t])()&&c[t]===e&&c.splice(t--,1);c.length||d.fx.stop(),Ft=void 0},d.fx.timer=function(e){d.timers.push(e),d.fx.start()},d.fx.interval=13,d.fx.start=function(){sn||(sn=!0,En())},d.fx.stop=function(){sn=null},d.fx.speeds={slow:600,fast:200,_default:400},d.fn.delay=function(e,t){return e=d.fx&&d.fx.speeds[e]||e,this.queue(t=t||"fx",function(c,h){var _=i.setTimeout(c,e);h.stop=function(){i.clearTimeout(_)}})},function(){var e=E.createElement("input"),c=E.createElement("select").appendChild(E.createElement("option"));e.type="checkbox",R.checkOn=""!==e.value,R.optSelected=c.selected,(e=E.createElement("input")).value="t",e.type="radio",R.radioValue="t"===e.value}();var Xn,zt=d.expr.attrHandle;d.fn.extend({attr:function(e,t){return Ze(this,d.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){d.removeAttr(this,e)})}}),d.extend({attr:function(e,t,c){var h,_,m=e.nodeType;if(3!==m&&8!==m&&2!==m)return void 0===e.getAttribute?d.prop(e,t,c):((1!==m||!d.isXMLDoc(e))&&(_=d.attrHooks[t.toLowerCase()]||(d.expr.match.bool.test(t)?Xn:void 0)),void 0!==c?null===c?void d.removeAttr(e,t):_&&"set"in _&&void 0!==(h=_.set(e,c,t))?h:(e.setAttribute(t,c+""),c):_&&"get"in _&&null!==(h=_.get(e,t))?h:null==(h=d.find.attr(e,t))?void 0:h)},attrHooks:{type:{set:function(e,t){if(!R.radioValue&&"radio"===t&&ae(e,"input")){var c=e.value;return e.setAttribute("type",t),c&&(e.value=c),t}}}},removeAttr:function(e,t){var c,h=0,_=t&&t.match(qe);if(_&&1===e.nodeType)for(;c=_[h++];)e.removeAttribute(c)}}),Xn={set:function(e,t,c){return!1===t?d.removeAttr(e,c):e.setAttribute(c,c),c}},d.each(d.expr.match.bool.source.match(/\w+/g),function(e,t){var c=zt[t]||d.find.attr;zt[t]=function(h,_,m){var M,W,N=_.toLowerCase();return m||(W=zt[N],zt[N]=M,M=null!=c(h,_,m)?N:null,zt[N]=W),M}});var Mr=/^(?:input|select|textarea|button)$/i,Er=/^(?:a|area)$/i;function Rt(e){return(e.match(qe)||[]).join(" ")}function St(e){return e.getAttribute&&e.getAttribute("class")||""}function Cn(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(qe)||[]}d.fn.extend({prop:function(e,t){return Ze(this,d.prop,e,t,arguments.length>1)},removeProp:function(e){return this.each(function(){delete this[d.propFix[e]||e]})}}),d.extend({prop:function(e,t,c){var h,_,m=e.nodeType;if(3!==m&&8!==m&&2!==m)return(1!==m||!d.isXMLDoc(e))&&(_=d.propHooks[t=d.propFix[t]||t]),void 0!==c?_&&"set"in _&&void 0!==(h=_.set(e,c,t))?h:e[t]=c:_&&"get"in _&&null!==(h=_.get(e,t))?h:e[t]},propHooks:{tabIndex:{get:function(e){var t=d.find.attr(e,"tabindex");return t?parseInt(t,10):Mr.test(e.nodeName)||Er.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),R.optSelected||(d.propHooks.selected={get:function(e){return null},set:function(e){}}),d.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){d.propFix[this.toLowerCase()]=this}),d.fn.extend({addClass:function(e){var t,c,h,_,m,M;return S(e)?this.each(function(W){d(this).addClass(e.call(this,W,St(this)))}):(t=Cn(e)).length?this.each(function(){if(h=St(this),c=1===this.nodeType&&" "+Rt(h)+" "){for(m=0;m<t.length;m++)c.indexOf(" "+(_=t[m])+" ")<0&&(c+=_+" ");M=Rt(c),h!==M&&this.setAttribute("class",M)}}):this},removeClass:function(e){var t,c,h,_,m,M;return S(e)?this.each(function(W){d(this).removeClass(e.call(this,W,St(this)))}):arguments.length?(t=Cn(e)).length?this.each(function(){if(h=St(this),c=1===this.nodeType&&" "+Rt(h)+" "){for(m=0;m<t.length;m++)for(_=t[m];c.indexOf(" "+_+" ")>-1;)c=c.replace(" "+_+" "," ");M=Rt(c),h!==M&&this.setAttribute("class",M)}}):this:this.attr("class","")},toggleClass:function(e,t){var c,h,_,m,M=typeof e,W="string"===M||Array.isArray(e);return S(e)?this.each(function(N){d(this).toggleClass(e.call(this,N,St(this),t),t)}):"boolean"==typeof t&&W?t?this.addClass(e):this.removeClass(e):(c=Cn(e),this.each(function(){if(W)for(m=d(this),_=0;_<c.length;_++)m.hasClass(h=c[_])?m.removeClass(h):m.addClass(h);else(void 0===e||"boolean"===M)&&((h=St(this))&&ye.set(this,"__className__",h),this.setAttribute&&this.setAttribute("class",h||!1===e?"":ye.get(this,"__className__")||""))}))},hasClass:function(e){var t,c,h=0;for(t=" "+e+" ";c=this[h++];)if(1===c.nodeType&&(" "+Rt(St(c))+" ").indexOf(t)>-1)return!0;return!1}});var Cr=/\r/g;d.fn.extend({val:function(e){var t,c,h,_=this[0];return arguments.length?(h=S(e),this.each(function(m){var M;1===this.nodeType&&(null==(M=h?e.call(this,m,d(this).val()):e)?M="":"number"==typeof M?M+="":Array.isArray(M)&&(M=d.map(M,function(W){return null==W?"":W+""})),(!(t=d.valHooks[this.type]||d.valHooks[this.nodeName.toLowerCase()])||!("set"in t)||void 0===t.set(this,M,"value"))&&(this.value=M))})):_?(t=d.valHooks[_.type]||d.valHooks[_.nodeName.toLowerCase()])&&"get"in t&&void 0!==(c=t.get(_,"value"))?c:"string"==typeof(c=_.value)?c.replace(Cr,""):null==c?"":c:void 0}}),d.extend({valHooks:{option:{get:function(e){var t=d.find.attr(e,"value");return null!=t?t:Rt(d.text(e))}},select:{get:function(e){var t,c,h,_=e.options,m=e.selectedIndex,M="select-one"===e.type,W=M?null:[],N=M?m+1:_.length;for(h=m<0?N:M?m:0;h<N;h++)if(((c=_[h]).selected||h===m)&&!c.disabled&&(!c.parentNode.disabled||!ae(c.parentNode,"optgroup"))){if(t=d(c).val(),M)return t;W.push(t)}return W},set:function(e,t){for(var c,h,_=e.options,m=d.makeArray(t),M=_.length;M--;)((h=_[M]).selected=d.inArray(d.valHooks.option.get(h),m)>-1)&&(c=!0);return c||(e.selectedIndex=-1),m}}}}),d.each(["radio","checkbox"],function(){d.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=d.inArray(d(e).val(),t)>-1}},R.checkOn||(d.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})});var Yt=i.location,er={guid:Date.now()},Dn=/\?/;d.parseXML=function(e){var t,c;if(!e||"string"!=typeof e)return null;try{t=(new i.DOMParser).parseFromString(e,"text/xml")}catch(h){}return c=t&&t.getElementsByTagName("parsererror")[0],(!t||c)&&d.error("Invalid XML: "+(c?d.map(c.childNodes,function(h){return h.textContent}).join("\n"):e)),t};var tr=/^(?:focusinfocus|focusoutblur)$/,nr=function(e){e.stopPropagation()};d.extend(d.event,{trigger:function(e,t,c,h){var _,m,M,W,N,K,J,re,Y=[c||E],se=x.call(e,"type")?e.type:e,Ce=x.call(e,"namespace")?e.namespace.split("."):[];if(m=re=M=c=c||E,3!==c.nodeType&&8!==c.nodeType&&!tr.test(se+d.event.triggered)&&(se.indexOf(".")>-1&&(Ce=se.split("."),se=Ce.shift(),Ce.sort()),N=se.indexOf(":")<0&&"on"+se,(e=e[d.expando]?e:new d.Event(se,"object"==typeof e&&e)).isTrigger=h?2:3,e.namespace=Ce.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+Ce.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=c),t=null==t?[e]:d.makeArray(t,[e]),J=d.event.special[se]||{},h||!J.trigger||!1!==J.trigger.apply(c,t))){if(!h&&!J.noBubble&&!F(c)){for(tr.test((W=J.delegateType||se)+se)||(m=m.parentNode);m;m=m.parentNode)Y.push(m),M=m;M===(c.ownerDocument||E)&&Y.push(M.defaultView||M.parentWindow||i)}for(_=0;(m=Y[_++])&&!e.isPropagationStopped();)re=m,e.type=_>1?W:J.bindType||se,(K=(ye.get(m,"events")||Object.create(null))[e.type]&&ye.get(m,"handle"))&&K.apply(m,t),(K=N&&m[N])&&K.apply&&wt(m)&&(e.result=K.apply(m,t),!1===e.result&&e.preventDefault());return e.type=se,!h&&!e.isDefaultPrevented()&&(!J._default||!1===J._default.apply(Y.pop(),t))&&wt(c)&&N&&S(c[se])&&!F(c)&&((M=c[N])&&(c[N]=null),d.event.triggered=se,e.isPropagationStopped()&&re.addEventListener(se,nr),c[se](),e.isPropagationStopped()&&re.removeEventListener(se,nr),d.event.triggered=void 0,M&&(c[N]=M)),e.result}},simulate:function(e,t,c){var h=d.extend(new d.Event,c,{type:e,isSimulated:!0});d.event.trigger(h,null,t)}}),d.fn.extend({trigger:function(e,t){return this.each(function(){d.event.trigger(e,t,this)})},triggerHandler:function(e,t){var c=this[0];if(c)return d.event.trigger(e,t,c,!0)}});var Dr=/\[\]$/,rr=/\r?\n/g,wr=/^(?:submit|button|image|reset|file)$/i,Pr=/^(?:input|select|textarea|keygen)/i;function wn(e,t,c,h){var _;if(Array.isArray(t))d.each(t,function(m,M){c||Dr.test(e)?h(e,M):wn(e+"["+("object"==typeof M&&null!=M?m:"")+"]",M,c,h)});else if(c||"object"!==V(t))h(e,t);else for(_ in t)wn(e+"["+_+"]",t[_],c,h)}d.param=function(e,t){var c,h=[],_=function(m,M){var W=S(M)?M():M;h[h.length]=encodeURIComponent(m)+"="+encodeURIComponent(null==W?"":W)};if(null==e)return"";if(Array.isArray(e)||e.jquery&&!d.isPlainObject(e))d.each(e,function(){_(this.name,this.value)});else for(c in e)wn(c,e[c],t,_);return h.join("&")},d.fn.extend({serialize:function(){return d.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=d.prop(this,"elements");return e?d.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!d(this).is(":disabled")&&Pr.test(this.nodeName)&&!wr.test(e)&&(this.checked||!Wt.test(e))}).map(function(e,t){var c=d(this).val();return null==c?null:Array.isArray(c)?d.map(c,function(h){return{name:t.name,value:h.replace(rr,"\r\n")}}):{name:t.name,value:c.replace(rr,"\r\n")}}).get()}});var Or=/%20/g,Ar=/#.*$/,$r=/([?&])_=[^&]*/,Ir=/^(.*?):[ \t]*([^\r\n]*)$/gm,Lr=/^(?:GET|HEAD)$/,Rr=/^\/\//,ir={},Pn={},ar="*/".concat("*"),On=E.createElement("a");function or(e){return function(t,c){"string"!=typeof t&&(c=t,t="*");var h,_=0,m=t.toLowerCase().match(qe)||[];if(S(c))for(;h=m[_++];)"+"===h[0]?(h=h.slice(1)||"*",(e[h]=e[h]||[]).unshift(c)):(e[h]=e[h]||[]).push(c)}}function sr(e,t,c,h){var _={},m=e===Pn;function M(W){var N;return _[W]=!0,d.each(e[W]||[],function(K,J){var re=J(t,c,h);return"string"!=typeof re||m||_[re]?m?!(N=re):void 0:(t.dataTypes.unshift(re),M(re),!1)}),N}return M(t.dataTypes[0])||!_["*"]&&M("*")}function An(e,t){var c,h,_=d.ajaxSettings.flatOptions||{};for(c in t)void 0!==t[c]&&((_[c]?e:h||(h={}))[c]=t[c]);return h&&d.extend(!0,e,h),e}On.href=Yt.href,d.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Yt.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Yt.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":ar,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":d.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?An(An(e,d.ajaxSettings),t):An(d.ajaxSettings,e)},ajaxPrefilter:or(ir),ajaxTransport:or(Pn),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0);var c,h,_,m,M,W,N,K,J,re,Y=d.ajaxSetup({},t=t||{}),se=Y.context||Y,Ce=Y.context&&(se.nodeType||se.jquery)?d(se):d.event,Re=d.Deferred(),Oe=d.Callbacks("once memory"),nt=Y.statusCode||{},Je={},bt={},vt="canceled",ke={readyState:0,getResponseHeader:function(Be){var Ge;if(N){if(!m)for(m={};Ge=Ir.exec(_);)m[Ge[1].toLowerCase()+" "]=(m[Ge[1].toLowerCase()+" "]||[]).concat(Ge[2]);Ge=m[Be.toLowerCase()+" "]}return null==Ge?null:Ge.join(", ")},getAllResponseHeaders:function(){return N?_:null},setRequestHeader:function(Be,Ge){return null==N&&(Be=bt[Be.toLowerCase()]=bt[Be.toLowerCase()]||Be,Je[Be]=Ge),this},overrideMimeType:function(Be){return null==N&&(Y.mimeType=Be),this},statusCode:function(Be){var Ge;if(Be)if(N)ke.always(Be[ke.status]);else for(Ge in Be)nt[Ge]=[nt[Ge],Be[Ge]];return this},abort:function(Be){var Ge=Be||vt;return c&&c.abort(Ge),Bt(0,Ge),this}};if(Re.promise(ke),Y.url=((e||Y.url||Yt.href)+"").replace(Rr,Yt.protocol+"//"),Y.type=t.method||t.type||Y.method||Y.type,Y.dataTypes=(Y.dataType||"*").toLowerCase().match(qe)||[""],null==Y.crossDomain){W=E.createElement("a");try{W.href=Y.url,W.href=W.href,Y.crossDomain=On.protocol+"//"+On.host!=W.protocol+"//"+W.host}catch(Be){Y.crossDomain=!0}}if(Y.data&&Y.processData&&"string"!=typeof Y.data&&(Y.data=d.param(Y.data,Y.traditional)),sr(ir,Y,t,ke),N)return ke;for(J in(K=d.event&&Y.global)&&0==d.active++&&d.event.trigger("ajaxStart"),Y.type=Y.type.toUpperCase(),Y.hasContent=!Lr.test(Y.type),h=Y.url.replace(Ar,""),Y.hasContent?Y.data&&Y.processData&&0===(Y.contentType||"").indexOf("application/x-www-form-urlencoded")&&(Y.data=Y.data.replace(Or,"+")):(re=Y.url.slice(h.length),Y.data&&(Y.processData||"string"==typeof Y.data)&&(h+=(Dn.test(h)?"&":"?")+Y.data,delete Y.data),!1===Y.cache&&(h=h.replace($r,"$1"),re=(Dn.test(h)?"&":"?")+"_="+er.guid+++re),Y.url=h+re),Y.ifModified&&(d.lastModified[h]&&ke.setRequestHeader("If-Modified-Since",d.lastModified[h]),d.etag[h]&&ke.setRequestHeader("If-None-Match",d.etag[h])),(Y.data&&Y.hasContent&&!1!==Y.contentType||t.contentType)&&ke.setRequestHeader("Content-Type",Y.contentType),ke.setRequestHeader("Accept",Y.dataTypes[0]&&Y.accepts[Y.dataTypes[0]]?Y.accepts[Y.dataTypes[0]]+("*"!==Y.dataTypes[0]?", "+ar+"; q=0.01":""):Y.accepts["*"]),Y.headers)ke.setRequestHeader(J,Y.headers[J]);if(Y.beforeSend&&(!1===Y.beforeSend.call(se,ke,Y)||N))return ke.abort();if(vt="abort",Oe.add(Y.complete),ke.done(Y.success),ke.fail(Y.error),c=sr(Pn,Y,t,ke)){if(ke.readyState=1,K&&Ce.trigger("ajaxSend",[ke,Y]),N)return ke;Y.async&&Y.timeout>0&&(M=i.setTimeout(function(){ke.abort("timeout")},Y.timeout));try{N=!1,c.send(Je,Bt)}catch(Be){if(N)throw Be;Bt(-1,Be)}}else Bt(-1,"No Transport");function Bt(Be,Ge,Zt,In){var yt,Qt,xt,At,$t,ft=Ge;N||(N=!0,M&&i.clearTimeout(M),c=void 0,_=In||"",ke.readyState=Be>0?4:0,yt=Be>=200&&Be<300||304===Be,Zt&&(At=function Sr(e,t,c){for(var h,_,m,M,W=e.contents,N=e.dataTypes;"*"===N[0];)N.shift(),void 0===h&&(h=e.mimeType||t.getResponseHeader("Content-Type"));if(h)for(_ in W)if(W[_]&&W[_].test(h)){N.unshift(_);break}if(N[0]in c)m=N[0];else{for(_ in c){if(!N[0]||e.converters[_+" "+N[0]]){m=_;break}M||(M=_)}m=m||M}if(m)return m!==N[0]&&N.unshift(m),c[m]}(Y,ke,Zt)),!yt&&d.inArray("script",Y.dataTypes)>-1&&d.inArray("json",Y.dataTypes)<0&&(Y.converters["text script"]=function(){}),At=function Br(e,t,c,h){var _,m,M,W,N,K={},J=e.dataTypes.slice();if(J[1])for(M in e.converters)K[M.toLowerCase()]=e.converters[M];for(m=J.shift();m;)if(e.responseFields[m]&&(c[e.responseFields[m]]=t),!N&&h&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),N=m,m=J.shift())if("*"===m)m=N;else if("*"!==N&&N!==m){if(!(M=K[N+" "+m]||K["* "+m]))for(_ in K)if((W=_.split(" "))[1]===m&&(M=K[N+" "+W[0]]||K["* "+W[0]])){!0===M?M=K[_]:!0!==K[_]&&(m=W[0],J.unshift(W[1]));break}if(!0!==M)if(M&&e.throws)t=M(t);else try{t=M(t)}catch(re){return{state:"parsererror",error:M?re:"No conversion from "+N+" to "+m}}}return{state:"success",data:t}}(Y,At,ke,yt),yt?(Y.ifModified&&(($t=ke.getResponseHeader("Last-Modified"))&&(d.lastModified[h]=$t),($t=ke.getResponseHeader("etag"))&&(d.etag[h]=$t)),204===Be||"HEAD"===Y.type?ft="nocontent":304===Be?ft="notmodified":(ft=At.state,Qt=At.data,yt=!(xt=At.error))):(xt=ft,(Be||!ft)&&(ft="error",Be<0&&(Be=0))),ke.status=Be,ke.statusText=(Ge||ft)+"",yt?Re.resolveWith(se,[Qt,ft,ke]):Re.rejectWith(se,[ke,ft,xt]),ke.statusCode(nt),nt=void 0,K&&Ce.trigger(yt?"ajaxSuccess":"ajaxError",[ke,Y,yt?Qt:xt]),Oe.fireWith(se,[ke,ft]),K&&(Ce.trigger("ajaxComplete",[ke,Y]),--d.active||d.event.trigger("ajaxStop")))}return ke},getJSON:function(e,t,c){return d.get(e,t,c,"json")},getScript:function(e,t){return d.get(e,void 0,t,"script")}}),d.each(["get","post"],function(e,t){d[t]=function(c,h,_,m){return S(h)&&(m=m||_,_=h,h=void 0),d.ajax(d.extend({url:c,type:t,dataType:m,data:h,success:_},d.isPlainObject(c)&&c))}}),d.ajaxPrefilter(function(e){var t;for(t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),d._evalUrl=function(e,t,c){return d.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(h){d.globalEval(h,t,c)}})},d.fn.extend({wrapAll:function(e){var t;return this[0]&&(S(e)&&(e=e.call(this[0])),t=d(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var c=this;c.firstElementChild;)c=c.firstElementChild;return c}).append(this)),this},wrapInner:function(e){return S(e)?this.each(function(t){d(this).wrapInner(e.call(this,t))}):this.each(function(){var t=d(this),c=t.contents();c.length?c.wrapAll(e):t.append(e)})},wrap:function(e){var t=S(e);return this.each(function(c){d(this).wrapAll(t?e.call(this,c):e)})},unwrap:function(e){return this.parent(e).not("body").each(function(){d(this).replaceWith(this.childNodes)}),this}}),d.expr.pseudos.hidden=function(e){return!d.expr.pseudos.visible(e)},d.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},d.ajaxSettings.xhr=function(){try{return new i.XMLHttpRequest}catch(e){}};var jr={0:200,1223:204},Gt=d.ajaxSettings.xhr();R.cors=!!Gt&&"withCredentials"in Gt,R.ajax=Gt=!!Gt,d.ajaxTransport(function(e){var t,c;if(R.cors||Gt&&!e.crossDomain)return{send:function(h,_){var m,M=e.xhr();if(M.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(m in e.xhrFields)M[m]=e.xhrFields[m];for(m in e.mimeType&&M.overrideMimeType&&M.overrideMimeType(e.mimeType),!e.crossDomain&&!h["X-Requested-With"]&&(h["X-Requested-With"]="XMLHttpRequest"),h)M.setRequestHeader(m,h[m]);t=function(W){return function(){t&&(t=c=M.onload=M.onerror=M.onabort=M.ontimeout=M.onreadystatechange=null,"abort"===W?M.abort():"error"===W?"number"!=typeof M.status?_(0,"error"):_(M.status,M.statusText):_(jr[M.status]||M.status,M.statusText,"text"!==(M.responseType||"text")||"string"!=typeof M.responseText?{binary:M.response}:{text:M.responseText},M.getAllResponseHeaders()))}},M.onload=t(),c=M.onerror=M.ontimeout=t("error"),void 0!==M.onabort?M.onabort=c:M.onreadystatechange=function(){4===M.readyState&&i.setTimeout(function(){t&&c()})},t=t("abort");try{M.send(e.hasContent&&e.data||null)}catch(W){if(t)throw W}},abort:function(){t&&t()}}}),d.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),d.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return d.globalEval(e),e}}}),d.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),d.ajaxTransport("script",function(e){var t,c;if(e.crossDomain||e.scriptAttrs)return{send:function(h,_){t=d("<script>").attr(e.scriptAttrs||{}).prop({charset:e.scriptCharset,src:e.url}).on("load error",c=function(m){t.remove(),c=null,m&&_("error"===m.type?404:200,m.type)}),E.head.appendChild(t[0])},abort:function(){c&&c()}}});var e,ur=[],$n=/(=)\?(?=&|$)|\?\?/;d.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=ur.pop()||d.expando+"_"+er.guid++;return this[e]=!0,e}}),d.ajaxPrefilter("json jsonp",function(e,t,c){var h,_,m,M=!1!==e.jsonp&&($n.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&$n.test(e.data)&&"data");if(M||"jsonp"===e.dataTypes[0])return h=e.jsonpCallback=S(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,M?e[M]=e[M].replace($n,"$1"+h):!1!==e.jsonp&&(e.url+=(Dn.test(e.url)?"&":"?")+e.jsonp+"="+h),e.converters["script json"]=function(){return m||d.error(h+" was not called"),m[0]},e.dataTypes[0]="json",_=i[h],i[h]=function(){m=arguments},c.always(function(){void 0===_?d(i).removeProp(h):i[h]=_,e[h]&&(e.jsonpCallback=t.jsonpCallback,ur.push(h)),m&&S(_)&&_(m[0]),m=_=void 0}),"script"}),R.createHTMLDocument=((e=E.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===e.childNodes.length),d.parseHTML=function(e,t,c){return"string"!=typeof e?[]:("boolean"==typeof t&&(c=t,t=!1),t||(R.createHTMLDocument?((h=(t=E.implementation.createHTMLDocument("")).createElement("base")).href=E.location.href,t.head.appendChild(h)):t=E),m=!c&&[],(_=be.exec(e))?[t.createElement(_[1])]:(_=O([e],t,m),m&&m.length&&d(m).remove(),d.merge([],_.childNodes)));var h,_,m},d.fn.load=function(e,t,c){var h,_,m,M=this,W=e.indexOf(" ");return W>-1&&(h=Rt(e.slice(W)),e=e.slice(0,W)),S(t)?(c=t,t=void 0):t&&"object"==typeof t&&(_="POST"),M.length>0&&d.ajax({url:e,type:_||"GET",dataType:"html",data:t}).done(function(N){m=arguments,M.html(h?d("<div>").append(d.parseHTML(N)).find(h):N)}).always(c&&function(N,K){M.each(function(){c.apply(this,m||[N.responseText,K,N])})}),this},d.expr.pseudos.animated=function(e){return d.grep(d.timers,function(t){return e===t.elem}).length},d.offset={setOffset:function(e,t,c){var h,_,m,M,W,N,J=d.css(e,"position"),re=d(e),Y={};"static"===J&&(e.style.position="relative"),W=re.offset(),m=d.css(e,"top"),N=d.css(e,"left"),("absolute"===J||"fixed"===J)&&(m+N).indexOf("auto")>-1?(M=(h=re.position()).top,_=h.left):(M=parseFloat(m)||0,_=parseFloat(N)||0),S(t)&&(t=t.call(e,c,d.extend({},W))),null!=t.top&&(Y.top=t.top-W.top+M),null!=t.left&&(Y.left=t.left-W.left+_),"using"in t?t.using.call(e,Y):re.css(Y)}},d.fn.extend({offset:function(e){if(arguments.length)return void 0===e?this:this.each(function(_){d.offset.setOffset(this,e,_)});var t,c,h=this[0];return h?h.getClientRects().length?{top:(t=h.getBoundingClientRect()).top+(c=h.ownerDocument.defaultView).pageYOffset,left:t.left+c.pageXOffset}:{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,c,h=this[0],_={top:0,left:0};if("fixed"===d.css(h,"position"))t=h.getBoundingClientRect();else{for(t=this.offset(),c=h.ownerDocument,e=h.offsetParent||c.documentElement;e&&(e===c.body||e===c.documentElement)&&"static"===d.css(e,"position");)e=e.parentNode;e&&e!==h&&1===e.nodeType&&((_=d(e).offset()).top+=d.css(e,"borderTopWidth",!0),_.left+=d.css(e,"borderLeftWidth",!0))}return{top:t.top-_.top-d.css(h,"marginTop",!0),left:t.left-_.left-d.css(h,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===d.css(e,"position");)e=e.offsetParent;return e||Mt})}}),d.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,t){var c="pageYOffset"===t;d.fn[e]=function(h){return Ze(this,function(_,m,M){var W;if(F(_)?W=_:9===_.nodeType&&(W=_.defaultView),void 0===M)return W?W[t]:_[m];W?W.scrollTo(c?W.pageXOffset:M,c?M:W.pageYOffset):_[m]=M},e,h,arguments.length)}}),d.each(["top","left"],function(e,t){d.cssHooks[t]=qn(R.pixelPosition,function(c,h){if(h)return h=Vt(c,t),yn.test(h)?d(c).position()[t]+"px":h})}),d.each({Height:"height",Width:"width"},function(e,t){d.each({padding:"inner"+e,content:t,"":"outer"+e},function(c,h){d.fn[h]=function(_,m){var M=arguments.length&&(c||"boolean"!=typeof _),W=c||(!0===_||!0===m?"margin":"border");return Ze(this,function(N,K,J){var re;return F(N)?0===h.indexOf("outer")?N["inner"+e]:N.document.documentElement["client"+e]:9===N.nodeType?(re=N.documentElement,Math.max(N.body["scroll"+e],re["scroll"+e],N.body["offset"+e],re["offset"+e],re["client"+e])):void 0===J?d.css(N,K,W):d.style(N,K,J,W)},t,M?_:void 0,M)}})}),d.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){d.fn[t]=function(c){return this.on(t,c)}}),d.fn.extend({bind:function(e,t,c){return this.on(e,null,t,c)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,c,h){return this.on(t,e,c,h)},undelegate:function(e,t,c){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",c)},hover:function(e,t){return this.on("mouseenter",e).on("mouseleave",t||e)}}),d.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,t){d.fn[t]=function(c,h){return arguments.length>0?this.on(t,null,c,h):this.trigger(t)}});var Nr=/^[\s\uFEFF\xA0]+|([^\s\uFEFF\xA0])[\s\uFEFF\xA0]+$/g;d.proxy=function(e,t){var c,h,_;if("string"==typeof t&&(c=e[t],t=e,e=c),S(e))return h=p.call(arguments,2),_=function(){return e.apply(t||this,h.concat(p.call(arguments)))},_.guid=e.guid=e.guid||d.guid++,_},d.holdReady=function(e){e?d.readyWait++:d.ready(!0)},d.isArray=Array.isArray,d.parseJSON=JSON.parse,d.nodeName=ae,d.isFunction=S,d.isWindow=F,d.camelCase=lt,d.type=V,d.now=Date.now,d.isNumeric=function(e){var t=d.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},d.trim=function(e){return null==e?"":(e+"").replace(Nr,"$1")},void 0!==(o=function(){return d}.apply(l,[]))&&(r.exports=o);var Wr=i.jQuery,Ur=i.$;return d.noConflict=function(e){return i.$===d&&(i.$=Ur),e&&i.jQuery===d&&(i.jQuery=Wr),d},void 0===s&&(i.jQuery=i.$=d),d})},53251:(r,l,n)=>{"use strict";n.d(l,{Nh:()=>rt,SP:()=>rn,uX:()=>ct});var o=n(15664),i=n(17144),s=n(47429),u=n(69808),a=n(5e3),p=n(90508),v=n(76360),y=n(95698),g=n(68675),D=n(71884),b=n(82722),x=n(63900),w=n(35684),L=n(77579),R=n(50727),S=n(54968),F=n(39646),E=n(56451),U=n(60515),P=n(69751),V=n(82805),X=n(41777),ce=n(50226),d=n(63191),he=n(91159),ae=n(70925),I=n(29071);function Q(ie,de){1&ie&&a.Hsn(0)}const ue=["*"];function ee(ie,de){}const Le=function(ie){return{animationDuration:ie}},xe=function(ie,de){return{value:ie,params:de}},Ae=["tabListContainer"],Pe=["tabList"],Ke=["tabListInner"],te=["nextPaginator"],fe=["previousPaginator"],ge=["tabBodyWrapper"],be=["tabHeader"];function Se(ie,de){}function we(ie,de){if(1&ie&&a.YNc(0,Se,0,0,"ng-template",10),2&ie){const O=a.oxw().$implicit;a.Q6J("cdkPortalOutlet",O.templateLabel)}}function pe(ie,de){if(1&ie&&a._uU(0),2&ie){const O=a.oxw().$implicit;a.Oqu(O.textLabel)}}function He(ie,de){if(1&ie){const O=a.EpF();a.TgZ(0,"div",6),a.NdJ("click",function(){const G=a.CHM(O),oe=G.$implicit,Fe=G.index,Ye=a.oxw(),Xe=a.MAs(1);return Ye._handleClick(oe,Xe,Fe)})("cdkFocusChange",function(G){const Fe=a.CHM(O).index;return a.oxw()._tabFocusChanged(G,Fe)}),a.TgZ(1,"div",7),a.YNc(2,we,1,1,"ng-template",8),a.YNc(3,pe,1,1,"ng-template",null,9,a.W1O),a.qZA()()}if(2&ie){const O=de.$implicit,q=de.index,G=a.MAs(4),oe=a.oxw();a.ekj("mat-tab-label-active",oe.selectedIndex===q),a.Q6J("id",oe._getTabLabelId(q))("ngClass",O.labelClass)("disabled",O.disabled)("matRippleDisabled",O.disabled||oe.disableRipple),a.uIk("tabIndex",oe._getTabIndex(O,q))("aria-posinset",q+1)("aria-setsize",oe._tabs.length)("aria-controls",oe._getTabContentId(q))("aria-selected",oe.selectedIndex===q)("aria-label",O.ariaLabel||null)("aria-labelledby",!O.ariaLabel&&O.ariaLabelledby?O.ariaLabelledby:null),a.xp6(2),a.Q6J("ngIf",O.templateLabel)("ngIfElse",G)}}function Ne(ie,de){if(1&ie){const O=a.EpF();a.TgZ(0,"mat-tab-body",11),a.NdJ("_onCentered",function(){return a.CHM(O),a.oxw()._removeTabBodyWrapperHeight()})("_onCentering",function(G){return a.CHM(O),a.oxw()._setTabBodyWrapperHeight(G)}),a.qZA()}if(2&ie){const O=de.$implicit,q=de.index,G=a.oxw();a.ekj("mat-tab-body-active",G.selectedIndex===q),a.Q6J("id",G._getTabContentId(q))("ngClass",O.bodyClass)("content",O.content)("position",O.position)("origin",O.origin)("animationDuration",G.animationDuration),a.uIk("tabindex",null!=G.contentTabIndex&&G.selectedIndex===q?G.contentTabIndex:null)("aria-labelledby",G._getTabLabelId(q))}}const je=new a.OlP("MatInkBarPositioner",{providedIn:"root",factory:function qe(){return de=>({left:de?(de.offsetLeft||0)+"px":"0",width:de?(de.offsetWidth||0)+"px":"0"})}});let ze=(()=>{class ie{constructor(O,q,G,oe){this._elementRef=O,this._ngZone=q,this._inkBarPositioner=G,this._animationMode=oe}alignToElement(O){this.show(),this._ngZone.onStable.pipe((0,y.q)(1)).subscribe(()=>{const q=this._inkBarPositioner(O),G=this._elementRef.nativeElement;G.style.left=q.left,G.style.width=q.width})}show(){this._elementRef.nativeElement.style.visibility="visible"}hide(){this._elementRef.nativeElement.style.visibility="hidden"}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(a.R0b),a.Y36(je),a.Y36(v.Qb,8))},ie.\u0275dir=a.lG2({type:ie,selectors:[["mat-ink-bar"]],hostAttrs:[1,"mat-ink-bar"],hostVars:2,hostBindings:function(O,q){2&O&&a.ekj("_mat-animation-noopable","NoopAnimations"===q._animationMode)}}),ie})();const Ue=new a.OlP("MatTabContent"),Dt=new a.OlP("MatTabLabel"),gt=new a.OlP("MAT_TAB"),ht=(0,p.Id)(class{}),Ze=new a.OlP("MAT_TAB_GROUP");let ct=(()=>{class ie extends ht{constructor(O,q){super(),this._viewContainerRef=O,this._closestTabGroup=q,this.textLabel="",this._contentPortal=null,this._stateChanges=new L.x,this.position=null,this.origin=null,this.isActive=!1}get templateLabel(){return this._templateLabel}set templateLabel(O){this._setTemplateLabelInput(O)}get content(){return this._contentPortal}ngOnChanges(O){(O.hasOwnProperty("textLabel")||O.hasOwnProperty("disabled"))&&this._stateChanges.next()}ngOnDestroy(){this._stateChanges.complete()}ngOnInit(){this._contentPortal=new s.UE(this._explicitContent||this._implicitContent,this._viewContainerRef)}_setTemplateLabelInput(O){O&&O._closestTab===this&&(this._templateLabel=O)}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.s_b),a.Y36(Ze,8))},ie.\u0275cmp=a.Xpm({type:ie,selectors:[["mat-tab"]],contentQueries:function(O,q,G){if(1&O&&(a.Suo(G,Dt,5),a.Suo(G,Ue,7,a.Rgc)),2&O){let oe;a.iGM(oe=a.CRH())&&(q.templateLabel=oe.first),a.iGM(oe=a.CRH())&&(q._explicitContent=oe.first)}},viewQuery:function(O,q){if(1&O&&a.Gf(a.Rgc,7),2&O){let G;a.iGM(G=a.CRH())&&(q._implicitContent=G.first)}},inputs:{disabled:"disabled",textLabel:["label","textLabel"],ariaLabel:["aria-label","ariaLabel"],ariaLabelledby:["aria-labelledby","ariaLabelledby"],labelClass:"labelClass",bodyClass:"bodyClass"},exportAs:["matTab"],features:[a._Bn([{provide:gt,useExisting:ie}]),a.qOj,a.TTD],ngContentSelectors:ue,decls:1,vars:0,template:function(O,q){1&O&&(a.F$t(),a.YNc(0,Q,1,0,"ng-template"))},encapsulation:2}),ie})();const hn={translateTab:(0,X.X$)("translateTab",[(0,X.SB)("center, void, left-origin-center, right-origin-center",(0,X.oB)({transform:"none"})),(0,X.SB)("left",(0,X.oB)({transform:"translate3d(-100%, 0, 0)",minHeight:"1px"})),(0,X.SB)("right",(0,X.oB)({transform:"translate3d(100%, 0, 0)",minHeight:"1px"})),(0,X.eR)("* => left, * => right, left => center, right => center",(0,X.jt)("{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)")),(0,X.eR)("void => left-origin-center",[(0,X.oB)({transform:"translate3d(-100%, 0, 0)"}),(0,X.jt)("{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)")]),(0,X.eR)("void => right-origin-center",[(0,X.oB)({transform:"translate3d(100%, 0, 0)"}),(0,X.jt)("{{animationDuration}} cubic-bezier(0.35, 0, 0.25, 1)")])])};let pn=(()=>{class ie extends s.Pl{constructor(O,q,G,oe){super(O,q,oe),this._host=G,this._centeringSub=R.w0.EMPTY,this._leavingSub=R.w0.EMPTY}ngOnInit(){super.ngOnInit(),this._centeringSub=this._host._beforeCentering.pipe((0,g.O)(this._host._isCenterPosition(this._host._position))).subscribe(O=>{O&&!this.hasAttached()&&this.attach(this._host._content)}),this._leavingSub=this._host._afterLeavingCenter.subscribe(()=>{this.detach()})}ngOnDestroy(){super.ngOnDestroy(),this._centeringSub.unsubscribe(),this._leavingSub.unsubscribe()}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a._Vd),a.Y36(a.s_b),a.Y36((0,a.Gpc)(()=>wt)),a.Y36(u.K0))},ie.\u0275dir=a.lG2({type:ie,selectors:[["","matTabBodyHost",""]],features:[a.qOj]}),ie})(),lt=(()=>{class ie{constructor(O,q,G){this._elementRef=O,this._dir=q,this._dirChangeSubscription=R.w0.EMPTY,this._translateTabComplete=new L.x,this._onCentering=new a.vpe,this._beforeCentering=new a.vpe,this._afterLeavingCenter=new a.vpe,this._onCentered=new a.vpe(!0),this.animationDuration="500ms",q&&(this._dirChangeSubscription=q.change.subscribe(oe=>{this._computePositionAnimationState(oe),G.markForCheck()})),this._translateTabComplete.pipe((0,D.x)((oe,Fe)=>oe.fromState===Fe.fromState&&oe.toState===Fe.toState)).subscribe(oe=>{this._isCenterPosition(oe.toState)&&this._isCenterPosition(this._position)&&this._onCentered.emit(),this._isCenterPosition(oe.fromState)&&!this._isCenterPosition(this._position)&&this._afterLeavingCenter.emit()})}set position(O){this._positionIndex=O,this._computePositionAnimationState()}ngOnInit(){"center"==this._position&&null!=this.origin&&(this._position=this._computePositionFromOrigin(this.origin))}ngOnDestroy(){this._dirChangeSubscription.unsubscribe(),this._translateTabComplete.complete()}_onTranslateTabStarted(O){const q=this._isCenterPosition(O.toState);this._beforeCentering.emit(q),q&&this._onCentering.emit(this._elementRef.nativeElement.clientHeight)}_getLayoutDirection(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}_isCenterPosition(O){return"center"==O||"left-origin-center"==O||"right-origin-center"==O}_computePositionAnimationState(O=this._getLayoutDirection()){this._position=this._positionIndex<0?"ltr"==O?"left":"right":this._positionIndex>0?"ltr"==O?"right":"left":"center"}_computePositionFromOrigin(O){const q=this._getLayoutDirection();return"ltr"==q&&O<=0||"rtl"==q&&O>0?"left-origin-center":"right-origin-center"}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(ce.Is,8),a.Y36(a.sBO))},ie.\u0275dir=a.lG2({type:ie,inputs:{_content:["content","_content"],origin:"origin",animationDuration:"animationDuration",position:"position"},outputs:{_onCentering:"_onCentering",_beforeCentering:"_beforeCentering",_afterLeavingCenter:"_afterLeavingCenter",_onCentered:"_onCentered"}}),ie})(),wt=(()=>{class ie extends lt{constructor(O,q,G){super(O,q,G)}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(ce.Is,8),a.Y36(a.sBO))},ie.\u0275cmp=a.Xpm({type:ie,selectors:[["mat-tab-body"]],viewQuery:function(O,q){if(1&O&&a.Gf(s.Pl,5),2&O){let G;a.iGM(G=a.CRH())&&(q._portalHost=G.first)}},hostAttrs:[1,"mat-tab-body"],features:[a.qOj],decls:3,vars:6,consts:[["cdkScrollable","",1,"mat-tab-body-content"],["content",""],["matTabBodyHost",""]],template:function(O,q){1&O&&(a.TgZ(0,"div",0,1),a.NdJ("@translateTab.start",function(oe){return q._onTranslateTabStarted(oe)})("@translateTab.done",function(oe){return q._translateTabComplete.next(oe)}),a.YNc(2,ee,0,0,"ng-template",2),a.qZA()),2&O&&a.Q6J("@translateTab",a.WLB(3,xe,q._position,a.VKq(1,Le,q.animationDuration)))},directives:[pn],styles:['.mat-tab-body-content{height:100%;overflow:auto}.mat-tab-group-dynamic-height .mat-tab-body-content{overflow:hidden}.mat-tab-body-content[style*="visibility: hidden"]{display:none}\n'],encapsulation:2,data:{animation:[hn.translateTab]}}),ie})();const Pt=new a.OlP("MAT_TABS_CONFIG"),ye=(0,p.Id)(class{});let tt=(()=>{class ie extends ye{constructor(O){super(),this.elementRef=O}focus(){this.elementRef.nativeElement.focus()}getOffsetLeft(){return this.elementRef.nativeElement.offsetLeft}getOffsetWidth(){return this.elementRef.nativeElement.offsetWidth}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq))},ie.\u0275dir=a.lG2({type:ie,selectors:[["","matTabLabelWrapper",""]],hostVars:3,hostBindings:function(O,q){2&O&&(a.uIk("aria-disabled",!!q.disabled),a.ekj("mat-tab-disabled",q.disabled))},inputs:{disabled:"disabled"},features:[a.qOj]}),ie})();const Xt=(0,ae.i$)({passive:!0});let nn=(()=>{class ie{constructor(O,q,G,oe,Fe,Ye,Xe){this._elementRef=O,this._changeDetectorRef=q,this._viewportRuler=G,this._dir=oe,this._ngZone=Fe,this._platform=Ye,this._animationMode=Xe,this._scrollDistance=0,this._selectedIndexChanged=!1,this._destroyed=new L.x,this._showPaginationControls=!1,this._disableScrollAfter=!0,this._disableScrollBefore=!0,this._stopScrolling=new L.x,this.disablePagination=!1,this._selectedIndex=0,this.selectFocusedIndex=new a.vpe,this.indexFocused=new a.vpe,Fe.runOutsideAngular(()=>{(0,S.R)(O.nativeElement,"mouseleave").pipe((0,b.R)(this._destroyed)).subscribe(()=>{this._stopInterval()})})}get selectedIndex(){return this._selectedIndex}set selectedIndex(O){O=(0,d.su)(O),this._selectedIndex!=O&&(this._selectedIndexChanged=!0,this._selectedIndex=O,this._keyManager&&this._keyManager.updateActiveItem(O))}ngAfterViewInit(){(0,S.R)(this._previousPaginator.nativeElement,"touchstart",Xt).pipe((0,b.R)(this._destroyed)).subscribe(()=>{this._handlePaginatorPress("before")}),(0,S.R)(this._nextPaginator.nativeElement,"touchstart",Xt).pipe((0,b.R)(this._destroyed)).subscribe(()=>{this._handlePaginatorPress("after")})}ngAfterContentInit(){const O=this._dir?this._dir.change:(0,F.of)("ltr"),q=this._viewportRuler.change(150),G=()=>{this.updatePagination(),this._alignInkBarToSelectedTab()};this._keyManager=new o.Em(this._items).withHorizontalOrientation(this._getLayoutDirection()).withHomeAndEnd().withWrap(),this._keyManager.updateActiveItem(this._selectedIndex),this._ngZone.onStable.pipe((0,y.q)(1)).subscribe(G),(0,E.T)(O,q,this._items.changes,this._itemsResized()).pipe((0,b.R)(this._destroyed)).subscribe(()=>{this._ngZone.run(()=>{Promise.resolve().then(()=>{this._scrollDistance=Math.max(0,Math.min(this._getMaxScrollDistance(),this._scrollDistance)),G()})}),this._keyManager.withHorizontalOrientation(this._getLayoutDirection())}),this._keyManager.change.pipe((0,b.R)(this._destroyed)).subscribe(oe=>{this.indexFocused.emit(oe),this._setTabFocus(oe)})}_itemsResized(){return"function"!=typeof ResizeObserver?U.E:this._items.changes.pipe((0,g.O)(this._items),(0,x.w)(O=>new P.y(q=>this._ngZone.runOutsideAngular(()=>{const G=new ResizeObserver(()=>{q.next()});return O.forEach(oe=>{G.observe(oe.elementRef.nativeElement)}),()=>{G.disconnect()}}))),(0,w.T)(1))}ngAfterContentChecked(){this._tabLabelCount!=this._items.length&&(this.updatePagination(),this._tabLabelCount=this._items.length,this._changeDetectorRef.markForCheck()),this._selectedIndexChanged&&(this._scrollToLabel(this._selectedIndex),this._checkScrollingControls(),this._alignInkBarToSelectedTab(),this._selectedIndexChanged=!1,this._changeDetectorRef.markForCheck()),this._scrollDistanceChanged&&(this._updateTabScrollPosition(),this._scrollDistanceChanged=!1,this._changeDetectorRef.markForCheck())}ngOnDestroy(){this._destroyed.next(),this._destroyed.complete(),this._stopScrolling.complete()}_handleKeydown(O){if(!(0,he.Vb)(O))switch(O.keyCode){case he.K5:case he.L_:this.focusIndex!==this.selectedIndex&&(this.selectFocusedIndex.emit(this.focusIndex),this._itemSelected(O));break;default:this._keyManager.onKeydown(O)}}_onContentChanges(){const O=this._elementRef.nativeElement.textContent;O!==this._currentTextContent&&(this._currentTextContent=O||"",this._ngZone.run(()=>{this.updatePagination(),this._alignInkBarToSelectedTab(),this._changeDetectorRef.markForCheck()}))}updatePagination(){this._checkPaginationEnabled(),this._checkScrollingControls(),this._updateTabScrollPosition()}get focusIndex(){return this._keyManager?this._keyManager.activeItemIndex:0}set focusIndex(O){!this._isValidIndex(O)||this.focusIndex===O||!this._keyManager||this._keyManager.setActiveItem(O)}_isValidIndex(O){if(!this._items)return!0;const q=this._items?this._items.toArray()[O]:null;return!!q&&!q.disabled}_setTabFocus(O){if(this._showPaginationControls&&this._scrollToLabel(O),this._items&&this._items.length){this._items.toArray()[O].focus();const q=this._tabListContainer.nativeElement;q.scrollLeft="ltr"==this._getLayoutDirection()?0:q.scrollWidth-q.offsetWidth}}_getLayoutDirection(){return this._dir&&"rtl"===this._dir.value?"rtl":"ltr"}_updateTabScrollPosition(){if(this.disablePagination)return;const O=this.scrollDistance,q="ltr"===this._getLayoutDirection()?-O:O;this._tabList.nativeElement.style.transform=`translateX(${Math.round(q)}px)`,(this._platform.TRIDENT||this._platform.EDGE)&&(this._tabListContainer.nativeElement.scrollLeft=0)}get scrollDistance(){return this._scrollDistance}set scrollDistance(O){this._scrollTo(O)}_scrollHeader(O){return this._scrollTo(this._scrollDistance+("before"==O?-1:1)*this._tabListContainer.nativeElement.offsetWidth/3)}_handlePaginatorClick(O){this._stopInterval(),this._scrollHeader(O)}_scrollToLabel(O){if(this.disablePagination)return;const q=this._items?this._items.toArray()[O]:null;if(!q)return;const G=this._tabListContainer.nativeElement.offsetWidth,{offsetLeft:oe,offsetWidth:Fe}=q.elementRef.nativeElement;let Ye,Xe;"ltr"==this._getLayoutDirection()?(Ye=oe,Xe=Ye+Fe):(Xe=this._tabListInner.nativeElement.offsetWidth-oe,Ye=Xe-Fe);const Kt=this.scrollDistance,an=this.scrollDistance+G;Ye<Kt?this.scrollDistance-=Kt-Ye+60:Xe>an&&(this.scrollDistance+=Xe-an+60)}_checkPaginationEnabled(){if(this.disablePagination)this._showPaginationControls=!1;else{const O=this._tabListInner.nativeElement.scrollWidth>this._elementRef.nativeElement.offsetWidth;O||(this.scrollDistance=0),O!==this._showPaginationControls&&this._changeDetectorRef.markForCheck(),this._showPaginationControls=O}}_checkScrollingControls(){this.disablePagination?this._disableScrollAfter=this._disableScrollBefore=!0:(this._disableScrollBefore=0==this.scrollDistance,this._disableScrollAfter=this.scrollDistance==this._getMaxScrollDistance(),this._changeDetectorRef.markForCheck())}_getMaxScrollDistance(){return this._tabListInner.nativeElement.scrollWidth-this._tabListContainer.nativeElement.offsetWidth||0}_alignInkBarToSelectedTab(){const O=this._items&&this._items.length?this._items.toArray()[this.selectedIndex]:null,q=O?O.elementRef.nativeElement:null;q?this._inkBar.alignToElement(q):this._inkBar.hide()}_stopInterval(){this._stopScrolling.next()}_handlePaginatorPress(O,q){q&&null!=q.button&&0!==q.button||(this._stopInterval(),(0,V.H)(650,100).pipe((0,b.R)((0,E.T)(this._stopScrolling,this._destroyed))).subscribe(()=>{const{maxScrollDistance:G,distance:oe}=this._scrollHeader(O);(0===oe||oe>=G)&&this._stopInterval()}))}_scrollTo(O){if(this.disablePagination)return{maxScrollDistance:0,distance:0};const q=this._getMaxScrollDistance();return this._scrollDistance=Math.max(0,Math.min(q,O)),this._scrollDistanceChanged=!0,this._checkScrollingControls(),{maxScrollDistance:q,distance:this._scrollDistance}}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(a.sBO),a.Y36(I.rL),a.Y36(ce.Is,8),a.Y36(a.R0b),a.Y36(ae.t4),a.Y36(v.Qb,8))},ie.\u0275dir=a.lG2({type:ie,inputs:{disablePagination:"disablePagination"}}),ie})(),kt=(()=>{class ie extends nn{constructor(O,q,G,oe,Fe,Ye,Xe){super(O,q,G,oe,Fe,Ye,Xe),this._disableRipple=!1}get disableRipple(){return this._disableRipple}set disableRipple(O){this._disableRipple=(0,d.Ig)(O)}_itemSelected(O){O.preventDefault()}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(a.sBO),a.Y36(I.rL),a.Y36(ce.Is,8),a.Y36(a.R0b),a.Y36(ae.t4),a.Y36(v.Qb,8))},ie.\u0275dir=a.lG2({type:ie,inputs:{disableRipple:"disableRipple"},features:[a.qOj]}),ie})(),pt=(()=>{class ie extends kt{constructor(O,q,G,oe,Fe,Ye,Xe){super(O,q,G,oe,Fe,Ye,Xe)}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(a.sBO),a.Y36(I.rL),a.Y36(ce.Is,8),a.Y36(a.R0b),a.Y36(ae.t4),a.Y36(v.Qb,8))},ie.\u0275cmp=a.Xpm({type:ie,selectors:[["mat-tab-header"]],contentQueries:function(O,q,G){if(1&O&&a.Suo(G,tt,4),2&O){let oe;a.iGM(oe=a.CRH())&&(q._items=oe)}},viewQuery:function(O,q){if(1&O&&(a.Gf(ze,7),a.Gf(Ae,7),a.Gf(Pe,7),a.Gf(Ke,7),a.Gf(te,5),a.Gf(fe,5)),2&O){let G;a.iGM(G=a.CRH())&&(q._inkBar=G.first),a.iGM(G=a.CRH())&&(q._tabListContainer=G.first),a.iGM(G=a.CRH())&&(q._tabList=G.first),a.iGM(G=a.CRH())&&(q._tabListInner=G.first),a.iGM(G=a.CRH())&&(q._nextPaginator=G.first),a.iGM(G=a.CRH())&&(q._previousPaginator=G.first)}},hostAttrs:[1,"mat-tab-header"],hostVars:4,hostBindings:function(O,q){2&O&&a.ekj("mat-tab-header-pagination-controls-enabled",q._showPaginationControls)("mat-tab-header-rtl","rtl"==q._getLayoutDirection())},inputs:{selectedIndex:"selectedIndex"},outputs:{selectFocusedIndex:"selectFocusedIndex",indexFocused:"indexFocused"},features:[a.qOj],ngContentSelectors:ue,decls:14,vars:10,consts:[["aria-hidden","true","type","button","mat-ripple","","tabindex","-1",1,"mat-tab-header-pagination","mat-tab-header-pagination-before","mat-elevation-z4",3,"matRippleDisabled","disabled","click","mousedown","touchend"],["previousPaginator",""],[1,"mat-tab-header-pagination-chevron"],[1,"mat-tab-label-container",3,"keydown"],["tabListContainer",""],["role","tablist",1,"mat-tab-list",3,"cdkObserveContent"],["tabList",""],[1,"mat-tab-labels"],["tabListInner",""],["aria-hidden","true","type","button","mat-ripple","","tabindex","-1",1,"mat-tab-header-pagination","mat-tab-header-pagination-after","mat-elevation-z4",3,"matRippleDisabled","disabled","mousedown","click","touchend"],["nextPaginator",""]],template:function(O,q){1&O&&(a.F$t(),a.TgZ(0,"button",0,1),a.NdJ("click",function(){return q._handlePaginatorClick("before")})("mousedown",function(oe){return q._handlePaginatorPress("before",oe)})("touchend",function(){return q._stopInterval()}),a._UZ(2,"div",2),a.qZA(),a.TgZ(3,"div",3,4),a.NdJ("keydown",function(oe){return q._handleKeydown(oe)}),a.TgZ(5,"div",5,6),a.NdJ("cdkObserveContent",function(){return q._onContentChanges()}),a.TgZ(7,"div",7,8),a.Hsn(9),a.qZA(),a._UZ(10,"mat-ink-bar"),a.qZA()(),a.TgZ(11,"button",9,10),a.NdJ("mousedown",function(oe){return q._handlePaginatorPress("after",oe)})("click",function(){return q._handlePaginatorClick("after")})("touchend",function(){return q._stopInterval()}),a._UZ(13,"div",2),a.qZA()),2&O&&(a.ekj("mat-tab-header-pagination-disabled",q._disableScrollBefore),a.Q6J("matRippleDisabled",q._disableScrollBefore||q.disableRipple)("disabled",q._disableScrollBefore||null),a.xp6(5),a.ekj("_mat-animation-noopable","NoopAnimations"===q._animationMode),a.xp6(6),a.ekj("mat-tab-header-pagination-disabled",q._disableScrollAfter),a.Q6J("matRippleDisabled",q._disableScrollAfter||q.disableRipple)("disabled",q._disableScrollAfter||null))},directives:[p.wG,i.wD,ze],styles:[".mat-tab-header{display:flex;overflow:hidden;position:relative;flex-shrink:0}.mat-tab-header-pagination{-webkit-user-select:none;user-select:none;position:relative;display:none;justify-content:center;align-items:center;min-width:32px;cursor:pointer;z-index:2;-webkit-tap-highlight-color:transparent;touch-action:none;box-sizing:content-box;background:none;border:none;outline:0;padding:0}.mat-tab-header-pagination::-moz-focus-inner{border:0}.mat-tab-header-pagination-controls-enabled .mat-tab-header-pagination{display:flex}.mat-tab-header-pagination-before,.mat-tab-header-rtl .mat-tab-header-pagination-after{padding-left:4px}.mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-rtl .mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(-135deg)}.mat-tab-header-rtl .mat-tab-header-pagination-before,.mat-tab-header-pagination-after{padding-right:4px}.mat-tab-header-rtl .mat-tab-header-pagination-before .mat-tab-header-pagination-chevron,.mat-tab-header-pagination-after .mat-tab-header-pagination-chevron{transform:rotate(45deg)}.mat-tab-header-pagination-chevron{border-style:solid;border-width:2px 2px 0 0;height:8px;width:8px}.mat-tab-header-pagination-disabled{box-shadow:none;cursor:default}.mat-tab-list{flex-grow:1;position:relative;transition:transform 500ms cubic-bezier(0.35, 0, 0.25, 1)}.mat-ink-bar{position:absolute;bottom:0;height:2px;transition:500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable.mat-ink-bar{transition:none;animation:none}.mat-tab-group-inverted-header .mat-ink-bar{bottom:auto;top:0}.cdk-high-contrast-active .mat-ink-bar{outline:solid 2px;height:0}.mat-tab-labels{display:flex}[mat-align-tabs=center]>.mat-tab-header .mat-tab-labels{justify-content:center}[mat-align-tabs=end]>.mat-tab-header .mat-tab-labels{justify-content:flex-end}.mat-tab-label-container{display:flex;flex-grow:1;overflow:hidden;z-index:1}._mat-animation-noopable.mat-tab-list{transition:none;animation:none}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.cdk-high-contrast-active .mat-tab-label:focus{outline:dotted 2px;outline-offset:-2px}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{min-width:72px}}\n"],encapsulation:2}),ie})(),Mt=0;class Ot{}const mn=(0,p.pj)((0,p.Kr)(class{constructor(ie){this._elementRef=ie}}),"primary");let Nt=(()=>{class ie extends mn{constructor(O,q,G,oe){var Fe;super(O),this._changeDetectorRef=q,this._animationMode=oe,this._tabs=new a.n_E,this._indexToSelect=0,this._lastFocusedTabIndex=null,this._tabBodyWrapperHeight=0,this._tabsSubscription=R.w0.EMPTY,this._tabLabelSubscription=R.w0.EMPTY,this._selectedIndex=null,this.headerPosition="above",this.selectedIndexChange=new a.vpe,this.focusChange=new a.vpe,this.animationDone=new a.vpe,this.selectedTabChange=new a.vpe(!0),this._groupId=Mt++,this.animationDuration=G&&G.animationDuration?G.animationDuration:"500ms",this.disablePagination=!(!G||null==G.disablePagination)&&G.disablePagination,this.dynamicHeight=!(!G||null==G.dynamicHeight)&&G.dynamicHeight,this.contentTabIndex=null!==(Fe=null==G?void 0:G.contentTabIndex)&&void 0!==Fe?Fe:null}get dynamicHeight(){return this._dynamicHeight}set dynamicHeight(O){this._dynamicHeight=(0,d.Ig)(O)}get selectedIndex(){return this._selectedIndex}set selectedIndex(O){this._indexToSelect=(0,d.su)(O,null)}get animationDuration(){return this._animationDuration}set animationDuration(O){this._animationDuration=/^\d+$/.test(O+"")?O+"ms":O}get contentTabIndex(){return this._contentTabIndex}set contentTabIndex(O){this._contentTabIndex=(0,d.su)(O,null)}get backgroundColor(){return this._backgroundColor}set backgroundColor(O){const q=this._elementRef.nativeElement;q.classList.remove(`mat-background-${this.backgroundColor}`),O&&q.classList.add(`mat-background-${O}`),this._backgroundColor=O}ngAfterContentChecked(){const O=this._indexToSelect=this._clampTabIndex(this._indexToSelect);if(this._selectedIndex!=O){const q=null==this._selectedIndex;if(!q){this.selectedTabChange.emit(this._createChangeEvent(O));const G=this._tabBodyWrapper.nativeElement;G.style.minHeight=G.clientHeight+"px"}Promise.resolve().then(()=>{this._tabs.forEach((G,oe)=>G.isActive=oe===O),q||(this.selectedIndexChange.emit(O),this._tabBodyWrapper.nativeElement.style.minHeight="")})}this._tabs.forEach((q,G)=>{q.position=G-O,null!=this._selectedIndex&&0==q.position&&!q.origin&&(q.origin=O-this._selectedIndex)}),this._selectedIndex!==O&&(this._selectedIndex=O,this._lastFocusedTabIndex=null,this._changeDetectorRef.markForCheck())}ngAfterContentInit(){this._subscribeToAllTabChanges(),this._subscribeToTabLabels(),this._tabsSubscription=this._tabs.changes.subscribe(()=>{const O=this._clampTabIndex(this._indexToSelect);if(O===this._selectedIndex){const q=this._tabs.toArray();let G;for(let oe=0;oe<q.length;oe++)if(q[oe].isActive){this._indexToSelect=this._selectedIndex=oe,this._lastFocusedTabIndex=null,G=q[oe];break}!G&&q[O]&&Promise.resolve().then(()=>{q[O].isActive=!0,this.selectedTabChange.emit(this._createChangeEvent(O))})}this._changeDetectorRef.markForCheck()})}_subscribeToAllTabChanges(){this._allTabs.changes.pipe((0,g.O)(this._allTabs)).subscribe(O=>{this._tabs.reset(O.filter(q=>q._closestTabGroup===this||!q._closestTabGroup)),this._tabs.notifyOnChanges()})}ngOnDestroy(){this._tabs.destroy(),this._tabsSubscription.unsubscribe(),this._tabLabelSubscription.unsubscribe()}realignInkBar(){this._tabHeader&&this._tabHeader._alignInkBarToSelectedTab()}updatePagination(){this._tabHeader&&this._tabHeader.updatePagination()}focusTab(O){const q=this._tabHeader;q&&(q.focusIndex=O)}_focusChanged(O){this._lastFocusedTabIndex=O,this.focusChange.emit(this._createChangeEvent(O))}_createChangeEvent(O){const q=new Ot;return q.index=O,this._tabs&&this._tabs.length&&(q.tab=this._tabs.toArray()[O]),q}_subscribeToTabLabels(){this._tabLabelSubscription&&this._tabLabelSubscription.unsubscribe(),this._tabLabelSubscription=(0,E.T)(...this._tabs.map(O=>O._stateChanges)).subscribe(()=>this._changeDetectorRef.markForCheck())}_clampTabIndex(O){return Math.min(this._tabs.length-1,Math.max(O||0,0))}_getTabLabelId(O){return`mat-tab-label-${this._groupId}-${O}`}_getTabContentId(O){return`mat-tab-content-${this._groupId}-${O}`}_setTabBodyWrapperHeight(O){if(!this._dynamicHeight||!this._tabBodyWrapperHeight)return;const q=this._tabBodyWrapper.nativeElement;q.style.height=this._tabBodyWrapperHeight+"px",this._tabBodyWrapper.nativeElement.offsetHeight&&(q.style.height=O+"px")}_removeTabBodyWrapperHeight(){const O=this._tabBodyWrapper.nativeElement;this._tabBodyWrapperHeight=O.clientHeight,O.style.height="",this.animationDone.emit()}_handleClick(O,q,G){O.disabled||(this.selectedIndex=q.focusIndex=G)}_getTabIndex(O,q){var G;return O.disabled?null:q===(null!==(G=this._lastFocusedTabIndex)&&void 0!==G?G:this.selectedIndex)?0:-1}_tabFocusChanged(O,q){O&&"mouse"!==O&&"touch"!==O&&(this._tabHeader.focusIndex=q)}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(a.sBO),a.Y36(Pt,8),a.Y36(v.Qb,8))},ie.\u0275dir=a.lG2({type:ie,inputs:{dynamicHeight:"dynamicHeight",selectedIndex:"selectedIndex",headerPosition:"headerPosition",animationDuration:"animationDuration",contentTabIndex:"contentTabIndex",disablePagination:"disablePagination",backgroundColor:"backgroundColor"},outputs:{selectedIndexChange:"selectedIndexChange",focusChange:"focusChange",animationDone:"animationDone",selectedTabChange:"selectedTabChange"},features:[a.qOj]}),ie})(),rn=(()=>{class ie extends Nt{constructor(O,q,G,oe){super(O,q,G,oe)}}return ie.\u0275fac=function(O){return new(O||ie)(a.Y36(a.SBq),a.Y36(a.sBO),a.Y36(Pt,8),a.Y36(v.Qb,8))},ie.\u0275cmp=a.Xpm({type:ie,selectors:[["mat-tab-group"]],contentQueries:function(O,q,G){if(1&O&&a.Suo(G,ct,5),2&O){let oe;a.iGM(oe=a.CRH())&&(q._allTabs=oe)}},viewQuery:function(O,q){if(1&O&&(a.Gf(ge,5),a.Gf(be,5)),2&O){let G;a.iGM(G=a.CRH())&&(q._tabBodyWrapper=G.first),a.iGM(G=a.CRH())&&(q._tabHeader=G.first)}},hostAttrs:[1,"mat-tab-group"],hostVars:4,hostBindings:function(O,q){2&O&&a.ekj("mat-tab-group-dynamic-height",q.dynamicHeight)("mat-tab-group-inverted-header","below"===q.headerPosition)},inputs:{color:"color",disableRipple:"disableRipple"},exportAs:["matTabGroup"],features:[a._Bn([{provide:Ze,useExisting:ie}]),a.qOj],decls:6,vars:7,consts:[[3,"selectedIndex","disableRipple","disablePagination","indexFocused","selectFocusedIndex"],["tabHeader",""],["class","mat-tab-label mat-focus-indicator","role","tab","matTabLabelWrapper","","mat-ripple","","cdkMonitorElementFocus","",3,"id","mat-tab-label-active","ngClass","disabled","matRippleDisabled","click","cdkFocusChange",4,"ngFor","ngForOf"],[1,"mat-tab-body-wrapper"],["tabBodyWrapper",""],["role","tabpanel",3,"id","mat-tab-body-active","ngClass","content","position","origin","animationDuration","_onCentered","_onCentering",4,"ngFor","ngForOf"],["role","tab","matTabLabelWrapper","","mat-ripple","","cdkMonitorElementFocus","",1,"mat-tab-label","mat-focus-indicator",3,"id","ngClass","disabled","matRippleDisabled","click","cdkFocusChange"],[1,"mat-tab-label-content"],[3,"ngIf","ngIfElse"],["tabTextLabel",""],[3,"cdkPortalOutlet"],["role","tabpanel",3,"id","ngClass","content","position","origin","animationDuration","_onCentered","_onCentering"]],template:function(O,q){1&O&&(a.TgZ(0,"mat-tab-header",0,1),a.NdJ("indexFocused",function(oe){return q._focusChanged(oe)})("selectFocusedIndex",function(oe){return q.selectedIndex=oe}),a.YNc(2,He,5,15,"div",2),a.qZA(),a.TgZ(3,"div",3,4),a.YNc(5,Ne,1,10,"mat-tab-body",5),a.qZA()),2&O&&(a.Q6J("selectedIndex",q.selectedIndex||0)("disableRipple",q.disableRipple)("disablePagination",q.disablePagination),a.xp6(2),a.Q6J("ngForOf",q._tabs),a.xp6(1),a.ekj("_mat-animation-noopable","NoopAnimations"===q._animationMode),a.xp6(2),a.Q6J("ngForOf",q._tabs))},directives:[pt,wt,u.sg,tt,p.wG,o.kH,u.mk,u.O5,s.Pl],styles:[".mat-tab-group{display:flex;flex-direction:column;max-width:100%}.mat-tab-group.mat-tab-group-inverted-header{flex-direction:column-reverse}.mat-tab-label{height:48px;padding:0 24px;cursor:pointer;box-sizing:border-box;opacity:.6;min-width:160px;text-align:center;display:inline-flex;justify-content:center;align-items:center;white-space:nowrap;position:relative}.mat-tab-label:focus{outline:none}.mat-tab-label:focus:not(.mat-tab-disabled){opacity:1}.cdk-high-contrast-active .mat-tab-label:focus{outline:dotted 2px;outline-offset:-2px}.mat-tab-label.mat-tab-disabled{cursor:default}.cdk-high-contrast-active .mat-tab-label.mat-tab-disabled{opacity:.5}.mat-tab-label .mat-tab-label-content{display:inline-flex;justify-content:center;align-items:center;white-space:nowrap}.cdk-high-contrast-active .mat-tab-label{opacity:1}@media(max-width: 599px){.mat-tab-label{padding:0 12px}}@media(max-width: 959px){.mat-tab-label{padding:0 12px}}.mat-tab-group[mat-stretch-tabs]>.mat-tab-header .mat-tab-label{flex-basis:0;flex-grow:1}.mat-tab-body-wrapper{position:relative;overflow:hidden;display:flex;transition:height 500ms cubic-bezier(0.35, 0, 0.25, 1)}._mat-animation-noopable.mat-tab-body-wrapper{transition:none;animation:none}.mat-tab-body{top:0;left:0;right:0;bottom:0;position:absolute;display:block;overflow:hidden;outline:0;flex-basis:100%}.mat-tab-body.mat-tab-body-active{position:relative;overflow-x:hidden;overflow-y:auto;z-index:1;flex-grow:1}.mat-tab-group.mat-tab-group-dynamic-height .mat-tab-body.mat-tab-body-active{overflow-y:hidden}\n"],encapsulation:2}),ie})(),rt=(()=>{class ie{}return ie.\u0275fac=function(O){return new(O||ie)},ie.\u0275mod=a.oAB({type:ie}),ie.\u0275inj=a.cJS({imports:[[u.ez,p.BQ,s.eL,p.si,i.Q8,o.rt],p.BQ]}),ie})()},63372:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{N:()=>CCSignatureModule,z:()=>CCSignatureComponent});var _angular_common__WEBPACK_IMPORTED_MODULE_11__=__webpack_require__(69808),_angular_core__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(5e3),_angular_forms__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(93075),_angular_material_button__WEBPACK_IMPORTED_MODULE_14__=__webpack_require__(47423),_angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__=__webpack_require__(67322),_angular_material_input__WEBPACK_IMPORTED_MODULE_16__=__webpack_require__(98833),_angular_material_tabs__WEBPACK_IMPORTED_MODULE_12__=__webpack_require__(53251),_maids_cc_lib_common__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(88476),_maids_cc_lib_validation__WEBPACK_IMPORTED_MODULE_15__=__webpack_require__(58015),_ngx_translate_core__WEBPACK_IMPORTED_MODULE_17__=__webpack_require__(51062),jquery__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(58259),rxjs__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(77579),rxjs__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(4707),rxjs_operators__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(82722),rxjs_operators__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(50590),rxjs_operators__WEBPACK_IMPORTED_MODULE_8__=__webpack_require__(63900),rxjs_operators__WEBPACK_IMPORTED_MODULE_9__=__webpack_require__(78372),_angular_cdk_bidi__WEBPACK_IMPORTED_MODULE_10__=__webpack_require__(50226);const _c0=["visibleCanvas"],_c1=["tabGroup"];window.JSON||(window.JSON={});var json2=function(){function f(r){return r<10?"0"+r:r}"function"!=typeof Date.prototype.toJSON&&(Date.prototype.toJSON=function(r){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(r){return this.valueOf()});var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;function quote(r){return escapable.lastIndex=0,escapable.test(r)?'"'+r.replace(escapable,function(l){var n=meta[l];return"string"==typeof n?n:"\\u"+("0000"+l.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+r+'"'}function str(r,l){var n,o,i,s,a,u=gap,p=l[r];switch(p&&"object"==typeof p&&"function"==typeof p.toJSON&&(p=p.toJSON(r)),"function"==typeof rep&&(p=rep.call(l,r,p)),typeof p){case"string":return quote(p);case"number":return isFinite(p)?String(p):"null";case"boolean":case"null":return String(p);case"object":if(!p)return"null";if(gap+=indent,a=[],"[object Array]"===Object.prototype.toString.apply(p)){for(s=p.length,n=0;n<s;n+=1)a[n]=str(n,p)||"null";return i=0===a.length?"[]":gap?"[\n"+gap+a.join(",\n"+gap)+"\n"+u+"]":"["+a.join(",")+"]",gap=u,i}if(rep&&"object"==typeof rep)for(s=rep.length,n=0;n<s;n+=1)"string"==typeof(o=rep[n])&&(i=str(o,p))&&a.push(quote(o)+(gap?": ":":")+i);else for(o in p)Object.hasOwnProperty.call(p,o)&&(i=str(o,p))&&a.push(quote(o)+(gap?": ":":")+i);return i=0===a.length?"{}":gap?"{\n"+gap+a.join(",\n"+gap)+"\n"+u+"}":"{"+a.join(",")+"}",gap=u,i}}"function"!=typeof JSON.stringify&&(JSON.stringify=function(r,l,n){var o;if(gap="",indent="","number"==typeof n)for(o=0;o<n;o+=1)indent+=" ";else"string"==typeof n&&(indent=n);if(rep=l,l&&"function"!=typeof l&&("object"!=typeof l||"number"!=typeof l.length))throw new Error("JSON.stringify");return str("",{"":r})}),"function"!=typeof JSON.parse&&(JSON.parse=function(text,reviver){var j;function walk(r,l){var n,o,i=r[l];if(i&&"object"==typeof i)for(n in i)Object.hasOwnProperty.call(i,n)&&(void 0!==(o=walk(i,n))?i[n]=o:delete i[n]);return reviver.call(r,l,i)}if(cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,function(r){return"\\u"+("0000"+r.charCodeAt(0).toString(16)).slice(-4)})),/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),"function"==typeof reviver?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}();const stats=function(r){for(var o,l={mean:0,variance:0,deviation:0},n=r.length,i=0,s=n;s--;i+=r[s]);for(o=l.mean=i/n,s=n,i=0;s--;i+=Math.pow(r[s]-o,2));return l.deviation=Math.sqrt(l.variance=i/n),l};var generate141Matrix=function(r){let l=[];for(var n=0;n<r;n++){for(var o=[],i=0;i<r;i++)i==n?o.push(4):1==Math.abs(n-i)?o.push(1):o.push(0);l.push(o)}return l},generateConstantMatrix=function(r){let l=[];l.push(numeric.sub(numeric.mul(r[1],6),r[0]));for(let n=2;n<r.length-2;n++)l.push(numeric.mul(r[n],6));return l.push(numeric.sub(numeric.mul(r[r.length-2],6),r[r.length-1])),l},convertBSplineControlPointsToBezierControlPoints=function(r){for(var l=[],n=0;n<r.length-1;n++){if(0==n)var o=r[0];else o=u;var i=numeric.add(numeric.mul(2/3,r[n]),numeric.mul(1/3,r[n+1])),s=numeric.add(numeric.mul(1/3,r[n]),numeric.mul(2/3,r[n+1]));if(n==r.length-2)var u=r[r.length-1];else u=numeric.add(numeric.mul(.16666666666666666,r[n]),numeric.mul(.6666666666666666,r[n+1]),numeric.mul(.16666666666666666,r[n+2]));l.push([o,i,s,u])}return l};const getBezierControlPoints=function(r){if(r.length<4){if(3===r.length)return[i=[r[0],r[1],r[1],r[2]]];if(2===r.length)return[i=[r[0],r[0],r[1],r[1]]];if(1===r.length)return[i=[r[0],r[0],r[0],r[0]]]}var l=generate141Matrix(r.length-2),n=generateConstantMatrix(r),o=numeric.dot(numeric.inv(l),n);o.splice(0,0,r[0]),o.push(r[r.length-1]);var i=convertBSplineControlPointsToBezierControlPoints(o);return i};var numeric$1="undefined"==typeof exports?function(){}:exports;function initModule(r){function l(n,o){var i=this,s=r.extend({},r.fn.signaturePad.defaults,o),u=r(n),a=r(s.canvas,u),p=a.get(0),v=null,y={x:null,y:null},g=[],D=!1,b=!1,x=!1,w=!1,L=30,R=L,S=0;function F(){clearTimeout(D),D=!1,b=!1}function E(te,fe){var ge,be,Se;if(te.preventDefault(),ge=r(te.target).offset(),clearTimeout(D),D=!1,void 0!==te.targetTouches?(be=Math.floor(te.targetTouches[0].pageX-ge.left),Se=Math.floor(te.targetTouches[0].pageY-ge.top)):(be=Math.floor(te.pageX-ge.left),Se=Math.floor(te.pageY-ge.top)),y.x===be&&y.y===Se)return!0;null===y.x&&(y.x=be),null===y.y&&(y.y=Se),fe&&(Se+=fe),v.beginPath(),v.moveTo(y.x,y.y),v.lineTo(be,Se),v.lineCap=s.penCap,v.stroke(),v.closePath(),g.push({lx:be,ly:Se,mx:y.x,my:y.y}),y.x=be,y.y=Se,s.onDraw&&"function"==typeof s.onDraw&&s.onDraw.apply(i)}function U(){P()}function P(te){te?E(te,1):(x?a.each(function(){this.removeEventListener("touchmove",E)}):a.unbind("mousemove.signaturepad"),g.length>0&&s.onDrawEnd&&"function"==typeof s.onDrawEnd&&s.onDrawEnd.apply(i)),y.x=null,y.y=null,s.output&&g.length>0&&r(s.output,u).val(JSON.stringify(g))}function X(){v.clearRect(0,0,p.width,p.height),v.fillStyle=s.bgColour,v.fillRect(0,0,p.width,p.height),s.displayOnly||function V(){if(!s.lineWidth)return!1;v.beginPath(),v.lineWidth=s.lineWidth,v.strokeStyle=s.lineColour,v.moveTo(s.lineMargin,s.lineTop),v.lineTo(p.width-s.lineMargin,s.lineTop),v.stroke(),v.closePath()}(),v.lineWidth=s.penWidth,v.strokeStyle=s.penColour,r(s.output,u).val(""),g=[],P()}function ce(te,fe){E(te,null==y.x?1:fe)}function d(te,fe){x?fe.addEventListener("touchmove",ce,!1):a.bind("mousemove.signaturepad",ce),E(te,1)}function ae(te){if(w)return!1;w=!0,r("input").blur(),void 0!==te.targetTouches&&(x=!0),x?(a.each(function(){this.addEventListener("touchend",U,!1),this.addEventListener("touchcancel",U,!1)}),a.unbind("mousedown.signaturepad")):(r(document).bind("mouseup.signaturepad",function(){b&&(P(),F())}),a.bind("mouseleave.signaturepad",function(fe){b&&P(fe),b&&!D&&(D=setTimeout(function(){P(),F()},500))}),a.each(function(){this.removeEventListener("touchstart",I)}))}function I(te){te.preventDefault(),b=!0,ae(te),d(te,this)}function Q(){r(s.typed,u).hide(),X(),a.each(function(){this.addEventListener("touchstart",I)}),a.bind("mousedown.signaturepad",function(te){if(te.preventDefault(),te.which>1)return!1;b=!0,ae(te),d(te)}),r(s.clear,u).bind("click.signaturepad",function(te){te.preventDefault(),X()}),r(s.typeIt,u).bind("click.signaturepad",function(te){te.preventDefault(),ue()}),r(s.drawIt,u).unbind("click.signaturepad"),r(s.drawIt,u).bind("click.signaturepad",function(te){te.preventDefault()}),r(s.typeIt,u).removeClass(s.currentClass),r(s.drawIt,u).addClass(s.currentClass),r(s.sig,u).addClass(s.currentClass),r(s.typeItDesc,u).hide(),r(s.drawItDesc,u).show(),r(s.clear,u).show()}function ue(){X(),function he(){w=!1,a.each(function(){this.removeEventListener&&(this.removeEventListener("touchend",U),this.removeEventListener("touchcancel",U),this.removeEventListener("touchmove",E),this.removeEventListener("touchstart",I))}),r(document).unbind("mouseup.signaturepad"),a.unbind("mousedown.signaturepad"),a.unbind("mousemove.signaturepad"),a.unbind("mouseleave.signaturepad"),r(s.clear,u).unbind("click.signaturepad")}(),r(s.typed,u).show(),r(s.drawIt,u).bind("click.signaturepad",function(te){te.preventDefault(),Q()}),r(s.typeIt,u).unbind("click.signaturepad"),r(s.typeIt,u).bind("click.signaturepad",function(te){te.preventDefault()}),r(s.output,u).val(""),r(s.drawIt,u).removeClass(s.currentClass),r(s.typeIt,u).addClass(s.currentClass),r(s.sig,u).removeClass(s.currentClass),r(s.drawItDesc,u).hide(),r(s.clear,u).hide(),r(s.typeItDesc,u).show(),R=L=r(s.typed,u).css("font-size").replace(/px/,"")}function ee(te){var fe=r(s.typed,u),ge=r.trim(te.replace(/>/g,"&gt;").replace(/</g,"&lt;")),be=S,Se=.5*R;if(S=ge.length,fe.html(ge),ge){if(S>be&&fe.outerWidth()>p.width)for(;R>4&&fe.outerWidth()>p.width;)R--,fe.css("font-size",R+"px");if(S<be&&fe.outerWidth()+Se<p.width&&R<L)for(;R<512&&fe.outerWidth()+Se<p.width&&R<L;)R++,fe.css("font-size",R+"px")}else fe.css("font-size",L+"px")}function Le(te,fe){r("p."+fe.errorClass,te).remove(),te.removeClass(fe.errorClass),r("input, label",te).removeClass(fe.errorClass)}function xe(te,fe,ge){te.nameInvalid&&(fe.prepend(['<p class="',ge.errorClass,'">',ge.errorMessage,"</p>"].join("")),r(ge.name,fe).focus(),r(ge.name,fe).addClass(ge.errorClass),r("label[for="+r(ge.name).attr("id")+"]",fe).addClass(ge.errorClass)),te.drawInvalid&&fe.prepend(['<p class="',ge.errorClass,'">',ge.errorMessageDraw,"</p>"].join(""))}function Ae(){var te=!0,fe={drawInvalid:!1,nameInvalid:!1},ge=[u,s],be=[fe,u,s];return s.onBeforeValidate&&"function"==typeof s.onBeforeValidate?s.onBeforeValidate.apply(i,ge):Le.apply(i,ge),s.drawOnly&&g.length<1&&(fe.drawInvalid=!0,te=!1),""===r(s.name,u).val()&&(fe.nameInvalid=!0,te=!1),s.onFormError&&"function"==typeof s.onFormError?s.onFormError.apply(i,be):xe.apply(i,be),te}function Pe(te,fe,ge){for(var be in te)"object"==typeof te[be]&&(fe.beginPath(),fe.moveTo(te[be].mx,te[be].my),fe.lineTo(te[be].lx,te[be].ly),fe.lineCap=s.penCap,fe.stroke(),fe.closePath(),ge&&te[be].lx&&g.push({lx:te[be].lx,ly:te[be].ly,mx:te[be].mx,my:te[be].my}))}r.extend(i,{signaturePad:"{{version}}",init:function(){!function Ke(){parseFloat((/CPU.+OS ([0-9_]{3}).*AppleWebkit.*Mobile/i.exec(navigator.userAgent)||[0,"4_2"])[1].replace("_","."))<4.1&&(r.fn.Oldoffset=r.fn.offset,r.fn.offset=function(){var te=r(this).Oldoffset();return te.top-=window.scrollY,te.left-=window.scrollX,te}),r(s.typed,u).bind("selectstart.signaturepad",function(te){return r(te.target).is(":input")}),a.bind("selectstart.signaturepad",function(te){return r(te.target).is(":input")}),!p.getContext&&FlashCanvas&&FlashCanvas.initElement(p),p.getContext&&(v=p.getContext("2d"),r(s.sig,u).show(),s.displayOnly||(s.drawOnly||(r(s.name,u).bind("keyup.signaturepad",function(){ee(r(this).val())}),r(s.name,u).bind("blur.signaturepad",function(){ee(r(this).val())}),r(s.drawIt,u).bind("click.signaturepad",function(te){te.preventDefault(),Q()})),s.drawOnly||"drawIt"===s.defaultAction?Q():ue(),s.validateFields&&(r(n).is("form")?r(n).bind("submit.signaturepad",function(){return Ae()}):r(n).parents("form").bind("submit.signaturepad",function(){return Ae()})),r(s.sigNav,u).show()))}()},updateOptions:function(te){r.extend(s,te)},regenerate:function(te){i.clearCanvas(),r(s.typed,u).hide(),"string"==typeof te&&(te=JSON.parse(te)),Pe(te,v,!0),s.output&&r(s.output,u).length>0&&r(s.output,u).val(JSON.stringify(g))},clearCanvas:function(){X()},getSignature:function(){return g},getSignatureString:function(){return JSON.stringify(g)},getSignatureImage:function(){var te=document.createElement("canvas"),fe=null,ge=null;return te.style.position="absolute",te.style.top="-999em",te.width=p.width,te.height=p.height,document.body.appendChild(te),!te.getContext&&FlashCanvas&&FlashCanvas.initElement(te),(fe=te.getContext("2d")).fillStyle=s.bgColour,fe.fillRect(0,0,p.width,p.height),fe.lineWidth=s.penWidth,fe.strokeStyle=s.penColour,Pe(g,fe),ge=te.toDataURL.apply(te,arguments),document.body.removeChild(te),te=null,ge},validateForm:function(){return Ae()}})}r.fn.signaturePad=function(n){var o=null;return this.each(function(){r.data(this,"plugin-signaturePad")?(o=r.data(this,"plugin-signaturePad")).updateOptions(n):((o=new l(this,n)).init(),r.data(this,"plugin-signaturePad",o))}),o},r.fn.signaturePad.defaults={defaultAction:"typeIt",displayOnly:!1,drawOnly:!1,canvas:"canvas",sig:".sig",sigNav:".sigNav",bgColour:"#ffffff",penColour:"#145394",penWidth:2,penCap:"round",lineColour:"#ccc",lineWidth:2,lineMargin:5,lineTop:35,name:".name",typed:".typed",clear:".clearButton",typeIt:".typeIt a",drawIt:".drawIt a",typeItDesc:".typeItDesc",drawItDesc:".drawItDesc",output:".output",currentClass:"current",validateFields:!0,errorClass:"error",errorMessage:"Please enter your name",errorMessageDraw:"Please sign the document",onBeforeValidate:null,onFormError:null,onDraw:null,onDrawEnd:null,scale:[1,1],autoscale:!1,drawBezierCurves:!1,variableStrokeWidth:!1,bezierSkip:4}}"undefined"!=typeof global&&(global.numeric=numeric$1),numeric$1.version="1.2.6",numeric$1.bench=function(r,l){var n,i,s;for(void 0===l&&(l=15),i=.5,n=new Date;;){for(s=i*=2;s>3;s-=4)r(),r(),r(),r();for(;s>0;)r(),s--;if(new Date-n>l)break}for(s=i;s>3;s-=4)r(),r(),r(),r();for(;s>0;)r(),s--;return 1e3*(3*i-1)/(new Date-n)},numeric$1._myIndexOf=function(r){var n,l=this.length;for(n=0;n<l;++n)if(this[n]===r)return n;return-1},numeric$1.myIndexOf=Array.prototype.indexOf?Array.prototype.indexOf:numeric$1._myIndexOf,numeric$1.Function=Function,numeric$1.precision=4,numeric$1.largeArray=50,numeric$1.prettyPrint=function(r){function l(i){if(0===i)return"0";if(isNaN(i))return"NaN";if(i<0)return"-"+l(-i);if(isFinite(i)){var s=Math.floor(Math.log(i)/Math.log(10)),u=i/Math.pow(10,s),a=u.toPrecision(numeric$1.precision);return 10===parseFloat(a)&&(s++,a=(u=1).toPrecision(numeric$1.precision)),parseFloat(a).toString()+"e"+s.toString()}return"Infinity"}var o=[];return function n(i){var s;if(void 0===i)return o.push(Array(numeric$1.precision+8).join(" ")),!1;if("string"==typeof i)return o.push('"'+i+'"'),!1;if("boolean"==typeof i)return o.push(i.toString()),!1;if("number"==typeof i){var u=l(i),a=i.toPrecision(numeric$1.precision),p=parseFloat(i.toString()).toString(),v=[u,a,p,parseFloat(a).toString(),parseFloat(p).toString()];for(s=1;s<v.length;s++)v[s].length<u.length&&(u=v[s]);return o.push(Array(numeric$1.precision+8-u.length).join(" ")+u),!1}if(null===i)return o.push("null"),!1;if("function"==typeof i){o.push(i.toString());var y=!1;for(s in i)i.hasOwnProperty(s)&&(o.push(y?",\n":"\n{"),y=!0,o.push(s),o.push(": \n"),n(i[s]));return y&&o.push("}\n"),!0}if(i instanceof Array){if(i.length>numeric$1.largeArray)return o.push("...Large Array..."),!0;for(y=!1,o.push("["),s=0;s<i.length;s++)s>0&&(o.push(","),y&&o.push("\n ")),y=n(i[s]);return o.push("]"),!0}for(s in o.push("{"),y=!1,i)i.hasOwnProperty(s)&&(y&&o.push(",\n"),y=!0,o.push(s),o.push(": \n"),n(i[s]));return o.push("}"),!0}(r),o.join("")},numeric$1.parseDate=function(r){return function l(n){if("string"==typeof n)return Date.parse(n.replace(/-/g,"/"));if(n instanceof Array){var i,o=[];for(i=0;i<n.length;i++)o[i]=l(n[i]);return o}throw new Error("parseDate: parameter must be arrays of strings")}(r)},numeric$1.parseFloat=function(r){return function l(n){if("string"==typeof n)return parseFloat(n);if(n instanceof Array){var i,o=[];for(i=0;i<n.length;i++)o[i]=l(n[i]);return o}throw new Error("parseFloat: parameter must be arrays of strings")}(r)},numeric$1.parseCSV=function(r){var n,o,g,l=r.split("\n"),i=[],s=/(([^'",]*)|('[^']*')|("[^"]*")),/g,u=/^\s*(([+-]?[0-9]+(\.[0-9]*)?(e[+-]?[0-9]+)?)|([+-]?[0-9]*(\.[0-9]+)?(e[+-]?[0-9]+)?))\s*$/,p=0;for(o=0;o<l.length;o++){var y,v=(l[o]+",").match(s);if(v.length>0){for(i[p]=[],n=0;n<v.length;n++)y=(g=v[n]).substr(0,g.length-1),i[p][n]=u.test(y)?parseFloat(y):y;p++}}return i},numeric$1.toCSV=function(r){var n,o,i,u,a,l=numeric$1.dim(r);for(i=l[0],a=[],n=0;n<i;n++){for(u=[],o=0;o<i;o++)u[o]=r[n][o].toString();a[n]=u.join(", ")}return a.join("\n")+"\n"},numeric$1.getURL=function(r){var l=new XMLHttpRequest;return l.open("GET",r,!1),l.send(),l},numeric$1.imageURL=function(r){function n(R,S,F){void 0===S&&(S=0),void 0===F&&(F=R.length);var X,E=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],U=-1;for(X=S;X<F;X++)U=U>>>8^E[255&(U^R[X])];return-1^U}var s,u,p,v,y,g,D,b,x,w,o=r[0].length,i=r[0][0].length,L=[137,80,78,71,13,10,26,10,0,0,0,13,73,72,68,82,i>>24&255,i>>16&255,i>>8&255,255&i,o>>24&255,o>>16&255,o>>8&255,255&o,8,2,0,0,0,-1,-2,-3,-4,-5,-6,-7,-8,73,68,65,84,8,29];for(w=n(L,12,29),L[29]=w>>24&255,L[30]=w>>16&255,L[31]=w>>8&255,L[32]=255&w,s=1,u=0,D=0;D<o;D++){for(L.push(D<o-1?0:1),g=3*i+1+(0===D)>>8&255,L.push(y=3*i+1+(0===D)&255),L.push(g),L.push(255&~y),L.push(255&~g),0===D&&L.push(0),b=0;b<i;b++)for(p=0;p<3;p++)u=(u+(s=(s+(y=(y=r[p][D][b])>255?255:y<0?0:Math.round(y)))%65521))%65521,L.push(y);L.push(0)}return L.push((x=(u<<16)+s)>>24&255),L.push(x>>16&255),L.push(x>>8&255),L.push(255&x),L[33]=(v=L.length-41)>>24&255,L[34]=v>>16&255,L[35]=v>>8&255,L[36]=255&v,w=n(L,37),L.push(w>>24&255),L.push(w>>16&255),L.push(w>>8&255),L.push(255&w),L.push(0),L.push(0),L.push(0),L.push(0),L.push(73),L.push(69),L.push(78),L.push(68),L.push(174),L.push(66),L.push(96),L.push(130),"data:image/png;base64,"+function l(R){var F,E,U,P,X,ce,d,S=R.length,he="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",ae="";for(F=0;F<S;F+=3)X=((3&(E=R[F]))<<4)+((U=R[F+1])>>4),ce=((15&U)<<2)+((P=R[F+2])>>6),d=63&P,F+1>=S?ce=d=64:F+2>=S&&(d=64),ae+=he.charAt(E>>2)+he.charAt(X)+he.charAt(ce)+he.charAt(d);return ae}(L)},numeric$1._dim=function(r){for(var l=[];"object"==typeof r;)l.push(r.length),r=r[0];return l},numeric$1.dim=function(r){var l;return"object"==typeof r?"object"==typeof(l=r[0])?"object"==typeof l[0]?numeric$1._dim(r):[r.length,l.length]:[r.length]:[]},numeric$1.mapreduce=function(r,l){return Function("x","accum","_s","_k",'if(typeof accum === "undefined") accum = '+l+';\nif(typeof x === "number") { var xi = x; '+r+'; return accum; }\nif(typeof _s === "undefined") _s = numeric.dim(x);\nif(typeof _k === "undefined") _k = 0;\nvar _n = _s[_k];\nvar i,xi;\nif(_k < _s.length-1) {\n    for(i=_n-1;i>=0;i--) {\n        accum = arguments.callee(x[i],accum,_s,_k+1);\n    }    return accum;\n}\nfor(i=_n-1;i>=1;i-=2) { \n    xi = x[i];\n    '+r+";\n    xi = x[i-1];\n    "+r+";\n}\nif(i === 0) {\n    xi = x[i];\n    "+r+"\n}\nreturn accum;")},numeric$1.mapreduce2=function(r,l){return Function("x","var n = x.length;\nvar i,xi;\n"+l+";\nfor(i=n-1;i!==-1;--i) { \n    xi = x[i];\n    "+r+";\n}\nreturn accum;")},numeric$1.same=function r(l,n){var o,i;if(l instanceof Array&&n instanceof Array){if((i=l.length)!==n.length)return!1;for(o=0;o<i;o++)if(l[o]!==n[o]&&("object"!=typeof l[o]||!r(l[o],n[o])))return!1;return!0}return!1},numeric$1.rep=function(r,l,n){void 0===n&&(n=0);var s,o=r[n],i=Array(o);if(n===r.length-1){for(s=o-2;s>=0;s-=2)i[s+1]=l,i[s]=l;return-1===s&&(i[0]=l),i}for(s=o-1;s>=0;s--)i[s]=numeric$1.rep(r,l,n+1);return i},numeric$1.dotMMsmall=function(r,l){var n,o,i,s,u,a,p,v,y,g,D;for(s=r.length,u=l.length,a=l[0].length,p=Array(s),n=s-1;n>=0;n--){for(v=Array(a),y=r[n],i=a-1;i>=0;i--){for(g=y[u-1]*l[u-1][i],o=u-2;o>=1;o-=2)g+=y[o]*l[o][i]+y[D=o-1]*l[D][i];0===o&&(g+=y[0]*l[0][i]),v[i]=g}p[n]=v}return p},numeric$1._getCol=function(r,l,n){var i;for(i=r.length-1;i>0;--i)n[i]=r[i][l],n[--i]=r[i][l];0===i&&(n[0]=r[0][l])},numeric$1.dotMMbig=function(r,l){var y,g,n=numeric$1._getCol,o=l.length,i=Array(o),s=r.length,u=l[0].length,a=new Array(s),v=numeric$1.dotVV;for(--o,y=--s;-1!==y;--y)a[y]=Array(u);for(y=--u;-1!==y;--y)for(n(l,y,i),g=s;-1!==g;--g)a[g][y]=v(r[g],i);return a},numeric$1.dotMV=function(r,l){var i,n=r.length,s=Array(n),u=numeric$1.dotVV;for(i=n-1;i>=0;i--)s[i]=u(r[i],l);return s},numeric$1.dotVM=function(r,l){var o,i,s,u,p,g,D;for(s=r.length,u=l[0].length,p=Array(u),i=u-1;i>=0;i--){for(g=r[s-1]*l[s-1][i],o=s-2;o>=1;o-=2)g+=r[o]*l[o][i]+r[D=o-1]*l[D][i];0===o&&(g+=r[0]*l[0][i]),p[i]=g}return p},numeric$1.dotVV=function(r,l){var n,i,o=r.length,s=r[o-1]*l[o-1];for(n=o-2;n>=1;n-=2)s+=r[n]*l[n]+r[i=n-1]*l[i];return 0===n&&(s+=r[0]*l[0]),s},numeric$1.dot=function(r,l){var n=numeric$1.dim;switch(1e3*n(r).length+n(l).length){case 2002:return l.length<10?numeric$1.dotMMsmall(r,l):numeric$1.dotMMbig(r,l);case 2001:return numeric$1.dotMV(r,l);case 1002:return numeric$1.dotVM(r,l);case 1001:return numeric$1.dotVV(r,l);case 1e3:return numeric$1.mulVS(r,l);case 1:return numeric$1.mulSV(r,l);case 0:return r*l;default:throw new Error("numeric.dot only works on vectors and matrices")}},numeric$1.diag=function(r){var l,n,o,u,i=r.length,s=Array(i);for(l=i-1;l>=0;l--){for(u=Array(i),n=l+2,o=i-1;o>=n;o-=2)u[o]=0,u[o-1]=0;for(o>l&&(u[o]=0),u[l]=r[l],o=l-1;o>=1;o-=2)u[o]=0,u[o-1]=0;0===o&&(u[0]=0),s[l]=u}return s},numeric$1.getDiag=function(r){var n,l=Math.min(r.length,r[0].length),o=Array(l);for(n=l-1;n>=1;--n)o[n]=r[n][n],o[--n]=r[n][n];return 0===n&&(o[0]=r[0][0]),o},numeric$1.identity=function(r){return numeric$1.diag(numeric$1.rep([r],1))},numeric$1.pointwise=function(r,l,n){void 0===n&&(n="");var i,u,o=[],s=/\[i\]$/,a="",p=!1;for(i=0;i<r.length;i++)s.test(r[i])?a=u=r[i].substring(0,r[i].length-3):u=r[i],"ret"===u&&(p=!0),o.push(u);return o[r.length]="_s",o[r.length+1]="_k",o[r.length+2]='if(typeof _s === "undefined") _s = numeric.dim('+a+');\nif(typeof _k === "undefined") _k = 0;\nvar _n = _s[_k];\nvar i'+(p?"":", ret = Array(_n)")+";\nif(_k < _s.length-1) {\n    for(i=_n-1;i>=0;i--) ret[i] = arguments.callee("+r.join(",")+",_s,_k+1);\n    return ret;\n}\n"+n+"\nfor(i=_n-1;i!==-1;--i) {\n    "+l+"\n}\nreturn ret;",Function.apply(null,o)},numeric$1.pointwise2=function(r,l,n){void 0===n&&(n="");var i,u,o=[],s=/\[i\]$/,a="",p=!1;for(i=0;i<r.length;i++)s.test(r[i])?a=u=r[i].substring(0,r[i].length-3):u=r[i],"ret"===u&&(p=!0),o.push(u);return o[r.length]="var _n = "+a+".length;\nvar i"+(p?"":", ret = Array(_n)")+";\n"+n+"\nfor(i=_n-1;i!==-1;--i) {\n"+l+"\n}\nreturn ret;",Function.apply(null,o)},numeric$1._biforeach=function r(l,n,o,i,s){var u;if(i!==o.length-1)for(u=o[i]-1;u>=0;u--)r("object"==typeof l?l[u]:l,"object"==typeof n?n[u]:n,o,i+1,s);else s(l,n)},numeric$1._biforeach2=function r(l,n,o,i,s){if(i===o.length-1)return s(l,n);var u,a=o[i],p=Array(a);for(u=a-1;u>=0;--u)p[u]=r("object"==typeof l?l[u]:l,"object"==typeof n?n[u]:n,o,i+1,s);return p},numeric$1._foreach=function r(l,n,o,i){var s;if(o!==n.length-1)for(s=n[o]-1;s>=0;s--)r(l[s],n,o+1,i);else i(l)},numeric$1._foreach2=function r(l,n,o,i){if(o===n.length-1)return i(l);var s,u=n[o],a=Array(u);for(s=u-1;s>=0;s--)a[s]=r(l[s],n,o+1,i);return a},numeric$1.ops2={add:"+",sub:"-",mul:"*",div:"/",mod:"%",and:"&&",or:"||",eq:"===",neq:"!==",lt:"<",gt:">",leq:"<=",geq:">=",band:"&",bor:"|",bxor:"^",lshift:"<<",rshift:">>",rrshift:">>>"},numeric$1.opseq={addeq:"+=",subeq:"-=",muleq:"*=",diveq:"/=",modeq:"%=",lshifteq:"<<=",rshifteq:">>=",rrshifteq:">>>=",bandeq:"&=",boreq:"|=",bxoreq:"^="},numeric$1.mathfuns=["abs","acos","asin","atan","ceil","cos","exp","floor","log","round","sin","sqrt","tan","isNaN","isFinite"],numeric$1.mathfuns2=["atan2","pow","max","min"],numeric$1.ops1={neg:"-",not:"!",bnot:"~",clone:""},numeric$1.mapreducers={any:["if(xi) return true;","var accum = false;"],all:["if(!xi) return false;","var accum = true;"],sum:["accum += xi;","var accum = 0;"],prod:["accum *= xi;","var accum = 1;"],norm2Squared:["accum += xi*xi;","var accum = 0;"],norminf:["accum = max(accum,abs(xi));","var accum = 0, max = Math.max, abs = Math.abs;"],norm1:["accum += abs(xi)","var accum = 0, abs = Math.abs;"],sup:["accum = max(accum,xi);","var accum = -Infinity, max = Math.max;"],inf:["accum = min(accum,xi);","var accum = Infinity, min = Math.min;"]},function(){var r,l;for(r=0;r<numeric$1.mathfuns2.length;++r)numeric$1.ops2[l=numeric$1.mathfuns2[r]]=l;for(r in numeric$1.ops2)if(numeric$1.ops2.hasOwnProperty(r)){l=numeric$1.ops2[r];var n,o,i="";-1!==numeric$1.myIndexOf.call(numeric$1.mathfuns2,r)?(i="var "+l+" = Math."+l+";\n",n=function(s,u,a){return s+" = "+l+"("+u+","+a+")"},o=function(s,u){return s+" = "+l+"("+s+","+u+")"}):(n=function(s,u,a){return s+" = "+u+" "+l+" "+a},o=numeric$1.opseq.hasOwnProperty(r+"eq")?function(s,u){return s+" "+l+"= "+u}:function(s,u){return s+" = "+s+" "+l+" "+u}),numeric$1[r+"VV"]=numeric$1.pointwise2(["x[i]","y[i]"],n("ret[i]","x[i]","y[i]"),i),numeric$1[r+"SV"]=numeric$1.pointwise2(["x","y[i]"],n("ret[i]","x","y[i]"),i),numeric$1[r+"VS"]=numeric$1.pointwise2(["x[i]","y"],n("ret[i]","x[i]","y"),i),numeric$1[r]=Function("var n = arguments.length, i, x = arguments[0], y;\nvar VV = numeric."+r+"VV, VS = numeric."+r+"VS, SV = numeric."+r+'SV;\nvar dim = numeric.dim;\nfor(i=1;i!==n;++i) { \n  y = arguments[i];\n  if(typeof x === "object") {\n      if(typeof y === "object") x = numeric._biforeach2(x,y,dim(x),0,VV);\n      else x = numeric._biforeach2(x,y,dim(x),0,VS);\n  } else if(typeof y === "object") x = numeric._biforeach2(x,y,dim(y),0,SV);\n  else '+o("x","y")+"\n}\nreturn x;\n"),numeric$1[l]=numeric$1[r],numeric$1[r+"eqV"]=numeric$1.pointwise2(["ret[i]","x[i]"],o("ret[i]","x[i]"),i),numeric$1[r+"eqS"]=numeric$1.pointwise2(["ret[i]","x"],o("ret[i]","x"),i),numeric$1[r+"eq"]=Function("var n = arguments.length, i, x = arguments[0], y;\nvar V = numeric."+r+"eqV, S = numeric."+r+'eqS\nvar s = numeric.dim(x);\nfor(i=1;i!==n;++i) { \n  y = arguments[i];\n  if(typeof y === "object") numeric._biforeach(x,y,s,0,V);\n  else numeric._biforeach(x,y,s,0,S);\n}\nreturn x;\n')}for(r=0;r<numeric$1.mathfuns2.length;++r)delete numeric$1.ops2[l=numeric$1.mathfuns2[r]];for(r=0;r<numeric$1.mathfuns.length;++r)numeric$1.ops1[l=numeric$1.mathfuns[r]]=l;for(r in numeric$1.ops1)numeric$1.ops1.hasOwnProperty(r)&&(i="",l=numeric$1.ops1[r],-1!==numeric$1.myIndexOf.call(numeric$1.mathfuns,r)&&Math.hasOwnProperty(l)&&(i="var "+l+" = Math."+l+";\n"),numeric$1[r+"eqV"]=numeric$1.pointwise2(["ret[i]"],"ret[i] = "+l+"(ret[i]);",i),numeric$1[r+"eq"]=Function("x",'if(typeof x !== "object") return '+l+"x\nvar i;\nvar V = numeric."+r+"eqV;\nvar s = numeric.dim(x);\nnumeric._foreach(x,s,0,V);\nreturn x;\n"),numeric$1[r+"V"]=numeric$1.pointwise2(["x[i]"],"ret[i] = "+l+"(x[i]);",i),numeric$1[r]=Function("x",'if(typeof x !== "object") return '+l+"(x)\nvar i;\nvar V = numeric."+r+"V;\nvar s = numeric.dim(x);\nreturn numeric._foreach2(x,s,0,V);\n"));for(r=0;r<numeric$1.mathfuns.length;++r)delete numeric$1.ops1[l=numeric$1.mathfuns[r]];for(r in numeric$1.mapreducers)numeric$1.mapreducers.hasOwnProperty(r)&&(numeric$1[r+"V"]=numeric$1.mapreduce2((l=numeric$1.mapreducers[r])[0],l[1]),numeric$1[r]=Function("x","s","k",l[1]+'if(typeof x !== "object") {    xi = x;\n'+l[0]+';\n    return accum;\n}if(typeof s === "undefined") s = numeric.dim(x);\nif(typeof k === "undefined") k = 0;\nif(k === s.length-1) return numeric.'+r+"V(x);\nvar xi;\nvar n = x.length, i;\nfor(i=n-1;i!==-1;--i) {\n   xi = arguments.callee(x[i]);\n"+l[0]+";\n}\nreturn accum;\n"))}(),numeric$1.truncVV=numeric$1.pointwise(["x[i]","y[i]"],"ret[i] = round(x[i]/y[i])*y[i];","var round = Math.round;"),numeric$1.truncVS=numeric$1.pointwise(["x[i]","y"],"ret[i] = round(x[i]/y)*y;","var round = Math.round;"),numeric$1.truncSV=numeric$1.pointwise(["x","y[i]"],"ret[i] = round(x/y[i])*y[i];","var round = Math.round;"),numeric$1.trunc=function(r,l){return"object"==typeof r?"object"==typeof l?numeric$1.truncVV(r,l):numeric$1.truncVS(r,l):"object"==typeof l?numeric$1.truncSV(r,l):Math.round(r/l)*l},numeric$1.inv=function(x){var u,a,v,y,g,D,b,l=numeric$1.dim(x),n=Math.abs,o=l[0],i=l[1],s=numeric$1.clone(x),p=numeric$1.identity(o);for(D=0;D<i;++D){var w=-1,L=-1;for(g=D;g!==o;++g)(b=n(s[g][D]))>L&&(w=g,L=b);for(a=s[w],s[w]=s[D],s[D]=a,y=p[w],p[w]=p[D],p[D]=y,x=a[D],b=D;b!==i;++b)a[b]/=x;for(b=i-1;-1!==b;--b)y[b]/=x;for(g=o-1;-1!==g;--g)if(g!==D){for(v=p[g],x=(u=s[g])[D],b=D+1;b!==i;++b)u[b]-=a[b]*x;for(b=i-1;b>0;--b)v[b]-=y[b]*x,v[--b]-=y[b]*x;0===b&&(v[0]-=y[0]*x)}}return p},numeric$1.det=function(r){var l=numeric$1.dim(r);if(2!==l.length||l[0]!==l[1])throw new Error("numeric: det() only works on square matrices");var i,s,u,p,v,y,g,D,n=l[0],o=1,a=numeric$1.clone(r);for(s=0;s<n-1;s++){for(u=s,i=s+1;i<n;i++)Math.abs(a[i][s])>Math.abs(a[u][s])&&(u=i);for(u!==s&&(g=a[u],a[u]=a[s],a[s]=g,o*=-1),p=a[s],i=s+1;i<n;i++){for(y=(v=a[i])[s]/p[s],u=s+1;u<n-1;u+=2)D=u+1,v[u]-=p[u]*y,v[D]-=p[D]*y;u!==n&&(v[u]-=p[u]*y)}if(0===p[s])return 0;o*=p[s]}return o*a[s][s]},numeric$1.transpose=function(r){var l,n,u,a,p,o=r.length,i=r[0].length,s=Array(i);for(n=0;n<i;n++)s[n]=Array(o);for(l=o-1;l>=1;l-=2){for(a=r[l],u=r[l-1],n=i-1;n>=1;--n)(p=s[n])[l]=a[n],p[l-1]=u[n],(p=s[--n])[l]=a[n],p[l-1]=u[n];0===n&&((p=s[0])[l]=a[0],p[l-1]=u[0])}if(0===l){for(u=r[0],n=i-1;n>=1;--n)s[n][0]=u[n],s[--n][0]=u[n];0===n&&(s[0][0]=u[0])}return s},numeric$1.negtranspose=function(r){var l,n,u,a,p,o=r.length,i=r[0].length,s=Array(i);for(n=0;n<i;n++)s[n]=Array(o);for(l=o-1;l>=1;l-=2){for(a=r[l],u=r[l-1],n=i-1;n>=1;--n)(p=s[n])[l]=-a[n],p[l-1]=-u[n],(p=s[--n])[l]=-a[n],p[l-1]=-u[n];0===n&&((p=s[0])[l]=-a[0],p[l-1]=-u[0])}if(0===l){for(u=r[0],n=i-1;n>=1;--n)s[n][0]=-u[n],s[--n][0]=-u[n];0===n&&(s[0][0]=-u[0])}return s},numeric$1._random=function r(l,n){var o,u,i=l[n],s=Array(i);if(n===l.length-1){for(u=Math.random,o=i-1;o>=1;o-=2)s[o]=u(),s[o-1]=u();return 0===o&&(s[0]=u()),s}for(o=i-1;o>=0;o--)s[o]=r(l,n+1);return s},numeric$1.random=function(r){return numeric$1._random(r,0)},numeric$1.norm2=function(r){return Math.sqrt(numeric$1.norm2Squared(r))},numeric$1.linspace=function(r,l,n){if(void 0===n&&(n=Math.max(Math.round(l-r)+1,1)),n<2)return 1===n?[r]:[];var o,i=Array(n);for(o=--n;o>=0;o--)i[o]=(o*l+(n-o)*r)/n;return i},numeric$1.getBlock=function(r,l,n){var i=numeric$1.dim(r);return function o(s,u){var a,p=l[u],v=n[u]-p,y=Array(v);if(u===i.length-1){for(a=v;a>=0;a--)y[a]=s[a+p];return y}for(a=v;a>=0;a--)y[a]=o(s[a+p],u+1);return y}(r,0)},numeric$1.setBlock=function(r,l,n,o){var s=numeric$1.dim(r);return function i(u,a,p){var v,y=l[p],g=n[p]-y;if(p===s.length-1)for(v=g;v>=0;v--)u[v+y]=a[v];for(v=g;v>=0;v--)i(u[v+y],a[v],p+1)}(r,o,0),r},numeric$1.getRange=function(r,l,n){var s,u,p,v,o=l.length,i=n.length,a=Array(o);for(s=o-1;-1!==s;--s)for(a[s]=Array(i),p=a[s],v=r[l[s]],u=i-1;-1!==u;--u)p[u]=v[n[u]];return a},numeric$1.blockMatrix=function(r){var l=numeric$1.dim(r);if(l.length<4)return numeric$1.blockMatrix([r]);var i,s,u,a,p,n=l[0],o=l[1];for(i=0,s=0,u=0;u<n;++u)i+=r[u][0].length;for(a=0;a<o;++a)s+=r[0][a][0].length;var v=Array(i);for(u=0;u<i;++u)v[u]=Array(s);var g,D,b,x,w,y=0;for(u=0;u<n;++u){for(g=s,a=o-1;-1!==a;--a)for(g-=(p=r[u][a])[0].length,b=p.length-1;-1!==b;--b)for(D=v[y+b],x=(w=p[b]).length-1;-1!==x;--x)D[g+x]=w[x];y+=r[u][0].length}return v},numeric$1.tensor=function(r,l){if("number"==typeof r||"number"==typeof l)return numeric$1.mul(r,l);var n=numeric$1.dim(r),o=numeric$1.dim(l);if(1!==n.length||1!==o.length)throw new Error("numeric: tensor product is only defined for vectors");var a,p,v,y,i=n[0],s=o[0],u=Array(i);for(p=i-1;p>=0;p--){for(a=Array(s),y=r[p],v=s-1;v>=3;--v)a[v]=y*l[v],a[--v]=y*l[v],a[--v]=y*l[v],a[--v]=y*l[v];for(;v>=0;)a[v]=y*l[v],--v;u[p]=a}return u},numeric$1.T=function(r,l){this.x=r,this.y=l},numeric$1.t=function(r,l){return new numeric$1.T(r,l)},numeric$1.Tbinop=function(r,l,n,o,i){var u;if("string"!=typeof i)for(u in i="",numeric$1)numeric$1.hasOwnProperty(u)&&(r.indexOf(u)>=0||l.indexOf(u)>=0||n.indexOf(u)>=0||o.indexOf(u)>=0)&&u.length>1&&(i+="var "+u+" = numeric."+u+";\n");return Function(["y"],"var x = this;\nif(!(y instanceof numeric.T)) { y = new numeric.T(y); }\n"+i+"\nif(x.y) {  if(y.y) {    return new numeric.T("+o+");\n  }\n  return new numeric.T("+n+");\n}\nif(y.y) {\n  return new numeric.T("+l+");\n}\nreturn new numeric.T("+r+");\n")},numeric$1.T.prototype.add=numeric$1.Tbinop("add(x.x,y.x)","add(x.x,y.x),y.y","add(x.x,y.x),x.y","add(x.x,y.x),add(x.y,y.y)"),numeric$1.T.prototype.sub=numeric$1.Tbinop("sub(x.x,y.x)","sub(x.x,y.x),neg(y.y)","sub(x.x,y.x),x.y","sub(x.x,y.x),sub(x.y,y.y)"),numeric$1.T.prototype.mul=numeric$1.Tbinop("mul(x.x,y.x)","mul(x.x,y.x),mul(x.x,y.y)","mul(x.x,y.x),mul(x.y,y.x)","sub(mul(x.x,y.x),mul(x.y,y.y)),add(mul(x.x,y.y),mul(x.y,y.x))"),numeric$1.T.prototype.reciprocal=function(){var r=numeric$1.mul,l=numeric$1.div;if(this.y){var n=numeric$1.add(r(this.x,this.x),r(this.y,this.y));return new numeric$1.T(l(this.x,n),l(numeric$1.neg(this.y),n))}return new T(l(1,this.x))},numeric$1.T.prototype.div=function r(l){if(l instanceof numeric$1.T||(l=new numeric$1.T(l)),l.y)return this.mul(l.reciprocal());var n=numeric$1.div;return this.y?new numeric$1.T(n(this.x,l.x),n(this.y,l.x)):new numeric$1.T(n(this.x,l.x))},numeric$1.T.prototype.dot=numeric$1.Tbinop("dot(x.x,y.x)","dot(x.x,y.x),dot(x.x,y.y)","dot(x.x,y.x),dot(x.y,y.x)","sub(dot(x.x,y.x),dot(x.y,y.y)),add(dot(x.x,y.y),dot(x.y,y.x))"),numeric$1.T.prototype.transpose=function(){var r=numeric$1.transpose,l=this.x,n=this.y;return n?new numeric$1.T(r(l),r(n)):new numeric$1.T(r(l))},numeric$1.T.prototype.transjugate=function(){var r=numeric$1.transpose,l=this.x,n=this.y;return n?new numeric$1.T(r(l),numeric$1.negtranspose(n)):new numeric$1.T(r(l))},numeric$1.Tunop=function(r,l,n){return"string"!=typeof n&&(n=""),Function("var x = this;\n"+n+"\nif(x.y) {  "+l+";\n}\n"+r+";\n")},numeric$1.T.prototype.exp=numeric$1.Tunop("return new numeric.T(ex)","return new numeric.T(mul(cos(x.y),ex),mul(sin(x.y),ex))","var ex = numeric.exp(x.x), cos = numeric.cos, sin = numeric.sin, mul = numeric.mul;"),numeric$1.T.prototype.conj=numeric$1.Tunop("return new numeric.T(x.x);","return new numeric.T(x.x,numeric.neg(x.y));"),numeric$1.T.prototype.neg=numeric$1.Tunop("return new numeric.T(neg(x.x));","return new numeric.T(neg(x.x),neg(x.y));","var neg = numeric.neg;"),numeric$1.T.prototype.sin=numeric$1.Tunop("return new numeric.T(numeric.sin(x.x))","return x.exp().sub(x.neg().exp()).div(new numeric.T(0,2));"),numeric$1.T.prototype.cos=numeric$1.Tunop("return new numeric.T(numeric.cos(x.x))","return x.exp().add(x.neg().exp()).div(2);"),numeric$1.T.prototype.abs=numeric$1.Tunop("return new numeric.T(numeric.abs(x.x));","return new numeric.T(numeric.sqrt(numeric.add(mul(x.x,x.x),mul(x.y,x.y))));","var mul = numeric.mul;"),numeric$1.T.prototype.log=numeric$1.Tunop("return new numeric.T(numeric.log(x.x));","var theta = new numeric.T(numeric.atan2(x.y,x.x)), r = x.abs();\nreturn new numeric.T(numeric.log(r.x),theta.x);"),numeric$1.T.prototype.norm2=numeric$1.Tunop("return numeric.norm2(x.x);","var f = numeric.norm2Squared;\nreturn Math.sqrt(f(x.x)+f(x.y));"),numeric$1.T.prototype.inv=function(){var r=this;if(void 0===r.y)return new numeric$1.T(numeric$1.inv(r.x));var u,a,p,v,y,g,D,b,x,w,L,R,S,F,E,U,P,V,l=r.x.length,n=numeric$1.identity(l),o=numeric$1.rep([l,l],0),i=numeric$1.clone(r.x),s=numeric$1.clone(r.y);for(x=0;x<l;x++){for(R=(F=i[x][x])*F+(E=s[x][x])*E,L=x,w=x+1;w<l;w++)(S=(F=i[w][x])*F+(E=s[w][x])*E)>R&&(L=w,R=S);for(L!==x&&(V=i[x],i[x]=i[L],i[L]=V,V=s[x],s[x]=s[L],s[L]=V,V=n[x],n[x]=n[L],n[L]=V,V=o[x],o[x]=o[L],o[L]=V),y=n[x],g=o[x],F=(u=i[x])[x],E=(a=s[x])[x],w=x+1;w<l;w++)u[w]=((U=u[w])*F+(P=a[w])*E)/R,a[w]=(P*F-U*E)/R;for(w=0;w<l;w++)y[w]=((U=y[w])*F+(P=g[w])*E)/R,g[w]=(P*F-U*E)/R;for(w=x+1;w<l;w++){for(D=n[w],b=o[w],F=(p=i[w])[x],E=(v=s[w])[x],L=x+1;L<l;L++)p[L]-=(U=u[L])*F-(P=a[L])*E,v[L]-=P*F+U*E;for(L=0;L<l;L++)D[L]-=(U=y[L])*F-(P=g[L])*E,b[L]-=P*F+U*E}}for(x=l-1;x>0;x--)for(y=n[x],g=o[x],w=x-1;w>=0;w--)for(D=n[w],b=o[w],F=i[w][x],E=s[w][x],L=l-1;L>=0;L--)D[L]-=F*(U=y[L])-E*(P=g[L]),b[L]-=F*P+E*U;return new numeric$1.T(n,o)},numeric$1.T.prototype.get=function(r){var i,l=this.x,n=this.y,o=0,s=r.length;if(n){for(;o<s;)l=l[i=r[o]],n=n[i],o++;return new numeric$1.T(l,n)}for(;o<s;)l=l[i=r[o]],o++;return new numeric$1.T(l)},numeric$1.T.prototype.set=function(r,l){var s,n=this.x,o=this.y,i=0,u=r.length,a=l.x,p=l.y;if(0===u)return p?this.y=p:o&&(this.y=void 0),this.x=n,this;if(p){for(o||(o=numeric$1.rep(numeric$1.dim(n),0),this.y=o);i<u-1;)n=n[s=r[i]],o=o[s],i++;return n[s=r[i]]=a,o[s]=p,this}if(o){for(;i<u-1;)n=n[s=r[i]],o=o[s],i++;return n[s=r[i]]=a,o[s]=a instanceof Array?numeric$1.rep(numeric$1.dim(a),0):0,this}for(;i<u-1;)n=n[s=r[i]],i++;return n[s=r[i]]=a,this},numeric$1.T.prototype.getRows=function(r,l){var o,s,n=l-r+1,i=Array(n),u=this.x,a=this.y;for(o=r;o<=l;o++)i[o-r]=u[o];if(a){for(s=Array(n),o=r;o<=l;o++)s[o-r]=a[o];return new numeric$1.T(i,s)}return new numeric$1.T(i)},numeric$1.T.prototype.setRows=function(r,l,n){var o,i=this.x,s=this.y,u=n.x,a=n.y;for(o=r;o<=l;o++)i[o]=u[o-r];if(a)for(s||(s=numeric$1.rep(numeric$1.dim(i),0),this.y=s),o=r;o<=l;o++)s[o]=a[o-r];else if(s)for(o=r;o<=l;o++)s[o]=numeric$1.rep([u[o-r].length],0);return this},numeric$1.T.prototype.getRow=function(r){var l=this.x,n=this.y;return n?new numeric$1.T(l[r],n[r]):new numeric$1.T(l[r])},numeric$1.T.prototype.setRow=function(r,l){var n=this.x,o=this.y,i=l.x,s=l.y;return n[r]=i,s?(o||(o=numeric$1.rep(numeric$1.dim(n),0),this.y=o),o[r]=s):o&&(o=numeric$1.rep([i.length],0)),this},numeric$1.T.prototype.getBlock=function(r,l){var n=this.x,o=this.y,i=numeric$1.getBlock;return o?new numeric$1.T(i(n,r,l),i(o,r,l)):new numeric$1.T(i(n,r,l))},numeric$1.T.prototype.setBlock=function(r,l,n){n instanceof numeric$1.T||(n=new numeric$1.T(n));var o=this.x,i=this.y,s=numeric$1.setBlock,u=n.x,a=n.y;if(a)return i||(this.y=numeric$1.rep(numeric$1.dim(this),0),i=this.y),s(o,r,l,u),s(i,r,l,a),this;s(o,r,l,u),i&&s(i,r,l,numeric$1.rep(numeric$1.dim(u),0))},numeric$1.T.rep=function(r,l){var n=numeric$1.T;l instanceof n||(l=new n(l));var o=l.x,i=l.y,s=numeric$1.rep;return i?new n(s(r,o),s(r,i)):new n(s(r,o))},numeric$1.T.diag=function r(l){l instanceof numeric$1.T||(l=new numeric$1.T(l));var n=l.x,o=l.y,i=numeric$1.diag;return o?new numeric$1.T(i(n),i(o)):new numeric$1.T(i(n))},numeric$1.T.eig=function(){if(this.y)throw new Error("eig: not implemented for complex matrices.");return numeric$1.eig(this.x)},numeric$1.T.identity=function(r){return new numeric$1.T(numeric$1.identity(r))},numeric$1.T.prototype.getDiag=function(){var r=numeric$1,l=this.x,n=this.y;return n?new r.T(r.getDiag(l),r.getDiag(n)):new r.T(r.getDiag(l))},numeric$1.house=function(r){var l=numeric$1.clone(r),o=(r[0]>=0?1:-1)*numeric$1.norm2(r);l[0]+=o;var i=numeric$1.norm2(l);if(0===i)throw new Error("eig: internal error");return numeric$1.div(l,i)},numeric$1.toUpperHessenberg=function(r){var l=numeric$1.dim(r);if(2!==l.length||l[0]!==l[1])throw new Error("numeric: toUpperHessenberg() only works on square matrices");var o,i,s,u,a,v,y,g,D,x,n=l[0],p=numeric$1.clone(r),b=numeric$1.identity(n);for(i=0;i<n-2;i++){for(u=Array(n-i-1),o=i+1;o<n;o++)u[o-i-1]=p[o][i];if(numeric$1.norm2(u)>0){for(a=numeric$1.house(u),v=numeric$1.getBlock(p,[i+1,i],[n-1,n-1]),y=numeric$1.tensor(a,numeric$1.dot(a,v)),o=i+1;o<n;o++)for(g=p[o],D=y[o-i-1],s=i;s<n;s++)g[s]-=2*D[s-i];for(v=numeric$1.getBlock(p,[0,i+1],[n-1,n-1]),y=numeric$1.tensor(numeric$1.dot(v,a),a),o=0;o<n;o++)for(g=p[o],D=y[o],s=i+1;s<n;s++)g[s]-=2*D[s-i-1];for(v=Array(n-i-1),o=i+1;o<n;o++)v[o-i-1]=b[o];for(y=numeric$1.tensor(a,numeric$1.dot(a,v)),o=i+1;o<n;o++)for(x=b[o],D=y[o-i-1],s=0;s<n;s++)x[s]-=2*D[s]}}return{H:p,Q:b}},numeric$1.epsilon=2220446049250313e-31,numeric$1.QRFrancis=function(r,l){void 0===l&&(l=1e4),r=numeric$1.clone(r),numeric$1.clone(r);var s,u,a,y,g,D,b,w,L,R,S,F,E,U,P,V,i=numeric$1.dim(r)[0],x=numeric$1.identity(i);if(i<3)return{Q:x,B:[[0,i-1]]};var X=numeric$1.epsilon;for(V=0;V<l;V++){for(U=0;U<i-1;U++)if(Math.abs(r[U+1][U])<X*(Math.abs(r[U][U])+Math.abs(r[U+1][U+1]))){var ce=numeric$1.QRFrancis(numeric$1.getBlock(r,[0,0],[U,U]),l),d=numeric$1.QRFrancis(numeric$1.getBlock(r,[U+1,U+1],[i-1,i-1]),l);for(R=Array(U+1),E=0;E<=U;E++)R[E]=x[E];for(S=numeric$1.dot(ce.Q,R),E=0;E<=U;E++)x[E]=S[E];for(R=Array(i-U-1),E=U+1;E<i;E++)R[E-U-1]=x[E];for(S=numeric$1.dot(d.Q,R),E=U+1;E<i;E++)x[E]=S[E-U-1];return{Q:x,B:ce.B.concat(numeric$1.add(d.B,U+1))}}var he,ae,I;for(D=(a=r[i-2][i-2])+(y=r[i-1][i-1]),g=a*y-r[i-2][i-1]*r[i-1][i-2],b=numeric$1.getBlock(r,[0,0],[2,2]),D*D>=4*g?(he=.5*(D+Math.sqrt(D*D-4*g)),ae=.5*(D-Math.sqrt(D*D-4*g)),b=numeric$1.add(numeric$1.sub(numeric$1.dot(b,b),numeric$1.mul(b,he+ae)),numeric$1.diag(numeric$1.rep([3],he*ae)))):b=numeric$1.add(numeric$1.sub(numeric$1.dot(b,b),numeric$1.mul(b,D)),numeric$1.diag(numeric$1.rep([3],g))),u=numeric$1.house(s=[b[0][0],b[1][0],b[2][0]]),S=numeric$1.tensor(u,numeric$1.dot(u,R=[r[0],r[1],r[2]])),E=0;E<3;E++)for(L=r[E],F=S[E],P=0;P<i;P++)L[P]-=2*F[P];for(R=numeric$1.getBlock(r,[0,0],[i-1,2]),S=numeric$1.tensor(numeric$1.dot(R,u),u),E=0;E<i;E++)for(L=r[E],F=S[E],P=0;P<3;P++)L[P]-=2*F[P];for(S=numeric$1.tensor(u,numeric$1.dot(u,R=[x[0],x[1],x[2]])),E=0;E<3;E++)for(w=x[E],F=S[E],P=0;P<i;P++)w[P]-=2*F[P];for(U=0;U<i-2;U++){for(P=U;P<=U+1;P++)if(Math.abs(r[P+1][P])<X*(Math.abs(r[P][P])+Math.abs(r[P+1][P+1]))){for(ce=numeric$1.QRFrancis(numeric$1.getBlock(r,[0,0],[P,P]),l),d=numeric$1.QRFrancis(numeric$1.getBlock(r,[P+1,P+1],[i-1,i-1]),l),R=Array(P+1),E=0;E<=P;E++)R[E]=x[E];for(S=numeric$1.dot(ce.Q,R),E=0;E<=P;E++)x[E]=S[E];for(R=Array(i-P-1),E=P+1;E<i;E++)R[E-P-1]=x[E];for(S=numeric$1.dot(d.Q,R),E=P+1;E<i;E++)x[E]=S[E-P-1];return{Q:x,B:ce.B.concat(numeric$1.add(d.B,P+1))}}for(I=Math.min(i-1,U+3),s=Array(I-U),E=U+1;E<=I;E++)s[E-U-1]=r[E][U];for(u=numeric$1.house(s),R=numeric$1.getBlock(r,[U+1,U],[I,i-1]),S=numeric$1.tensor(u,numeric$1.dot(u,R)),E=U+1;E<=I;E++)for(L=r[E],F=S[E-U-1],P=U;P<i;P++)L[P]-=2*F[P-U];for(R=numeric$1.getBlock(r,[0,U+1],[i-1,I]),S=numeric$1.tensor(numeric$1.dot(R,u),u),E=0;E<i;E++)for(L=r[E],F=S[E],P=U+1;P<=I;P++)L[P]-=2*F[P-U-1];for(R=Array(I-U),E=U+1;E<=I;E++)R[E-U-1]=x[E];for(S=numeric$1.tensor(u,numeric$1.dot(u,R)),E=U+1;E<=I;E++)for(w=x[E],F=S[E-U-1],P=0;P<i;P++)w[P]-=2*F[P]}}throw new Error("numeric: eigenvalue iteration does not converge -- increase maxiter?")},numeric$1.eig=function(r,l){var s,u,g,b,x,w,L,R,S,E,U,P,V,X,ce,d,I,n=numeric$1.toUpperHessenberg(r),o=numeric$1.QRFrancis(n.H,l),i=numeric$1.T,p=o.B,v=numeric$1.dot(o.Q,numeric$1.dot(n.H,numeric$1.transpose(o.Q))),y=new i(numeric$1.dot(o.Q,n.Q)),D=p.length,he=Math.sqrt;for(u=0;u<D;u++)if((s=p[u][0])!==p[u][1]){if(x=v[s][s],L=v[b=s+1][s],R=v[b][b],0===(w=v[s][b])&&0===L)continue;(E=(S=-x-R)*S-4*(x*R-w*L))>=0?((ce=(x-(U=S<0?-.5*(S-he(E)):-.5*(S+he(E))))*(x-U)+w*w)>(d=L*L+(R-U)*(R-U))?(V=(x-U)/(ce=he(ce)),X=w/ce):(V=L/(d=he(d)),X=(R-U)/d),g=new i([[X,-V],[V,X]]),y.setRows(s,b,g.dot(y.getRows(s,b)))):(U=-.5*S,P=.5*he(-E),(ce=(x-U)*(x-U)+w*w)>(d=L*L+(R-U)*(R-U))?(V=(x-U)/(ce=he(ce+P*P)),X=w/ce,U=0,P/=ce):(V=L/(d=he(d+P*P)),X=(R-U)/d,U=P/d,P=0),g=new i([[X,-V],[V,X]],[[U,P],[P,-U]]),y.setRows(s,b,g.dot(y.getRows(s,b))))}var ae=y.dot(r).dot(y.transjugate()),Q=numeric$1.T.identity(I=r.length);for(b=0;b<I;b++)if(b>0)for(u=b-1;u>=0;u--){var ue=ae.get([u,u]),ee=ae.get([b,b]);numeric$1.neq(ue.x,ee.x)||numeric$1.neq(ue.y,ee.y)?(U=ae.getRow(u).getBlock([u],[b-1]),P=Q.getRow(b).getBlock([u],[b-1]),Q.set([b,u],ae.get([u,b]).neg().sub(U.dot(P)).div(ue.sub(ee)))):Q.setRow(b,Q.getRow(u))}for(b=0;b<I;b++)U=Q.getRow(b),Q.setRow(b,U.div(U.norm2()));return Q=Q.transpose(),Q=y.transjugate().dot(Q),{lambda:ae.getDiag(),E:Q}},numeric$1.ccsSparse=function(r){var n,o,i,l=r.length,s=[];for(o=l-1;-1!==o;--o)for(i in n=r[o]){for(i=parseInt(i);i>=s.length;)s[s.length]=0;0!==n[i]&&s[i]++}var u=s.length,a=Array(u+1);for(a[0]=0,o=0;o<u;++o)a[o+1]=a[o]+s[o];var p=Array(a[u]),v=Array(a[u]);for(o=l-1;-1!==o;--o)for(i in n=r[o])0!==n[i]&&(s[i]--,p[a[i]+s[i]]=o,v[a[i]+s[i]]=n[i]);return[a,p,v]},numeric$1.ccsFull=function(r){var a,p,y,l=r[0],n=r[1],o=r[2],i=numeric$1.ccsDim(r),u=i[1],D=numeric$1.rep([i[0],u],0);for(a=0;a<u;a++)for(y=l[a+1],p=l[a];p<y;++p)D[n[p]][a]=o[p];return D},numeric$1.ccsTSolve=function(r,l,n,o,i){function s(U){var P;if(0===n[U]){for(n[U]=1,P=u[U];P<u[U+1];++P)s(a[P]);i[g]=U,++g}}var D,b,x,w,L,R,E,u=r[0],a=r[1],p=r[2],y=Math.max,g=0;for(void 0===o&&(n=numeric$1.rep([u.length-1],0)),void 0===o&&(o=numeric$1.linspace(0,n.length-1)),void 0===i&&(i=[]),D=o.length-1;-1!==D;--D)s(o[D]);for(i.length=g,D=i.length-1;-1!==D;--D)n[i[D]]=0;for(D=o.length-1;-1!==D;--D)n[b=o[D]]=l[b];for(D=i.length-1;-1!==D;--D){for(w=y(u[(b=i[D])+1],x=u[b]),L=x;L!==w;++L)if(a[L]===b){n[b]/=p[L];break}for(E=n[b],L=x;L!==w;++L)(R=a[L])!==b&&(n[R]-=E*p[L])}return n},numeric$1.ccsDFS=function(r){this.k=Array(r),this.k1=Array(r),this.j=Array(r)},numeric$1.ccsDFS.prototype.dfs=function(r,l,n,o,i,s){var a,D,b,u=0,p=i.length,v=this.k,y=this.k1,g=this.j;if(0===o[r])for(o[r]=1,g[0]=r,v[0]=D=l[r],y[0]=b=l[r+1];;)if(D>=b){if(i[p]=g[u],0===u)return;++p,D=v[--u],b=y[u]}else 0===o[a=s[n[D]]]?(o[a]=1,v[u]=D,g[++u]=a,D=l[a],y[u]=b=l[a+1]):++D},numeric$1.ccsLPSolve=function(r,l,n,o,i,s,u){var w,L,R,S,E,U,P,V,d,a=r[0],p=r[1],v=r[2],D=l[0],b=l[1],x=l[2];for(L=D[i],R=D[i+1],o.length=0,w=L;w<R;++w)u.dfs(s[b[w]],a,p,n,o,s);for(w=o.length-1;-1!==w;--w)n[o[w]]=0;for(w=L;w!==R;++w)n[S=s[b[w]]]=x[w];for(w=o.length-1;-1!==w;--w){for(U=a[(S=o[w])+1],P=E=a[S];P<U;++P)if(s[p[P]]===S){n[S]/=v[P];break}for(d=n[S],P=E;P<U;++P)(V=s[p[P]])!==S&&(n[V]-=d*v[P])}return n},numeric$1.ccsLUP1=function(r,l){var b,x,w,S,F,E,U,n=r[0].length-1,o=[numeric$1.rep([n+1],0),[],[]],i=[numeric$1.rep([n+1],0),[],[]],s=o[0],u=o[1],a=o[2],p=i[0],v=i[1],y=i[2],g=numeric$1.rep([n],0),D=numeric$1.rep([n],0),V=numeric$1.ccsLPSolve,ce=(Math,Math.abs),d=numeric$1.linspace(0,n-1),he=numeric$1.linspace(0,n-1),ae=new numeric$1.ccsDFS(n);for(void 0===l&&(l=1),b=0;b<n;++b){for(V(o,r,g,D,b,he,ae),S=-1,F=-1,x=D.length-1;-1!==x;--x)!((w=D[x])<=b)&&(E=ce(g[w]))>S&&(F=w,S=E);for(ce(g[b])<l*S&&(x=d[b],d[b]=S=d[F],he[S]=b,d[F]=x,he[x]=F,S=g[b],g[b]=g[F],g[F]=S),F=p[b],U=g[b],u[S=s[b]]=d[b],a[S]=1,++S,x=D.length-1;-1!==x;--x)E=g[w=D[x]],D[x]=0,g[w]=0,w<=b?(v[F]=w,y[F]=E,++F):(u[S]=d[w],a[S]=E/U,++S);s[b+1]=S,p[b+1]=F}for(x=u.length-1;-1!==x;--x)u[x]=he[u[x]];return{L:o,U:i,P:d,Pinv:he}},numeric$1.ccsDFS0=function(r){this.k=Array(r),this.k1=Array(r),this.j=Array(r)},numeric$1.ccsDFS0.prototype.dfs=function(r,l,n,o,i,s,u){var p,b,x,a=0,v=i.length,y=this.k,g=this.k1,D=this.j;if(0===o[r])for(o[r]=1,D[0]=r,y[0]=b=l[s[r]],g[0]=x=l[s[r]+1];;){if(isNaN(b))throw new Error("Ow!");if(b>=x){if(i[v]=s[D[a]],0===a)return;++v,b=y[--a],x=g[a]}else 0===o[p=n[b]]?(o[p]=1,y[a]=b,D[++a]=p,b=l[p=s[p]],g[a]=x=l[p+1]):++b}},numeric$1.ccsLPSolve0=function(r,l,n,o,i,s,u,a){var L,R,S,F,U,P,V,X,he,p=r[0],v=r[1],y=r[2],b=l[0],x=l[1],w=l[2];for(R=b[i],S=b[i+1],o.length=0,L=R;L<S;++L)a.dfs(x[L],p,v,n,o,s,u);for(L=o.length-1;-1!==L;--L)n[u[F=o[L]]]=0;for(L=R;L!==S;++L)n[F=x[L]]=w[L];for(L=o.length-1;-1!==L;--L){for(X=u[F=o[L]],P=p[F+1],V=U=p[F];V<P;++V)if(v[V]===X){n[X]/=y[V];break}for(he=n[X],V=U;V<P;++V)n[v[V]]-=he*y[V];n[X]=he}},numeric$1.ccsLUP0=function(r,l){var b,x,w,S,F,E,U,n=r[0].length-1,o=[numeric$1.rep([n+1],0),[],[]],i=[numeric$1.rep([n+1],0),[],[]],s=o[0],u=o[1],a=o[2],p=i[0],v=i[1],y=i[2],g=numeric$1.rep([n],0),D=numeric$1.rep([n],0),V=numeric$1.ccsLPSolve0,ce=(Math,Math.abs),d=numeric$1.linspace(0,n-1),he=numeric$1.linspace(0,n-1),ae=new numeric$1.ccsDFS0(n);for(void 0===l&&(l=1),b=0;b<n;++b){for(V(o,r,g,D,b,he,d,ae),S=-1,F=-1,x=D.length-1;-1!==x;--x)!((w=D[x])<=b)&&(E=ce(g[d[w]]))>S&&(F=w,S=E);for(ce(g[d[b]])<l*S&&(x=d[b],d[b]=S=d[F],he[S]=b,d[F]=x,he[x]=F),F=p[b],U=g[d[b]],u[S=s[b]]=d[b],a[S]=1,++S,x=D.length-1;-1!==x;--x)E=g[d[w=D[x]]],D[x]=0,g[d[w]]=0,w<=b?(v[F]=w,y[F]=E,++F):(u[S]=d[w],a[S]=E/U,++S);s[b+1]=S,p[b+1]=F}for(x=u.length-1;-1!==x;--x)u[x]=he[u[x]];return{L:o,U:i,P:d,Pinv:he}},numeric$1.ccsLUP=numeric$1.ccsLUP0,numeric$1.ccsDim=function(r){return[numeric$1.sup(r[1])+1,r[0].length-1]},numeric$1.ccsGetBlock=function(r,l,n){var o=numeric$1.ccsDim(r),i=o[0],s=o[1];void 0===l?l=numeric$1.linspace(0,i-1):"number"==typeof l&&(l=[l]),void 0===n?n=numeric$1.linspace(0,s-1):"number"==typeof n&&(n=[n]);var u,y,D,b,v=l.length,g=n.length,w=numeric$1.rep([s],0),L=[],R=[],S=[w,L,R],F=r[0],E=r[1],U=r[2],P=numeric$1.rep([i],0),V=0,X=numeric$1.rep([i],0);for(y=0;y<g;++y){var ce=F[b=n[y]],d=F[b+1];for(u=ce;u<d;++u)X[D=E[u]]=1,P[D]=U[u];for(u=0;u<v;++u)X[l[u]]&&(L[V]=u,R[V]=P[l[u]],++V);for(u=ce;u<d;++u)X[D=E[u]]=0;w[y+1]=V}return S},numeric$1.ccsDot=function(r,l){var E,U,P,V,X,d,he,ae,I,Q,n=r[0],o=r[1],i=r[2],s=l[0],u=l[1],a=l[2],p=numeric$1.ccsDim(r),v=numeric$1.ccsDim(l),y=p[0],D=v[1],b=numeric$1.rep([y],0),x=numeric$1.rep([y],0),w=Array(y),L=numeric$1.rep([D],0),R=[],S=[],F=[L,R,S];for(P=0;P!==D;++P){for(X=s[P+1],ae=0,U=s[P];U<X;++U)for(Q=a[U],d=n[(I=u[U])+1],E=n[I];E<d;++E)0===x[he=o[E]]&&(w[ae]=he,x[he]=1,ae+=1),b[he]=b[he]+i[E]*Q;for(L[P+1]=X=(V=L[P])+ae,U=ae-1;-1!==U;--U)R[Q=V+U]=E=w[U],S[Q]=b[E],x[E]=0,b[E]=0;L[P+1]=L[P]+ae}return F},numeric$1.ccsLUPSolve=function(r,l){var n=r.L,o=r.U,s=l[0],u=!1;"object"!=typeof s&&(s=(l=[[0,l.length],numeric$1.linspace(0,l.length-1),l])[0],u=!0);var F,E,P,V,X,a=l[1],p=l[2],v=n[0].length-1,y=s.length-1,g=numeric$1.rep([v],0),D=Array(v),b=numeric$1.rep([v],0),x=Array(v),w=numeric$1.rep([y+1],0),L=[],R=[],S=numeric$1.ccsTSolve,ce=0;for(F=0;F<y;++F){for(V=0,P=s[F+1],E=s[F];E<P;++E)x[V]=X=r.Pinv[a[E]],b[X]=p[E],++V;for(x.length=V,S(n,b,g,x,D),E=x.length-1;-1!==E;--E)b[x[E]]=0;if(S(o,g,b,D,x),u)return b;for(E=D.length-1;-1!==E;--E)g[D[E]]=0;for(E=x.length-1;-1!==E;--E)L[ce]=X=x[E],R[ce]=b[X],b[X]=0,++ce;w[F+1]=ce}return[w,L,R]},numeric$1.ccsbinop=function(r,l){return void 0===l&&(l=""),Function("X","Y","var Xi = X[0], Xj = X[1], Xv = X[2];\nvar Yi = Y[0], Yj = Y[1], Yv = Y[2];\nvar n = Xi.length-1,m = Math.max(numeric.sup(Xj),numeric.sup(Yj))+1;\nvar Zi = numeric.rep([n+1],0), Zj = [], Zv = [];\nvar x = numeric.rep([m],0),y = numeric.rep([m],0);\nvar xk,yk,zk;\nvar i,j,j0,j1,k,p=0;\n"+l+"for(i=0;i<n;++i) {\n  j0 = Xi[i]; j1 = Xi[i+1];\n  for(j=j0;j!==j1;++j) {\n    k = Xj[j];\n    x[k] = 1;\n    Zj[p] = k;\n    ++p;\n  }\n  j0 = Yi[i]; j1 = Yi[i+1];\n  for(j=j0;j!==j1;++j) {\n    k = Yj[j];\n    y[k] = Yv[j];\n    if(x[k] === 0) {\n      Zj[p] = k;\n      ++p;\n    }\n  }\n  Zi[i+1] = p;\n  j0 = Xi[i]; j1 = Xi[i+1];\n  for(j=j0;j!==j1;++j) x[Xj[j]] = Xv[j];\n  j0 = Zi[i]; j1 = Zi[i+1];\n  for(j=j0;j!==j1;++j) {\n    k = Zj[j];\n    xk = x[k];\n    yk = y[k];\n"+r+"\n    Zv[j] = zk;\n  }\n  j0 = Xi[i]; j1 = Xi[i+1];\n  for(j=j0;j!==j1;++j) x[Xj[j]] = 0;\n  j0 = Yi[i]; j1 = Yi[i+1];\n  for(j=j0;j!==j1;++j) y[Yj[j]] = 0;\n}\nreturn [Zi,Zj,Zv];")},function(){var k,A,B,C;for(k in numeric$1.ops2)A=isFinite(eval("1"+numeric$1.ops2[k]+"0"))?"[Y[0],Y[1],numeric."+k+"(X,Y[2])]":"NaN",B=isFinite(eval("0"+numeric$1.ops2[k]+"1"))?"[X[0],X[1],numeric."+k+"(X[2],Y)]":"NaN",C=isFinite(eval("1"+numeric$1.ops2[k]+"0"))&&isFinite(eval("0"+numeric$1.ops2[k]+"1"))?"numeric.ccs"+k+"MM(X,Y)":"NaN",numeric$1["ccs"+k+"MM"]=numeric$1.ccsbinop("zk = xk "+numeric$1.ops2[k]+"yk;"),numeric$1["ccs"+k]=Function("X","Y",'if(typeof X === "number") return '+A+';\nif(typeof Y === "number") return '+B+";\nreturn "+C+";\n")}(),numeric$1.ccsScatter=function(r){var y,l=r[0],n=r[1],o=r[2],i=numeric$1.sup(n)+1,s=l.length,u=numeric$1.rep([i],0),a=Array(s),p=Array(s),v=numeric$1.rep([i],0);for(y=0;y<s;++y)v[n[y]]++;for(y=0;y<i;++y)u[y+1]=u[y]+v[y];var D,b,g=u.slice(0);for(y=0;y<s;++y)a[D=g[b=n[y]]]=l[y],p[D]=o[y],g[b]=g[b]+1;return[u,a,p]},numeric$1.ccsGather=function(r){var v,y,D,b,l=r[0],n=r[1],o=r[2],i=l.length-1,s=n.length,u=Array(s),a=Array(s),p=Array(s);for(b=0,v=0;v<i;++v)for(D=l[v+1],y=l[v];y!==D;++y)a[b]=v,u[b]=n[y],p[b]=o[y],++b;return[u,a,p]},numeric$1.sdim=function r(l,n,o){if(void 0===n&&(n=[]),"object"!=typeof l)return n;var i;for(i in void 0===o&&(o=0),o in n||(n[o]=0),l.length>n[o]&&(n[o]=l.length),l)l.hasOwnProperty(i)&&r(l[i],n,o+1);return n},numeric$1.sclone=function r(l,n,o){void 0===n&&(n=0),void 0===o&&(o=numeric$1.sdim(l).length);var i,s=Array(l.length);if(n===o-1){for(i in l)l.hasOwnProperty(i)&&(s[i]=l[i]);return s}for(i in l)l.hasOwnProperty(i)&&(s[i]=r(l[i],n+1,o));return s},numeric$1.sdiag=function(r){var n,i,l=r.length,o=Array(l);for(n=l-1;n>=1;n-=2)i=n-1,o[n]=[],o[n][n]=r[n],o[i]=[],o[i][i]=r[i];return 0===n&&(o[0]=[],o[0][0]=r[n]),o},numeric$1.sidentity=function(r){return numeric$1.sdiag(numeric$1.rep([r],1))},numeric$1.stranspose=function(r){var o,i,s,l=[];for(o in r)if(r.hasOwnProperty(o))for(i in s=r[o])!s.hasOwnProperty(i)||("object"!=typeof l[i]&&(l[i]=[]),l[i][o]=s[i]);return l},numeric$1.sLUP=function(r,l){throw new Error("The function numeric.sLUP had a bug in it and has been removed. Please use the new numeric.ccsLUP function instead.")},numeric$1.sdotMM=function(r,l){var u,a,p,v,y,g,b,n=r.length,i=numeric$1.stranspose(l),s=i.length,D=Array(n);for(p=n-1;p>=0;p--){for(b=[],u=r[p],y=s-1;y>=0;y--){for(v in g=0,a=i[y],u)!u.hasOwnProperty(v)||v in a&&(g+=u[v]*a[v]);g&&(b[y]=g)}D[p]=b}return D},numeric$1.sdotMV=function(r,l){var o,i,s,a,n=r.length,u=Array(n);for(i=n-1;i>=0;i--){for(s in a=0,o=r[i])!o.hasOwnProperty(s)||l[s]&&(a+=o[s]*l[s]);a&&(u[i]=a)}return u},numeric$1.sdotVM=function(r,l){var n,o,i,s,u=[];for(n in r)if(r.hasOwnProperty(n))for(o in s=r[n],i=l[n])!i.hasOwnProperty(o)||(u[o]||(u[o]=0),u[o]+=s*i[o]);return u},numeric$1.sdotVV=function(r,l){var n,o=0;for(n in r)r[n]&&l[n]&&(o+=r[n]*l[n]);return o},numeric$1.sdot=function(r,l){var n=numeric$1.sdim(r).length,o=numeric$1.sdim(l).length;switch(1e3*n+o){case 0:return r*l;case 1001:return numeric$1.sdotVV(r,l);case 2001:return numeric$1.sdotMV(r,l);case 1002:return numeric$1.sdotVM(r,l);case 2002:return numeric$1.sdotMM(r,l);default:throw new Error("numeric.sdot not implemented for tensors of order "+n+" and "+o)}},numeric$1.sscatter=function(r){var n,o,i,a,s=r.length,u=[];for(o=r[0].length-1;o>=0;--o)if(r[s-1][o]){for(a=u,i=0;i<s-2;i++)a[n=r[i][o]]||(a[n]=[]),a=a[n];a[r[i][o]]=r[i+1][o]}return u},numeric$1.sgather=function r(l,n,o){var i,s,u;for(s in void 0===n&&(n=[]),void 0===o&&(o=[]),i=o.length,l)if(l.hasOwnProperty(s))if(o[i]=parseInt(s),"number"==typeof(u=l[s])){if(u){if(0===n.length)for(s=i+1;s>=0;--s)n[s]=[];for(s=i;s>=0;--s)n[s].push(o[s]);n[i+1].push(u)}}else r(u,n,o);return o.length>i&&o.pop(),n},numeric$1.cLU=function(r){var s,u,a,p,v,y,l=r[0],n=r[1],o=r[2],X=l.length,i=0;for(s=0;s<X;s++)l[s]>i&&(i=l[s]);i++;var w,g=Array(i),D=Array(i),b=numeric$1.rep([i],1/0),x=numeric$1.rep([i],-1/0);for(a=0;a<X;a++)(u=n[a])<b[s=l[a]]&&(b[s]=u),u>x[s]&&(x[s]=u);for(s=0;s<i-1;s++)x[s]>x[s+1]&&(x[s+1]=x[s]);for(s=i-1;s>=1;s--)b[s]<b[s-1]&&(b[s-1]=b[s]);for(s=0;s<i;s++)D[s]=numeric$1.rep([x[s]-b[s]+1],0),g[s]=numeric$1.rep([s-b[s]],0);for(a=0;a<X;a++)D[s=l[a]][n[a]-b[s]]=o[a];for(s=0;s<i-1;s++)for(p=s-b[s],S=D[s],u=s+1;b[u]<=s&&u<i;u++)if(y=x[s]-s,w=(F=D[u])[v=s-b[u]]/S[p]){for(a=1;a<=y;a++)F[a+v]-=w*S[a+p];g[u][s-b[u]]=w}var ce,d,S=[],F=[],E=[],U=[],P=[],V=[];for(X=0,ce=0,s=0;s<i;s++){for(p=b[s],v=x[s],d=D[s],u=s;u<=v;u++)d[u-p]&&(S[X]=s,F[X]=u,E[X]=d[u-p],X++);for(d=g[s],u=p;u<s;u++)d[u-p]&&(U[ce]=s,P[ce]=u,V[ce]=d[u-p],ce++);U[ce]=s,P[ce]=s,V[ce]=1,ce++}return{U:[S,F,E],L:[U,P,V]}},numeric$1.cLUsolve=function(r,l){var x,L,n=r.L,o=r.U,i=numeric$1.clone(l),u=n[1],a=n[2],v=o[1],y=o[2],g=o[0].length,b=i.length;for(L=0,x=0;x<b;x++){for(;u[L]<x;)i[x]-=a[L]*i[u[L]],L++;L++}for(L=g-1,x=b-1;x>=0;x--){for(;v[L]>x;)i[x]-=y[L]*i[v[L]],L--;i[x]/=y[L],L--}return i},numeric$1.cgrid=function(r,l){"number"==typeof r&&(r=[r,r]);var o,i,s,n=numeric$1.rep(r,-1);for("function"!=typeof l&&(l="L"===l?function(u,a){return u>=r[0]/2||a<r[1]/2}:function(u,a){return!0}),s=0,o=1;o<r[0]-1;o++)for(i=1;i<r[1]-1;i++)l(o,i)&&(n[o][i]=s,s++);return n},numeric$1.cdelsq=function(r){var s,u,a,p,v,l=[[-1,0],[0,-1],[0,1],[1,0]],n=numeric$1.dim(r),o=n[0],i=n[1],y=[],g=[],D=[];for(s=1;s<o-1;s++)for(u=1;u<i-1;u++)if(!(r[s][u]<0)){for(a=0;a<4;a++)!(r[p=s+l[a][0]][v=u+l[a][1]]<0)&&(y.push(r[s][u]),g.push(r[p][v]),D.push(-1));y.push(r[s][u]),g.push(r[s][u]),D.push(4)}return[y,g,D]},numeric$1.cdotMV=function(r,l){var n,u,p,o=r[0],i=r[1],s=r[2],a=o.length;for(p=0,u=0;u<a;u++)o[u]>p&&(p=o[u]);for(p++,n=numeric$1.rep([p],0),u=0;u<a;u++)n[o[u]]+=s[u]*l[i[u]];return n},numeric$1.Spline=function(r,l,n,o,i){this.x=r,this.yl=l,this.yr=n,this.kl=o,this.kr=i},numeric$1.Spline.prototype._at=function(a,l){var p,v,y,n=this.x,o=this.yl,i=this.yr,u=this.kr,g=numeric$1.add,D=numeric$1.sub,b=numeric$1.mul;p=D(b(this.kl[l],n[l+1]-n[l]),D(i[l+1],o[l])),v=g(b(u[l+1],n[l]-n[l+1]),D(i[l+1],o[l]));var x=(y=(a-n[l])/(n[l+1]-n[l]))*(1-y);return g(g(g(b(1-y,o[l]),b(y,i[l+1])),b(p,x*(1-y))),b(v,x*y))},numeric$1.Spline.prototype.at=function(r){if("number"==typeof r){var n,o,i,l=this.x,v=l.length,s=Math.floor;for(n=0,o=v-1;o-n>1;)l[i=s((n+o)/2)]<=r?n=i:o=i;return this._at(r,n)}v=r.length;var y,g=Array(v);for(y=v-1;-1!==y;--y)g[y]=this.at(r[y]);return g},numeric$1.Spline.prototype.diff=function(){var u,a,p,r=this.x,l=this.yl,n=this.yr,o=this.kl,i=this.kr,s=l.length,v=o,y=i,g=Array(s),D=Array(s),b=numeric$1.add,x=numeric$1.mul,w=numeric$1.div,L=numeric$1.sub;for(u=s-1;-1!==u;--u)a=r[u+1]-r[u],p=L(n[u+1],l[u]),g[u]=w(b(x(p,6),x(o[u],-4*a),x(i[u+1],-2*a)),a*a),D[u+1]=w(b(x(p,-6),x(o[u],2*a),x(i[u+1],4*a)),a*a);return new numeric$1.Spline(r,v,y,g,D)},numeric$1.Spline.prototype.roots=function(){var F=[],n=this.x,o=this.yl,i=this.yr,s=this.kl,u=this.kr;"number"==typeof o[0]&&(o=[o],i=[i],s=[s],u=[u]);var v,y,g,w,L,R,S,E,U,P,V,X,ce,d,he,ae,Q,ue,ee,Le,xe,Ae,Pe,fe,a=o.length,p=n.length-1,Ke=(F=Array(a),Math.sqrt);for(v=0;v!==a;++v){for(w=o[v],L=i[v],R=s[v],S=u[v],E=[],y=0;y!==p;y++){for(y>0&&L[y]*w[y]<0&&E.push(n[y]),ce=(P=S[y+1]/(ae=n[y+1]-n[y]))+3*(V=w[y])+2*(U=R[y]/ae)-3*(X=L[y+1]),d=3*(P+U+2*(V-X)),(he=(fe=U-P+3*(V-X))*fe+12*P*V)<=0?Q=(ue=ce/d)>n[y]&&ue<n[y+1]?[n[y],ue,n[y+1]]:[n[y],n[y+1]]:(ue=(ce-Ke(he))/d,ee=(ce+Ke(he))/d,Q=[n[y]],ue>n[y]&&ue<n[y+1]&&Q.push(ue),ee>n[y]&&ee<n[y+1]&&Q.push(ee),Q.push(n[y+1])),ue=this._at(xe=Q[0],y),g=0;g<Q.length-1;g++)if(ee=this._at(Ae=Q[g+1],y),0!==ue)if(0===ee||ue*ee>0)xe=Ae,ue=ee;else{for(var te=0;!((Pe=(ue*Ae-ee*xe)/(ue-ee))<=xe||Pe>=Ae);)if((Le=this._at(Pe,y))*ee>0)Ae=Pe,ee=Le,-1===te&&(ue*=.5),te=-1;else{if(!(Le*ue>0))break;xe=Pe,ue=Le,1===te&&(ee*=.5),te=1}E.push(Pe),ue=this._at(xe=Q[g+1],y)}else E.push(xe),xe=Ae,ue=ee;0===ee&&E.push(Ae)}F[v]=E}return"number"==typeof this.yl[0]?F[0]:F},numeric$1.spline=function(r,l,n,o){var p,i=r.length,s=[],u=[],a=[],v=numeric$1.sub,y=numeric$1.mul,g=numeric$1.add;for(p=i-2;p>=0;p--)u[p]=r[p+1]-r[p],a[p]=v(l[p+1],l[p]);("string"==typeof n||"string"==typeof o)&&(n=o="periodic");var D=[[],[],[]];switch(typeof n){case"undefined":s[0]=y(3/(u[0]*u[0]),a[0]),D[0].push(0,0),D[1].push(0,1),D[2].push(2/u[0],1/u[0]);break;case"string":s[0]=g(y(3/(u[i-2]*u[i-2]),a[i-2]),y(3/(u[0]*u[0]),a[0])),D[0].push(0,0,0),D[1].push(i-2,0,1),D[2].push(1/u[i-2],2/u[i-2]+2/u[0],1/u[0]);break;default:s[0]=n,D[0].push(0),D[1].push(0),D[2].push(1)}for(p=1;p<i-1;p++)s[p]=g(y(3/(u[p-1]*u[p-1]),a[p-1]),y(3/(u[p]*u[p]),a[p])),D[0].push(p,p,p),D[1].push(p-1,p,p+1),D[2].push(1/u[p-1],2/u[p-1]+2/u[p],1/u[p]);switch(typeof o){case"undefined":s[i-1]=y(3/(u[i-2]*u[i-2]),a[i-2]),D[0].push(i-1,i-1),D[1].push(i-2,i-1),D[2].push(1/u[i-2],2/u[i-2]);break;case"string":D[1][D[1].length-1]=0;break;default:s[i-1]=o,D[0].push(i-1),D[1].push(i-1),D[2].push(1)}s="number"!=typeof s[0]?numeric$1.transpose(s):[s];var b=Array(s.length);if("string"==typeof n)for(p=b.length-1;-1!==p;--p)b[p]=numeric$1.ccsLUPSolve(numeric$1.ccsLUP(numeric$1.ccsScatter(D)),s[p]),b[p][i-1]=b[p][0];else for(p=b.length-1;-1!==p;--p)b[p]=numeric$1.cLUsolve(numeric$1.cLU(D),s[p]);return b="number"==typeof l[0]?b[0]:numeric$1.transpose(b),new numeric$1.Spline(r,l,l,b,b)},numeric$1.fftpow2=function r(l,n){var o=l.length;if(1!==o){var u,a,i=Math.cos,s=Math.sin,p=Array(o/2),v=Array(o/2),y=Array(o/2),g=Array(o/2);for(a=o/2,u=o-1;-1!==u;--u)y[--a]=l[u],g[a]=n[u],--u,p[a]=l[u],v[a]=n[u];r(p,v),r(y,g),a=o/2;var D,x,w,b=-6.283185307179586/o;for(u=o-1;-1!==u;--u)-1==--a&&(a=o/2-1),x=i(D=b*u),w=s(D),l[u]=p[a]+x*y[a]-w*g[a],n[u]=v[a]+x*g[a]+w*y[a]}},numeric$1._ifftpow2=function r(l,n){var o=l.length;if(1!==o){var u,a,i=Math.cos,s=Math.sin,p=Array(o/2),v=Array(o/2),y=Array(o/2),g=Array(o/2);for(a=o/2,u=o-1;-1!==u;--u)y[--a]=l[u],g[a]=n[u],--u,p[a]=l[u],v[a]=n[u];r(p,v),r(y,g),a=o/2;var D,x,w,b=6.283185307179586/o;for(u=o-1;-1!==u;--u)-1==--a&&(a=o/2-1),x=i(D=b*u),w=s(D),l[u]=p[a]+x*y[a]-w*g[a],n[u]=v[a]+x*g[a]+w*y[a]}},numeric$1.ifftpow2=function(r,l){numeric$1._ifftpow2(r,l),numeric$1.diveq(r,r.length),numeric$1.diveq(l,l.length)},numeric$1.convpow2=function(r,l,n,o){var i,u,a,p,v;for(numeric$1.fftpow2(r,l),numeric$1.fftpow2(n,o),i=r.length-1;-1!==i;--i)r[i]=(u=r[i])*(a=n[i])-(p=l[i])*(v=o[i]),l[i]=u*v+p*a;numeric$1.ifftpow2(r,l)},numeric$1.T.prototype.fft=function(){var g,b,r=this.x,l=this.y,n=r.length,o=Math.log,i=o(2),s=Math.ceil(o(2*n-1)/i),u=Math.pow(2,s),a=numeric$1.rep([u],0),p=numeric$1.rep([u],0),v=Math.cos,y=Math.sin,D=-3.141592653589793/n,x=numeric$1.rep([u],0),w=numeric$1.rep([u],0);for(Math.floor(n/2),g=0;g<n;g++)x[g]=r[g];if(void 0!==l)for(g=0;g<n;g++)w[g]=l[g];for(a[0]=1,g=1;g<=u/2;g++)a[g]=v(b=D*g*g),p[g]=y(b),a[u-g]=v(b),p[u-g]=y(b);var R=new numeric$1.T(x,w),S=new numeric$1.T(a,p);return R=R.mul(S),numeric$1.convpow2(R.x,R.y,numeric$1.clone(S.x),numeric$1.neg(S.y)),(R=R.mul(S)).x.length=n,R.y.length=n,R},numeric$1.T.prototype.ifft=function(){var g,b,r=this.x,l=this.y,n=r.length,o=Math.log,i=o(2),s=Math.ceil(o(2*n-1)/i),u=Math.pow(2,s),a=numeric$1.rep([u],0),p=numeric$1.rep([u],0),v=Math.cos,y=Math.sin,D=3.141592653589793/n,x=numeric$1.rep([u],0),w=numeric$1.rep([u],0);for(Math.floor(n/2),g=0;g<n;g++)x[g]=r[g];if(void 0!==l)for(g=0;g<n;g++)w[g]=l[g];for(a[0]=1,g=1;g<=u/2;g++)a[g]=v(b=D*g*g),p[g]=y(b),a[u-g]=v(b),p[u-g]=y(b);var R=new numeric$1.T(x,w),S=new numeric$1.T(a,p);return R=R.mul(S),numeric$1.convpow2(R.x,R.y,numeric$1.clone(S.x),numeric$1.neg(S.y)),(R=R.mul(S)).x.length=n,R.y.length=n,R.div(n)},numeric$1.gradient=function(r,l){var n=l.length,o=r(l);if(isNaN(o))throw new Error("gradient: f(x) is a NaN!");var i,u,a,R,S,F,U,P,V,b=Math.max,s=numeric$1.clone(l),p=Array(n),w=(b=Math.max,Math.abs),L=Math.min,E=0;for(i=0;i<n;i++)for(var X=b(1e-6*o,1e-8);;){if(++E>20)throw new Error("Numerical gradient fails");if(s[i]=l[i]+X,u=r(s),s[i]=l[i]-X,a=r(s),s[i]=l[i],isNaN(u)||isNaN(a))X/=16;else{if(p[i]=(u-a)/(2*X),R=l[i]-X,S=l[i],F=l[i]+X,U=(u-o)/X,P=(o-a)/X,V=b(w(p[i]),w(o),w(u),w(a),w(R),w(S),w(F),1e-8),!(L(b(w(U-p[i]),w(P-p[i]),w(U-P))/V,X/V)>.001))break;X/=16}}return p},numeric$1.uncmin=function(r,l,n,o,i,s,u){var a=numeric$1.gradient;void 0===u&&(u={}),void 0===n&&(n=1e-8),void 0===o&&(o=function(Se){return a(r,Se)}),void 0===i&&(i=1e3);var y,g,p=(l=numeric$1.clone(l)).length,v=r(l);if(isNaN(v))throw new Error("uncmin: f(x0) is a NaN!");var D=Math.max,b=numeric$1.norm2;n=D(n,numeric$1.epsilon);var x,w,L,Q,ue,ee,Le,Ae,Ke,te,R=u.Hinv||numeric$1.identity(p),S=numeric$1.dot,E=numeric$1.sub,U=numeric$1.add,P=numeric$1.tensor,V=numeric$1.div,X=numeric$1.mul,ce=numeric$1.all,d=numeric$1.isFinite,he=numeric$1.neg,ae=0,be="";for(w=o(l);ae<i;){if("function"==typeof s&&s(ae,l,v,w,R)){be="Callback returned true";break}if(!ce(d(w))){be="Gradient has Infinity or NaN";break}if(!ce(d(x=he(S(R,w))))){be="Search direction has Infinity or NaN";break}if((te=b(x))<n){be="Newton step smaller than tol";break}for(Ke=1,g=S(w,x),ue=l;ae<i&&!(Ke*te<n)&&(ue=U(l,Q=X(x,Ke)),(y=r(ue))-v>=.1*Ke*g||isNaN(y));)Ke*=.5,++ae;if(Ke*te<n){be="Line search step size smaller than tol";break}if(ae===i){be="maxit reached during line search";break}Ae=S(ee=E(L=o(ue),w),Q),Le=S(R,ee),R=E(U(R,X((Ae+S(ee,Le))/(Ae*Ae),P(Q,Q))),V(U(P(Le,Q),P(Q,Le)),Ae)),l=ue,v=y,w=L,++ae}return{solution:l,f:v,gradient:w,invHessian:R,iterations:ae,message:be}},numeric$1.Dopri=function(r,l,n,o,i,s,u){this.x=r,this.y=l,this.f=n,this.ymid=o,this.iterations=i,this.events=u,this.message=s},numeric$1.Dopri.prototype._at=function(w,l){function n(ce){return ce*ce}var v,y,g,D,b,x,P,V,X,o=this,i=o.x,s=o.y,u=o.f,F=(Math,numeric$1.add),E=numeric$1.mul,U=numeric$1.sub;return b=s[l+1],x=o.ymid[l],P=U(u[l],E(D=s[l],1/((v=i[l])-(g=v+.5*((y=i[l+1])-v)))+2/(v-y))),V=U(u[l+1],E(b,1/(y-g)+2/(y-v))),F(F(F(F(E(D,(X=[n(w-y)*(w-g)/n(v-y)/(v-g),n(w-v)*n(w-y)/n(v-g)/n(y-g),n(w-v)*(w-g)/n(y-v)/(y-g),(w-v)*n(w-y)*(w-g)/n(v-y)/(v-g),(w-y)*n(w-v)*(w-g)/n(v-y)/(y-g)])[0]),E(x,X[1])),E(b,X[2])),E(P,X[3])),E(V,X[4]))},numeric$1.Dopri.prototype.at=function(r){var l,n,o,i=Math.floor;if("number"!=typeof r){var s=r.length,u=Array(s);for(l=s-1;-1!==l;--l)u[l]=this.at(r[l]);return u}var a=this.x;for(l=0,n=a.length-1;n-l>1;)a[o=i(.5*(l+n))]<=r?l=o:n=o;return this._at(r,l)},numeric$1.dopri=function(r,l,n,o,i,s,u){void 0===i&&(i=1e-6),void 0===s&&(s=1e3);var y,g,D,b,x,w,he,ae,Le,xe,pe,He,Ne,a=[r],p=[n],v=[o(r,n)],L=[],S=[.075,.225],F=[44/45,-56/15,32/9],E=[19372/6561,-25360/2187,64448/6561,-212/729],U=[9017/3168,-355/33,46732/5247,49/176,-5103/18656],P=[35/384,0,500/1113,125/192,-2187/6784,11/84],V=[.10013431883002395,0,.3918321794184259,-.02982460176594817,.05893268337240795,-.04497888809104361,.023904308236133973],X=[.2,.3,.8,8/9,1,1],ce=[-71/57600,0,71/16695,-71/1920,17253/339200,-22/525,.025],d=0,I=(l-r)/10,Q=0,ue=numeric$1.add,ee=numeric$1.mul,Pe=(Math,Math.min),Ke=Math.abs,te=numeric$1.norminf,fe=Math.pow,ge=numeric$1.any,be=numeric$1.lt,Se=numeric$1.and,Ee=new numeric$1.Dopri(a,p,v,L,-1,"");for("function"==typeof u&&(pe=u(r,n));r<l&&Q<s;)if(++Q,r+I>l&&(I=l-r),y=o(r+X[0]*I,ue(n,ee(.2*I,v[d]))),g=o(r+X[1]*I,ue(ue(n,ee(S[0]*I,v[d])),ee(S[1]*I,y))),D=o(r+X[2]*I,ue(ue(ue(n,ee(F[0]*I,v[d])),ee(F[1]*I,y)),ee(F[2]*I,g))),b=o(r+X[3]*I,ue(ue(ue(ue(n,ee(E[0]*I,v[d])),ee(E[1]*I,y)),ee(E[2]*I,g)),ee(E[3]*I,D))),x=o(r+X[4]*I,ue(ue(ue(ue(ue(n,ee(U[0]*I,v[d])),ee(U[1]*I,y)),ee(U[2]*I,g)),ee(U[3]*I,D)),ee(U[4]*I,b))),w=o(r+I,Le=ue(ue(ue(ue(ue(n,ee(v[d],I*P[0])),ee(g,I*P[2])),ee(D,I*P[3])),ee(b,I*P[4])),ee(x,I*P[5]))),(xe="number"==typeof(he=ue(ue(ue(ue(ue(ee(v[d],I*ce[0]),ee(g,I*ce[2])),ee(D,I*ce[3])),ee(b,I*ce[4])),ee(x,I*ce[5])),ee(w,I*ce[6])))?Ke(he):te(he))>i){if(r+(I=.2*I*fe(i/xe,.25))===r){Ee.msg="Step size became too small";break}}else{if(L[d]=ue(ue(ue(ue(ue(ue(n,ee(v[d],I*V[0])),ee(g,I*V[2])),ee(D,I*V[3])),ee(b,I*V[4])),ee(x,I*V[5])),ee(w,I*V[6])),a[++d]=r+I,p[d]=Le,v[d]=w,"function"==typeof u){var je,Ue,qe=r,ze=r+.5*I;if(He=u(ze,L[d-1]),ge(Ne=Se(be(pe,0),be(0,He)))||(qe=ze,pe=He,He=u(ze=r+I,Le),Ne=Se(be(pe,0),be(0,He))),ge(Ne)){for(var gt,Qe,ht=0,Ze=1,ct=1;;){if("number"==typeof pe)Ue=(ct*He*qe-Ze*pe*ze)/(ct*He-Ze*pe);else for(Ue=ze,ae=pe.length-1;-1!==ae;--ae)pe[ae]<0&&He[ae]>0&&(Ue=Pe(Ue,(ct*He[ae]*qe-Ze*pe[ae]*ze)/(ct*He[ae]-Ze*pe[ae])));if(Ue<=qe||Ue>=ze)break;Qe=u(Ue,je=Ee._at(Ue,d-1)),ge(gt=Se(be(pe,0),be(0,Qe)))?(ze=Ue,He=Qe,Ne=gt,ct=1,-1===ht?Ze*=.5:Ze=1,ht=-1):(qe=Ue,pe=Qe,Ze=1,1===ht?ct*=.5:ct=1,ht=1)}return Le=Ee._at(.5*(r+Ue),d-1),Ee.f[d]=o(Ue,je),Ee.x[d]=Ue,Ee.y[d]=je,Ee.ymid[d-1]=Le,Ee.events=Ne,Ee.iterations=Q,Ee}}r+=I,n=Le,pe=He,I=Pe(.8*I*fe(i/xe,.25),4*I)}return Ee.iterations=Q,Ee},numeric$1.LU=function(r,l){l=l||!1;var o,i,s,u,a,p,v,y,g,n=Math.abs,D=r.length,b=D-1,x=new Array(D);for(l||(r=numeric$1.clone(r)),s=0;s<D;++s){for(v=s,g=n((p=r[s])[s]),i=s+1;i<D;++i)g<(u=n(r[i][s]))&&(g=u,v=i);for(x[s]=v,v!=s&&(r[s]=r[v],r[v]=p,p=r[s]),a=p[s],o=s+1;o<D;++o)r[o][s]/=a;for(o=s+1;o<D;++o){for(y=r[o],i=s+1;i<b;++i)y[i]-=y[s]*p[i],y[++i]-=y[s]*p[i];i===b&&(y[i]-=y[s]*p[i])}}return{LU:r,P:x}},numeric$1.LUsolve=function(r,l){var n,o,p,v,g,i=r.LU,s=i.length,u=numeric$1.clone(l),a=r.P;for(n=s-1;-1!==n;--n)u[n]=l[n];for(n=0;n<s;++n)for(p=a[n],a[n]!==n&&(g=u[n],u[n]=u[p],u[p]=g),v=i[n],o=0;o<n;++o)u[n]-=u[o]*v[o];for(n=s-1;n>=0;--n){for(v=i[n],o=n+1;o<s;++o)u[n]-=u[o]*v[o];u[n]/=v[n]}return u},numeric$1.solve=function(r,l,n){return numeric$1.LUsolve(numeric$1.LU(r,n),l)},numeric$1.echelonize=function(r){var u,a,p,v,y,g,D,b,l=numeric$1.dim(r),n=l[0],o=l[1],i=numeric$1.identity(n),s=Array(n),x=Math.abs,w=numeric$1.diveq;for(r=numeric$1.clone(r),u=0;u<n;++u){for(p=0,y=r[u],g=i[u],a=1;a<o;++a)x(y[p])<x(y[a])&&(p=a);for(s[u]=p,w(g,y[p]),w(y,y[p]),a=0;a<n;++a)if(a!==u){for(b=(D=r[a])[p],v=o-1;-1!==v;--v)D[v]-=y[v]*b;for(D=i[a],v=n-1;-1!==v;--v)D[v]-=g[v]*b}}return{I:i,A:r,P:s}},numeric$1.__solveLP=function(r,l,n,o,i,s,u){var L,te,be,we,a=numeric$1.sum,v=numeric$1.mul,y=numeric$1.sub,g=numeric$1.dot,D=numeric$1.div,b=numeric$1.add,x=r.length,w=n.length,R=!1,E=1,ce=(numeric$1.transpose(l),numeric$1.transpose),he=Math.sqrt,ae=Math.abs,ee=Math.min,Le=numeric$1.all,xe=numeric$1.gt,Ae=Array(x),Pe=Array(w),fe=(numeric$1.rep([w],1),numeric$1.solve),ge=y(n,g(l,s)),Se=g(r,r);for(be=0;be<i;++be){var pe,Ne;for(pe=w-1;-1!==pe;--pe)Pe[pe]=D(l[pe],ge[pe]);var Ee=ce(Pe);for(pe=x-1;-1!==pe;--pe)Ae[pe]=a(Ee[pe]);E=.25*ae(Se/g(r,Ae));var je=100*he(Se/g(Ae,Ae));for((!isFinite(E)||E>je)&&(E=je),we=b(r,v(E,Ae)),te=g(Ee,Pe),pe=x-1;-1!==pe;--pe)te[pe][pe]+=1;Ne=fe(te,D(we,E),!0);var qe=D(ge,g(l,Ne)),ze=1;for(pe=w-1;-1!==pe;--pe)qe[pe]<0&&(ze=ee(ze,-.999*qe[pe]));if(L=y(s,v(Ne,ze)),!Le(xe(ge=y(n,g(l,L)),0)))return{solution:s,message:"",iterations:be};if(s=L,E<o)return{solution:L,message:"",iterations:be};if(u){var Ue=g(r,we),et=g(l,we);for(R=!0,pe=w-1;-1!==pe;--pe)if(Ue*et[pe]<0){R=!1;break}}else R=!(s[x-1]>=0);if(R)return{solution:L,message:"Unbounded",iterations:be}}return{solution:s,message:"maximum iteration count exceeded",iterations:be}},numeric$1._solveLP=function(r,l,n,o,i){var s=r.length,u=n.length,y=numeric$1.sub,g=numeric$1.dot,x=numeric$1.rep([s],0).concat([1]),w=numeric$1.rep([u,1],-1),L=numeric$1.blockMatrix([[l,w]]),R=n,S=numeric$1.rep([s],0).concat(Math.max(0,numeric$1.sup(numeric$1.neg(n)))+1),F=numeric$1.__solveLP(x,L,R,o,i,S,!1),E=numeric$1.clone(F.solution);if(E.length=s,numeric$1.inf(y(n,g(l,E)))<0)return{solution:NaN,message:"Infeasible",iterations:F.iterations};var P=numeric$1.__solveLP(r,l,n,o,i-F.iterations,E,!0);return P.iterations+=F.iterations,P},numeric$1.solveLP=function(r,l,n,o,i,s,u){if(void 0===u&&(u=1e3),void 0===s&&(s=numeric$1.epsilon),void 0===o)return numeric$1._solveLP(r,l,n,s,u);var x,a=o.length,p=o[0].length,v=l.length,y=numeric$1.echelonize(o),g=numeric$1.rep([p],0),D=y.P,b=[];for(x=D.length-1;-1!==x;--x)g[D[x]]=1;for(x=p-1;-1!==x;--x)0===g[x]&&b.push(x);var w=numeric$1.getRange,L=numeric$1.linspace(0,a-1),R=numeric$1.linspace(0,v-1),S=w(o,L,b),F=w(l,R,D),E=w(l,R,b),U=numeric$1.dot,P=numeric$1.sub,V=U(F,y.I),X=P(E,U(V,S)),ce=P(n,U(V,i)),d=Array(D.length),he=Array(b.length);for(x=D.length-1;-1!==x;--x)d[x]=r[D[x]];for(x=b.length-1;-1!==x;--x)he[x]=r[b[x]];var ae=P(he,U(d,U(y.I,S))),I=numeric$1._solveLP(ae,X,ce,s,u),Q=I.solution;if(Q!=Q)return I;var ue=U(y.I,P(i,U(S,Q))),ee=Array(r.length);for(x=D.length-1;-1!==x;--x)ee[D[x]]=ue[x];for(x=b.length-1;-1!==x;--x)ee[b[x]]=Q[x];return{solution:ee,message:I.message,iterations:I.iterations}},numeric$1.MPStoLP=function(r){function l(P){throw new Error("MPStoLP: "+P+"\nLine "+s+": "+r[s]+"\nCurrent state: "+o[n]+"\n")}r instanceof String&&r.split("\n");var s,u,a,x,n=0,o=["Initial state","NAME","ROWS","COLUMNS","RHS","BOUNDS","ENDATA"],i=r.length,p=0,v={},y=[],g=0,D={},b=0,w=[],L=[],R=[];for(s=0;s<i;++s){var S=(a=r[s]).match(/\S*/g),F=[];for(u=0;u<S.length;++u)""!==S[u]&&F.push(S[u]);if(0!==F.length){for(u=0;u<o.length&&a.substr(0,o[u].length)!==o[u];++u);if(u<o.length){if(n=u,1===u&&(x=F[1]),6===u)return{name:x,c:w,A:numeric$1.transpose(L),b:R,rows:v,vars:D};continue}switch(n){case 0:case 1:l("Unexpected line");case 2:switch(F[0]){case"N":0===p?p=F[1]:l("Two or more N rows");break;case"L":v[F[1]]=g,y[g]=1,R[g]=0,++g;break;case"G":v[F[1]]=g,y[g]=-1,R[g]=0,++g;break;case"E":v[F[1]]=g,y[g]=0,R[g]=0,++g;break;default:l("Parse error "+numeric$1.prettyPrint(F))}break;case 3:D.hasOwnProperty(F[0])||(D[F[0]]=b,w[b]=0,L[b]=numeric$1.rep([g],0),++b);var E=D[F[0]];for(u=1;u<F.length;u+=2)if(F[u]!==p){var U=v[F[u]];L[E][U]=(y[U]<0?-1:1)*parseFloat(F[u+1])}else w[E]=parseFloat(F[u+1]);break;case 4:for(u=1;u<F.length;u+=2)R[v[F[u]]]=(y[v[F[u]]]<0?-1:1)*parseFloat(F[u+1]);break;case 5:break;case 6:l("Internal error")}}}l("Reached end of file without ENDATA")},numeric$1.seedrandom={pow:Math.pow,random:Math.random},function(r,l,n,o,i,s,u){function a(g){var D,x=this,w=g.length,L=0,R=x.i=x.j=x.m=0;for(x.S=[],x.c=[],w||(g=[w++]);L<n;)x.S[L]=L++;for(L=0;L<n;L++)R=y(R+(D=x.S[L])+g[L%w]),x.S[L]=x.S[R],x.S[R]=D;x.g=function(S){var F=x.S,E=y(x.i+1),U=F[E],P=y(x.j+U),V=F[P];F[E]=V,F[P]=U;for(var X=F[y(U+V)];--S;)E=y(E+1),P=y(P+(U=F[E])),F[E]=V=F[P],F[P]=U,X=X*n+F[y(U+V)];return x.i=E,x.j=P,X},x.g(n)}function p(g,D,b,x,w){if(b=[],w=typeof g,D&&"object"==w)for(x in g)if(x.indexOf("S")<5)try{b.push(p(g[x],D-1))}catch(L){}return b.length?b:g+("string"!=w?"\0":"")}function v(g,D,b,x){for(g+="",b=0,x=0;x<g.length;x++)D[y(x)]=y((b^=19*D[y(x)])+g.charCodeAt(x));for(x in g="",D)g+=String.fromCharCode(D[x]);return g}function y(g){return 255&g}l.seedrandom=function(g,D){var x,b=[];return g=v(p(D?[g,r]:arguments.length?g:[(new Date).getTime(),r,window],3),b),v((x=new a(b)).S,r),l.random=function(){for(var w=x.g(6),L=u,R=0;w<i;)w=(w+R)*n,L*=n,R=x.g(1);for(;w>=s;)w/=2,L/=2,R>>>=1;return(w+R)/L},g},u=l.pow(n,6),i=l.pow(2,i),s=2*i,v(l.random(),r)}([],numeric$1.seedrandom,256,0,52),function(r){function l(p){if("object"!=typeof p)return p;var y,v=[],g=p.length;for(y=0;y<g;y++)v[y+1]=l(p[y]);return v}function n(p){if("object"!=typeof p)return p;var y,v=[],g=p.length;for(y=1;y<g;y++)v[y-1]=n(p[y]);return v}r.solveQP=function a(p,v,y,g,D,b){p=l(p),v=l(v),y=l(y);var x,w,L,S,X,F=[],E=[],U=[],P=[],V=[];if(D=D||0,b=b?l(b):[void 0,0],g=g?l(g):[],w=p.length-1,L=y[1].length-1,!g)for(x=1;x<=L;x+=1)g[x]=0;for(x=1;x<=L;x+=1)E[x]=0;for(S=Math.min(w,L),x=1;x<=w;x+=1)U[x]=0;for(F[1]=0,x=1;x<=2*w+S*(S+5)/2+2*L+1;x+=1)P[x]=0;for(x=1;x<=2;x+=1)V[x]=0;return function u(p,v,y,g,D,b,x,w,L,R,S,F,E,U,P,V){function X(){for(U[1]=U[1]+1,ue=te,I=1;I<=R;I+=1){for(ue+=1,pe=-w[I],Q=1;Q<=g;Q+=1)pe+=x[Q][I]*D[Q];if(Math.abs(pe)<et&&(pe=0),I>S)P[ue]=pe;else if(P[ue]=-Math.abs(pe),pe>0){for(Q=1;Q<=g;Q+=1)x[Q][I]=-x[Q][I];w[I]=-w[I]}}for(I=1;I<=E;I+=1)P[te+F[I]]=0;for(ge=0,we=0,I=1;I<=R;I+=1)P[te+I]<we*P[Se+I]&&(ge=I,we=P[te+I]/P[Se+I]);return 0===ge?999:0}function ce(){for(I=1;I<=g;I+=1){for(pe=0,Q=1;Q<=g;Q+=1)pe+=p[Q][I]*x[Q][ge];P[I]=pe}for(ee=Ae,I=1;I<=g;I+=1)P[ee+I]=0;for(Q=E+1;Q<=g;Q+=1)for(I=1;I<=g;I+=1)P[ee+I]=P[ee+I]+p[I][Q]*P[Q];for(ze=!0,I=E;I>=1;I-=1){for(pe=P[I],ee=(ue=Ke+I*(I+3)/2)-I,Q=I+1;Q<=E;Q+=1)pe-=P[ue]*P[Pe+Q],ue+=Q;if(P[Pe+I]=pe/=P[ee],F[I]<S||pe<0)break;ze=!1,xe=I}if(!ze)for(He=P[fe+xe]/P[Pe+xe],I=1;I<=E&&!(F[I]<S||P[Pe+I]<0);I+=1)(we=P[fe+I]/P[Pe+I])<He&&(He=we,xe=I);for(pe=0,I=Ae+1;I<=Ae+g;I+=1)pe+=P[I]*P[I];if(Math.abs(pe)<=et){if(ze)return V[1]=1,999;for(I=1;I<=E;I+=1)P[fe+I]=P[fe+I]-He*P[Pe+I];return P[fe+E+1]=P[fe+E+1]+He,700}for(pe=0,I=1;I<=g;I+=1)pe+=P[Ae+I]*x[I][ge];for(Ne=-P[te+ge]/pe,Ue=!0,ze||He<Ne&&(Ne=He,Ue=!1),I=1;I<=g;I+=1)D[I]=D[I]+Ne*P[Ae+I],Math.abs(D[I])<et&&(D[I]=0);for(b[1]=b[1]+Ne*pe*(Ne/2+P[fe+E+1]),I=1;I<=E;I+=1)P[fe+I]=P[fe+I]-Ne*P[Pe+I];if(P[fe+E+1]=P[fe+E+1]+Ne,!Ue){for(pe=-w[ge],Q=1;Q<=g;Q+=1)pe+=D[Q]*x[Q][ge];if(ge>S)P[te+ge]=pe;else if(P[te+ge]=-Math.abs(pe),pe>0){for(Q=1;Q<=g;Q+=1)x[Q][ge]=-x[Q][ge];w[ge]=-w[ge]}return 700}for(F[E+=1]=ge,ue=Ke+(E-1)*E/2+1,I=1;I<=E-1;I+=1)P[ue]=P[I],ue+=1;if(E===g)P[ue]=P[g];else{for(I=g;I>=E+1&&0!==P[I]&&(Ee=Math.max(Math.abs(P[I-1]),Math.abs(P[I])),je=Math.min(Math.abs(P[I-1]),Math.abs(P[I])),we=P[I-1]>=0?Math.abs(Ee*Math.sqrt(1+je*je/(Ee*Ee))):-Math.abs(Ee*Math.sqrt(1+je*je/(Ee*Ee))),je=P[I]/we,1!=(Ee=P[I-1]/we));I-=1)if(0===Ee)for(P[I-1]=je*we,Q=1;Q<=g;Q+=1)we=p[Q][I-1],p[Q][I-1]=p[Q][I],p[Q][I]=we;else for(P[I-1]=we,qe=je/(1+Ee),Q=1;Q<=g;Q+=1)p[Q][I]=qe*(p[Q][I-1]+(we=Ee*p[Q][I-1]+je*p[Q][I]))-p[Q][I],p[Q][I-1]=we;P[ue]=P[E]}return 0}function d(){if(0===P[ee=(ue=Ke+xe*(xe+1)/2+1)+xe]||(Ee=Math.max(Math.abs(P[ee-1]),Math.abs(P[ee])),je=Math.min(Math.abs(P[ee-1]),Math.abs(P[ee])),we=P[ee-1]>=0?Math.abs(Ee*Math.sqrt(1+je*je/(Ee*Ee))):-Math.abs(Ee*Math.sqrt(1+je*je/(Ee*Ee))),je=P[ee]/we,1==(Ee=P[ee-1]/we)))return 798;if(0===Ee){for(I=xe+1;I<=E;I+=1)we=P[ee-1],P[ee-1]=P[ee],P[ee]=we,ee+=I;for(I=1;I<=g;I+=1)we=p[I][xe],p[I][xe]=p[I][xe+1],p[I][xe+1]=we}else{for(qe=je/(1+Ee),I=xe+1;I<=E;I+=1)P[ee]=qe*(P[ee-1]+(we=Ee*P[ee-1]+je*P[ee]))-P[ee],P[ee-1]=we,ee+=I;for(I=1;I<=g;I+=1)p[I][xe+1]=qe*(p[I][xe]+(we=Ee*p[I][xe]+je*p[I][xe+1]))-p[I][xe+1],p[I][xe]=we}return 0}function he(){for(ee=ue-xe,I=1;I<=xe;I+=1)P[ee]=P[ue],ue+=1,ee+=1;return P[fe+xe]=P[fe+xe+1],F[xe]=F[xe+1],(xe+=1)<E?797:0}function ae(){return P[fe+E]=P[fe+E+1],P[fe+E+1]=0,F[E]=0,E-=1,U[2]=U[2]+1,0}var I,Q,ue,ee,Le,xe,Ae,Pe,Ke,te,fe,ge,be,Se,we,pe,He,Ne,Ee,je,qe,ze,Ue,et,Dt,gt,Qe;be=Math.min(g,R),ue=2*g+be*(be+5)/2+2*R+1,et=1e-60;do{Dt=1+.1*(et+=et),gt=1+.2*et}while(Dt<=1||gt<=1);for(I=1;I<=g;I+=1)P[I]=v[I];for(I=g+1;I<=ue;I+=1)P[I]=0;for(I=1;I<=R;I+=1)F[I]=0;if(Le=[],0===V[1]){if(function s(p,v,y,g){var D,b,x,w,L,R;for(b=1;b<=y;b+=1){if(g[1]=b,R=0,(x=b-1)<1){if((R=p[b][b]-R)<=0)break;p[b][b]=Math.sqrt(R)}else{for(w=1;w<=x;w+=1){for(L=p[w][b],D=1;D<w;D+=1)L-=p[D][b]*p[D][w];p[w][b]=L/=p[w][w],R+=L*L}if((R=p[b][b]-R)<=0)break;p[b][b]=Math.sqrt(R)}g[1]=0}}(p,0,g,Le),0!==Le[1])return void(V[1]=2);(function i(p,v,y,g){var D,b,x,w;for(b=1;b<=y;b+=1){for(w=0,D=1;D<b;D+=1)w+=p[D][b]*g[D];g[b]=(g[b]-w)/p[b][b]}for(x=1;x<=y;x+=1)for(g[b=y+1-x]=g[b]/p[b][b],w=-g[b],D=1;D<b;D+=1)g[D]=g[D]+w*p[D][b]})(p,0,g,v),function o(p,v,y){var g,D,b,x,w;for(b=1;b<=y;b+=1){for(p[b][b]=1/p[b][b],w=-p[b][b],g=1;g<b;g+=1)p[g][b]=w*p[g][b];if(y<(x=b+1))break;for(D=x;D<=y;D+=1)for(w=p[b][D],p[b][D]=0,g=1;g<=b;g+=1)p[g][D]=p[g][D]+w*p[g][b]}}(p,0,g)}else{for(Q=1;Q<=g;Q+=1)for(D[Q]=0,I=1;I<=Q;I+=1)D[Q]=D[Q]+p[I][Q]*v[I];for(Q=1;Q<=g;Q+=1)for(v[Q]=0,I=Q;I<=g;I+=1)v[Q]=v[Q]+p[Q][I]*D[I]}for(b[1]=0,Q=1;Q<=g;Q+=1)for(D[Q]=v[Q],b[1]=b[1]+P[Q]*D[Q],P[Q]=0,I=Q+1;I<=g;I+=1)p[I][Q]=0;for(b[1]=-b[1]/2,V[1]=0,Se=(te=(Ke=(fe=(Pe=(Ae=g)+g)+be)+be+1)+be*(be+1)/2)+R,I=1;I<=R;I+=1){for(pe=0,Q=1;Q<=g;Q+=1)pe+=x[Q][I]*x[Q][I];P[Se+I]=Math.sqrt(pe)}for(E=0,U[1]=0,U[2]=0,Qe=0;;){if(999===(Qe=X()))return;for(;0!==(Qe=ce());){if(999===Qe)return;if(700===Qe)if(xe===E)ae();else{for(;d(),797===(Qe=he()););ae()}}}}(p,v,0,w,U,F,y,g,0,L,D,E,0,V,P,b),X="",1===b[1]&&(X="constraints are inconsistent, no solution!"),2===b[1]&&(X="matrix D in quadratic function is not positive definite!"),{solution:n(U),value:n(F),unconstrained_solution:n(v),iterations:n(V),iact:n(E),message:X}}}(numeric$1),numeric$1.svd=function(r){function l(he,ae){return(he=Math.abs(he))>(ae=Math.abs(ae))?he*Math.sqrt(1+ae*ae/he/he):0==ae?he:ae*Math.sqrt(1+he*he/ae/ae)}var n,o=numeric$1.epsilon,i=1e-64/o,u=0,a=0,p=0,v=0,y=0,g=numeric$1.clone(r),D=g.length,b=g[0].length;if(D<b)throw"Need more rows than columns";var x=new Array(b),w=new Array(b);for(a=0;a<b;a++)x[a]=w[a]=0;var L=numeric$1.rep([b,b],0),R=0,S=0,F=0,E=0,U=0,P=0,V=0;for(a=0;a<b;a++){for(x[a]=S,V=0,y=a+1,p=a;p<D;p++)V+=g[p][a]*g[p][a];if(V<=i)S=0;else for(R=g[a][a],S=Math.sqrt(V),R>=0&&(S=-S),F=R*S-V,g[a][a]=R-S,p=y;p<b;p++){for(V=0,v=a;v<D;v++)V+=g[v][a]*g[v][p];for(R=V/F,v=a;v<D;v++)g[v][p]+=R*g[v][a]}for(w[a]=S,V=0,p=y;p<b;p++)V+=g[a][p]*g[a][p];if(V<=i)S=0;else{for(R=g[a][a+1],S=Math.sqrt(V),R>=0&&(S=-S),F=R*S-V,g[a][a+1]=R-S,p=y;p<b;p++)x[p]=g[a][p]/F;for(p=y;p<D;p++){for(V=0,v=y;v<b;v++)V+=g[p][v]*g[a][v];for(v=y;v<b;v++)g[p][v]+=V*x[v]}}(U=Math.abs(w[a])+Math.abs(x[a]))>E&&(E=U)}for(a=b-1;-1!=a;a+=-1){if(0!=S){for(F=S*g[a][a+1],p=y;p<b;p++)L[p][a]=g[a][p]/F;for(p=y;p<b;p++){for(V=0,v=y;v<b;v++)V+=g[a][v]*L[v][p];for(v=y;v<b;v++)L[v][p]+=V*L[v][a]}}for(p=y;p<b;p++)L[a][p]=0,L[p][a]=0;L[a][a]=1,S=x[a],y=a}for(a=b-1;-1!=a;a+=-1){for(S=w[a],p=y=a+1;p<b;p++)g[a][p]=0;if(0!=S){for(F=g[a][a]*S,p=y;p<b;p++){for(V=0,v=y;v<D;v++)V+=g[v][a]*g[v][p];for(R=V/F,v=a;v<D;v++)g[v][p]+=R*g[v][a]}for(p=a;p<D;p++)g[p][a]=g[p][a]/S}else for(p=a;p<D;p++)g[p][a]=0;g[a][a]+=1}for(o*=E,v=b-1;-1!=v;v+=-1)for(var X=0;X<50;X++){var ce=!1;for(y=v;-1!=y;y+=-1){if(Math.abs(x[y])<=o){ce=!0;break}if(Math.abs(w[y-1])<=o)break}if(!ce){u=0,V=1;var d=y-1;for(a=y;a<v+1&&(R=V*x[a],x[a]=u*x[a],!(Math.abs(R)<=o));a++)for(F=l(R,S=w[a]),w[a]=F,u=S/F,V=-R/F,p=0;p<D;p++)g[p][d]=(U=g[p][d])*u+(P=g[p][a])*V,g[p][a]=-U*V+P*u}if(P=w[v],y==v){if(P<0)for(w[v]=-P,p=0;p<b;p++)L[p][v]=-L[p][v];break}if(X>=49)throw"Error: no convergence.";for(E=w[y],S=l(R=(((U=w[v-1])-P)*(U+P)+((S=x[v-1])-(F=x[v]))*(S+F))/(2*F*U),1),R=R<0?((E-P)*(E+P)+F*(U/(R-S)-F))/E:((E-P)*(E+P)+F*(U/(R+S)-F))/E,u=1,V=1,a=y+1;a<v+1;a++){for(U=w[a],F=V*(S=x[a]),S*=u,P=l(R,F),x[a-1]=P,R=E*(u=R/P)+S*(V=F/P),S=-E*V+S*u,F=U*V,U*=u,p=0;p<b;p++)L[p][a-1]=(E=L[p][a-1])*u+(P=L[p][a])*V,L[p][a]=-E*V+P*u;for(P=l(R,F),w[a-1]=P,R=(u=R/P)*S+(V=F/P)*U,E=-V*S+u*U,p=0;p<D;p++)g[p][a-1]=(U=g[p][a-1])*u+(P=g[p][a])*V,g[p][a]=-U*V+P*u}x[y]=0,x[v]=R,w[v]=E}for(a=0;a<w.length;a++)w[a]<o&&(w[a]=0);for(a=0;a<b;a++)for(p=a-1;p>=0;p--)if(w[p]<w[a]){for(u=w[p],w[p]=w[a],w[a]=u,v=0;v<g.length;v++)n=g[v][a],g[v][a]=g[v][p],g[v][p]=n;for(v=0;v<L.length;v++)n=L[v][a],L[v][a]=L[v][p],L[v][p]=n;a=p}return{U:g,S:w,V:L}};let SignatureService=(()=>{class r{constructor(){this.defaultOptions={drawOnly:!0,drawBezierCurves:!0,variableStrokeWidth:!0,lineTop:0,lineMargin:0,lineWidth:0,bgColour:"#FFFFFF",penWidth:4,penColour:"black",validateFields:!1},this.validators={required:()=>n=>!n.value||n.value.length<=0?{required:"This field is required"}:null,validateNoEdge:(n,o)=>i=>null==i.value?null:this.canvasHasInkyEdge(n,o)?{edgePoints:"Signature should not touch the edges"}:null}}isPixelColored(n,o){return this.colorDistance(n,o)>5}canvasHasInkyEdge(n,o){let i=n.getContext("2d");if(!i)return console.error("an error occurred when trying to get the canvas element"),!1;const{height:s,width:u}=n,v=(x,w,L,R)=>{var S;return null!==(S=this.getRectangleData(i,x,w,L,R,o))&&void 0!==S?S:0};return 4<=v(0,0,3,s)+v(u-3,0,3,s)+v(0,0,u,3)+v(0,s-3,u,3)}getResizedImage(n,o,i){return new Promise((s,u)=>{const a=document.createElement("img");a.onload=()=>{const p=document.createElement("canvas");p.width=o,p.height=i;const v=p.getContext("2d");null==v||v.drawImage(a,0,0,o,i),s(p)},a.src=n||""})}colorDistance(n,o){return["r","g","b"].map(i=>Math.abs(n[i]-o[i])).reduce((i,s)=>i+s,0)}getRectangleData(n,o,i,s,u,a){try{return this.dataToInkMap(n.getImageData(o,i,s,u),a).reduce((p,v)=>p+(this.colorDistance(v,a)>0?1:0),0)}catch(p){return}}dataToInkMap({data:n},o){const i=[],u={r:0,b:0,g:0},a={r:255,g:255,b:255};for(let p=0;p<n.length;p+=4)this.colorDistance({r:n[p],g:n[p+1],b:n[p+2]},o)>10?i.push(u):i.push(a);return i}}return r.\u0275fac=function(n){return new(n||r)},r.\u0275prov=_angular_core__WEBPACK_IMPORTED_MODULE_1__.Yz7({token:r,factory:r.\u0275fac,providedIn:"root"}),r})();initModule(jquery__WEBPACK_IMPORTED_MODULE_0__);let CCSignatureComponent=(()=>{class r extends _maids_cc_lib_common__WEBPACK_IMPORTED_MODULE_2__.Hr{constructor(n,o,i,s,u,a,p,v,y){super(n),this._bidi=o,this.signatureService=i,this.elRef=s,this.cdRef=u,this.zone=a,this.window=p,this.document=v,this.controlDir=y,this.width=405,this.aspect=2.5,this.signatureFont="Alex Brush",this.options={},this.bgColourRGB={r:255,g:255,b:255},this.typedSignatureForm=new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.cw({signature:new _angular_forms__WEBPACK_IMPORTED_MODULE_3__.NI}),this.activeTab=0,this.canvasClass="pad signature-pad rounded mx-0",this._valueChanges=new rxjs__WEBPACK_IMPORTED_MODULE_4__.x,this._img$=new rxjs__WEBPACK_IMPORTED_MODULE_5__.t,this.dir$=this._bidi.change.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.R)(this._destroy$)),y.valueAccessor=this}get height(){return this.getHeightFor(this.width)}set img(n){var o,i;this._img$.next(n),null===(o=this.controlDir.control)||void 0===o||o.setValue(n),null===(i=this.controlDir.control)||void 0===i||i.disable()}ngAfterViewInit(){setTimeout(()=>{const n=jquery__WEBPACK_IMPORTED_MODULE_0__(this.elRef.nativeElement);this.sigElm=n.signaturePad(Object.assign(Object.assign(Object.assign({},this.signatureService.defaultOptions),this.options),{onDrawEnd:()=>{setTimeout(()=>this._valueChanges.next(this.visibleCanvas.nativeElement.toDataURL()))}})),this.resizeCanvas(),this.window.size.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_7__.P)(),(0,rxjs_operators__WEBPACK_IMPORTED_MODULE_8__.w)(()=>this._img$),(0,rxjs_operators__WEBPACK_IMPORTED_MODULE_6__.R)(this._destroy$)).subscribe(o=>this.fillSignature(o)),this.registerBuiltinValidators()},100)}ngOnInit(){this.handleValueChanges()}registerBuiltinValidators(){const n=this.controlDir.validator,o=[this.signatureService.validators.validateNoEdge(this.visibleCanvas.nativeElement,this.bgColourRGB),this.signatureService.validators.required()];n&&o.push(n),this.controlDir.control.setValidators(o)}handleValueChanges(){this._valueChanges.pipe((0,rxjs_operators__WEBPACK_IMPORTED_MODULE_9__.b)(100)).subscribe(n=>{this.touch(),this._signalView2Model(n)})}save(n){if(this.clear(),null==this.typedSignatureForm.value.signature)return;const o=this.visibleCanvas.nativeElement.getContext("2d"),{width:i,height:s}=this.dimensions(),u={left:.05*i,right:.05*i,top:.05*s,bottom:.05*s},a=i/2-Math.max(u.left,u.right),p=.75*s-Math.max(u.top,u.bottom),v=Math.min(a,p).toFixed(0);o.font=`${v}px ${this.signatureFont}`,o.fillStyle="#000",o.textAlign="center",o.textBaseline="middle",o.fillText(n,i/2,s/2,i-u.left-u.right),this.activeTab=0,this._valueChanges.next(this.visibleCanvas.nativeElement.toDataURL())}fillSignature(n){return new Promise((o,i)=>{n||i("no data provided");const s=new Image;s.onload=()=>o(s),s.onerror=u=>i(u),s.src=n}).then(o=>{this.visibleCanvas.nativeElement.getContext("2d").drawImage(o,0,0,this.width,this.height)})}clearIfTyped(){this.typedSignatureForm.value.signature&&(this.typedSignatureForm.reset(),this.clear())}clear(){const{height:n,width:o}=this.dimensions();this.sigElm.clearCanvas();const i=this.visibleCanvas.nativeElement.getContext("2d");{const{r:s,g:u,b:a}=this.bgColourRGB;i.fillStyle=`#${s}${u}${a}ff`}i.fillRect(0,0,o,n),this.controlDir.reset(),this.cdRef.markForCheck()}resizeCanvas(){const{width:n}=this.formField.nativeElement.getBoundingClientRect();n<2||this.zone.runOutsideAngular(()=>{const o=n-2,i=this.getHeightFor(o);this.signatureService.getResizedImage(this.controlDir.value,o,i).then(s=>this.visibleCanvas.nativeElement.getContext("2d").drawImage(s,0,0)),this.width=o,this.cdRef.detectChanges()})}dimensions(){return{width:this.width,height:this.height}}getHeightFor(n){return n/this.aspect}}return r.\u0275fac=function(n){return new(n||r)(_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_core__WEBPACK_IMPORTED_MODULE_1__.zs3),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_cdk_bidi__WEBPACK_IMPORTED_MODULE_10__.Is),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(SignatureService),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_core__WEBPACK_IMPORTED_MODULE_1__.SBq),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_core__WEBPACK_IMPORTED_MODULE_1__.sBO),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_core__WEBPACK_IMPORTED_MODULE_1__.R0b),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_maids_cc_lib_common__WEBPACK_IMPORTED_MODULE_2__.un),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_common__WEBPACK_IMPORTED_MODULE_11__.K0),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Y36(_angular_forms__WEBPACK_IMPORTED_MODULE_3__.a5,2))},r.\u0275cmp=_angular_core__WEBPACK_IMPORTED_MODULE_1__.Xpm({type:r,selectors:[["cc-signature"]],viewQuery:function(n,o){if(1&n&&(_angular_core__WEBPACK_IMPORTED_MODULE_1__.Gf(_c0,5,_angular_core__WEBPACK_IMPORTED_MODULE_1__.SBq),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Gf(_c1,5,_angular_material_tabs__WEBPACK_IMPORTED_MODULE_12__.SP),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Gf(_angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.KE,5,_angular_core__WEBPACK_IMPORTED_MODULE_1__.SBq)),2&n){let i;_angular_core__WEBPACK_IMPORTED_MODULE_1__.iGM(i=_angular_core__WEBPACK_IMPORTED_MODULE_1__.CRH())&&(o.visibleCanvas=i.first),_angular_core__WEBPACK_IMPORTED_MODULE_1__.iGM(i=_angular_core__WEBPACK_IMPORTED_MODULE_1__.CRH())&&(o.tabGroupElement=i.first),_angular_core__WEBPACK_IMPORTED_MODULE_1__.iGM(i=_angular_core__WEBPACK_IMPORTED_MODULE_1__.CRH())&&(o.formField=i.first)}},hostVars:2,hostBindings:function(n,o){2&n&&_angular_core__WEBPACK_IMPORTED_MODULE_1__.Udp("display","block")},inputs:{aspect:"aspect",signatureFont:"signatureFont",options:"options",img:"img"},features:[_angular_core__WEBPACK_IMPORTED_MODULE_1__.qOj],decls:37,vars:38,consts:[[1,"w-100",3,"sizeChange"],["formField",""],["type","text","hidden","","matInput","",3,"formControl"],[2,"font-family","Signature","visibility","hidden"],[3,"dir","selectedIndex","selectedIndexChange"],["tabGroup",""],[3,"label"],[1,"w-100","typed"],["sigPad",""],["id","signature-pad",2,"border","1px solid grey",3,"mousedown"],["visibleCanvas",""],[1,"w-100"],["mat-raised-button","","color","primary",1,"w-100",3,"click"],["id","image_for_crop"],[1,"col-12",3,"formGroup","ngSubmit"],[1,"col-12","mt-3"],["matInput","","type","text","autocomplete","name","formControlName","signature","required",""],["mat-button","","color","accent","type","submit",1,"col-12",3,"disabled"],[3,"control"]],template:function(n,o){if(1&n&&(_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(0,"mat-form-field",0,1),_angular_core__WEBPACK_IMPORTED_MODULE_1__.NdJ("sizeChange",function(){return o.resizeCanvas()}),_angular_core__WEBPACK_IMPORTED_MODULE_1__._UZ(2,"input",2),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(3,"div",3),_angular_core__WEBPACK_IMPORTED_MODULE_1__._uU(4),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(5,"translate"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA(),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(6,"mat-tab-group",4,5),_angular_core__WEBPACK_IMPORTED_MODULE_1__.NdJ("selectedIndexChange",function(s){return o.activeTab=s}),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(8,"async"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(9,"mat-tab",6),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(10,"titlecase"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(11,"translate"),_angular_core__WEBPACK_IMPORTED_MODULE_1__._UZ(12,"div",7,8),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(14,"canvas",9,10),_angular_core__WEBPACK_IMPORTED_MODULE_1__.NdJ("mousedown",function(){return o.clearIfTyped()}),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA(),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(16,"div",11)(17,"button",12),_angular_core__WEBPACK_IMPORTED_MODULE_1__.NdJ("click",function(){return o.clear()}),_angular_core__WEBPACK_IMPORTED_MODULE_1__._uU(18),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(19,"titlecase"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(20,"translate"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA()(),_angular_core__WEBPACK_IMPORTED_MODULE_1__._UZ(21,"div",13),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA(),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(22,"mat-tab",6),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(23,"titlecase"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(24,"translate"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(25,"form",14),_angular_core__WEBPACK_IMPORTED_MODULE_1__.NdJ("ngSubmit",function(){return o.save(o.typedSignatureForm.value.signature)}),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(26,"mat-form-field",15)(27,"mat-label"),_angular_core__WEBPACK_IMPORTED_MODULE_1__._uU(28),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(29,"translate"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA(),_angular_core__WEBPACK_IMPORTED_MODULE_1__._UZ(30,"input",16),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA(),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(31,"button",17),_angular_core__WEBPACK_IMPORTED_MODULE_1__._uU(32),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(33,"titlecase"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.ALo(34,"translate"),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA()()()(),_angular_core__WEBPACK_IMPORTED_MODULE_1__.TgZ(35,"mat-error"),_angular_core__WEBPACK_IMPORTED_MODULE_1__._UZ(36,"cc-validation-message",18),_angular_core__WEBPACK_IMPORTED_MODULE_1__.qZA()()),2&n){let i;_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(2),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Q6J("formControl",o.controlDir.control),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(2),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Oqu(_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(5,16,"__cc_core.signature.sample-text")),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(2),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Q6J("dir",null!==(i=_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(8,18,o.dir$))&&void 0!==i?i:"auto")("selectedIndex",o.activeTab),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(3),_angular_core__WEBPACK_IMPORTED_MODULE_1__.s9C("label",_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(10,20,_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(11,22,"__cc_core.signature.drawit"))),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(5),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Tol(o.canvasClass),_angular_core__WEBPACK_IMPORTED_MODULE_1__.uIk("width",o.width)("height",o.height),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(4),_angular_core__WEBPACK_IMPORTED_MODULE_1__.hij(" ",_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(19,24,_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(20,26,"__cc_core.actions.clear"))," "),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(4),_angular_core__WEBPACK_IMPORTED_MODULE_1__.s9C("label",_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(23,28,_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(24,30,"__cc_core.signature.typeit"))),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(3),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Q6J("formGroup",o.typedSignatureForm),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(3),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Oqu(_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(29,32,"__cc_core.signature.type-your-signature")),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(3),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Q6J("disabled",!o.typedSignatureForm.valid),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(1),_angular_core__WEBPACK_IMPORTED_MODULE_1__.hij(" ",_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(33,34,_angular_core__WEBPACK_IMPORTED_MODULE_1__.lcZ(34,36,"__cc_core.actions.confirm"))," "),_angular_core__WEBPACK_IMPORTED_MODULE_1__.xp6(4),_angular_core__WEBPACK_IMPORTED_MODULE_1__.Q6J("control",o.controlDir.control)}},directives:[_angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.KE,_angular_material_tabs__WEBPACK_IMPORTED_MODULE_12__.SP,_angular_material_tabs__WEBPACK_IMPORTED_MODULE_12__.uX,_angular_material_button__WEBPACK_IMPORTED_MODULE_14__.lW,_maids_cc_lib_validation__WEBPACK_IMPORTED_MODULE_15__.Oq,_maids_cc_lib_common__WEBPACK_IMPORTED_MODULE_2__.on,_angular_material_input__WEBPACK_IMPORTED_MODULE_16__.Nt,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Fj,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.JJ,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.oH,_angular_cdk_bidi__WEBPACK_IMPORTED_MODULE_10__.Lv,_angular_forms__WEBPACK_IMPORTED_MODULE_3__._Y,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.JL,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.sg,_angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.hX,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.u,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.Q7,_angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.TO],pipes:[_ngx_translate_core__WEBPACK_IMPORTED_MODULE_17__.X$,_angular_common__WEBPACK_IMPORTED_MODULE_11__.Ov,_angular_common__WEBPACK_IMPORTED_MODULE_11__.rS],encapsulation:2,changeDetection:0}),r})(),CCSignatureModule=(()=>{class r{}return r.\u0275fac=function(n){return new(n||r)},r.\u0275mod=_angular_core__WEBPACK_IMPORTED_MODULE_1__.oAB({type:r}),r.\u0275inj=_angular_core__WEBPACK_IMPORTED_MODULE_1__.cJS({imports:[[_angular_common__WEBPACK_IMPORTED_MODULE_11__.ez,_angular_forms__WEBPACK_IMPORTED_MODULE_3__.UX,_angular_material_input__WEBPACK_IMPORTED_MODULE_16__.c,_angular_material_tabs__WEBPACK_IMPORTED_MODULE_12__.Nh,_angular_material_form_field__WEBPACK_IMPORTED_MODULE_13__.lN,_maids_cc_lib_validation__WEBPACK_IMPORTED_MODULE_15__.iv,_maids_cc_lib_common__WEBPACK_IMPORTED_MODULE_2__.Wu,_maids_cc_lib_common__WEBPACK_IMPORTED_MODULE_2__.hq,_angular_material_button__WEBPACK_IMPORTED_MODULE_14__.ot,_ngx_translate_core__WEBPACK_IMPORTED_MODULE_17__.aw]]}),r})()}}]);
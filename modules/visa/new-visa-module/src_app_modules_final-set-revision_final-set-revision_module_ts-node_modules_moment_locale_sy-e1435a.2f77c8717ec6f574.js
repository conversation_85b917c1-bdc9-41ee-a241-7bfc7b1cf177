(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_final-set-revision_final-set-revision_module_ts-node_modules_moment_locale_sy-e1435a"],{3232:(M,C,o)=>{"use strict";o.r(C),o.d(C,{FinalSetRevisionModule:()=>W});var m=o(69808),g=o(50727),e=o(5e3),r=o(40520),c=o(8188),u=o(43604);let b=(()=>{class a{constructor(t,n){this._http=t,this._api=n}fetchSettlementList$(t,n){return this._http.get(u.b.getSettlementList+`?page=${t}&size=${n}`)}getSettlementById(t){return this._http.get(u.b.getSettlementById+"?housemaid="+t)}calculateRevisedSettlement(t){return this._http.post(u.b.calculateRevisedSettlement+`?isReviseCase=true&terminationMode=${t.terminationMode}`,t)}sendRevisedFinalSettlement(t){const n=this.objectDestructuring(t);return this._http.post(u.b.sendRevisedFinalSettlement,n)}downloadCashAdvance(t){return this._http.get(u.b.downloadCashAdvance+"/"+t)}downloadContract(t){return this._http.get(u.b.downloadContract+"/"+t)}objectDestructuring(t){const{id:n,housemaid:s,terminationMode:i,prorated:h,vacations:f,gratuity:x,resignationFees:D,cashAdvance:ee,renewalExpenses:te,payHerByCash:ne,forgivenessAmountEnteredByExpertDelighter:se,forgivenessAmountCalculatedByERP:ae,forgivenessAmountCalculatedByERPNonRenewal:ie,forgivenessAmountDecidedByManager:oe,calculatedFinalSettlementValue:le,revisedFinalSettlement:re,reviseSMSNeeded:ce}=t;return delete s.label,{id:n,housemaid:s,terminationMode:i,prorated:h,vacations:f,gratuity:x,resignationFees:D,cashAdvance:ee,renewalExpenses:te,payHerByCash:ne,forgivenessAmountEnteredByExpertDelighter:se,forgivenessAmountCalculatedByERP:ae,forgivenessAmountCalculatedByERPNonRenewal:ie,forgivenessAmountDecidedByManager:oe,calculatedFinalSettlementValue:le,revisedFinalSettlement:re,reviseSMSNeeded:ce}}recalculateGratuity(t,n,s){const h=new r.LE({fromObject:{housemaid:t,calculationDate:n,gratuityBasedDays:s}});return this._http.get(u.b.recalculateGratuity,{params:h})}recalculateResignationFees(t,n,s,i){const f=new r.LE({fromObject:{housemaid:t,terminationMode:n,noticePeriodServed:s,daysServed:i}});return this._http.get(u.b.recalculateResignationFees,{params:f})}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(r.eN),e.LFG(c.JV))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac,providedIn:"root"}),a})();var v=o(1402),A=o(62764);const N=function(){return[10,20,30,40,50]};function J(a,l){if(1&a){const t=e.EpF();e.TgZ(0,"cc-datagrid",2),e.NdJ("page",function(s){return e.CHM(t),e.oxw().getNextPage(s)}),e.qZA()}if(2&a){const t=l.$implicit,n=e.oxw();e.Q6J("columns",n.gridCols)("data",t.content)("length",t.totalElements)("pageOnFront",!1)("pageSize",t.size)("pageSizeOptions",e.DdM(12,N))("columnMovable",!0)("columnHideable",!0)("showColumnMenuButton",!0)("showColumnMenuHeader",!1)("columnMenuButtonIcon","settings")("showFirstLastButtons",!0)}}let H=(()=>{class a{constructor(t,n){this.service=t,this.router=n,this.subscription=new g.w0,this.gridCols=[{field:"housemaid.name",header:"Housemaid"},{field:"housemaid.nationality.code",header:"Nationality"},{field:"finalSettlementAmount",header:"Final settlement amount"},{field:"pendingForTerminationDate",header:"Pending for termination date"},{field:"operations",header:"Details",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"icon",icon:"info",color:"primary",mode:"single",disabled:!1,hidden:s=>!1,callback:s=>this.viewDetails(s.housemaid.id)}]}}]}viewDetails(t){this.router.navigateByUrl(`visa/v2/final-settlement-revision/todo-details/${t}`)}ngOnInit(){const t=this.service.fetchSettlementList$(0,20).subscribe(n=>{this.settlementList=n});this.subscription.add(t)}getNextPage(t){const n=this.service.fetchSettlementList$(t.pageIndex,20).subscribe(s=>{this.settlementList=s});this.subscription.add(n)}ngOnDestroy(){this.subscription.unsubscribe()}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(b),e.Y36(v.F0))},a.\u0275cmp=e.Xpm({type:a,selectors:[["app-final-set-revision"]],decls:2,vars:1,consts:[[1,"margin-top"],[3,"columns","data","length","pageOnFront","pageSize","pageSizeOptions","columnMovable","columnHideable","showColumnMenuButton","showColumnMenuHeader","columnMenuButtonIcon","showFirstLastButtons","page",4,"ngIf"],[3,"columns","data","length","pageOnFront","pageSize","pageSizeOptions","columnMovable","columnHideable","showColumnMenuButton","showColumnMenuHeader","columnMenuButtonIcon","showFirstLastButtons","page"]],template:function(t,n){1&t&&(e._UZ(0,"div",0),e.YNc(1,J,1,13,"cc-datagrid",1)),2&t&&(e.xp6(1),e.Q6J("ngIf",n.settlementList))},directives:[m.O5,A.Ge],styles:[""]}),a})();var d=o(65620);const R=(0,d.PH)("[ Settlement ] Fetch Settlement Data",(0,d.Ky)()),w=(0,d.PH)("[ Settlement ] fetch Settlement Success",(0,d.Ky)()),k=((0,d.PH)("[ Settlement ] fetch Settlement failure",(0,d.Ky)()),(0,d.Lq)({settlementList:{content:[],number:0,size:20,totalElements:0,totalPages:0},settlementFormData:{}},(0,d.on)(w,(a,{payload:l})=>Object.assign(Object.assign({},a),{settlementList:l}))));var _=o(26991),I=o(63900),P=o(54004),S=o(21799);let L=(()=>{class a{constructor(t,n,s){this._actions=t,this._settlementService=n,this.notifications=s,this.fetchSettlementList$=(0,_.GW)(()=>this._actions.pipe((0,_.l4)(R),(0,I.w)(i=>this._settlementService.fetchSettlementList$(i.page,i.size)),(0,P.U)(i=>w({payload:i}))))}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(_.eX),e.LFG(b),e.LFG(S.zg))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})(),U=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({imports:[[d.Aw.forFeature("settlement",k),_.sQ.forFeature([L])],d.Aw,_.sQ]}),a})();var F=o(29690),Q=o(48966),O=o(82599),p=o(93075),T=o(43687),Z=o(79136),y=o(92431),j=o(65868),B=o(57902);function Y(a,l){if(1&a){const t=e.EpF();e.TgZ(0,"div",0)(1,"cc-input",35),e.NdJ("change",function(){return e.CHM(t),e.oxw(2).updateNoticeFee()})("ngModelChange",function(s){return e.CHM(t),e.oxw(2).settlementData.daysServed=s}),e.qZA()()}if(2&a){const t=e.oxw(2);e.xp6(1),e.Q6J("ngModel",t.settlementData.daysServed)}}function G(a,l){if(1&a&&(e.TgZ(0,"div")(1,"h5",36)(2,"span",37),e._uU(3,"Revise Final Settlement: "),e.qZA(),e._uU(4),e.qZA()()),2&a){const t=e.oxw(2);e.xp6(4),e.hij("AED ",t.settlementData.revisedFinalSettlement," ")}}function q(a,l){1&a&&(e.TgZ(0,"div",38),e._uU(1," Now we have to pay for the maid, please re-adjust the forgiveness amount "),e.qZA())}function $(a,l){if(1&a){const t=e.EpF();e.TgZ(0,"form",2)(1,"div",3)(2,"h4",4),e._uU(3),e.qZA(),e.TgZ(4,"div",5),e._uU(5," Final settlement total before revise: "),e.TgZ(6,"span",6),e._uU(7),e.qZA()()(),e.TgZ(8,"div",7)(9,"div",0)(10,"cc-input",8),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.prorated=s}),e.qZA()(),e.TgZ(11,"div",0)(12,"label",9),e._uU(13,"Calculate Gratuity Based on 21 Days:"),e.qZA(),e.TgZ(14,"cc-slide-toggle",10),e.NdJ("change",function(){return e.CHM(t),e.oxw().onGratuityBasedDaysChange()})("ngModelChange",function(s){return e.CHM(t),e.oxw().gratuityBasedDays=s}),e.qZA()(),e.TgZ(15,"div",0)(16,"cc-input",11),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.vacations=s}),e.qZA()(),e.TgZ(17,"div",0)(18,"cc-input",12),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.gratuity=s}),e.qZA()(),e.TgZ(19,"div",0)(20,"label",9),e._uU(21,"Maid served notice period:"),e.qZA(),e.TgZ(22,"cc-radio-group",13),e.NdJ("change",function(){return e.CHM(t),e.oxw().onNoticePeriodServedChange()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.noticePeriodServed=s}),e.TgZ(23,"cc-radio-button",14),e._uU(24,"Yes"),e.qZA(),e.TgZ(25,"cc-radio-button",14),e._uU(26,"No"),e.qZA(),e.TgZ(27,"cc-radio-button",14),e._uU(28,"Partially"),e.qZA()()(),e.YNc(29,Y,2,1,"div",15),e.TgZ(30,"div",0)(31,"cc-input",16),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.resignationFees=s}),e.qZA()(),e.TgZ(32,"div",0)(33,"cc-input",17),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.cashAdvance=s}),e.qZA()(),e.TgZ(34,"div",0)(35,"cc-input",18),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.additions=s}),e.qZA()(),e.TgZ(36,"div",0)(37,"cc-input",19),e.NdJ("change",function(){return e.CHM(t),e.oxw().onChangeHandler()})("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.deductions=s}),e.qZA()(),e.TgZ(38,"div",20)(39,"label",21),e._uU(40,"Pay her by cash:"),e.qZA(),e.TgZ(41,"div")(42,"cc-radio-group",22),e.NdJ("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.payHerByCash=s}),e.TgZ(43,"cc-radio-button",23),e._uU(44," no "),e.qZA(),e.TgZ(45,"cc-radio-button",23),e._uU(46," yes "),e.qZA()()()(),e.TgZ(47,"div",24)(48,"button",25),e.NdJ("click",function(){return e.CHM(t),e.oxw().calculateRevisedSettlement()}),e._uU(49," Calculate Revised Final Settlement "),e.qZA()(),e.YNc(50,G,5,1,"div",26),e.YNc(51,q,2,0,"div",27),e.TgZ(52,"div")(53,"cc-input",28),e.NdJ("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.desiredFinalSettlementAmountDecidedByErp=s}),e.qZA(),e.TgZ(54,"cc-input",29),e.NdJ("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.desiredFinalSettlementAmountDecidedByManager=s}),e.qZA(),e.TgZ(55,"cc-input",30),e.NdJ("ngModelChange",function(s){return e.CHM(t),e.oxw().settlementData.automaticForgivenessAmount=s}),e.qZA()(),e.TgZ(56,"div")(57,"button",31),e.NdJ("click",function(){return e.CHM(t),e.oxw().downloadCashAdvance()}),e._uU(58," Download cash advance "),e.qZA()(),e.TgZ(59,"div")(60,"button",31),e.NdJ("click",function(){return e.CHM(t),e.oxw().downloadContract()}),e._uU(61," Download contract "),e.qZA()()(),e.TgZ(62,"div",32),e._UZ(63,"cc-divider"),e.qZA(),e.TgZ(64,"div",33)(65,"button",34),e.NdJ("click",function(){return e.CHM(t),e.oxw().sendRevisedFinalSettlement()}),e._uU(66," Send Revised Final Settlement "),e.qZA(),e.TgZ(67,"button",34),e.NdJ("click",function(){return e.CHM(t),e.oxw().correctNoNeedToEdit()}),e._uU(68," Correct No Need To Edit "),e.qZA()()()}if(2&a){const t=e.oxw();e.xp6(3),e.hij("Final settlement of (",t.houseMaidNmae,")"),e.xp6(4),e.hij("AED ",t.settlementData.finalValue,""),e.xp6(3),e.Q6J("ngModel",t.settlementData.prorated),e.xp6(4),e.Q6J("ngModel",t.gratuityBasedDays),e.xp6(2),e.Q6J("ngModel",t.settlementData.vacations),e.xp6(2),e.Q6J("ngModel",t.settlementData.gratuity),e.xp6(4),e.Q6J("ngModel",t.settlementData.noticePeriodServed),e.xp6(1),e.Q6J("value","Yes"),e.xp6(2),e.Q6J("value","No"),e.xp6(2),e.Q6J("value","Partially"),e.xp6(2),e.Q6J("ngIf","Partially"==t.settlementData.noticePeriodServed),e.xp6(2),e.Q6J("ngModel",t.settlementData.resignationFees),e.xp6(2),e.Q6J("ngModel",t.settlementData.cashAdvance),e.xp6(2),e.Q6J("ngModel",t.settlementData.additions),e.xp6(2),e.Q6J("ngModel",t.settlementData.deductions),e.xp6(5),e.Q6J("ngModel",t.settlementData.payHerByCash),e.xp6(1),e.Q6J("value",!1),e.xp6(2),e.Q6J("value",!0),e.xp6(5),e.Q6J("ngIf",t.settlementData.revisedFinalSettlement),e.xp6(1),e.Q6J("ngIf",t.settlementData.finalValue<=0&&t.settlementData.revisedFinalSettlement>0),e.xp6(2),e.Q6J("ngModel",t.settlementData.desiredFinalSettlementAmountDecidedByErp)("disabled",!0),e.xp6(1),e.Q6J("ngModel",t.settlementData.desiredFinalSettlementAmountDecidedByManager),e.xp6(1),e.Q6J("disabled",!0)("ngModel",t.settlementData.automaticForgivenessAmount),e.xp6(10),e.Q6J("disabled",t.isSend),e.xp6(2),e.Q6J("disabled",t.isCorrect)}}let V=(()=>{class a{constructor(t,n,s,i,h,f,x,D){this.service=t,this.route=n,this.location=s,this.mediaService=i,this.dialog=h,this._dialog=f,this.cdr=x,this._notificationService=D,this.subscriptions=new g.w0,this.houseMaidNmae="",this.isSend=!0,this.isCorrect=!0,this.forgivenessAmount="",this.forgivenessNumValue=0,this.counter=0,this.gratuityBasedDays=!1}calculateRevisedSettlement(){const t=this.service.calculateRevisedSettlement(this.settlementData).subscribe(n=>{this.settlementData=n,this.checkingCounter()});this.subscriptions.add(t)}ngOnInit(){this.getIdFromUrl(),this.fetchSettlementData()}ngOnDestroy(){this.subscriptions.unsubscribe()}getIdFromUrl(){const t=this.route.params.subscribe(n=>{this.maidsId=n.id});this.subscriptions.add(t)}checkingCounter(){0==this.counter&&(this.isCorrect=!1),this.isSend=!1,this.counter++}fetchSettlementData(){const t=this.service.getSettlementById(this.maidsId).subscribe(n=>{this.settlementData=n,this.cloneSettlementData=n,this.houseMaidNmae=n.housemaid.label,this.forgivenessAmountString(),this.gratuityBasedDays="21"==n.gratuityBasedDays,n.finalSettlementCalculationDate&&this.service.recalculateGratuity(this.maidsId,n.finalSettlementCalculationDate,n.gratuityBasedDays).subscribe(s=>{this.settlementData.gratuity=s})},n=>console.log(n));this.subscriptions.add(t)}forgivenessAmountString(){(this.settlementData.forgivenessAmountEnteredByExpertDelighter||0===this.settlementData.forgivenessAmountEnteredByExpertDelighter)&&(this.forgivenessAmount="Forgiveness Amount Entered By Expert Delighter",this.forgivenessNumValue=this.settlementData.forgivenessAmountEnteredByExpertDelighter),(this.settlementData.forgivenessAmountCalculatedByERP||0===this.settlementData.forgivenessAmountCalculatedByERP)&&(this.forgivenessAmount="Forgiveness Amount Calculated By ERP",this.forgivenessNumValue=this.settlementData.forgivenessAmountCalculatedByERP),(this.settlementData.forgivenessAmountCalculatedByERPNonRenewal||0===this.settlementData.forgivenessAmountCalculatedByERPNonRenewal)&&(this.forgivenessAmount="Forgiveness Amount Calculated By ERP/Non-Renewal",this.forgivenessNumValue=this.settlementData.forgivenessAmountCalculatedByERPNonRenewal),(this.settlementData.forgivenessAmountTawafuq||0===this.settlementData.forgivenessAmountTawafuq)&&(console.log(4),this.forgivenessAmount="Forgiveness Amount Tawafuq",this.forgivenessNumValue=this.settlementData.forgivenessAmountTawafuq)}sendRevisedFinalSettlement(){const t=this.service.sendRevisedFinalSettlement(this.settlementData).subscribe(n=>{this._notificationService.notifySuccess(n),console.log(n),this.location.back()},n=>console.log(n));this.subscriptions.add(t)}correctNoNeedToEdit(){this._dialog.confirm("Warning","Are you sure there is no need to edit?",()=>{const t=this.settlementData;delete t.revisedFinalSettlement,delete t.reviseSMSNeeded;const n=this.service.sendRevisedFinalSettlement(t).subscribe(s=>{console.log(s)},s=>console.log(s));this.subscriptions.add(n)},()=>{},"Yes","No")}handleChange(){this.isCorrect=!0}onChangeHandler(){this.counter++,this.isCorrect=!0}preview(t){return this.mediaService.getFile("public/download/"+t).subscribe(n=>{this.previewFile(n)})}previewFile(t){let n=new Blob([t],{type:t.type});const s=URL.createObjectURL(n);let i=t.type.split("/")[1];this.dialog.open(F.s,{width:"100%",data:{url:s,blob:n,type:i}})}onGratuityBasedDaysChange(){this.service.recalculateGratuity(this.maidsId,this.settlementData.finalSettlementCalculationDate,this.gratuityBasedDays?21:14).subscribe(t=>{this.settlementData.gratuity=t})}downloadCashAdvance(){const t=this.service.downloadCashAdvance(this.maidsId).subscribe(n=>{this.preview(n.uuid)});this.subscriptions.add(t)}downloadContract(){const t=this.service.downloadContract(this.maidsId).subscribe(n=>{this.preview(n.uuid)});this.subscriptions.add(t)}onNoticePeriodServedChange(){if("Yes"==this.settlementData.noticePeriodServed)return this.settlementData.daysServed=0,void(this.settlementData.resignationFees=0);this.settlementData.daysServed=0,this.service.recalculateResignationFees(this.maidsId,this.settlementData.terminationMode,this.settlementData.noticePeriodServed,this.settlementData.daysServed||0).subscribe(t=>{this.settlementData.resignationFees=t})}updateNoticeFee(){this.settlementData.daysServed<31&&this.settlementData.daysServed>=0&&this.service.recalculateResignationFees(this.maidsId,this.settlementData.terminationMode,this.settlementData.noticePeriodServed,this.settlementData.daysServed||0).subscribe(t=>{this.settlementData.resignationFees=t})}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(b),e.Y36(v.gz),e.Y36(m.Ye),e.Y36(S.yJ),e.Y36(Q.uw),e.Y36(O.uY),e.Y36(e.sBO),e.Y36(S.zg))},a.\u0275cmp=e.Xpm({type:a,selectors:[["app-settlement-details"]],decls:2,vars:1,consts:[[1,"d-flex","justify-content-center","align-items-center"],["class","d-flex flex-column justify-content-start align-items-center min-w-800px mt-8",4,"ngIf"],[1,"d-flex","flex-column","justify-content-start","align-items-center","min-w-800px","mt-8"],[1,"text-alignment","margin-top"],[1,""],[1,"text-color-wine","mb-8"],[1,"text-black"],[1,"d-flex","flex-column","gap-2","mt-10"],["label","Prorated","name","prorated","type","number",1,"col-md-12",3,"ngModel","change","ngModelChange"],["for","",1,"col-4"],["name","gratuityBasedDays",1,"px-2","w-75","rounded",3,"ngModel","change","ngModelChange"],["label","Vacations","type","number","name","vacations",1,"col-md-12",3,"ngModel","change","ngModelChange"],["label","Gratuity","type","number","name","gratuity","step","0.1",1,"col-md-12",3,"ngModel","change","ngModelChange"],["name","noticePeriodServed",1,"px-2","w-75","rounded",3,"ngModel","change","ngModelChange"],[1,"m-2",3,"value"],["class","d-flex justify-content-center align-items-center",4,"ngIf"],["label","Resignation Fees","type","number","name","resignationFees",1,"col-md-12",3,"ngModel","change","ngModelChange"],["label","Cash Advance","type","number","name","cashAdvance",1,"col-md-12",3,"ngModel","change","ngModelChange"],["label","Additions","type","number","name","additions",1,"col-md-12",3,"ngModel","change","ngModelChange"],["label","Deductions","type","number","name","deductions",1,"col-md-12",3,"ngModel","change","ngModelChange"],[1,"d-flex","justify-content-start","align-items-center","margin-top-1r"],[1,"col-4"],["name","payHerByCash",1,"px-2","w-75","rounded",3,"ngModel","ngModelChange"],[1,"margin-x-4",3,"value"],[1,"d-flex","justify-content-center","align-items-center","my-4"],["cc-raised-button","","color","accent",1,"rounded",3,"click"],[4,"ngIf"],["class","red ml-3",4,"ngIf"],["label","Desired Amount Decided By ERP","name","desiredFinalSettlementAmountDecidedByErp","type","number",1,"col-md-12",3,"ngModel","disabled","ngModelChange"],["label","Desired Amount Decided By Manager","name","desiredFinalSettlementAmountDecidedByManager","type","number",1,"col-md-12",3,"ngModel","ngModelChange"],["label","Automatic Forgiveness Amount","name","automaticForgivenessAmount","type","number",1,"col-md-12",3,"disabled","ngModel","ngModelChange"],[1,"transparentBG","color","padding-left",3,"click"],[1,"w-100","my-4"],[1,"d-flex","justify-content-between","w-100","px-3"],["cc-raised-button","",1,"",3,"disabled","click"],["label","Days Served","type","number","name","daysServed",1,"col-md-12",3,"ngModel","change","ngModelChange"],[1,"ml-3"],[1,"text-primary"],[1,"red","ml-3"]],template:function(t,n){1&t&&(e.TgZ(0,"main",0),e.YNc(1,$,69,27,"form",1),e.qZA()),2&t&&(e.xp6(1),e.Q6J("ngIf",n.settlementData))},directives:[m.O5,p._Y,p.JL,p.F,T.G,p.JJ,p.On,Z.I,y.u6,y.UF,j.uu,B.Y],styles:[""]}),a})();var K=o(54657);const X=[{path:"",component:H,canLoad:[c.H4],canActivate:[c.H4]},{path:"todo-details/:id",component:V,canLoad:[c.H4],canActivate:[c.H4],data:{label:"Final Set Revision Todo Details"}}];let W=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({imports:[[F.J,m.ez,y.XD,B.A,p.u5,v.Bz.forChild(X),A.Gz,U,T.f,K.JC,j.S6,j.S6,Z.B],v.Bz]}),a})()},46700:(M,C,o)=>{var m={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function g(r){var c=e(r);return o(c)}function e(r){if(!o.o(m,r)){var c=new Error("Cannot find module '"+r+"'");throw c.code="MODULE_NOT_FOUND",c}return m[r]}g.keys=function(){return Object.keys(m)},g.resolve=e,M.exports=g,g.id=46700}}]);
"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_rpa-visa-prioritize_rpa-visa-prioritize_module_ts"],{76734:(St,j,l)=>{l.r(j),l.d(j,{RpaVisaPrioritizeModule:()=>xt});var m=l(69808),e=l(5e3),$=l(13859),x=l(97582),P=l(63900),o=l(93075),A=l(88476),ie=l(43604),v=l(54004),re=l(92340),ae=l(40520);let h=(()=>{class a{constructor(t){this._http=t,this._api=ie.b.rpaVisaPrioritize,this.getAllParameters$=this._http.get(this._api.getAllParameters).pipe((0,v.U)(r=>r.map(n=>({id:n.id,name:n.name,code:n.code,value:n.value,isUpdated:!1})))),this.getAllPriorities$=this._http.get(this._api.getAllPriorities),this.getConfigurations$=this._http.get(this._api.getConfigurations),this.getInitialVisaSteps$=this._http.get(this._api.getInitialVisaSteps),this.updateAllParameters=r=>this._http.post(this._api.updateParameters,r),this.updatePriorities=r=>this._http.post(this._api.updatePriorities,r),this.getAllCreditCard=this._http.get(this._api.creditCardList),this.getCreditCard=r=>this._http.get(this._api.getCreditCard(r)),this.getReservedCardsList=this._http.get(this._api.reservedCreditCardList),this.createCreditCart=r=>this._http.post(this._api.createCreditCard,r),this.updateCreditCard=r=>this._http.post(this._api.updateCreditCard,r),this.deleteCreditCard=r=>this._http.delete(this._api.deleteCreditCard(r)),this.markCreditCardAsReserved=r=>this._http.post(this._api.makeCreditCardAsReserved,r),this.markCreditCardAsAvailable=r=>this._http.post(this._api.markCreditCardAsAvailable(r),{}),this.getEligibleCards=this._http.get(this._api.getEligibleCards),this.getCreditCardStatus=this._http.get(this._api.creditCardStatus),this.getProcessesGroups=this._http.get(this._api.getProcessesGroups),this.getProcessesGroup=r=>this._http.get(this._api.getProcessesGroup(r)),this.createProcessesGroup=r=>this._http.post(this._api.createProcessesGroup,r),this.updateProcessesGroup=r=>this._http.post(this._api.updateProcessesGroup,r),this.deleteProcessesGroup=r=>this._http.delete(this._api.deleteProcessesGroup(r)),this.getRoboticProcesses=this._http.get(this._api.getRoboticProcesses),this.getCenterList=this._http.get(this._api.centerList),this.getCenterPriorityList=this._http.get(this._api.centerPriorityList),this.getBookedMaids=this._http.get(this._api.getBookedMaids),this.createCenter=r=>this._http.post(this._api.createCenter,r),this.updateCenter=r=>this._http.post(this._api.updateCenter,r),this.getCenterPriorityTypes=this._http.get(this._api.getCenterPriorityTypes),this.getCenter=r=>this._http.get(this._api.getCenter(r)),this.deleteCenter=r=>this._http.delete(this._api.deleteCenter(r)),this.createCenterPriority=r=>this._http.post(this._api.createCenterPriority,r),this.updateCenterPriority=r=>this._http.post(this._api.updateCenterPriority,r),this.getCenterPriority=r=>this._http.get(this._api.getCenterPriority(r)),this.deleteCenterPriority=r=>this._http.delete(this._api.deleteCenterPriority(r)),this.getEmiratesOptions=this._http.get(`${re.N.apiBase}/public/picklist/items/locations/tag/inside_UAE`),this.getAvailableCenters=r=>this._http.get(this._api.getAvailableCenters(r)),this.selectCenter=(r,n)=>this._http.post(this._api.selectCenter(r),n),this.getLawpParameters$=this._http.get(this._api.getLawpParameters),this.updateLawpParameter=r=>this._http.post(this._api.updateLawpParameter,r)}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(ae.eN))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})();var H=l(61135);const z={params:{page:0,size:20}};let g=(()=>{class a{constructor(){this.refreshSubject=new H.X(0),this.refresh$=this.refreshSubject.asObservable(),this.searchSubject=new H.X(z),this.search$=this.searchSubject.asObservable()}refresh(){this.refreshSubject.next(1)}updateSearchState(t){const r=Object.assign(Object.assign({},this.searchSubject.value),t);this.searchSubject.next(r)}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})();var ne=l(8188);let k=(()=>{class a{constructor(t,r,n){this._apiService=t,this._stateService=r,this.picklist=n,this.eligibleCards=[],this.monthOptions=["January","February","March","April","May","June","July","August","September","October","November","December"].map(s=>({id:s.toLocaleLowerCase(),text:s})),this.yearsOptions=this.getNext10Years().map(s=>s.toString()).map(s=>({id:s,text:s})),this.weekDaysOptions=[{id:"Sunday",text:"Sunday"},{id:"Monday",text:"Monday"},{id:"Tuesday",text:"Tuesday"},{id:"Wednesday",text:"Wednesday"},{id:"Thursday",text:"Thursday"},{id:"Friday",text:"Friday"},{id:"Saturday",text:"Saturday"}],this.timeUnitsOptions=[{id:"AM",text:"AM"},{id:"PM",text:"PM"}],this.hoursOptions=[{id:"1",text:"1"},{id:"2",text:"2"},{id:"3",text:"3"},{id:"4",text:"4"},{id:"5",text:"5"},{id:"6",text:"6"},{id:"7",text:"7"},{id:"8",text:"8"},{id:"9",text:"9"},{id:"10",text:"10"},{id:"11",text:"11"},{id:"12",text:"12"}],this.pcOptions=this._stateService.refresh$.pipe((0,P.w)(()=>this._apiService.getEligibleCards),(0,v.U)(s=>(this.eligibleCards=s,s.map(c=>({id:c.id,text:c.name,data:{name:c.name}}))))),this.getProcessNamesOptions=s=>this.eligibleCards.filter(c=>c.name===s).flatMap(c=>c.processes.map(d=>({id:d.id.toString(),text:d.name,data:{name:d.name}}))),this.creditCardStatus=this._apiService.getCreditCardStatus.pipe((0,v.U)(s=>s.filter(c=>"RESERVED"!==Object.keys(c)[0])),(0,v.U)(s=>s.map(c=>({id:Object.keys(c)[0],text:Object.values(c)[0]})))),this.roboticProcesses=this._apiService.getRoboticProcesses.pipe((0,v.U)(s=>s.map(c=>({id:c.id,text:c.name})))),this.emiratesOptions=this._apiService.getEmiratesOptions.pipe((0,v.U)(s=>s.map(c=>({id:c.id,text:c.name}))))}creditCardsNamesOptions(t,r){var n,s;const c=null===(s=null===(n=this.eligibleCards.find(d=>d.name===t))||void 0===n?void 0:n.processes.filter(d=>d.name===r).find(d=>d.name===r))||void 0===s?void 0:s.cards;return c?[{id:c.id,text:c.name}]:[]}getNext10Years(){return Array.from({length:11},(t,r)=>(new Date).getFullYear()+r)}}return a.\u0275fac=function(t){return new(t||a)(e.LFG(h),e.LFG(g),e.LFG(ne.Ab))},a.\u0275prov=e.Yz7({token:a,factory:a.\u0275fac}),a})();var _=l(48966),Z=l(21799),p=l(82599),f=l(65868);const oe=[[["","dialog-content",""]],[["","dialog-actions",""]]],se=["[dialog-content]","[dialog-actions]"];let w=(()=>{class a{constructor(){this.title=""}ngOnInit(){}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.Xpm({type:a,selectors:[["dynamic-dialog"]],inputs:{title:"title"},ngContentSelectors:se,decls:11,vars:2,consts:[["cc-std-dialog",""],["cc-dialog-title","",3,"align"],["cc-dialog-close-button","","cc-dialog-close",""],["cc-flat-button","","cc-dialog-close","",1,"px-5"]],template:function(t,r){1&t&&(e.F$t(oe),e.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),e._uU(3),e.qZA(),e._UZ(4,"a",2),e.qZA(),e.TgZ(5,"cc-dialog-content"),e.Hsn(6),e.qZA(),e.TgZ(7,"cc-dialog-actions")(8,"button",3),e._uU(9,"Cancel"),e.qZA(),e.Hsn(10,1),e.qZA()()),2&t&&(e.xp6(2),e.Q6J("align","start"),e.xp6(1),e.Oqu(r.title))},directives:[p.iK,p.Cj,p.Zb,p.fX,p.zn,p.kL,p.Zu,f.uu],encapsulation:2,changeDetection:0}),a})();var S=l(43687),F=l(58015);const ce=function(a){return[a]};let K=(()=>{class a{constructor(){this.label="",this.visibility=!0,this.required=!1,this.type="number",this.maskSymbol="X",this.value="",this.maskedValue="",this.onChange=t=>{},this.onTouched=()=>{},this.lengthValidator=()=>t=>{if(!t.value)return null;const r=t.value.trimEnd().length;return this.length||this.minLength?this.length&&!this.minLength?r!==this.length?{length:`Length must be exactly ${this.length} characters`}:null:!this.length&&this.minLength?r<this.minLength?{length:`Length must be at least ${this.minLength} characters`}:null:this.length&&this.minLength&&(r<this.minLength||r>this.length)?{length:`Length must be between ${this.minLength} and ${this.length} characters`}:null:null}}onKeyDown(t){const r=["Backspace","Delete","ArrowLeft","ArrowRight","Tab"];"number"===this.type&&!/^\d$/.test(t.key)&&!r.includes(t.key)&&t.preventDefault(),this.value.length>=(this.length||1/0)&&!r.includes(t.key)&&t.preventDefault(),("Backspace"===t.key||"Delete"===t.key)&&(t.preventDefault(),this.value=this.value.slice(0,-1),this.updateMaskedValue(),this.onChange(this.value))}validate(t){return!this.length&&this.minLength&&this.value.length<this.minLength?{minLength:!0}:this.length&&!this.minLength&&this.value.length<this.length?{maxLength:!0}:this.length&&this.minLength&&this.value.length<this.minLength?{minLength:!0}:null}writeValue(t){this.value="string"==typeof t?t.slice(0,this.length||1/0).trimEnd():"",this.updateMaskedValue()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}onInput(t){if(t){const r=t[t.length-1],n=this.length||1/0;r!==this.maskSymbol&&this.isValidInput(r)&&this.value.length<n&&(this.value+=r)}else this.value="";this.updateMaskedValue(),this.onChange(this.value)}updateMaskedValue(){this.maskedValue=this.maskPassword(this.value)}maskPassword(t){return this.maskSymbol.repeat(t.length)}isValidInput(t){return"number"===this.type?/^\d$/.test(t):""!==t.trim()}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.Xpm({type:a,selectors:[["custom-input-mask"]],hostBindings:function(t,r){1&t&&e.NdJ("keydown",function(s){return r.onKeyDown(s)})},inputs:{label:"label",visibility:"visibility",required:"required",type:"type",maskSymbol:"maskSymbol",length:"length",minLength:"minLength"},features:[e._Bn([{provide:o.JU,useExisting:(0,e.Gpc)(()=>a),multi:!0},{provide:o.Cf,useExisting:(0,e.Gpc)(()=>a),multi:!0}])],decls:3,vars:6,consts:[[1,"custom-password","w-full",2,"width","100%","display","flex","align-items","center","position","relative"],["maxlength","[length]","minlength","[minLength]",2,"flex-grow","1",3,"label","ngModel","required","ccValidateBy","ngModelChange","input"],["model","ngModel"]],template:function(t,r){if(1&t){const n=e.EpF();e.TgZ(0,"div",0)(1,"cc-input",1,2),e.NdJ("ngModelChange",function(c){return r.maskedValue=c})("input",function(){e.CHM(n);const c=e.MAs(2);return r.onInput(c.value)}),e.qZA()()}2&t&&(e.xp6(1),e.Q6J("label",r.label)("ngModel",r.maskedValue)("required",r.required)("ccValidateBy",e.VKq(4,ce,r.lengthValidator())))},directives:[S.G,o.nD,o.wO,o.JJ,o.On,o.Q7,F.KE],encapsulation:2}),a})();var O=l(11523),y=l(26523);const le=function(a){return[a]};let q=class{constructor(i,t,r,n,s,c){this._apiService=i,this._stateService=t,this._dataService=r,this._dialogRef=n,this.notification=s,this._fb=c,this.monthOptions=this._dataService.monthOptions,this.yearOptions=this._dataService.yearsOptions,this.emailValidator=()=>d=>d.value?/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/.test(d.value.toLowerCase())?null:{invalidEmail:"Please enter a valid Email"}:null,this.form=this._fb.group({bucketCode:["",o.kI.required],name:["",o.kI.required],type:["",o.kI.required],amount:[""],number:["",o.kI.required],monthExpiryDate:["",o.kI.required],yearExpiryDate:["",o.kI.required],cvv:["",o.kI.required],registeredPhoneNumber:["",o.kI.required],registeredEmail:["",[o.kI.required]]})}ngOnInit(){}onSave(){this._apiService.createCreditCart(this.form.value).subscribe({next:()=>{this.notification.notifySuccess("Added successfully."),this._stateService.refresh(),this._dialogRef.close()}})}};q.\u0275fac=function(i){return new(i||q)(e.Y36(h),e.Y36(g),e.Y36(k),e.Y36(_.so),e.Y36(Z.zg),e.Y36(o.qu))},q.\u0275cmp=e.Xpm({type:q,selectors:[["app-add-new-card"]],decls:16,vars:18,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["formControlName","name","label","Credit Card Name","required",""],["formControlName","type","label","Credit Card Type","required",""],["formControlName","number","label","Credit Card Number",3,"length","required"],["formControlName","amount","label","Amount",3,"symbol"],["formControlName","monthExpiryDate","label","Month Expiry Date",3,"data","required"],["formControlName","yearExpiryDate","label","Year Expiry Date",3,"data","required"],["formControlName","cvv","label","CVV (Card Verification Value)",3,"required","length","minLength"],["formControlName","bucketCode","label","Bucket Code",3,"type","required"],["formControlName","registeredPhoneNumber","label","Registered Phone Number","required",""],["formControlName","registeredEmail","label","Registered Email","required","",3,"ccValidateBy"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-input",3)(4,"cc-input",4)(5,"custom-input-mask",5)(6,"cc-amount-input",6)(7,"cc-select",7)(8,"cc-select",8)(9,"custom-input-mask",9)(10,"custom-input-mask",10)(11,"cc-phone-input",11)(12,"cc-input",12),e.qZA(),e.BQk(),e.ynx(13,13),e.TgZ(14,"button",14),e.NdJ("click",function(){return t.onSave()}),e._uU(15,"Add"),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Add New Card"),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(3),e.Q6J("length",16)("required",!0),e.xp6(1),e.Q6J("symbol",""),e.xp6(1),e.Q6J("data",t.monthOptions)("required",!0),e.xp6(1),e.Q6J("data",t.yearOptions)("required",!0),e.xp6(1),e.Q6J("required",!0)("length",4)("minLength",3),e.xp6(1),e.Q6J("type","text")("required",!0),e.xp6(2),e.Q6J("ccValidateBy",e.VKq(16,le,t.emailValidator())),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,S.G,o.JJ,o.u,o.Q7,K,O.Fi,y.jB,O.hi,F.KE,f.uu],encapsulation:2,changeDetection:0}),q=(0,x.gn)([A.kG],q);let D=class{constructor(i,t,r,n,s,c,d){this.apiService=i,this.stateService=t,this.dataService=r,this.notification=n,this.dialogRef=s,this.cdrRef=c,this.fb=d,this.form=this.fb.group({rpaPc:"",roboticProcess:"",creditCard:""}),this.pcOptions=this.dataService.pcOptions}ngOnInit(){const{rpaPc:i,roboticProcess:t}=this.form.controls;i.valueChanges.subscribe(r=>{r||(this.processesOptions=[],this.cardsOptions=[]),this.form.patchValue({roboticProcess:"",creditCard:""},{emitEvent:!1}),this.cdrRef.detectChanges()}),t.valueChanges.subscribe(r=>{r&&this.form.patchValue({creditCard:""},{emitEvent:!1})}),i.valueChanges.pipe((0,v.U)(r=>{var n;return(null===(n=null==r?void 0:r.data)||void 0===n?void 0:n.name)?this.dataService.getProcessNamesOptions(r.data.name):[]})).subscribe(r=>{this.processesOptions=r,this.cdrRef.detectChanges()}),t.valueChanges.pipe((0,v.U)(r=>{var n,s;const c=this.form.value.rpaPc;return(null===(n=null==c?void 0:c.data)||void 0===n?void 0:n.name)&&(null===(s=null==r?void 0:r.data)||void 0===s?void 0:s.name)?this.dataService.creditCardsNamesOptions(c.data.name,r.data.name):[]})).subscribe(r=>{this.cardsOptions=r,this.cdrRef.detectChanges()})}onSave(){const{rpaPc:i,roboticProcess:t,creditCard:r}=this.form.value;this.apiService.markCreditCardAsReserved({rpaPc:{id:+i.id},roboticProcess:{id:+t.id},creditCard:{id:+r}}).subscribe(()=>{this.notification.notifySuccess("Added Successfully"),this.stateService.refresh(),this.dialogRef.close()})}};D.\u0275fac=function(i){return new(i||D)(e.Y36(h),e.Y36(g),e.Y36(k),e.Y36(Z.zg),e.Y36(_.so),e.Y36(e.sBO),e.Y36(o.qu))},D.\u0275cmp=e.Xpm({type:D,selectors:[["app-reserve-card"]],decls:10,vars:13,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["label","Pc Name","formControlName","RpaPc",3,"data","emitFullSelectOption","required"],["label","Process Name","formControlName","roboticProcess",3,"data","emitFullSelectOption","required"],["label","Credit Card Name","formControlName","creditCard",3,"data","required"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-select",3),e.ALo(4,"async"),e._UZ(5,"cc-select",4)(6,"cc-select",5),e.qZA(),e.BQk(),e.ynx(7,6),e.TgZ(8,"button",7),e.NdJ("click",function(){return t.onSave()}),e._uU(9," Add "),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Reserve Card"),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("data",e.lcZ(4,11,t.pcOptions))("emitFullSelectOption",!0)("required",!0),e.xp6(2),e.Q6J("data",t.processesOptions)("emitFullSelectOption",!0)("required",!0),e.xp6(1),e.Q6J("data",t.cardsOptions)("required",!0),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,y.jB,o.JJ,o.u,o.Q7,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),D=(0,x.gn)([A.kG],D);const de=function(a){return[a]};let U=class{constructor(i,t,r,n,s,c,d,u){this._apiService=i,this._stateService=t,this._dataService=r,this._notification=n,this._dialogRef=s,this._cdRef=c,this._fb=d,this.data=u,this.monthOptions=this._dataService.monthOptions,this.yearOptions=this._dataService.yearsOptions,this.creditCardStatus=[],this.emailValidator=()=>b=>b.value?/^[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/.test(b.value.toLowerCase())?null:{invalidEmail:"Please enter a valid Email"}:null,this.form=this._fb.group({bucketCode:["",o.kI.required],name:["",o.kI.required],status:[""],type:["",o.kI.required],amount:[""],number:["",o.kI.required],monthExpiryDate:["",o.kI.required],yearExpiryDate:["",o.kI.required],cvv:["",o.kI.required],registeredPhoneNumber:["",o.kI.required],registeredEmail:["",[o.kI.required,o.kI.email]]})}ngOnInit(){this._apiService.getCreditCard(this.data.record.id).subscribe(i=>{this.populateFormFields(i)}),this._dataService.creditCardStatus.subscribe(i=>{this.creditCardStatus=[...this.creditCardStatus,...i],this._cdRef.detectChanges()}),"Reserved"===this.data.record.status&&(this.creditCardStatus=[...this.creditCardStatus,{id:"RESERVED",text:"Reserved"}])}onSave(){const i=Object.assign(Object.assign({},this.form.value),{id:this.data.record.id});this._apiService.updateCreditCard(i).subscribe({next:()=>{this._notification.notifySuccess("Updated Successfully."),this._stateService.refresh(),this._dialogRef.close()}})}populateFormFields(i){this.form.setValue({bucketCode:i.bucketCode,name:i.name,type:i.type,status:i.status.toLocaleUpperCase().split(" ").join("_").toString(),amount:i.amount,number:i.number,monthExpiryDate:i.monthExpiryDate.toLocaleLowerCase(),yearExpiryDate:i.yearExpiryDate,cvv:i.cvv,registeredPhoneNumber:i.registeredPhoneNumber,registeredEmail:i.registeredEmail})}};U.\u0275fac=function(i){return new(i||U)(e.Y36(h),e.Y36(g),e.Y36(k),e.Y36(Z.zg),e.Y36(_.so),e.Y36(e.sBO),e.Y36(o.qu),e.Y36(_.WI))},U.\u0275cmp=e.Xpm({type:U,selectors:[["app-edit-card"]],decls:17,vars:19,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["formControlName","name","label","Credit Card Name","required",""],["formControlName","type","label","Credit Card Type","required",""],["label","Status","formControlName","status",3,"data"],["formControlName","number","label","Credit Card Number",3,"required","length"],["formControlName","amount","label","Amount",3,"symbol"],["formControlName","monthExpiryDate","label","Month Expiry Date",3,"data","required"],["formControlName","yearExpiryDate","label","Year Expiry Date",3,"data","required"],["formControlName","cvv","label","CVV (Card Verification Value)",3,"required","length","minLength"],["formControlName","bucketCode","label","Bucket Code",3,"type","required"],["formControlName","registeredPhoneNumber","label","Registered Phone Number","required",""],["formControlName","registeredEmail","label","Registered Email","required","",3,"ccValidateBy"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-input",3)(4,"cc-input",4)(5,"cc-select",5)(6,"custom-input-mask",6)(7,"cc-amount-input",7)(8,"cc-select",8)(9,"cc-select",9)(10,"custom-input-mask",10)(11,"custom-input-mask",11)(12,"cc-phone-input",12)(13,"cc-input",13),e.qZA(),e.BQk(),e.ynx(14,14),e.TgZ(15,"button",15),e.NdJ("click",function(){return t.onSave()}),e._uU(16,"Save "),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Edit "+t.data.record.name),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(3),e.Q6J("data",t.creditCardStatus),e.xp6(1),e.Q6J("required",!0)("length",16),e.xp6(1),e.Q6J("symbol",""),e.xp6(1),e.Q6J("data",t.monthOptions)("required",!0),e.xp6(1),e.Q6J("data",t.yearOptions)("required",!0),e.xp6(1),e.Q6J("required",!0)("length",4)("minLength",3),e.xp6(1),e.Q6J("type","text")("required",!0),e.xp6(2),e.Q6J("ccValidateBy",e.VKq(17,de,t.emailValidator())),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,S.G,o.JJ,o.u,o.Q7,y.jB,K,O.Fi,O.hi,F.KE,f.uu],encapsulation:2,changeDetection:0}),U=(0,x.gn)([A.kG],U);let pe=(()=>{class a{constructor(t,r,n,s,c){this.fb=t,this.apiService=r,this.stateService=n,this.dataService=s,this.dialogRef=c,this.form=this.fb.group({groupName:"",processes:""}),this.processesOptions$=this.dataService.roboticProcesses}ngOnInit(){}addProcessGroup(){const{groupName:t,processes:r}=this.form.value,n={groupName:t,processes:r.map(s=>({id:s}))};this.apiService.createProcessesGroup(n).subscribe({next:()=>{this.stateService.refresh(),this.dialogRef.close()}})}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(o.qu),e.Y36(h),e.Y36(g),e.Y36(k),e.Y36(_.so))},a.\u0275cmp=e.Xpm({type:a,selectors:[["ng-component"]],decls:15,vars:8,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],[3,"formGroup"],["label","Group Name","formControlName","groupName",3,"required"],["label","Processes","formControlName","processes",3,"data","multiple","required"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"]],template:function(t,r){1&t&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Add New Group"),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4)(6,"form",5),e._UZ(7,"cc-input",6)(8,"cc-select",7),e.ALo(9,"async"),e.qZA()(),e.TgZ(10,"div",8)(11,"button",9),e._uU(12,"Close"),e.qZA(),e.TgZ(13,"button",10),e.NdJ("click",function(){return r.addProcessGroup()}),e._uU(14," Add "),e.qZA()()()),2&t&&(e.xp6(6),e.Q6J("formGroup",r.form),e.xp6(1),e.Q6J("required",!0),e.xp6(1),e.Q6J("data",e.lcZ(9,6,r.processesOptions$))("multiple",!0)("required",!0),e.xp6(5),e.Q6J("disabled",!r.form.valid))},directives:[p.iK,p.Cj,p.Zb,p.zn,p.fX,p.kL,o._Y,o.JL,o.sg,S.G,o.JJ,o.u,o.Q7,y.jB,p.Zu,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),a})(),me=(()=>{class a{constructor(t,r,n,s,c,d,u){this.fb=t,this.apiService=r,this.stateService=n,this.dataService=s,this.cdRef=c,this.dialogRef=d,this.data=u,this.form=this.fb.group({groupName:"",processes:""}),this.processesOptions$=this.dataService.roboticProcesses}ngOnInit(){this.apiService.getProcessesGroup(this.data.id.toString()).subscribe({next:t=>{const r=t.processes.map(n=>n.id);this.form.patchValue({groupName:t.groupName,processes:r}),this.cdRef.markForCheck()}})}editProcessGroup(){const{groupName:t,processes:r}=this.form.value,n={id:this.data.id,groupName:t,processes:r.map(s=>({id:s}))};this.apiService.updateProcessesGroup(n).subscribe({next:()=>{this.stateService.refresh(),this.dialogRef.close()}})}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(o.qu),e.Y36(h),e.Y36(g),e.Y36(k),e.Y36(e.sBO),e.Y36(_.so),e.Y36(_.WI))},a.\u0275cmp=e.Xpm({type:a,selectors:[["app-edit-processes-group"]],decls:15,vars:9,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],[3,"formGroup"],["label","Group Name","formControlName","groupName",3,"required"],["label","Processes","formControlName","processes",3,"data","multiple","required"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","accent",3,"disabled","click"]],template:function(t,r){1&t&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4)(6,"form",5),e._UZ(7,"cc-input",6)(8,"cc-select",7),e.ALo(9,"async"),e.qZA()(),e.TgZ(10,"div",8)(11,"button",9),e._uU(12,"Close"),e.qZA(),e.TgZ(13,"button",10),e.NdJ("click",function(){return r.editProcessGroup()}),e._uU(14," Save "),e.qZA()()()),2&t&&(e.xp6(3),e.Oqu("Edit "+r.data.groupName),e.xp6(3),e.Q6J("formGroup",r.form),e.xp6(1),e.Q6J("required",!0),e.xp6(1),e.Q6J("data",e.lcZ(9,7,r.processesOptions$))("multiple",!0)("required",!0),e.xp6(5),e.Q6J("disabled",!r.form.valid))},directives:[p.iK,p.Cj,p.Zb,p.zn,p.fX,p.kL,o._Y,o.JL,o.sg,S.G,o.JJ,o.u,o.Q7,y.jB,p.Zu,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),a})();var C=l(69202),J=l(34378),B=l(62764),W=l(4882);function ue(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"div",15)(1,"button",16),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onEditCard(s)}),e._uU(2," Edit "),e.qZA(),e.TgZ(3,"button",17),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onDeleteCard(s)}),e._uU(4," Delete "),e.qZA()()}}function he(a,i){if(1&a&&(e.TgZ(0,"p"),e._uU(1),e.ALo(2,"number"),e.qZA()),2&a){const t=i.$implicit;e.xp6(1),e.Oqu(e.xi3(2,1,t.amount,"1.0-0"))}}function ge(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"cc-checkbox",18),e.NdJ("ngModelChange",function(n){return e.CHM(t).$implicit.autoLoad=n})("ngModelChange",function(){const s=e.CHM(t).$implicit;return e.oxw().onAutoLoadChecked(s)}),e.qZA()}2&a&&e.Q6J("ngModel",i.$implicit.autoLoad)}function _e(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"button",16),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onCompleteReserve(s)}),e._uU(1," Complete "),e.qZA()}}function fe(a,i){if(1&a&&(e.TgZ(0,"span",21),e._uU(1),e.qZA()),2&a){const t=i.$implicit;e.xp6(1),e.hij(" ",t.label," ")}}function Ce(a,i){if(1&a&&(e.TgZ(0,"div",19),e.YNc(1,fe,2,1,"span",20),e.qZA()),2&a){const t=i.$implicit;e.xp6(1),e.Q6J("ngForOf",t.processes)}}function be(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"div",22)(1,"button",16),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onEditProcessGroup(s)}),e._uU(2," Edit "),e.qZA(),e.TgZ(3,"button",17),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onDeleteProcessGroup(s)}),e._uU(4," Delete "),e.qZA()()}}const ve=function(a,i,t){return{operations:a,amount:i,autoLoad:t}},ye=function(a){return{operations:a}},xe=function(a,i){return{operations:a,processes:i}},Se=[{field:"name",header:"Credit Card Name"},{field:"type",header:"Credit Card Type"},{field:"number",header:"Credit Card Number"},{field:"status",header:"Status"},{field:"lastUpdatedStatusDate",header:"Last Update Status"},{field:"amount",header:"Amount"},{field:"monthExpiryDate",header:"Month Expiry Date",formatter:a=>a.monthExpiryDate.charAt(0).toUpperCase()+a.monthExpiryDate.slice(1)},{field:"yearExpiryDate",header:"Year Expiry Date"},{field:"cvv",header:"CVV (Card Verification Value)"},{field:"bucketCode",header:"Bucket Code"},{field:"registeredPhoneNumber",header:"Registered Phone Number"},{field:"registeredEmail",header:"Registered Email"},{field:"autoLoad",header:"Auto Load"},{field:"operations",header:"Actions"}],Te=[{field:"creditCard.label",header:"Credit Card"},{field:"RpaPc.label",header:"PC Name"},{field:"creditCard.status",header:"Status"},{field:"roboticProcess.label",header:"Process Name"},{field:"creationDate",header:"Reserved Date/Time"},{field:"operations",header:"Actions"}],Ze=[{field:"groupName",header:"Group Name",width:"15ch"},{field:"processes",header:"Processes"},{field:"operations",header:"Actions"}];let Q=class{constructor(i,t,r,n,s){this._apiService=i,this._stateService=t,this._dialog=r,this._notification=n,this._cdRef=s,this.cardsDetailsCols=Se,this.ongoingPaymentsCols=Te,this.processesGroupsCols=Ze,this.cardsDetails=[],this.ongoingPayments=[],this.processesGroups=[]}ngOnInit(){this._stateService.refresh$.pipe((0,P.w)(()=>this._apiService.getAllCreditCard)).subscribe({next:i=>{this.cardsDetails=i,this._cdRef.detectChanges()}}),this._stateService.refresh$.pipe((0,P.w)(()=>this._apiService.getReservedCardsList)).subscribe({next:i=>{this.ongoingPayments=i,this._cdRef.detectChanges()}}),this._stateService.refresh$.pipe((0,P.w)(()=>this._apiService.getProcessesGroups)).subscribe({next:i=>{this.processesGroups=i,this._cdRef.detectChanges()}})}openAddCardDialog(){this._dialog.originalOpen(q,{panelClass:"col-md-6",data:{}})}openReservedCardDialog(){this._dialog.originalOpen(D,{panelClass:"col-md-6",data:{}})}onEditCard(i){this._dialog.originalOpen(U,{panelClass:"col-md-6",data:{record:i}})}onDeleteCard(i){this._dialog.confirm(`Delete ${i.name}`,`Are you sure that you want to delete the card: ${i.name}?`,()=>{this._apiService.deleteCreditCard(i.id).subscribe({next:()=>{this._notification.notifySuccess("Deleted Successfully."),this._stateService.refresh()}})},()=>{},"Confirm","Cancel")}onCompleteReserve(i){this._apiService.markCreditCardAsAvailable(i.id).subscribe({next:()=>{this._stateService.refresh()}})}onDeleteProcessGroup(i){this._dialog.confirm(`Delete ${i.groupName}`,"Are you sure that you want to delete this group?",()=>{this._apiService.deleteProcessesGroup(i.id.toString()).subscribe({next:()=>this._stateService.refresh()})},()=>{},"Confirm","Cancel")}onEditProcessGroup(i){this._dialog.originalOpen(me,{panelClass:"col-md-6",data:{id:i.id,groupName:i.groupName}})}openAddProcessesGroupDialog(){this._dialog.originalOpen(pe,{panelClass:"col-md-6"})}onAutoLoadChecked(i){this._apiService.updateCreditCard({id:i.id,autoLoad:i.autoLoad}).subscribe()}};function Pe(a,i){if(1&a){const t=e.EpF();e.ynx(0),e.TgZ(1,"cc-input",1),e.NdJ("ngModelChange",function(n){return e.CHM(t),e.oxw().parameter.value=n})("ngModelChange",function(){e.CHM(t);const n=e.oxw();return n.onInputChange(n.parameter.value)}),e.qZA(),e.BQk()}if(2&a){const t=e.oxw();e.xp6(1),e.Q6J("label",t.parameter.name)("ngModel",t.parameter.value)}}Q.\u0275fac=function(i){return new(i||Q)(e.Y36(h),e.Y36(g),e.Y36(p.uY),e.Y36(Z.zg),e.Y36(e.sBO))},Q.\u0275cmp=e.Xpm({type:Q,selectors:[["credit-cards"]],decls:53,vars:32,consts:[[1,"mt-4"],[1,"row","d-flex","justify-content-end","px-3","pt-1","pb-2"],["cc-raised-button","","color","primary",3,"click"],[3,"expanded"],[2,"font-size","16px","font-weight","500"],[1,"m-0"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["cardsDetailsOperations",""],["amountTmp",""],["autoLoadTmp",""],[1,"py-4"],["onGoingPaymentsOperations",""],["processesTmp",""],["processGroupActions",""],[1,"d-flex","flex-column","flex-md-row","gap-1"],["cc-flat-button","","color","accent",3,"click"],["cc-flat-button","","color","warn",3,"click"],[3,"ngModel","ngModelChange"],[1,"d-flex","gap-1","flex-wrap","justify-content-center","my-2"],["style","\n                      background-color: grey;\n                      color: #fff;\n                      border-radius: 15px;\n                      padding: 0.3rem 1rem;\n                      margin-block: 0.1rem;\n                    ",4,"ngFor","ngForOf"],[2,"background-color","grey","color","#fff","border-radius","15px","padding","0.3rem 1rem","margin-block","0.1rem"],[1,"d-flex","flex-colum","justify-content-center","flex-md-row","gap-1"]],template:function(i,t){if(1&i&&(e._UZ(0,"div",0),e.TgZ(1,"cc-card")(2,"cc-card-content")(3,"div")(4,"div",1)(5,"button",2),e.NdJ("click",function(){return t.openAddCardDialog()}),e._uU(6," Add New Card "),e.qZA()(),e.TgZ(7,"cc-accordion")(8,"cc-panel",3)(9,"cc-panel-title",4)(10,"h3",5),e._uU(11,"Cards Details"),e.qZA()(),e.TgZ(12,"cc-panel-body"),e.ynx(13),e._UZ(14,"cc-datagrid",6),e.YNc(15,ue,5,0,"ng-template",7,8,e.W1O),e.YNc(17,he,3,4,"ng-template",7,9,e.W1O),e.YNc(19,ge,1,1,"ng-template",null,10,e.W1O),e.BQk(),e.qZA()()()(),e._UZ(21,"div",11),e.TgZ(22,"div")(23,"div",1)(24,"button",2),e.NdJ("click",function(){return t.openReservedCardDialog()}),e._uU(25," Reserve Card "),e.qZA()(),e.TgZ(26,"cc-accordion")(27,"cc-panel",3)(28,"cc-panel-title",4)(29,"h3",5),e._uU(30,"Ongoing Payments"),e.qZA()(),e.TgZ(31,"cc-panel-body"),e.ynx(32),e._UZ(33,"cc-datagrid",6),e.YNc(34,_e,2,0,"ng-template",7,12,e.W1O),e.BQk(),e.qZA()()()(),e._UZ(36,"div",11),e.TgZ(37,"div")(38,"div",1)(39,"button",2),e.NdJ("click",function(){return t.openAddProcessesGroupDialog()}),e._uU(40," Define New Group "),e.qZA()(),e.TgZ(41,"cc-accordion")(42,"cc-panel",3)(43,"cc-panel-title",4)(44,"h3",5),e._uU(45,"Processes Groups"),e.qZA()(),e.TgZ(46,"cc-panel-body"),e.ynx(47),e._UZ(48,"cc-datagrid",6),e.YNc(49,Ce,2,1,"ng-template",7,13,e.W1O),e.YNc(51,be,5,0,"ng-template",7,14,e.W1O),e.BQk(),e.qZA()()()()()()),2&i){const r=e.MAs(16),n=e.MAs(18),s=e.MAs(20),c=e.MAs(35),d=e.MAs(50),u=e.MAs(52);e.xp6(8),e.Q6J("expanded",!0),e.xp6(6),e.Q6J("columns",t.cardsDetailsCols)("data",t.cardsDetails)("showPaginator",!1)("loading",!1)("cellTemplate",e.kEZ(23,ve,r,n,s)),e.xp6(1),e.Q6J("ccGridCell",t.cardsDetails),e.xp6(2),e.Q6J("ccGridCell",t.cardsDetails),e.xp6(10),e.Q6J("expanded",!0),e.xp6(6),e.Q6J("columns",t.ongoingPaymentsCols)("data",t.ongoingPayments)("showPaginator",!1)("loading",!1)("cellTemplate",e.VKq(27,ye,c)),e.xp6(1),e.Q6J("ccGridCell",t.ongoingPayments),e.xp6(8),e.Q6J("expanded",!0),e.xp6(6),e.Q6J("columns",t.processesGroupsCols)("data",t.processesGroups)("showPaginator",!1)("loading",!1)("cellTemplate",e.WLB(29,xe,u,d)),e.xp6(1),e.Q6J("ccGridCell",t.processesGroups),e.xp6(2),e.Q6J("ccGridCell",t.processesGroups)}},directives:[C.Dt,C.uw,f.uu,J.I,J.CW,J.LL,J.G9,B.Ge,B.VC,W.E,o.JJ,o.On,m.sg],pipes:[m.JJ],encapsulation:2,changeDetection:0}),Q=(0,x.gn)([A.kG],Q);let Ae=(()=>{class a{constructor(){this.originalValue="",this.value=null,this.onChange=()=>{},this.onTouched=()=>{}}set parameter(t){this._parameter=t,this.originalValue=t.value}get parameter(){return this._parameter}ngOnInit(){}writeValue(t){void 0!==t&&(this.value=Object.assign({},t))}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){}onInputChange(t){this.parameter&&(this.parameter.isUpdated=this.originalValue!==t,this.parameter.value=t,this.onChange(this.parameter))}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.Xpm({type:a,selectors:[["parameter-input"]],inputs:{parameter:"parameter"},features:[e._Bn([{provide:o.JU,useExisting:a,multi:!0}])],decls:1,vars:1,consts:[[4,"ngIf"],[1,"w-100",3,"label","ngModel","ngModelChange"]],template:function(t,r){1&t&&e.YNc(0,Pe,2,2,"ng-container",0),2&t&&e.Q6J("ngIf",r.parameter)},directives:[m.O5,S.G,o.JJ,o.On],encapsulation:2,changeDetection:0}),a})();function ke(a,i){if(1&a){const t=e.EpF();e.ynx(0),e.TgZ(1,"cc-select",1),e.NdJ("ngModelChange",function(n){return e.CHM(t),e.oxw().model=n})("ngModelChange",function(){e.CHM(t);const n=e.oxw();return n.onInputChange(n.model)}),e.qZA(),e.BQk()}if(2&a){const t=e.oxw();e.xp6(1),e.Q6J("label",t.parameter.name)("data",t.options)("ngModel",t.model)("multiple",!0)}}let Je=(()=>{class a{constructor(){this._originalValue="",this.model="",this.options=null,this.value=this._parameter,this.onChange=()=>{},this.onTouched=()=>{}}set parameter(t){this._parameter=t,this._originalValue=t.value,this.model=t.value.split(","),console.log(this.model)}get parameter(){return this._parameter}ngOnInit(){}writeValue(t){void 0!==t&&(this.value=Object.assign({},t))}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){}onInputChange(t){this.parameter&&(this.parameter.isUpdated=this._originalValue!==t.join(","),this.parameter.value=t.join(","),this.onChange(this.parameter))}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.Xpm({type:a,selectors:[["parameter-select-input"]],inputs:{options:"options",parameter:"parameter"},features:[e._Bn([{provide:o.JU,useExisting:a,multi:!0}])],decls:1,vars:1,consts:[[4,"ngIf"],[1,"w-100",3,"label","data","ngModel","multiple","ngModelChange"]],template:function(t,r){1&t&&e.YNc(0,ke,2,4,"ng-container",0),2&t&&e.Q6J("ngIf",r.parameter)},directives:[m.O5,y.jB,o.JJ,o.On],encapsulation:2,changeDetection:0}),a})();function Oe(a,i){if(1&a&&e._UZ(0,"parameter-input",11),2&a){const t=e.oxw().$implicit;e.Q6J("parameter",t)("formControlName",t.name)}}function Ne(a,i){if(1&a&&(e.ynx(0),e.YNc(1,Oe,1,2,"parameter-input",10),e.BQk()),2&a){const t=i.$implicit;e.xp6(1),e.Q6J("ngIf","Critical steps"!==t.name)}}function we(a,i){if(1&a&&e._UZ(0,"parameter-select-input",13),2&a){const t=e.oxw().$implicit,r=e.oxw();e.Q6J("parameter",t)("options",r.visaSteps)("formControlName",t.name)}}function qe(a,i){if(1&a&&(e.ynx(0),e.YNc(1,we,1,3,"parameter-select-input",12),e.BQk()),2&a){const t=i.$implicit;e.xp6(1),e.Q6J("ngIf","Critical steps"===t.name)}}let De=(()=>{class a{constructor(t,r,n,s,c){this._apiService=t,this._stateService=r,this._fb=n,this._notification=s,this._cdRef=c,this.form=this._fb.group({}),this.visaSteps=[],this.canUpdate=!1}ngOnInit(){this._stateService.refresh$.pipe((0,P.w)(()=>this._apiService.getAllParameters$)).subscribe({next:t=>{t.forEach(r=>{this.form.addControl(r.name,this._fb.control(r))}),this.parameters=t,this._cdRef.detectChanges()}}),this.form.valueChanges.subscribe(t=>{this.canUpdate=Object.values(this.form.value).filter(r=>r.isUpdated).map(r=>(0,x._T)(r,["isUpdated"])).length>0,this._cdRef.detectChanges()}),this._apiService.getInitialVisaSteps$.pipe((0,v.U)(t=>t.map(r=>({id:r.taskName,text:r.taskLabel})))).subscribe(t=>{this.visaSteps=t,this._cdRef.detectChanges()})}onUpdate(){const t=Object.values(this.form.value).filter(r=>r.isUpdated).map(r=>(0,x._T)(r,["isUpdated"]));this._apiService.updateAllParameters(t).subscribe({next:r=>{this._notification.notifySuccess("Updated Successfully."),this._stateService.refresh()}})}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(h),e.Y36(g),e.Y36(o.qu),e.Y36(Z.zg),e.Y36(e.sBO))},a.\u0275cmp=e.Xpm({type:a,selectors:[["parameters"]],decls:15,vars:6,consts:[[1,"my-4","mx-3","parameters-container"],[1,"w-100",2,"border","2px solid #000","border-radius","5px","position","relative"],[1,"title",2,"position","absolute","top","-13.5px","left","25px","background-color","#fff"],[1,"px-1"],[1,"p-5"],[1,"vertical-grid",3,"formGroup"],[4,"ngFor","ngForOf"],[1,"d-flex","justify-content-center",3,"formGroup"],[3,"align"],["cc-raised-button","","color","accent",1,"px-5","mx-3",3,"disabled","click"],["class","vertical-grid__item",3,"parameter","formControlName",4,"ngIf"],[1,"vertical-grid__item",3,"parameter","formControlName"],["class","col-md-12 col-lg-8 px-0 pt-4",3,"parameter","options","formControlName",4,"ngIf"],[1,"col-md-12","col-lg-8","px-0","pt-4",3,"parameter","options","formControlName"]],template:function(t,r){1&t&&(e.TgZ(0,"div",0)(1,"cc-card")(2,"cc-card-content")(3,"div",1)(4,"div",2)(5,"p",3),e._uU(6,"Parameters"),e.qZA()(),e.TgZ(7,"div",4)(8,"form",5),e.YNc(9,Ne,2,1,"ng-container",6),e.qZA(),e.TgZ(10,"form",7),e.YNc(11,qe,2,1,"ng-container",6),e.qZA()(),e.TgZ(12,"cc-card-actions",8)(13,"button",9),e.NdJ("click",function(){return r.onUpdate()}),e._uU(14," Save "),e.qZA()()()()()()),2&t&&(e.xp6(8),e.Q6J("formGroup",r.form),e.xp6(1),e.Q6J("ngForOf",r.parameters),e.xp6(1),e.Q6J("formGroup",r.form),e.xp6(1),e.Q6J("ngForOf",r.parameters),e.xp6(1),e.Q6J("align","end"),e.xp6(1),e.Q6J("disabled",!r.canUpdate))},directives:[C.Dt,C.uw,o._Y,o.JL,o.sg,m.sg,m.O5,Ae,o.JJ,o.u,Je,C.zM,f.uu],styles:[".parameters-container[_ngcontent-%COMP%]   .vertical-grid[_ngcontent-%COMP%]{display:grid;grid-template-columns:repeat(4,1fr);grid-template-rows:repeat(4,1fr);grid-gap:20px;gap:20px;width:100%;max-width:100dvw;margin:0 auto;place-content:center;place-items:center;grid-auto-flow:column}.parameters-container[_ngcontent-%COMP%]   .vertical-grid__item[_ngcontent-%COMP%]{width:100%;display:flex;align-items:center;justify-content:center}.parameters-container[_ngcontent-%COMP%]   .vertical-grid__item.full-row[_ngcontent-%COMP%]{grid-column:span 4}@media screen and (max-width: 1000px){.parameters-container[_ngcontent-%COMP%]   .vertical-grid[_ngcontent-%COMP%]{grid-template-columns:repeat(2,1fr);grid-template-rows:repeat(8,1fr)}}@media screen and (max-width: 620px){.parameters-container[_ngcontent-%COMP%]   .vertical-grid[_ngcontent-%COMP%]{grid-template-columns:1fr;grid-template-rows:auto;grid-auto-flow:row}}"],changeDetection:0}),a})();var R=l(44409),T=l(32075),X=l(79136);function Ue(a,i){1&a&&(e.TgZ(0,"th",17),e._uU(1,"Priority #"),e.qZA())}function Qe(a,i){if(1&a&&(e.TgZ(0,"td",18),e._UZ(1,"input",19),e.qZA()),2&a){const t=i.$implicit;e.xp6(1),e.Q6J("ngModel",t.priorityOrder)}}function Le(a,i){1&a&&(e.TgZ(0,"th",20),e._uU(1," Priority Name "),e.qZA())}function Ee(a,i){if(1&a&&(e.TgZ(0,"td",21),e._uU(1),e.qZA()),2&a){const t=i.$implicit;e.xp6(1),e.hij(" ",t.name," ")}}function Ye(a,i){1&a&&(e.TgZ(0,"th",22),e._uU(1," Secondary Sorting "),e.qZA())}function Me(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"td",23)(1,"cc-select",24),e.NdJ("ngModelChange",function(n){return e.CHM(t).$implicit.secondarySorting=n}),e.qZA()()}if(2&a){const t=i.$implicit,r=e.oxw();e.xp6(1),e.Q6J("ngModel",t.secondarySorting)("data",r.secondarySortingOptions)}}function Ie(a,i){1&a&&e._UZ(0,"th",17)}function Ge(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"td",18)(1,"cc-slide-toggle",25),e.NdJ("ngModelChange",function(n){return e.CHM(t).$implicit.enabled=n}),e.qZA()()}if(2&a){const t=i.$implicit;e.xp6(1),e.Q6J("ngModel",t.enabled)}}function Be(a,i){1&a&&e._UZ(0,"tr",26)}function $e(a,i){1&a&&e._UZ(0,"tr",27)}let L=class{constructor(i,t,r,n){this.apiService=i,this.stateService=t,this.notification=r,this._cdRef=n,this.dataSource=new T.by([]),this.secondarySortingOptions=[],this.displayedColumns=["priorityOrder","priorityName","secondarySorting","enabled"]}ngOnInit(){this.stateService.refresh$.pipe((0,P.w)(()=>this.apiService.getAllPriorities$)).subscribe({next:i=>{this.dataSource.data=i.sort((t,r)=>t.priorityOrder-r.priorityOrder),this._cdRef.detectChanges()}}),this.apiService.getConfigurations$.pipe((0,v.U)(i=>i.secondarySortingOptions.map(t=>({id:t.code,text:t.label})))).subscribe({next:i=>{this.secondarySortingOptions=i,this._cdRef.detectChanges()}})}onDrop(i){(0,R.bA)(this.dataSource.data,i.previousIndex,i.currentIndex),this.dataSource.data=[...this.dataSource.data].map((t,r)=>Object.assign(Object.assign({},t),{priorityOrder:r+1})),this._cdRef.detectChanges()}onSave(){const i=this.dataSource.data.map(t=>({id:t.id,priorityOrder:t.priorityOrder,secondarySorting:t.secondarySorting,enabled:t.enabled}));this.apiService.updatePriorities(i).subscribe({next:t=>{this.notification.notifySuccess("Updated Successfully."),this.stateService.refresh()}})}};L.\u0275fac=function(i){return new(i||L)(e.Y36(h),e.Y36(g),e.Y36(Z.zg),e.Y36(e.sBO))},L.\u0275cmp=e.Xpm({type:L,selectors:[["priorities"]],decls:21,vars:5,consts:[[1,"priorities-container","py-4"],[1,"py-2"],["mat-table","","cdkDropList","",1,"priorities-table",3,"dataSource","cdkDropListDropped"],["matColumnDef","priorityOrder"],["mat-header-cell","","class","col-1",4,"matHeaderCellDef"],["mat-cell","","class","col-1",4,"matCellDef"],["matColumnDef","priorityName"],["mat-header-cell","","class","col-md-6 align-left pl-5 pr-4","style","text-align: left !important;",4,"matHeaderCellDef"],["mat-cell","","class","col-md-6 pl-5 pr-4","style","text-align: left !important;",4,"matCellDef"],["matColumnDef","secondarySorting"],["mat-header-cell","","class","col-md-3",4,"matHeaderCellDef"],["mat-cell","","class","col-md-3",4,"matCellDef"],["matColumnDef","enabled"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","","cdkDrag","",4,"matRowDef","matRowDefColumns"],[3,"align"],["cc-raised-button","",1,"px-5","mx-3",3,"color","click"],["mat-header-cell","",1,"col-1"],["mat-cell","",1,"col-1"],["type","text",1,"col-12",2,"appearance","none","border","none","background-color","transparent","pointer-events","none","cursor","pointer","text-align","center",3,"ngModel"],["mat-header-cell","",1,"col-md-6","align-left","pl-5","pr-4",2,"text-align","left !important"],["mat-cell","",1,"col-md-6","pl-5","pr-4",2,"text-align","left !important"],["mat-header-cell","",1,"col-md-3"],["mat-cell","",1,"col-md-3"],[3,"ngModel","data","ngModelChange"],[3,"ngModel","ngModelChange"],["mat-header-row",""],["mat-row","","cdkDrag",""]],template:function(i,t){1&i&&(e.TgZ(0,"div",0)(1,"cc-card")(2,"cc-card-content",1)(3,"table",2),e.NdJ("cdkDropListDropped",function(n){return t.onDrop(n)}),e.ynx(4,3),e.YNc(5,Ue,2,0,"th",4),e.YNc(6,Qe,2,1,"td",5),e.BQk(),e.ynx(7,6),e.YNc(8,Le,2,0,"th",7),e.YNc(9,Ee,2,1,"td",8),e.BQk(),e.ynx(10,9),e.YNc(11,Ye,2,0,"th",10),e.YNc(12,Me,2,2,"td",11),e.BQk(),e.ynx(13,12),e.YNc(14,Ie,1,0,"th",4),e.YNc(15,Ge,2,1,"td",5),e.BQk(),e.YNc(16,Be,1,0,"tr",13),e.YNc(17,$e,1,0,"tr",14),e.qZA()(),e.TgZ(18,"cc-card-actions",15)(19,"button",16),e.NdJ("click",function(){return t.onSave()}),e._uU(20,"Save"),e.qZA()()()()),2&i&&(e.xp6(3),e.Q6J("dataSource",t.dataSource),e.xp6(13),e.Q6J("matHeaderRowDef",t.displayedColumns),e.xp6(1),e.Q6J("matRowDefColumns",t.displayedColumns),e.xp6(1),e.Q6J("align","end"),e.xp6(1),e.Q6J("color","accent"))},directives:[C.Dt,C.uw,T.BZ,R.Wj,T.w1,T.fO,T.ge,T.Dz,T.ev,o.Fj,o.JJ,o.On,y.jB,X.I,T.as,T.XQ,T.nj,T.Gk,R.Zt,C.zM,f.uu],styles:[".priorities-container{width:100%}.priorities-container .priorities-table{width:100%!important;box-shadow:#63636333 0 2px 8px!important}.priorities-container .priorities-table tr.mat-header-row{background-color:#000000f2!important;color:#fff!important}.priorities-container .priorities-table .mat-header-cell{background-color:transparent!important;color:#fff!important;font-size:14px}.priorities-container .priorities-table .mat-form-field-wrapper{margin:0!important;padding:0!important;padding-block:1rem!important;padding-inline:1rem!important}.priorities-container .cdk-drag-animating{transition:transform .25s ease-in-out!important}.priorities-container .mat-row:nth-child(even){background-color:#00000012}.priorities-container .priorities-table.cdk-drop-list-dragging .mat-row:not(.cdk-drag-placeholder){transition:transform .25s ease-in-out!important}\n"],encapsulation:2,changeDetection:0}),L=(0,x.gn)([A.kG],L);var V=l(18505),Fe=l(39841),N=l(15439);let E=class{constructor(i,t,r,n,s,c,d){this.fb=i,this.apiService=t,this.dataService=r,this.dialogRef=n,this.notification=s,this.stateService=c,this.data=d,this.form=this.fb.group({emirate:"",center:"",limitation:"",workingDays:""}),this.emirates$=this.dataService.emiratesOptions,this.weekDays=this.dataService.weekDaysOptions}ngOnInit(){}onAdd(){const i=this.form.value,t={emirate:{id:i.emirate},enrollmentCenter:i.center,limitationPerDay:i.limitation,workingDays:Array.isArray(i.workingDays)?i.workingDays.join(","):i.workingDays};this.apiService.createCenter(t).subscribe(()=>{this.notification.notifySuccess("Added Successfully."),this.stateService.refresh(),this.dialogRef.close()})}};E.\u0275fac=function(i){return new(i||E)(e.Y36(o.qu),e.Y36(h),e.Y36(k),e.Y36(_.so),e.Y36(Z.zg),e.Y36(g),e.Y36(_.WI))},E.\u0275cmp=e.Xpm({type:E,selectors:[["ng-component"]],decls:11,vars:16,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["formControlName","emirate",3,"label","data","required"],["formControlName","center",3,"label","required"],["formControlName","limitation","type","number",3,"label","required"],["formControlName","workingDays",3,"label","data","required","multiple"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-select",3),e.ALo(4,"async"),e._UZ(5,"cc-input",4)(6,"cc-input",5)(7,"cc-select",6),e.qZA(),e.BQk(),e.ynx(8,7),e.TgZ(9,"button",8),e.NdJ("click",function(){return t.onAdd()}),e._uU(10," Add "),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Add new Center"),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("label","Emirate")("data",e.lcZ(4,14,t.emirates$))("required",!0),e.xp6(2),e.Q6J("label","Enrollment Center ")("required",!0),e.xp6(1),e.Q6J("label","Limitation per Center per day")("required",!0),e.xp6(1),e.Q6J("label","Working Days")("data",t.weekDays)("required",!0)("multiple",!0),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,y.jB,o.JJ,o.u,o.Q7,S.G,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),E=(0,x.gn)([A.kG],E);let Y=class{constructor(i,t,r,n,s,c,d){this.fb=i,this.apiService=t,this.dataService=r,this.stateService=n,this.notification=s,this.dialogRef=c,this.data=d,this.form=this.fb.group({emirate:"",center:"",limitation:"",workingDays:""}),this.emirates$=this.dataService.emiratesOptions,this.weekDays=this.dataService.weekDaysOptions}ngOnInit(){this.apiService.getCenter(this.data.centerId).subscribe(i=>{this.form.patchValue({emirate:i.emirate.id,center:i.enrollmentCenter,limitation:i.limitationPerDay,workingDays:i.workingDays?i.workingDays.split(","):[]})})}onEdit(){if(this.form.valid){const i=this.form.value,t={id:this.data.centerId,emirate:{id:i.emirate},enrollmentCenter:i.center,limitationPerDay:i.limitation,workingDays:Array.isArray(i.workingDays)?i.workingDays.join(","):i.workingDays};this.apiService.updateCenter(t).subscribe(r=>{this.notification.notifySuccess("Updated Successfully."),this.stateService.refresh(),this.dialogRef.close(r)})}}};Y.\u0275fac=function(i){return new(i||Y)(e.Y36(o.qu),e.Y36(h),e.Y36(k),e.Y36(g),e.Y36(Z.zg),e.Y36(_.so),e.Y36(_.WI))},Y.\u0275cmp=e.Xpm({type:Y,selectors:[["app-edi-center"]],decls:11,vars:16,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["formControlName","emirate",3,"label","data","required"],["formControlName","center",3,"label","required"],["formControlName","limitation","type","number",3,"label","required"],["formControlName","workingDays",3,"label","data","required","multiple"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-select",3),e.ALo(4,"async"),e._UZ(5,"cc-input",4)(6,"cc-input",5)(7,"cc-select",6),e.qZA(),e.BQk(),e.ynx(8,7),e.TgZ(9,"button",8),e.NdJ("click",function(){return t.onEdit()}),e._uU(10," Save "),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Edit "+t.data.centerName),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("label","Emirate")("data",e.lcZ(4,14,t.emirates$))("required",!0),e.xp6(2),e.Q6J("label","Enrollment Center ")("required",!0),e.xp6(1),e.Q6J("label","Limitation per Center per day")("required",!0),e.xp6(1),e.Q6J("label","Working Days")("data",t.weekDays)("required",!0)("multiple",!0),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,y.jB,o.JJ,o.u,o.Q7,S.G,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),Y=(0,x.gn)([A.kG],Y);var ee=l(85185);let M=class{constructor(i,t,r,n,s,c,d){this.fb=i,this.apiService=t,this.dataService=r,this.stateService=n,this.notification=s,this.dialogRef=c,this.data=d,this.form=this.fb.group({type:"",priority:"",whenToBook:"",center:"",startHour:"",startTimeUnit:"",endHour:"",endTimeUnit:""}),this.typeOptions$=this.apiService.getCenterPriorityTypes.pipe((0,v.U)(u=>u.map(b=>({id:b,text:b})))),this.centers$=this.apiService.getCenterList.pipe((0,v.U)(u=>u.map(b=>({id:b.id,text:b.enrollmentCenter})))),this.hoursOptions=this.dataService.hoursOptions,this.timeUnitsOptions=this.dataService.timeUnitsOptions}ngOnInit(){}onAdd(){if(this.form.valid){const i=this.form.value,t=N(`${i.startHour}:00 ${i.startTimeUnit}`,"h:mm A"),r=N(`${i.endHour}:00 ${i.endTimeUnit}`,"h:mm A"),n={type:i.type,priority:i.priority,whenToBook:i.whenToBook,center:{id:i.center},startTime:t.format("HH:mm:00"),endTime:r.format("HH:mm:00")};this.apiService.createCenterPriority(n).subscribe(()=>{this.stateService.refresh(),this.notification.notifySuccess("Added Successfully."),this.dialogRef.close()})}}};M.\u0275fac=function(i){return new(i||M)(e.Y36(o.qu),e.Y36(h),e.Y36(k),e.Y36(g),e.Y36(Z.zg),e.Y36(_.so),e.Y36(_.WI))},M.\u0275cmp=e.Xpm({type:M,selectors:[["ng-component"]],decls:24,vars:25,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["formControlName","type",3,"label","data","required"],["formControlName","priority","type","number",3,"label","required"],["formControlName","whenToBook","type","number",3,"label","required"],["formControlName","center",3,"label","data","required"],[1,"mb-3"],[1,"d-flex","gap-2","align-items-center"],[1,""],["formControlName","startHour","label","Select Hour",1,"col-5",3,"data","required"],["formControlName","startTimeUnit","label","Select Unit",1,"col-5",3,"data","required"],["formControlName","endHour","label","Select Hour",1,"col-5",3,"data","required"],["label","Select Unit","formControlName","endTimeUnit",1,"col-5",3,"data","required"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-select",3),e.ALo(4,"async"),e._UZ(5,"cc-input",4)(6,"cc-input",5)(7,"cc-select",6),e.ALo(8,"async"),e.TgZ(9,"div",7)(10,"div",8)(11,"cc-label",9),e._uU(12,"Start Time"),e.qZA(),e._UZ(13,"cc-select",10)(14,"cc-select",11),e.qZA()(),e.TgZ(15,"div",7)(16,"div",8)(17,"cc-label",9),e._uU(18,"End Time"),e.qZA(),e._UZ(19,"cc-select",12)(20,"cc-select",13),e.qZA()()(),e.BQk(),e.ynx(21,14),e.TgZ(22,"button",15),e.NdJ("click",function(){return t.onAdd()}),e._uU(23," Add "),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Define Center Priority"),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("label","Type")("data",e.lcZ(4,21,t.typeOptions$))("required",!0),e.xp6(2),e.Q6J("label","Priority")("required",!0),e.xp6(1),e.Q6J("label","When to Book?")("required",!0),e.xp6(1),e.Q6J("label","Enrollment Center")("data",e.lcZ(8,23,t.centers$))("required",!0),e.xp6(6),e.Q6J("data",t.hoursOptions)("required",!0),e.xp6(1),e.Q6J("data",t.timeUnitsOptions)("required",!0),e.xp6(5),e.Q6J("data",t.hoursOptions)("required",!0),e.xp6(1),e.Q6J("data",t.timeUnitsOptions)("required",!0),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,y.jB,o.JJ,o.u,o.Q7,S.G,ee.k_,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),M=(0,x.gn)([A.kG],M);let I=class{constructor(i,t,r,n,s,c,d){this.fb=i,this.apiService=t,this.dataService=r,this.stateService=n,this.notification=s,this.dialogRef=c,this.data=d,this.form=this.fb.group({type:"",priority:"",whenToBook:"",center:"",startHour:"",startTimeUnit:"",endHour:"",endTimeUnit:""}),this.typeOptions$=this.apiService.getCenterPriorityTypes.pipe((0,v.U)(u=>u.map(b=>({id:b,text:b})))),this.centers$=this.apiService.getCenterList.pipe((0,v.U)(u=>u.map(b=>({id:b.id,text:b.enrollmentCenter})))),this.hoursOptions=this.dataService.hoursOptions,this.timeUnitsOptions=this.dataService.timeUnitsOptions}ngOnInit(){this.apiService.getCenterPriority(this.data.priorityId).subscribe({next:i=>{const t=this.parseTimeString(i.startTime),r=this.parseTimeString(i.endTime);this.form.patchValue({type:i.type,priority:i.priority,whenToBook:i.whenToBook,center:i.center.id,startHour:t.hour,startTimeUnit:t.isPM?"PM":"AM",endHour:r.hour,endTimeUnit:r.isPM?"PM":"AM"})}})}onSave(){const i=this.form.value,t=N(`${i.startHour}:00 ${i.startTimeUnit}`,"h:mm A"),r=N(`${i.endHour}:00 ${i.endTimeUnit}`,"h:mm A"),n={id:this.data.priorityId,type:i.type,priority:i.priority,whenToBook:i.whenToBook,center:{id:i.center},startTime:t.format("HH:mm:00"),endTime:r.format("HH:mm:00")};this.apiService.updateCenterPriority(n).subscribe(()=>{this.dialogRef.close(),this.stateService.refresh(),this.notification.notifySuccess("Updated Successfully.")})}parseTimeString(i){const t="string"==typeof i?N(i,"HH:mm:ss"):N().hour(i.hour).minute(0);return{hour:t.format("h"),isPM:"PM"===t.format("A")}}};function Re(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"div",12)(1,"button",13),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onDeleteCenter(s)}),e._uU(2," Delete "),e.qZA(),e.TgZ(3,"button",14),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onEditCenter(s)}),e._uU(4," Edit "),e.qZA()()}}function Ve(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"div",12)(1,"button",13),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onDeletePriority(s)}),e._uU(2," Delete "),e.qZA(),e.TgZ(3,"button",14),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().onEditPriority(s)}),e._uU(4," Edit "),e.qZA()()}}function je(a,i){1&a&&e._uU(0),2&a&&e.hij(" ",i.index+1," ")}I.\u0275fac=function(i){return new(i||I)(e.Y36(o.qu),e.Y36(h),e.Y36(k),e.Y36(g),e.Y36(Z.zg),e.Y36(_.so),e.Y36(_.WI))},I.\u0275cmp=e.Xpm({type:I,selectors:[["app-edit-center-priority"]],decls:24,vars:25,consts:[[3,"title"],["dialog-content",""],[3,"formGroup"],["formControlName","type",3,"label","data","required"],["formControlName","priority","type","number",3,"label","required"],["formControlName","whenToBook","type","number",3,"label","required"],["formControlName","center",3,"label","data","required"],[1,"mb-3"],[1,"d-flex","gap-2","align-items-center"],[1,""],["label","Select Hour","formControlName","startHour",1,"col-5",3,"data","required"],["label","Select Unit","formControlName","startTimeUnit",1,"col-5",3,"data","required"],[1,"mb-3","d-flex","flex-column","flex-md-row"],["label","Select Hour","formControlName","endHour",1,"col-5",3,"data","required"],["label","Select Unit","formControlName","endTimeUnit",1,"col-5",3,"data","required"],["dialog-actions",""],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(i,t){1&i&&(e.TgZ(0,"dynamic-dialog",0),e.ynx(1,1),e.TgZ(2,"form",2),e._UZ(3,"cc-select",3),e.ALo(4,"async"),e._UZ(5,"cc-input",4)(6,"cc-input",5)(7,"cc-select",6),e.ALo(8,"async"),e.TgZ(9,"div",7)(10,"div",8)(11,"cc-label",9),e._uU(12,"Start Time"),e.qZA(),e._UZ(13,"cc-select",10)(14,"cc-select",11),e.qZA()(),e.TgZ(15,"div",12)(16,"div",8)(17,"cc-label",9),e._uU(18,"End Time"),e.qZA(),e._UZ(19,"cc-select",13)(20,"cc-select",14),e.qZA()()(),e.BQk(),e.ynx(21,15),e.TgZ(22,"button",16),e.NdJ("click",function(){return t.onSave()}),e._uU(23," Save "),e.qZA(),e.BQk(),e.qZA()),2&i&&(e.Q6J("title","Edit Center Priority"),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("label","Type")("data",e.lcZ(4,21,t.typeOptions$))("required",!0),e.xp6(2),e.Q6J("label","Priority")("required",!0),e.xp6(1),e.Q6J("label","When to Book?")("required",!0),e.xp6(1),e.Q6J("label","Enrollment Center")("data",e.lcZ(8,23,t.centers$))("required",!0),e.xp6(6),e.Q6J("data",t.hoursOptions)("required",!0),e.xp6(1),e.Q6J("data",t.timeUnitsOptions)("required",!0),e.xp6(5),e.Q6J("data",t.hoursOptions)("required",!0),e.xp6(1),e.Q6J("data",t.timeUnitsOptions)("required",!0),e.xp6(2),e.Q6J("disabled",!t.form.valid))},directives:[w,o._Y,o.JL,o.sg,y.jB,o.JJ,o.u,o.Q7,S.G,ee.k_,f.uu],pipes:[m.Ov],encapsulation:2,changeDetection:0}),I=(0,x.gn)([A.kG],I);const te=function(a){return{operations:a}},He=function(a){return{index:a}},ze=[{field:"emirate.label",header:"Emirate"},{field:"enrollmentCenter",header:"Enrollment Center"},{field:"limitationPerDay",header:"Limitation per Center per day"},{field:"workingDays",header:"Working Days"},{field:"operations",header:"Actions"}],Ke=[{field:"process",header:"Process"},{field:"type",header:"Type"},{field:"priority",header:"Priority"},{field:"whenToBook",header:"When to Book?"},{field:"startTime",header:"Start Time",formatter:a=>{if(!a||!a.startTime)return"--";const i=a.startTime;return N().hour(i.hour).minute(i.minute).format("h:mm A")}},{field:"endTime",header:"End Time",formatter:a=>{if(!a||!a.endTime)return"--";const i=a.endTime;return N().hour(i.hour).minute(i.minute).format("h:mm A")}},{field:"centerName",header:"Enrollment Center"},{field:"statusLabel",header:"Status"},{field:"operations",header:"Actions"}],We=[{field:"index",header:"Index"},{field:"date",header:"Date"},{field:"maidName",header:"Maid's Name"},{field:"maidType",header:"Maid type"},{field:"centerName",header:"Which center was she booked in?"},{field:"availableBiometricAppointment",header:"Date and time she was booked in that center"}];let G=class{constructor(i,t,r,n){this.apiService=i,this.stateService=t,this.cdRef=r,this.dialog=n,this.centerColumns=ze,this.priorityColumns=Ke,this.bookedMaidsColumns=We,this.centersData=[],this.prioritiesData=[],this.bookedMaidsData={content:[],number:0,size:0,totalElements:0,totalPages:0}}ngOnInit(){this.loadCenters(),this.loadCenterPriorities(),this.loadBookedMaids()}openAddCenterDialog(){this.dialog.originalOpen(E,{panelClass:"col-md-6",data:{}})}onEditCenter(i){this.dialog.originalOpen(Y,{panelClass:"col-md-6",data:{centerId:i.id,centerName:i.enrollmentCenter}})}onDeleteCenter(i){this.dialog.confirm(`Delete ${i.enrollmentCenter}`,"Are you sure that you want to delete this center?",()=>{this.deleteCenter(i)},()=>{},"Confirm","Cancel")}openAddProcessDialog(){this.dialog.originalOpen(M,{panelClass:"col-md-6",data:{}})}onEditPriority(i){this.dialog.originalOpen(I,{panelClass:"col-md-6",data:{priorityId:i.id}})}onDeletePriority(i){this.dialog.confirm("Are you sure you want to delete this priority?","",()=>{this.apiService.deleteCenterPriority(i.id).subscribe(()=>{this.stateService.refresh()})},()=>{},"Confirm","Cancel")}handleNextPage(i){this.stateService.updateSearchState({params:{page:i.pageIndex,size:i.pageSize}})}ngOnDestroy(){this.stateService.updateSearchState(z)}loadCenters(){this.stateService.refresh$.pipe((0,P.w)(()=>this.apiService.getCenterList),(0,V.b)(i=>{this.centersData=i,this.cdRef.markForCheck()})).subscribe()}loadCenterPriorities(){this.stateService.refresh$.pipe((0,P.w)(()=>this.apiService.getCenterPriorityList),(0,V.b)(i=>{this.prioritiesData=i,this.cdRef.markForCheck()})).subscribe()}loadBookedMaids(){(0,Fe.a)([this.stateService.refresh$,this.stateService.search$]).pipe((0,P.w)(()=>this.apiService.getBookedMaids),(0,V.b)(i=>{this.bookedMaidsData=i,this.cdRef.markForCheck()})).subscribe()}deleteCenter(i){this.apiService.deleteCenter(i.id).subscribe(()=>{this.stateService.refresh()})}};function Xe(a,i){if(1&a&&(e.TgZ(0,"div"),e._UZ(1,"cc-select",11),e.qZA()),2&a){const t=e.oxw();e.xp6(1),e.Q6J("data",t.selectOptions)("formControlName",t.data.parameter.name)}}function et(a,i){if(1&a&&(e.TgZ(0,"div"),e._UZ(1,"cc-select",11),e.qZA()),2&a){const t=e.oxw();e.xp6(1),e.Q6J("data",t.selectOptions)("formControlName",t.data.parameter.name)}}function tt(a,i){if(1&a&&(e._UZ(0,"cc-select",23),e.ALo(1,"titlecase")),2&a){const t=e.oxw().$implicit,r=e.oxw(2).$implicit,n=e.oxw(2);e.Q6J("data",n.getFieldOptions(r,t))("formControlName",t)("label",e.lcZ(1,3,t))}}function it(a,i){if(1&a&&(e._UZ(0,"cc-input",24),e.ALo(1,"titlecase")),2&a){const t=e.oxw().$implicit;e.Q6J("formControlName",t)("label",e.lcZ(1,2,t))}}function rt(a,i){if(1&a&&(e.TgZ(0,"div",20),e.YNc(1,tt,2,5,"cc-select",21),e.YNc(2,it,2,4,"cc-input",22),e.qZA()),2&a){const t=i.$implicit,r=e.oxw(2).$implicit,n=e.oxw(2);e.xp6(1),e.Q6J("ngIf","LIST"===n.getJsonFieldType(r,t)),e.xp6(1),e.Q6J("ngIf","STRING"===n.getJsonFieldType(r,t))}}function at(a,i){if(1&a&&(e.TgZ(0,"div",12)(1,"div",18),e.YNc(2,rt,3,2,"div",19),e.qZA()()),2&a){const t=e.oxw().$implicit,r=e.oxw(2);e.Q6J("formGroupName",t),e.xp6(2),e.Q6J("ngForOf",r.getNestedJsonKeys(t))}}function nt(a,i){if(1&a&&(e._UZ(0,"cc-select",23),e.ALo(1,"titlecase")),2&a){const t=e.oxw(2).$implicit,r=e.oxw(2);e.Q6J("data",r.getFieldOptions(t))("formControlName",t)("label",e.lcZ(1,3,t))}}function ot(a,i){if(1&a&&(e._UZ(0,"cc-input",24),e.ALo(1,"titlecase")),2&a){const t=e.oxw(2).$implicit;e.Q6J("formControlName",t)("label",e.lcZ(1,2,t))}}function st(a,i){if(1&a&&(e.TgZ(0,"div",15),e.YNc(1,nt,2,5,"cc-select",21),e.YNc(2,ot,2,4,"cc-input",22),e.qZA()),2&a){const t=e.oxw().$implicit,r=e.oxw(2);e.xp6(1),e.Q6J("ngIf","LIST"===r.getJsonFieldType(t)),e.xp6(1),e.Q6J("ngIf","STRING"===r.getJsonFieldType(t))}}function ct(a,i){if(1&a&&(e.TgZ(0,"div",14)(1,"h5",15),e._uU(2),e.ALo(3,"titlecase"),e.qZA(),e.YNc(4,at,3,2,"div",16),e.YNc(5,st,3,2,"ng-template",null,17,e.W1O),e.qZA()),2&a){const t=i.$implicit,r=e.MAs(6),n=e.oxw(2);e.xp6(2),e.Oqu(e.lcZ(3,3,t)),e.xp6(2),e.Q6J("ngIf",n.isNestedObject(t))("ngIfElse",r)}}function lt(a,i){if(1&a&&(e.TgZ(0,"div")(1,"div",12),e.YNc(2,ct,7,5,"div",13),e.qZA()()),2&a){const t=e.oxw();e.xp6(1),e.Q6J("formGroupName",t.data.parameter.name),e.xp6(1),e.Q6J("ngForOf",t.getJsonKeys())}}G.\u0275fac=function(i){return new(i||G)(e.Y36(h),e.Y36(g),e.Y36(e.sBO),e.Y36(p.uY))},G.\u0275cmp=e.Xpm({type:G,selectors:[["bio-appointments"]],decls:40,vars:28,consts:[[1,"mt-4"],[1,"py-2","mt-4","d-flex","justify-content-end","align-items-center"],["cc-raised-button","","color","primary",3,"click"],[3,"expanded"],[1,"m-0"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["centerDetailsOperations",""],[3,"columns","data","showPaginator","cellTemplate","loading"],["centerPriorityOperations",""],[3,"columns","data","length","pageIndex","pageSize","cellTemplate","page"],["indexTmp",""],[1,"d-flex","flex-column","flex-md-row","gap-1","justify-content-center"],["cc-flat-button","","color","warn",3,"click"],["cc-flat-button","","color","accent",3,"click"]],template:function(i,t){if(1&i&&(e._UZ(0,"div",0),e.TgZ(1,"cc-card")(2,"cc-card-content")(3,"section")(4,"div",1)(5,"button",2),e.NdJ("click",function(){return t.openAddCenterDialog()}),e._uU(6," Add New Center "),e.qZA()(),e.TgZ(7,"cc-accordion")(8,"cc-panel",3)(9,"cc-panel-title")(10,"h3",4),e._uU(11,"Center Details"),e.qZA()(),e.TgZ(12,"cc-panel-body"),e._UZ(13,"cc-datagrid",5),e.YNc(14,Re,5,0,"ng-template",6,7,e.W1O),e.qZA()()()(),e.TgZ(16,"section")(17,"div",1)(18,"button",2),e.NdJ("click",function(){return t.openAddProcessDialog()}),e._uU(19," Define New Priority "),e.qZA()(),e.TgZ(20,"cc-accordion")(21,"cc-panel",3)(22,"cc-panel-title")(23,"h3",4),e._uU(24,"Centers Priorities"),e.qZA()(),e.TgZ(25,"cc-panel-body"),e._UZ(26,"cc-datagrid",8),e.YNc(27,Ve,5,0,"ng-template",6,9,e.W1O),e.qZA()()()(),e.TgZ(29,"section"),e._UZ(30,"div",1),e.TgZ(31,"cc-accordion")(32,"cc-panel",3)(33,"cc-panel-title")(34,"h3",4),e._uU(35,"Booked Maids"),e.qZA()(),e.TgZ(36,"cc-panel-body")(37,"cc-datagrid",10),e.NdJ("page",function(n){return t.handleNextPage(n)}),e.qZA(),e.YNc(38,je,1,1,"ng-template",6,11,e.W1O),e.qZA()()()()()()),2&i){const r=e.MAs(15),n=e.MAs(28),s=e.MAs(39);e.xp6(8),e.Q6J("expanded",!0),e.xp6(5),e.Q6J("columns",t.centerColumns)("data",t.centersData)("showPaginator",!1)("loading",!1)("cellTemplate",e.VKq(22,te,r)),e.xp6(1),e.Q6J("ccGridCell",t.centersData),e.xp6(7),e.Q6J("expanded",!0),e.xp6(5),e.Q6J("columns",t.priorityColumns)("data",t.prioritiesData)("showPaginator",!1)("cellTemplate",e.VKq(24,te,n))("loading",!1),e.xp6(1),e.Q6J("ccGridCell",t.prioritiesData),e.xp6(5),e.Q6J("expanded",!0),e.xp6(5),e.Q6J("columns",t.bookedMaidsColumns)("data",null==t.bookedMaidsData?null:t.bookedMaidsData.content)("length",t.bookedMaidsData.totalElements)("pageIndex",t.bookedMaidsData.number)("pageSize",t.bookedMaidsData.size)("cellTemplate",e.VKq(26,He,s)),e.xp6(1),e.Q6J("ccGridCell",t.bookedMaidsData.content)}},directives:[C.Dt,C.uw,f.uu,J.I,J.CW,J.LL,J.G9,B.Ge,B.VC],encapsulation:2,changeDetection:0}),G=(0,x.gn)([A.kG],G);let dt=(()=>{class a{constructor(t,r,n,s,c,d,u){this.formBuilder=t,this.apiService=r,this.stateService=n,this.notificationService=s,this.cd=c,this.dialoRef=d,this.data=u,this.selectOptions=[],this.jsonFieldOptionsMap=new Map,this.form=this.formBuilder.group({})}ngOnInit(){this.handleParameterType(),this.buildRequiredForm(),this.cd.detectChanges()}onCancel(){this.dialoRef.close()}onSave(){const t={name:this.data.parameter.name,currentValue:""};"JSON"===this.data.parameter.type&&(t.currentValue=JSON.stringify(this.form.value[this.data.parameter.name])),["LIST","BOOLEAN"].includes(this.data.parameter.type)&&(t.currentValue=this.form.value[this.data.parameter.name]),this.apiService.updateLawpParameter(t).subscribe(r=>{this.notificationService.notifySuccess("Parameter updated successfully"),this.stateService.refresh(),this.dialoRef.close(!0)})}handleParameterType(){"LIST"===this.data.parameter.type&&this.handleListType(),"BOOLEAN"===this.data.parameter.type&&this.handleBooleanType(),"JSON"===this.data.parameter.type&&this.handleJsonType()}buildRequiredForm(){if(["LIST","BOOLEAN"].includes(this.data.parameter.type)){const t=this.createFormControl(this.data.parameter);this.form.addControl(this.data.parameter.name,t)}if("JSON"===this.data.parameter.type){const t=this.parseJsonValue(),r=this.buildJsonFormGroup(t);this.form.addControl(this.data.parameter.name,r)}}parseJsonValue(){return this.data.parameter.currentValue?JSON.parse(this.data.parameter.currentValue):{}}buildJsonFormGroup(t){const r=Object.entries(t).reduce((n,[s,c])=>(n[s]=this.isObject(c)?this.buildNestedFormGroup(c):this.formBuilder.control(c,o.kI.required),n),{});return this.formBuilder.group(r)}buildNestedFormGroup(t){const r=this.formBuilder.group({});return Object.keys(t).forEach(n=>{const s=this.formBuilder.control(t[n],o.kI.required);r.addControl(n,s)}),r}isObject(t){return"object"==typeof t&&null!==t}createFormControl(t){return this.formBuilder.control(this.data.parameter.currentValue,o.kI.required)}handleListType(){var t;this.selectOptions=(null===(t=this.data.parameter.allowedValues)||void 0===t?void 0:t.map(r=>({id:r,text:r})))||[]}handleBooleanType(){this.selectOptions=["true","false"].map(t=>({id:t,text:t}))}handleJsonType(){this.data.parameter.allowedValues&&this.createJsonFieldOptionsMapping()}createJsonFieldOptionsMapping(){this.jsonFieldOptionsMap=new Map;const t=this.data.parameter.allowedValues;if(!t)return;const n=(s,c)=>{"LIST"===(null==c?void 0:c.type)&&Array.isArray(c.options)&&this.jsonFieldOptionsMap.set(s,(s=>s.map(c=>({id:c,text:c})))(c.options))};for(const[s,c]of Object.entries(t))if(this.isObject(c)){if(c.type){n(s,c);continue}for(const[d,u]of Object.entries(c))n(`${s}.${d}`,u)}}getJsonFieldType(t,r){var n;const s=this.data.parameter.allowedValues;if(!s)return"STRING";const c=r&&this.isObject(s[t])?null===(n=s[t])||void 0===n?void 0:n[r]:s[t];return this.isObject(c)&&"LIST"===c.type?"LIST":"STRING"}getFieldOptions(t,r){var n;return(null===(n=this.jsonFieldOptionsMap)||void 0===n?void 0:n.get(r?`${t}.${r}`:t))||[]}getJsonKeys(){if("JSON"!==this.data.parameter.type||!this.data.parameter.currentValue)return[];const t=this.parseJsonValue();return Object.keys(t)}getNestedJsonKeys(t){if("JSON"!==this.data.parameter.type||!this.data.parameter.currentValue)return[];const n=this.parseJsonValue()[t];return this.isObject(n)?Object.keys(n):[]}isNestedObject(t){if("JSON"!==this.data.parameter.type||!this.data.parameter.currentValue)return!1;const r=this.parseJsonValue();return this.isObject(r[t])}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(o.qu),e.Y36(h),e.Y36(g),e.Y36(Z.zg),e.Y36(e.sBO),e.Y36(_.so),e.Y36(_.WI))},a.\u0275cmp=e.Xpm({type:a,selectors:[["ng-component"]],decls:16,vars:7,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-content",""],[3,"formGroup"],[1,"d-flex","flex-column","gap-4"],[3,"ngSwitch"],[4,"ngSwitchCase"],["cc-dialog-actions",""],["cc-raised-button","","color","primary",3,"disabled","click"],["cc-button","",3,"click"],["label","Select Value",3,"data","formControlName"],[3,"formGroupName"],["class","mb-4",4,"ngFor","ngForOf"],[1,"mb-4"],[1,"mb-3"],[3,"formGroupName",4,"ngIf","ngIfElse"],["simpleValue",""],[1,"d-flex","flex-column"],["class","col-md-12 mb-3",4,"ngFor","ngForOf"],[1,"col-md-12","mb-3"],[3,"data","formControlName","label",4,"ngIf"],[3,"formControlName","label",4,"ngIf"],[3,"data","formControlName","label"],[3,"formControlName","label"]],template:function(t,r){1&t&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3),e.qZA()(),e.TgZ(4,"div",3)(5,"form",4)(6,"div",5)(7,"div",6),e.YNc(8,Xe,2,2,"div",7),e.YNc(9,et,2,2,"div",7),e.YNc(10,lt,3,2,"div",7),e.qZA()()()(),e.TgZ(11,"div",8)(12,"button",9),e.NdJ("click",function(){return r.onSave()}),e._uU(13," Save "),e.qZA(),e.TgZ(14,"button",10),e.NdJ("click",function(){return r.onCancel()}),e._uU(15," Cancel "),e.qZA()()()),2&t&&(e.xp6(3),e.hij("Edit ",r.data.name,""),e.xp6(2),e.Q6J("formGroup",r.form),e.xp6(2),e.Q6J("ngSwitch",r.data.parameter.type),e.xp6(1),e.Q6J("ngSwitchCase","LIST"),e.xp6(1),e.Q6J("ngSwitchCase","BOOLEAN"),e.xp6(1),e.Q6J("ngSwitchCase","JSON"),e.xp6(2),e.Q6J("disabled",!r.form.valid))},directives:[p.iK,p.Cj,p.Zb,p.kL,o._Y,o.JL,o.sg,m.RF,m.n9,y.jB,o.JJ,o.u,o.x0,m.sg,m.O5,S.G,p.Zu,f.uu],pipes:[m.rS],encapsulation:2,changeDetection:0}),a})();function pt(a,i){if(1&a){const t=e.EpF();e.TgZ(0,"div")(1,"div",3)(2,"div",4)(3,"span",5),e._uU(4),e.qZA()(),e.TgZ(5,"div",6),e._uU(6),e.qZA(),e.TgZ(7,"button",7),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw().openEditDialog(s)}),e._uU(8,"Edit"),e.qZA()()()}if(2&a){const t=i.$implicit,r=e.oxw();e.xp6(4),e.Oqu(r.removeUnderscores(t.name)),e.xp6(2),e.Oqu(t.currentValue)}}let mt=(()=>{class a{constructor(t,r,n,s){this.apiService=t,this.stateService=r,this.dialog=n,this.cd=s,this.lawpParameters=[]}ngOnInit(){this.stateService.refresh$.pipe((0,P.w)(()=>this.apiService.getLawpParameters$)).subscribe(t=>{this.lawpParameters=t,this.cd.detectChanges()})}removeUnderscores(t){return t.replace(/_/g," ").toLowerCase()}openEditDialog(t){this.dialog.originalOpen(dt,{data:{parameter:t,name:this.removeUnderscores(t.name)},panelClass:"col-md-6"})}}return a.\u0275fac=function(t){return new(t||a)(e.Y36(h),e.Y36(g),e.Y36(p.uY),e.Y36(e.sBO))},a.\u0275cmp=e.Xpm({type:a,selectors:[["lawp-parameters"]],decls:8,vars:1,consts:[[1,"my-4","mx-3"],[1,"d-flex","flex-column","gap-4","px-4"],[4,"ngFor","ngForOf"],[1,"d-flex","justify-content-between","gap-4","align-items-center"],[1,"col-md-3"],[2,"text-wrap","balance","text-transform","capitalize"],[1,"form-control","h-100","col-md-6"],["cc-raised-button","","color","primary",3,"click"]],template:function(t,r){1&t&&(e.TgZ(0,"div",0)(1,"cc-card")(2,"cc-card-header")(3,"cc-card-title"),e._uU(4,"Lawp Parameters"),e.qZA()(),e.TgZ(5,"div",1),e.YNc(6,pt,9,2,"div",2),e.qZA(),e._UZ(7,"cc-card-content"),e.qZA()()),2&t&&(e.xp6(6),e.Q6J("ngForOf",r.lawpParameters))},directives:[C.Dt,C.oJ,C.K9,m.sg,f.uu,C.uw],encapsulation:2,changeDetection:0}),a})();function ut(a,i){1&a&&e._UZ(0,"credit-cards")}function ht(a,i){1&a&&e._UZ(0,"parameters")}function gt(a,i){1&a&&e._UZ(0,"priorities")}function _t(a,i){1&a&&e._UZ(0,"bio-appointments")}function ft(a,i){1&a&&e._UZ(0,"lawp-parameters")}let Ct=(()=>{class a{constructor(){}ngOnInit(){}}return a.\u0275fac=function(t){return new(t||a)},a.\u0275cmp=e.Xpm({type:a,selectors:[["app-shell"]],decls:12,vars:5,consts:[[1,"visa-prioritize"],["animationDuration","0ms"],[3,"label"],["ccTabContent",""]],template:function(t,r){1&t&&(e.TgZ(0,"div",0)(1,"cc-tab-group",1)(2,"cc-tab",2),e.YNc(3,ut,1,0,"ng-template",3),e.qZA(),e.TgZ(4,"cc-tab",2),e.YNc(5,ht,1,0,"ng-template",3),e.qZA(),e.TgZ(6,"cc-tab",2),e.YNc(7,gt,1,0,"ng-template",3),e.qZA(),e.TgZ(8,"cc-tab",2),e.YNc(9,_t,1,0,"ng-template",3),e.qZA(),e.TgZ(10,"cc-tab",2),e.YNc(11,ft,1,0,"ng-template",3),e.qZA()()()),2&t&&(e.xp6(2),e.Q6J("label","Credit Cards"),e.xp6(2),e.Q6J("label","Parameters"),e.xp6(2),e.Q6J("label","Priorities"),e.xp6(2),e.Q6J("label","Bio Appointments"),e.xp6(2),e.Q6J("label","Lawp Parameters"))},directives:[$.e6,$.eF,$.ST,Q,De,L,G,mt],styles:[".cc-tab-header .cc-tab-labels{justify-content:start!important}\n"],encapsulation:2}),a})();var bt=l(1402),vt=l(45834);const yt=[{path:"",component:Ct}];let xt=(()=>{class a{}return a.\u0275fac=function(t){return new(t||a)},a.\u0275mod=e.oAB({type:a}),a.\u0275inj=e.cJS({providers:[h,g,k],imports:[[m.ez,o.UX,o.u5,bt.Bz.forChild(yt),$.wA,J.yU,p.I8,B.Gz,f.S6,O.D$,S.f,X.B,y.lK,C.Ev,T.p0,B.Gz,R._t,O.D$,O.bY,O.SZ,F.YV,vt.L,W.$]]}),a})()}}]);
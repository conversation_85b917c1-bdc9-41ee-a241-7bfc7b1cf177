(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_maid-visa-summary_maid-visa-summary_module_ts-node_modules_moment_locale_sync-385071"],{63385:(k,M,i)=>{"use strict";i.r(M),i.d(M,{MaidVisaSummaryModule:()=>ss});var d=i(69808),o=i(93075),b=i(1402),m=i(65868),u=i(88476),C=i(62764),D=i(28172),J=i(43687),T=i(26523),F=i(45834),g=i(34378),l=i(97582),L=i(77579),z=i(18505),O=i(82722);const p={search:{params:{page:0,size:20},search:{}}};var n=i(65620);const h={updateSearch:(0,n.PH)("[MaidVisaSummary | Store Service | Update State] update search fields",(0,n.Ky)()),resetSearch:(0,n.PH)("[MaidVisaSummary | Store Service | Reset State] reset search fields"),resetState:(0,n.PH)("[MaidVisaSummary | Store Service | Reset] reset state")},Z="maid visa summary",P=(0,n.Lq)(p,(0,n.on)(h.updateSearch,(a,{search:t})=>Object.assign(Object.assign({},a),{search:{search:t.search,params:{page:t.params.page,size:t.params.size}}})),(0,n.on)(h.resetSearch,a=>Object.assign(Object.assign({},a),{search:p.search})),(0,n.on)(h.resetState,()=>Object.assign({},p))),A=(0,n.ZF)(Z),I={selectMaidVisaSummaryFeature:A,selectSearch:(0,n.P1)(A,a=>a.search)};var s=i(5e3),V=i(40520),U=i(43604),E=i(34782);let x=(()=>{class a{constructor(e){this.http=e}fetchMaidsList(e){var r;return this.http.get(`${U.b.searchMaid}${null!==(r=e.searchString)&&void 0!==r?r:""}`,{params:new V.LE({fromObject:{page:e.page,size:e.size}})}).pipe((0,E.d)(1))}getVisaSummaryList(e){return this.http.get(U.b.maidVisaSummary,{params:new V.LE({fromObject:this.getParamObj(e)})})}getParamObj(e){return Object.assign(Object.assign({},e.params),e.search)}}return a.\u0275fac=function(e){return new(e||a)(s.LFG(V.eN))},a.\u0275prov=s.Yz7({token:a,factory:a.\u0275fac}),a})();var j,S,N=i(54004),G=i(15439),H=i(85185);class y{constructor(t,e,r){this.fb=t,this.store=e,this.service=r,this.search=new s.vpe,this.reset=new s.vpe,this.typeOptions=[{id:"Reject",text:"Reject"},{id:"Message",text:"Message"},{id:"Client",text:"Client"},{id:"RequiresFollowUp",text:"Requires Follow-up"},{id:"FollowUpDone",text:"Follow-up Done"}],this.fetchMaidsList=c=>this.service.fetchMaidsList(c).pipe((0,N.U)(f=>f.map(R=>({id:R.id,text:R.label})))),j.set(this,new L.x),this.form=this.fb.group({from:"",to:"",historyType:"",housemaidId:""})}ngOnInit(){this.syncFormWithSearchState(),this.fetchMaidsListData()}ngOnDestroy(){(0,l.Q_)(this,j,"f").next(),(0,l.Q_)(this,j,"f").unsubscribe()}onSubmit(){const t=Object.assign(Object.assign({},this.form.value),{from:this.formatDate(this.form.value.from),to:this.formatDate(this.form.value.to)}),e=this.filterTruthyProps(t);this.search.emit(e)}onReset(){this.store.dispatch(h.resetSearch()),this.reset.emit()}fetchMaidsListData(){return this.service.fetchMaidsList({page:0,size:20}).pipe((0,N.U)(t=>t.map(e=>({id:e.id,text:e.label}))),(0,z.b)(t=>this.maidsListData=t),(0,O.R)((0,l.Q_)(this,j,"f"))).subscribe()}syncFormWithSearchState(){this.store.select(I.selectSearch).pipe((0,z.b)(t=>{var e,r,c,f;this.form.patchValue({from:(null===(e=t.search)||void 0===e?void 0:e.from)||"",to:(null===(r=t.search)||void 0===r?void 0:r.to)||"",housemaidId:null===(c=t.search)||void 0===c?void 0:c.housemaidId,historyType:null===(f=t.search)||void 0===f?void 0:f.historyType})}),(0,O.R)((0,l.Q_)(this,j,"f"))).subscribe()}filterTruthyProps(t){return Object.fromEntries(Object.entries(t).filter(([e,r])=>r))}formatDate(t){return t?G(new Date(t)).format("YYYY-MM-DD"):""}}j=new WeakMap,y.\u0275fac=function(t){return new(t||y)(s.Y36(o.qu),s.Y36(n.yh),s.Y36(x))},y.\u0275cmp=s.Xpm({type:y,selectors:[["search-filter"]],outputs:{search:"search",reset:"reset"},decls:27,vars:4,consts:[[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[2,"margin-right","2px"],[1,"row","d-flex","justify-content-around","px-4",3,"formGroup"],[1,"col-md-6"],["label","Select Maid","entityId","selectMaid","formControlName","housemaidId",3,"search","data"],["label","Select Type","formControlName","historyType",3,"data"],["formControlName","from","color","primary"],["formControlName","to","color","primary"],[1,"col-md-4","d-flex","justify-content-center","gap-3","my-2","py-2","col-md-12"],["cc-raised-button","","color","accent","type","button",2,"display","block","height","fit-content","padding-block","0.2rem","padding-inline","2rem",3,"click"],["cc-raised-button","","type","button",2,"display","block","height","fit-content","padding-block",".2rem","padding-inline","2rem","background-color","#808080","color","#fff",3,"click"]],template:function(t,e){1&t&&(s.TgZ(0,"cc-accordion",0)(1,"cc-panel",1)(2,"cc-panel-title",2)(3,"cc-icon",3),s._uU(4,"filter_alt"),s.qZA(),s.TgZ(5,"span"),s._uU(6,"Filter"),s.qZA()(),s.TgZ(7,"cc-panel-body")(8,"div")(9,"form",4)(10,"div",5),s._UZ(11,"cc-select",6),s.qZA(),s.TgZ(12,"div",5),s._UZ(13,"cc-select",7),s.qZA(),s.TgZ(14,"div",5)(15,"cc-datepicker",8)(16,"cc-label"),s._uU(17,"From Date"),s.qZA()()(),s.TgZ(18,"div",5)(19,"cc-datepicker",9)(20,"cc-label"),s._uU(21,"To Date"),s.qZA()()(),s.TgZ(22,"div",10)(23,"button",11),s.NdJ("click",function(){return e.onSubmit()}),s._uU(24," Search "),s.qZA(),s.TgZ(25,"button",12),s.NdJ("click",function(){return e.onReset()}),s._uU(26," Reset "),s.qZA()()()()()()()),2&t&&(s.xp6(9),s.Q6J("formGroup",e.form),s.xp6(2),s.Q6J("search",!0)("data",e.maidsListData),s.xp6(2),s.Q6J("data",e.typeOptions))},directives:[g.I,g.CW,g.LL,F.Q9,g.G9,o._Y,o.JL,o.sg,T.jB,o.JJ,o.u,D.AC,H.k_,m.uu],encapsulation:2,changeDetection:0});const W=function(){return[]},_=function(){return[10,20,30,40,50]};function K(a,t){if(1&a){const e=s.EpF();s.ynx(0),s.TgZ(1,"cc-datagrid",2),s.NdJ("page",function(c){return s.CHM(e),s.oxw().handleNextPage(c)}),s.qZA(),s.BQk()}if(2&a){const e=t.ngIf,r=s.oxw();s.xp6(1),s.Q6J("columns",r.gridCols)("data",e.content||s.DdM(9,W))("length",e.totalElements)("pageOnFront",!1)("pageIndex",e.number)("pageSize",e.size)("pageSizeOptions",s.DdM(10,_))("columnMenuDisplayOnHover",!1)("columnMenuButtonIcon","settings")}}const $=[{field:"historyDate",header:"Date"},{field:"request.maidName",header:"Maid",width:"100px"},{field:"documentTag",header:"Document"},{field:"type",header:"Type"},{field:"description",header:"Description",width:"65ch"},{field:"maidVisaMessage.success",header:"Delivered",formatter:a=>a.maidVisaMessage&&a.maidVisaMessage.success?"<span>Yes</span>":"<span>No</span>"}];class v{constructor(t,e,r){this.service=t,this.store=e,this.cdr=r,S.set(this,new L.x),this.gridCols=$}ngOnInit(){this.loadMaidVisaSummaryList(p.search),this.store.select(I.selectSearch).pipe((0,z.b)(t=>this.search=t),(0,O.R)((0,l.Q_)(this,S,"f"))).subscribe()}handleNextPage(t){const e={search:Object.assign({},this.search.search),params:{page:t.pageIndex,size:t.pageSize}};this.store.dispatch(h.updateSearch({search:e})),this.loadMaidVisaSummaryList(e)}onSearch(t){const e={params:Object.assign({},p.search.params),search:t};this.store.dispatch(h.updateSearch({search:e})),this.loadMaidVisaSummaryList(e)}onReset(){this.loadMaidVisaSummaryList(p.search),this.store.dispatch(h.resetSearch())}loadMaidVisaSummaryList(t){this.data$=this.service.getVisaSummaryList(t),this.cdr.detectChanges()}ngOnDestroy(){(0,l.Q_)(this,S,"f").next(),(0,l.Q_)(this,S,"f").unsubscribe()}}S=new WeakMap,v.\u0275fac=function(t){return new(t||v)(s.Y36(x),s.Y36(n.yh),s.Y36(s.sBO))},v.\u0275cmp=s.Xpm({type:v,selectors:[["ng-component"]],decls:3,vars:3,consts:[[3,"search","reset"],[4,"ngIf"],[3,"columns","data","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","columnMenuDisplayOnHover","columnMenuButtonIcon","page"]],template:function(t,e){1&t&&(s.TgZ(0,"search-filter",0),s.NdJ("search",function(c){return e.onSearch(c)})("reset",function(){return e.onReset()}),s.qZA(),s.YNc(1,K,2,11,"ng-container",1),s.ALo(2,"async")),2&t&&(s.xp6(1),s.Q6J("ngIf",s.lcZ(2,1,e.data$)))},directives:[y,d.O5,C.Ge],pipes:[d.Ov],encapsulation:2});let X=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=s.oAB({type:a}),a.\u0275inj=s.cJS({imports:[[n.Aw.forFeature(Z,P)],n.Aw]}),a})();const q=[{path:"",component:v}];let ss=(()=>{class a{}return a.\u0275fac=function(e){return new(e||a)},a.\u0275mod=s.oAB({type:a}),a.\u0275inj=s.cJS({providers:[x],imports:[[d.ez,o.UX,o.u5,b.Bz.forChild(q),X,C.Gz,u.n_,T.lK,m.S6,g.yU,F.L,D.Bp,J.f]]}),a})()},46700:(k,M,i)=>{var d={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function o(m){var u=b(m);return i(u)}function b(m){if(!i.o(d,m)){var u=new Error("Cannot find module '"+m+"'");throw u.code="MODULE_NOT_FOUND",u}return d[m]}o.keys=function(){return Object.keys(d)},o.resolve=b,k.exports=o,o.id=46700}}]);
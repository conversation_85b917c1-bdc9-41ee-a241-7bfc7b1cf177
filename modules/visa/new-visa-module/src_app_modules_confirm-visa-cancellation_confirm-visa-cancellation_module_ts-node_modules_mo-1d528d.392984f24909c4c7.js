(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_confirm-visa-cancellation_confirm-visa-cancellation_module_ts-node_modules_mo-1d528d"],{12948:(ee,k,l)=>{"use strict";l.r(k),l.d(k,{ConfirmVisaCancellationsModule:()=>Oe});var y=l(69808),u=l(65620);const S={searchState:{name:"",nationality:"",type:""}},C=(0,u.PH)("[ConfirmVisaCancellation | Base Store Serive] Reset State"),j=(0,u.PH)("[ConfirmVisaCancellation List | Store Service] Save Search",(0,u.Ky)()),te="confirm-visa-cancellation",pe=(0,u.Lq)(S,(0,u.on)(C,n=>Object.assign(Object.assign({},n),S)),(0,u.on)(j,(n,d)=>Object.assign(Object.assign({},n),{searchState:d.search})));var ae=l(26991),e=l(5e3);const ie=(0,u.ZF)(te),ge=((0,u.P1)(ie,n=>n),(0,u.P1)(ie,n=>n.searchState));var T=l(88476),z=l(54004);let L=(()=>{class n extends T.il{constructor(a){super(a),this.searchState$=this.store.select(ge),this.nationalitiesOptions$=this.picklistSelectors("nationalities").pipe((0,z.U)(t=>t.map(o=>({id:o.code,text:o.text,code:o.code}))))}pickListsCodes(){return["nationalities"]}saveSearch(a){this.store.dispatch(j({search:a}))}resetState(){this.store.dispatch(C())}}return n.\u0275fac=function(a){return new(a||n)(e.LFG(u.yh))},n.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac}),n})();var f=l(40520),p=l(8188),v=l(43604);let M=(()=>{class n{constructor(a,t){this._api=a,this._http=t}getToDoList(a,t,o,s,h){const m=new f.LE({fromObject:{page:t.page,size:t.size,taskName:a,nationality:o,type:s,name:h}});return this._http.get(v.b.visaCancellationList,{params:m,context:(new f.qT).set(p.hG,!0)}).pipe()}getMVPList(a,t,o){const h=new f.LE({fromObject:{nationality:a,type:t,name:null!=o?o:""}});return this._http.get(v.b.visaMvpList,{params:h,context:(new f.qT).set(p.hG,!0)}).pipe()}getNotesList(a){return this._http.get(v.b.visaCancellationListNote(a),{context:(new f.qT).set(p.hG,!0)}).pipe()}addNote(a,t){const o=(new f.qT).set(p.hG,!0);return this._http.post(v.b.visaCancellationAddNote(a),{note:t},{context:o}).pipe()}updateNote(a,t){const o=(new f.qT).set(p.hG,!0);return this._http.post(v.b.visaCancellationEditNote(a)+"?newNote="+t,{newNote:t},{context:o}).pipe()}deleteNote(a){const t=(new f.qT).set(p.hG,!0);return this._http.delete(v.b.visaCancellationDeleteNote(a),{context:t}).pipe()}moveToMvp(a){return this._http.get(v.b.visaCancellationMoveToMVP(a),{context:(new f.qT).set(p.hG,!0)}).pipe()}cancelMvp(a){return this._http.get(v.b.visaCancellationCancelMVP(a),{context:(new f.qT).set(p.hG,!0)}).pipe()}confirmCancellation(a){return this._http.get(v.b.visaCancellationConfirmToDo(a),{context:(new f.qT).set(p.hG,!0)}).pipe()}fetchNationalities(){return this._http.get([this._api,v.b.getNationalities].join("/"),{context:(new f.qT).set(p.hG,!1)}).pipe()}}return n.\u0275fac=function(a){return new(a||n)(e.LFG(p.JV),e.LFG(f.eN))},n.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var V=l(21799);let ve=(()=>{class n{constructor(a,t,o,s){this._actions=a,this._store=t,this._service=o,this._notificationService=s}}return n.\u0275fac=function(a){return new(a||n)(e.LFG(ae.eX),e.LFG(L),e.LFG(M),e.LFG(V.zg))},n.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac}),n})(),be=(()=>{class n{}return n.\u0275fac=function(a){return new(a||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[[y.ez,u.Aw.forFeature(te,pe),ae.sQ.forFeature([ve])]]}),n})();var Ce=l(1402),_e=l(4128),oe=l(39300),ne=l(63900),J=l(77579),N=l(48966),r=l(93075),c=l(82599),w=l(65868),U=l(42002);let De=(()=>{class n{constructor(a,t,o,s){this._formBuilder=a,this._service=t,this._dialogRef=o,this.data=s,this.destroy$=new J.x,this.form=this._formBuilder.group({notes:""})}ngOnInit(){}addNote(){this._dialogRef.close({id:this.data.toDoId,notes:this.form.value.notes})}close(){this._dialogRef.close()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}}return n.\u0275fac=function(a){return new(a||n)(e.Y36(r.qu),e.Y36(M),e.Y36(N.so),e.Y36(N.WI))},n.\u0275cmp=e.Xpm({type:n,selectors:[["add-note-dialog"]],decls:13,vars:2,consts:[["cc-std-dialog",""],["cc-dialog-title",""],["role","button","type","button","cc-icon-button","",3,"click"],[3,"formGroup"],["label","Note","formControlName","notes"],["cc-button","","color","basic",3,"click"],["cc-button","","color","accent",3,"disabled","click"]],template:function(a,t){1&a&&(e.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),e._uU(3,"Add Note"),e.qZA(),e.TgZ(4,"a",2),e.NdJ("click",function(){return t.close()}),e.qZA()(),e.TgZ(5,"cc-dialog-content")(6,"form",3),e._UZ(7,"cc-textarea",4),e.qZA()(),e.TgZ(8,"cc-dialog-actions")(9,"button",5),e.NdJ("click",function(){return t.close()})("click",function(){return t.form.reset()}),e._uU(10," Cancel "),e.qZA(),e.TgZ(11,"button",6),e.NdJ("click",function(){return t.addNote()}),e._uU(12," Add "),e.qZA()()()),2&a&&(e.xp6(6),e.Q6J("formGroup",t.form),e.xp6(5),e.Q6J("disabled",t.form.invalid))},directives:[c.iK,c.Cj,c.Zb,w.uu,c.kL,r._Y,r.JL,r.sg,U.Qf,r.JJ,r.u,c.Zu],styles:[""],changeDetection:0}),n})();var se=l(15222);let ye=(()=>{class n{constructor(a,t,o,s){this._formBuilder=a,this.dialog=t,this._dialogRef=o,this.data=s,this.destroy$=new J.x,this.form=this._formBuilder.group({notes:""})}ngOnInit(){var a;null===(a=this.form.get("notes"))||void 0===a||a.setValue(this.data.notes)}editNote(){this._dialogRef.close({update:!0,id:this.data.noteId,notes:this.form.value.notes})}deleteNote(){this.dialog.confirm("Warnning","Are you sure you want to delete this Note?",()=>{this._dialogRef.close({update:!1,id:this.data.noteId,notes:this.form.value.notes})})}close(){this._dialogRef.close()}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}}return n.\u0275fac=function(a){return new(a||n)(e.Y36(r.qu),e.Y36(c.uY),e.Y36(N.so),e.Y36(N.WI))},n.\u0275cmp=e.Xpm({type:n,selectors:[["edit-note-dialog"]],decls:15,vars:2,consts:[["cc-std-dialog",""],["cc-dialog-title",""],["role","button","type","button","cc-icon-button","",3,"click"],[3,"formGroup"],["label","Note","formControlName","notes"],["cc-button","","color","basic",3,"click"],["cc-button","","color","warn",3,"click"],["cc-button","","color","accent",3,"disabled","click"]],template:function(a,t){1&a&&(e.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),e._uU(3,"Edit Note"),e.qZA(),e.TgZ(4,"a",2),e.NdJ("click",function(){return t.close()}),e.qZA()(),e.TgZ(5,"cc-dialog-content")(6,"form",3),e._UZ(7,"cc-textarea",4),e.qZA()(),e.TgZ(8,"cc-dialog-actions")(9,"button",5),e.NdJ("click",function(){return t.close()})("click",function(){return t.form.reset()}),e._uU(10," Cancel "),e.qZA(),e.TgZ(11,"button",6),e.NdJ("click",function(){return t.deleteNote()}),e._uU(12,"Delete"),e.qZA(),e.TgZ(13,"button",7),e.NdJ("click",function(){return t.editNote()}),e._uU(14," Save "),e.qZA()()()),2&a&&(e.xp6(6),e.Q6J("formGroup",t.form),e.xp6(7),e.Q6J("disabled",t.form.invalid))},directives:[c.iK,c.Cj,c.Zb,w.uu,c.kL,r._Y,r.JL,r.sg,U.Qf,r.JJ,r.u,c.Zu],styles:[""],changeDetection:0}),n})();var R=l(62764);function je(n,d){1&n&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}let Ne=(()=>{class n{constructor(a,t,o,s,h,b,m){this._service=a,this._dialogRef=t,this._auth=o,this._dialog=s,this.cdr=h,this.notificationService=b,this.data=m,this.destroy$=new J.x,this.loggedUser$=this._auth.loggedUser$,this.notesColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{icon:"edit",type:"raised",mode:"single",disabled:!1,hidden:i=>i.creatorId!=this.user.id,callback:i=>this.edit(i)}]}},{field:"note",header:"Note"},{field:"creator",header:"User"},{field:"creationDate",header:"Creation Date"}]}ngOnInit(){this.loggedUser$.subscribe(a=>this.user=a),this.notesList$=this._service.getNotesList(this.data.toDoId),this.notesList$.subscribe(a=>{this.notesList=a,this.cdr.markForCheck()})}close(){this._dialogRef.close()}edit(a){this._dialog.originalOpen(ye,{width:"550px",data:{noteId:a.id,notes:a.note}}).afterClosed().pipe((0,oe.h)(se.Rx.filters.isDefined),(0,ne.w)(o=>o.update?this._service.updateNote(o.id,o.notes):this._service.deleteNote(o.id))).subscribe(o=>{this.notificationService.notifySuccess("Updated Successfully"),this.notesList$=this._service.getNotesList(this.data.toDoId),this.notesList$.subscribe(s=>{this.notesList=s,this.cdr.detectChanges()})})}ngOnDestroy(){this.destroy$.next(),this.destroy$.complete()}}return n.\u0275fac=function(a){return new(a||n)(e.Y36(M),e.Y36(N.so),e.Y36(p.JW),e.Y36(c.uY),e.Y36(e.sBO),e.Y36(V.zg),e.Y36(N.WI))},n.\u0275cmp=e.Xpm({type:n,selectors:[["list-note-dialog"]],decls:12,vars:5,consts:[["cc-std-dialog",""],["cc-dialog-title",""],["role","button","type","button","cc-icon-button","",3,"click"],[3,"noResultTemplate","data","columns","pageOnFront","showPaginator"],["cc-button","","color","basic",3,"click"],["noResultTpl",""]],template:function(a,t){if(1&a&&(e.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),e._uU(3,"Notes"),e.qZA(),e.TgZ(4,"a",2),e.NdJ("click",function(){return t.close()}),e.qZA()(),e.TgZ(5,"cc-dialog-content"),e._UZ(6,"cc-datagrid",3),e.qZA(),e.TgZ(7,"cc-dialog-actions")(8,"button",4),e.NdJ("click",function(){return t.close()}),e._uU(9,"Close"),e.qZA()()(),e.YNc(10,je,2,0,"ng-template",null,5,e.W1O)),2&a){const o=e.MAs(11);e.xp6(6),e.Q6J("noResultTemplate",o)("data",t.notesList)("columns",t.notesColumns)("pageOnFront",!0)("showPaginator",!1)}},directives:[c.iK,c.Cj,c.Zb,w.uu,c.kL,R.Ge,c.Zu],styles:[""],changeDetection:0}),n})();var A=l(13859),Z=l(34378),le=l(45834),de=l(43687),re=l(26523);function Se(n,d){if(1&n&&e._uU(0),2&n){const a=e.oxw();e.hij("Confirm Cancellation (",a.confirmCancellationTotal,") ")}}function Te(n,d){if(1&n&&e._uU(0),2&n){const a=e.oxw();e.hij(" Confirmed MVP Maids (",a.confirmedMVPTotal,") ")}}function we(n,d){1&n&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}const ce=function(){return{id:"MaidVisa",text:"Maid Visa"}},me=function(){return{id:"MaidCC",text:"Maid CC"}},ue=function(n,d){return[n,d]},_=function(){return[]},D=function(){return[10,20,30,40,50]};let Ze=(()=>{class n{constructor(a,t,o,s,h,b,m){this._store=a,this.formBuilder=t,this._service=o,this.cdr=s,this._dialog=h,this.notificationService=b,this.mediaService=m,this.search$=this._store.searchState$,this.form=this.formBuilder.group({}),this.confirmCancellationTotal=0,this.confirmedMVPTotal=0,this.terminationColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"menu",icon:"drag_indicator",disabled:!1,buttons:[{icon:"check_circle",type:"raised",text:"Confirm",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.confirm(i.id)},{icon:"adjust",type:"raised",text:"Move to MVP",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.moveToMvp(i.id)},{icon:"add",type:"raised",text:"Add Note",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.addNote(i.id)},{icon:"expand_more",type:"raised",text:"View Notes",mode:"single",disabled:i=>0==i.notesSize,hidden:i=>!1,callback:i=>this.viewNotes(i.id)}]}},{field:"housemaid.label",header:"Maid Name"},{field:"nationality.name",header:"Nationality"},{field:"dateOfTerminationAsString",header:"Termination Date"}],this.noShowColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"menu",icon:"drag_indicator",disabled:!1,buttons:[{icon:"check_circle",type:"raised",text:"Confirm",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.confirm(i.id)},{icon:"adjust",type:"raised",text:"Move to MVP",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.moveToMvp(i.id)},{icon:"add",type:"raised",text:"Add Note",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.addNote(i.id)},{icon:"expand_more",type:"raised",text:"View Notes",mode:"single",disabled:i=>0==i.notesSize,hidden:i=>!1,callback:i=>this.viewNotes(i.id)}]}},{field:"housemaid.label",header:"Maid Name"},{field:"nationality.name",header:"Nationality"},{field:"noShowDateAsString",header:"No-show date"}],this.noShowTypeColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"menu",disabled:!1,icon:"drag_indicator",buttons:[{icon:"check_circle",type:"raised",text:"Confirm",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.confirm(i.id)},{icon:"adjust",type:"raised",text:"Move to MVP",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.moveToMvp(i.id)},{icon:"add",type:"raised",text:"Add Note",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.addNote(i.id)},{icon:"expand_more",type:"raised",text:"View Notes",mode:"single",disabled:i=>0==i.notesSize,hidden:i=>!1,callback:i=>this.viewNotes(i.id)}]}},{field:"housemaid.label",header:"Maid Name"},{field:"nationality.name",header:"Nationality"},{field:"typeOfTermination",header:"Type"},{field:"noShowDateAsString",header:"No-show date"}],this.terminationTypeColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"menu",disabled:!1,icon:"drag_indicator",buttons:[{icon:"check_circle",type:"raised",text:"Confirm",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.confirm(i.id)},{icon:"adjust",type:"raised",text:"Move to MVP",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.moveToMvp(i.id)},{icon:"add",type:"raised",text:"Add Note",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.addNote(i.id)},{icon:"expand_more",type:"raised",text:"View Notes",mode:"single",disabled:i=>0==i.notesSize,hidden:i=>!1,callback:i=>this.viewNotes(i.id)}]}},{field:"housemaid.label",header:"Maid Name"},{field:"nationality.name",header:"Nationality"},{field:"typeOfTermination",header:"Type"},{field:"dateOfTerminationAsString",header:"Termination Date"}],this.firedColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"menu",disabled:!1,icon:"drag_indicator",buttons:[{icon:"check_circle",type:"raised",text:"Confirm",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.confirm(i.id)},{icon:"adjust",type:"raised",text:"Move to MVP",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.moveToMvp(i.id)},{icon:"add",type:"raised",text:"Add Note",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.addNote(i.id)},{icon:"expand_more",type:"raised",text:"View Notes",mode:"single",disabled:i=>0==i.notesSize,hidden:i=>!1,callback:i=>this.viewNotes(i.id)}]}},{field:"housemaid.label",header:"Maid Name"},{field:"nationality.name",header:"Nationality"},{field:"dateOfTerminationAsString",header:"Termination Date"}],this.mvpColumns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"menu",icon:"drag_indicator",disabled:!1,buttons:[{icon:"cancel",type:"raised",text:"Cancel MVP",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.cancelMVP(i.id)},{icon:"add",type:"raised",text:"Add Note",mode:"single",disabled:!1,hidden:i=>!1,callback:i=>this.addNote(i.id)},{icon:"expand_more",type:"raised",text:"View Notes",mode:"single",disabled:i=>0==i.notesSize,hidden:i=>!1,callback:i=>this.viewNotes(i.id)}]}},{field:"housemaid.label",header:"Maid Name"},{field:"nationality.name",header:"Nationality"},{field:"mvpDateAsString",header:"MVP Date"}]}ngOnInit(){this.nationalitiesOptions$=this._service.fetchNationalities().pipe((0,z.U)(a=>a.map(t=>({id:t.id,text:t.name,code:t.code})))),this.nationalitiesOptions$.subscribe(a=>this.nationalitiesOptions=a),this.fetchData(),this.fetchMVP()}saveSearch(a){this._store.saveSearch(a),this.cdr.detectChanges()}filter1(){this.fetchData()}filter(){this.fetchMVP()}fetchData(){var a,t,o,s,h,b,m,i,O,$,P,Y,F,q,E,I,G,Q,B,W,x,X,K,H;this.maidVisa$=this._service.getToDoList("maidVisa",{page:0,size:20},null!==(a=this.form.value.nationality)&&void 0!==a?a:"",null!==(t=this.form.value.type)&&void 0!==t?t:"",null!==(o=this.form.value.name)&&void 0!==o?o:""),this.nonRenewal$=this._service.getToDoList("nonRenewal",{page:0,size:20},null!==(s=this.form.value.nationality)&&void 0!==s?s:"",null!==(h=this.form.value.type)&&void 0!==h?h:"",null!==(b=this.form.value.name)&&void 0!==b?b:""),this.CCNoShow$=this._service.getToDoList("CCNoShow",{page:0,size:20},null!==(m=this.form.value.nationality)&&void 0!==m?m:"",null!==(i=this.form.value.type)&&void 0!==i?i:"",null!==(O=this.form.value.name)&&void 0!==O?O:""),this.resigned$=this._service.getToDoList("resigned",{page:0,size:20},null!==($=this.form.value.nationality)&&void 0!==$?$:"",null!==(P=this.form.value.type)&&void 0!==P?P:"",null!==(Y=this.form.value.name)&&void 0!==Y?Y:""),this.maidVisaNoShow$=this._service.getToDoList("maidVisaNoShow",{page:0,size:20},null!==(F=this.form.value.nationality)&&void 0!==F?F:"",null!==(q=this.form.value.type)&&void 0!==q?q:"",null!==(E=this.form.value.name)&&void 0!==E?E:""),this.absentMaid$=this._service.getToDoList("absentMaid",{page:0,size:20},null!==(I=this.form.value.nationality)&&void 0!==I?I:"",null!==(G=this.form.value.type)&&void 0!==G?G:"",null!==(Q=this.form.value.name)&&void 0!==Q?Q:""),this.noShow_noDeduction$=this._service.getToDoList("noShow_noDeduction",{page:0,size:20},null!==(B=this.form.value.nationality)&&void 0!==B?B:"",null!==(W=this.form.value.type)&&void 0!==W?W:"",null!==(x=this.form.value.name)&&void 0!==x?x:""),this.firedData$=this._service.getToDoList("fired",{page:0,size:20},null!==(X=this.form.value.nationality)&&void 0!==X?X:"",null!==(K=this.form.value.type)&&void 0!==K?K:"",null!==(H=this.form.value.name)&&void 0!==H?H:""),(0,_e.D)([this.maidVisa$,this.CCNoShow$,this.resigned$,this.maidVisaNoShow$,this.absentMaid$,this.nonRenewal$,this.noShow_noDeduction$,this.firedData$]).pipe((0,z.U)(g=>(this.maidVisaData=g[0],this.CCNoShowData=g[1],this.resignedData=g[2],this.maidVisaNoShowData=g[3],this.absentMaidData=g[4],this.nonRenewalData=g[5],this.noShow_noDeductionData=g[6],this.firedData=g[7],g.reduce(($e,fe)=>$e+(null!=fe.totalElements?fe.totalElements:0),0)))).subscribe(g=>{this.confirmCancellationTotal=g,this.cdr.detectChanges()})}fetchMVP(){var a,t,o;this.mvpList$=this._service.getMVPList(null!==(a=this.form.value.nationality)&&void 0!==a?a:"",null!==(t=this.form.value.type)&&void 0!==t?t:"",null!==(o=this.form.value.name)&&void 0!==o?o:""),this.mvpList$.subscribe(s=>{this.mvpData=s,this.confirmedMVPTotal=s.todoCount,this.cdr.detectChanges()})}pageChange(a,t){var o,s,h;this._service.getToDoList(t,{page:a.pageIndex,size:a.pageSize},null!==(o=this.form.value.nationality)&&void 0!==o?o:"",null!==(s=this.form.value.type)&&void 0!==s?s:"",null!==(h=this.form.value.name)&&void 0!==h?h:"").subscribe(m=>{switch(t){case"maidVisa":this.maidVisaData=m;break;case"nonRenewal":this.nonRenewalData=m;break;case"resigned":this.resignedData=m;break;case"maidVisaNoShow":this.maidVisaNoShowData=m;break;case"CCNoShow":this.CCNoShowData=m;break;case"noShow_noDeduction":this.noShow_noDeductionData=m;break;case"absentMaid":this.absentMaidData=m;break;case"fired":this.firedData=m}})}camelCaseToSpace(a){return"paymentrequestworkflow"===a?"Payment Request Workflow":a.replace(/(^[a-z]+)|[0-9]+|[A-Z][a-z]+|[A-Z]+(?=[A-Z][a-z]|[0-9])/g,(t,o)=>(o&&(t=t[0].toUpperCase()+t.substr(1)),t+" "))}getEnumValueLabel(a){return a&&a.toLowerCase().replace(/_/g," ").replace(/\w\S*/g,t=>t.charAt(0).toUpperCase()+t.substr(1).toLowerCase())}confirm(a){this._dialog.confirm("Warnning","Are you sure you want to cancel this maid's visa?",()=>{this._service.confirmCancellation(a).subscribe(()=>{this.notificationService.notifySuccess("Visa Canceled Successfully"),this.fetchData(),this.fetchMVP()})})}moveToMvp(a){this._dialog.confirm("Warnning","Are you sure you want to Move the maid to MVP?",()=>{this._service.moveToMvp(a).subscribe(()=>{this.notificationService.notifySuccess("Moved Successfully"),this.fetchData(),this.fetchMVP()})})}addNote(a){this._dialog.originalOpen(De,{width:"550px",data:{toDoId:a}}).afterClosed().pipe((0,oe.h)(se.Rx.filters.isDefined),(0,ne.w)(o=>this._service.addNote(o.id,o.notes))).subscribe(()=>{this.notificationService.notifySuccess("Note Added Successfully"),this.fetchData(),this.fetchMVP()})}viewNotes(a){this._dialog.originalOpen(Ne,{width:"850px",data:{toDoId:a}})}cancelMVP(a){this._dialog.confirm("Warnning","Are you sure you want to Cancel MVP?",()=>{this._service.cancelMvp(a).subscribe(()=>{this.notificationService.notifySuccess("Canceled Successfully"),this.fetchData(),this.fetchMVP()})})}}return n.\u0275fac=function(a){return new(a||n)(e.Y36(L),e.Y36(r.qu),e.Y36(M),e.Y36(e.sBO),e.Y36(c.uY),e.Y36(V.zg),e.Y36(V.yJ))},n.\u0275cmp=e.Xpm({type:n,selectors:[["app-confirm-visa-cancellation"]],decls:99,vars:100,consts:[[1,"fluid-container","mt-4"],["animationDuration","0ms"],["cc-tab-label",""],[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[2,"margin-right","2px"],[3,"formGroup","ccConnectForm"],[1,"row"],[1,"col-md-4","row"],["for","name",1,"col-md-4","pt-4"],["label","Name","formControlName","name",1,"col-md-8"],["for","nationality",1,"col-md-4","pt-4"],["label","Nationality","formControlName","nationality",1,"col-md-8",3,"data"],["for","maidNationality",1,"col-md-4","pt-4"],["label","Type","formControlName","type",1,"col-md-8",3,"data"],[1,"col-md-4","d-flex","justify-content-center","gap-2","py-3","mx-auto"],["cc-raised-button","","color","accent","type","submit",2,"padding","0.15rem 1.8rem",3,"click"],[3,"noResultTemplate","data","columns","length","pageIndex","pageSize","pageSizeOptions","page"],[3,"noResultTemplate","data","columns","pageOnFront","showPaginator"],["noResultTpl",""]],template:function(a,t){if(1&a&&(e.ynx(0),e.TgZ(1,"div",0)(2,"cc-tab-group",1)(3,"cc-tab"),e.YNc(4,Se,1,1,"ng-template",2),e.TgZ(5,"cc-accordion",3)(6,"cc-panel",4)(7,"cc-panel-title",5)(8,"cc-icon",6),e._uU(9,"filter_alt"),e.qZA(),e.TgZ(10,"span"),e._uU(11,"Filter"),e.qZA()(),e.TgZ(12,"cc-panel-body")(13,"div")(14,"form",7),e.ALo(15,"async"),e.TgZ(16,"div",8)(17,"div",9)(18,"label",10),e._uU(19,"Name"),e.qZA(),e._UZ(20,"cc-input",11),e.qZA(),e.TgZ(21,"div",9)(22,"label",12),e._uU(23,"Nationality"),e.qZA(),e._UZ(24,"cc-select",13),e.qZA(),e.TgZ(25,"div",9)(26,"label",14),e._uU(27,"Type"),e.qZA(),e._UZ(28,"cc-select",15),e.qZA(),e.TgZ(29,"div",16)(30,"button",17),e.NdJ("click",function(){return t.filter1()}),e._uU(31," Search "),e.qZA()()()()()()()(),e.TgZ(32,"div")(33,"h5"),e._uU(34,"Maidvisa"),e.qZA(),e.TgZ(35,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"maidVisa")}),e.qZA()(),e.TgZ(36,"div")(37,"h5"),e._uU(38,"Non-Renewal"),e.qZA(),e.TgZ(39,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"nonRenewal")}),e.qZA()(),e.TgZ(40,"div")(41,"h5"),e._uU(42,"Resigned"),e.qZA(),e.TgZ(43,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"resigned")}),e.qZA()(),e.TgZ(44,"div")(45,"h5"),e._uU(46,"MV - No Show"),e.qZA(),e.TgZ(47,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"maidVisaNoShow")}),e.qZA()(),e.TgZ(48,"div")(49,"h5"),e._uU(50,"CC - No Show"),e.qZA(),e.TgZ(51,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"CCNoShow")}),e.qZA()(),e.TgZ(52,"div")(53,"h5"),e._uU(54,"No Cancelation Signature Maids"),e.qZA(),e.TgZ(55,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"noShow_noDeduction")}),e.qZA()(),e.TgZ(56,"div")(57,"h5"),e._uU(58,"Absent Maid"),e.qZA(),e.TgZ(59,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"absentMaid")}),e.qZA()(),e.TgZ(60,"div")(61,"h5"),e._uU(62,"Fired Maids"),e.qZA(),e.TgZ(63,"cc-datagrid",18),e.NdJ("page",function(s){return t.pageChange(s,"fired")}),e.qZA()()(),e.TgZ(64,"cc-tab"),e.YNc(65,Te,1,1,"ng-template",2),e.TgZ(66,"cc-accordion",3)(67,"cc-panel",4)(68,"cc-panel-title",5)(69,"cc-icon",6),e._uU(70,"filter_alt"),e.qZA(),e.TgZ(71,"span"),e._uU(72,"Filter"),e.qZA()(),e.TgZ(73,"cc-panel-body")(74,"div")(75,"form",7),e.ALo(76,"async"),e.TgZ(77,"div",8)(78,"div",9)(79,"label",10),e._uU(80,"Name"),e.qZA(),e._UZ(81,"cc-input",11),e.qZA(),e.TgZ(82,"div",9)(83,"label",12),e._uU(84,"Nationality"),e.qZA(),e._UZ(85,"cc-select",13),e.qZA(),e.TgZ(86,"div",9)(87,"label",14),e._uU(88,"Type"),e.qZA(),e._UZ(89,"cc-select",15),e.qZA(),e.TgZ(90,"div",16)(91,"button",17),e.NdJ("click",function(){return t.filter()}),e._uU(92," Search "),e.qZA()()()()()()()(),e.TgZ(93,"div")(94,"h5"),e._uU(95,"Maidvisa"),e.qZA(),e._UZ(96,"cc-datagrid",19),e.qZA()()()(),e.BQk(),e.YNc(97,we,2,0,"ng-template",null,20,e.W1O)),2&a){const o=e.MAs(98);e.xp6(14),e.Q6J("formGroup",t.form)("ccConnectForm",e.lcZ(15,69,t.search$)),e.xp6(10),e.Q6J("data",t.nationalitiesOptions),e.xp6(4),e.Q6J("data",e.WLB(75,ue,e.DdM(73,ce),e.DdM(74,me))),e.xp6(7),e.Q6J("noResultTemplate",o)("data",t.maidVisaData?t.maidVisaData.content:e.DdM(78,_))("columns",t.terminationColumns)("length",t.maidVisaData?t.maidVisaData.totalElements:0)("pageIndex",t.maidVisaData?t.maidVisaData.number:0)("pageSize",t.maidVisaData?t.maidVisaData.size:0)("pageSizeOptions",e.DdM(79,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.nonRenewalData?t.nonRenewalData.content:e.DdM(80,_))("columns",t.terminationColumns)("length",t.nonRenewalData?t.nonRenewalData.totalElements:0)("pageIndex",t.nonRenewalData?t.nonRenewalData.number:0)("pageSize",t.nonRenewalData?t.nonRenewalData.size:0)("pageSizeOptions",e.DdM(81,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.resignedData?t.resignedData.content:e.DdM(82,_))("columns",t.terminationColumns)("length",t.resignedData?t.resignedData.totalElements:0)("pageIndex",t.resignedData?t.resignedData.number:0)("pageSize",t.resignedData?t.resignedData.size:0)("pageSizeOptions",e.DdM(83,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.maidVisaNoShowData?t.maidVisaNoShowData.content:e.DdM(84,_))("columns",t.noShowColumns)("length",t.maidVisaNoShowData?t.maidVisaNoShowData.totalElements:0)("pageIndex",t.maidVisaNoShowData?t.maidVisaNoShowData.number:0)("pageSize",t.maidVisaNoShowData?t.maidVisaNoShowData.size:0)("pageSizeOptions",e.DdM(85,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.CCNoShowData?t.CCNoShowData.content:e.DdM(86,_))("columns",t.noShowColumns)("length",t.CCNoShowData?t.CCNoShowData.totalElements:0)("pageIndex",t.CCNoShowData?t.CCNoShowData.number:0)("pageSize",t.CCNoShowData?t.CCNoShowData.size:0)("pageSizeOptions",e.DdM(87,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.noShow_noDeductionData?t.noShow_noDeductionData.content:e.DdM(88,_))("columns",t.noShowTypeColumns)("length",t.noShow_noDeductionData?t.noShow_noDeductionData.totalElements:0)("pageIndex",t.noShow_noDeductionData?t.noShow_noDeductionData.number:0)("pageSize",t.noShow_noDeductionData?t.noShow_noDeductionData.size:0)("pageSizeOptions",e.DdM(89,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.absentMaidData?t.absentMaidData.content:e.DdM(90,_))("columns",t.terminationTypeColumns)("length",t.absentMaidData?t.absentMaidData.totalElements:0)("pageIndex",t.absentMaidData?t.absentMaidData.number:0)("pageSize",t.absentMaidData?t.absentMaidData.size:0)("pageSizeOptions",e.DdM(91,D)),e.xp6(4),e.Q6J("noResultTemplate",o)("data",t.firedData?t.firedData.content:e.DdM(92,_))("columns",t.firedColumns)("length",t.firedData?t.firedData.totalElements:0)("pageIndex",t.firedData?t.firedData.number:0)("pageSize",t.firedData?t.firedData.size:0)("pageSizeOptions",e.DdM(93,D)),e.xp6(12),e.Q6J("formGroup",t.form)("ccConnectForm",e.lcZ(76,71,t.search$)),e.xp6(10),e.Q6J("data",t.nationalitiesOptions),e.xp6(4),e.Q6J("data",e.WLB(96,ue,e.DdM(94,ce),e.DdM(95,me))),e.xp6(7),e.Q6J("noResultTemplate",o)("data",t.mvpData?t.mvpData.todo:e.DdM(99,_))("columns",t.mvpColumns)("pageOnFront",!1)("showPaginator",!1)}},directives:[A.e6,A.eF,A.L3,Z.I,Z.CW,Z.LL,le.Q9,Z.G9,r._Y,r.JL,r.sg,T.Ls,de.G,r.JJ,r.u,re.jB,w.uu,R.Ge],pipes:[y.Ov],styles:[""],changeDetection:0}),n})();var Me=l(57902),Ve=l(4882),Ae=l(69202),ke=l(92431),ze=l(58015),he=l(11523),Le=l(54657),Je=l(467),Ue=l(63372);const Re=[{path:"",component:Ze}];let Oe=(()=>{class n{}return n.\u0275fac=function(a){return new(a||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({providers:[L],imports:[[U.l2,A.wA,y.ez,be,Ce.Bz.forChild(Re),Ae.Ev,r.UX,r.u5,T.er,Ve.$,re.lK,T.gZ,T.n_.forFeature({defaultPageSize:30}),Le.JC,de.f,Me.A,ze.YV,w.S6,c.I8,le.L,ke.XD,he.D$,Z.yU,R.Gz,c.I8,he.bY,Ue.N,Je.sJ.forChild({})]]}),n})()},46700:(ee,k,l)=>{var y={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function u(C){var j=S(C);return l(j)}function S(C){if(!l.o(y,C)){var j=new Error("Cannot find module '"+C+"'");throw j.code="MODULE_NOT_FOUND",j}return y[C]}u.keys=function(){return Object.keys(y)},u.resolve=S,ee.exports=u,u.id=46700}}]);
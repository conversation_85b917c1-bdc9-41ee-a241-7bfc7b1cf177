(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_initialize-officestaff-processing_initialize-officestaff-processing_module_ts-9ff66b"],{23132:(P,v,i)=>{"use strict";i.r(v),i.d(v,{InitializeOfficeStaffProcessingsModule:()=>oe});var u=i(69808),o=i(65620);const f=(0,o.PH)("[InitializeOfficeStaffProcessing List | Store Service] fetch officeStaffs",(0,o.Ky)()),h=(0,o.PH)("[InitializeOfficeStaffProcessings | Effect ] fetch officeStaffs success",(0,o.Ky)()),I=(0,o.PH)("[InitializeOfficeStaffProcessing List | Store Service] create officeStaff request",(0,o.Ky)()),x=(0,o.PH)("[InitializeOfficeStaffProcessing Create Request | Store Service] create new request"),L="medical",G=(0,o.Lq)({maidsList:{content:[],number:0,size:20,totalElements:0,totalPages:0}},(0,o.on)(h,(s,{payload:c})=>{var e;return Object.assign(Object.assign({},s),{maidsList:{content:null!==(e=c.content)&&void 0!==e?e:[],number:c.number,size:c.size,totalElements:c.totalElements,totalPages:c.totalPages}})}));var m=i(26991),S=i(63900),p=i(54004),J=i(11365),t=i(5e3);const C=(0,o.ZF)(L),A=((0,o.P1)(C,s=>s),(0,o.P1)(C,s=>s.maidsList));var g=i(40520),H=i(8188),F=i(43604);let M=(()=>{class s{constructor(e,a){this._api=e,this._http=a}fetchOfficeStaffsList(e){var a,r,n;const l=new g.LE({fromObject:{page:null!==(a=e.page)&&void 0!==a?a:0,size:null!==(r=e.size)&&void 0!==r?r:20,search:null!==(n=e.search)&&void 0!==n?n:""}});return this._http.get([this._api,F.b.officeStaffsList].join("/"),{params:l}).pipe()}createRequest(e){const a={officeStaff:{id:e}};return this._http.post([this._api,F.b.createRequest].join("/"),a).pipe()}markDone(e){const r=new g.LE({fromObject:{id:e}});return this._http.get([this._api,""].join("/"),{params:r}).pipe()}addPhoneNumber(e){const a={id:e.id,phoneNumber:e.phone};new g.LE({fromObject:a});let n=[this._api,""].join("/");return this._http.post(n,a).pipe()}}return s.\u0275fac=function(e){return new(e||s)(t.LFG(H.JV),t.LFG(g.eN))},s.\u0275prov=t.Yz7({token:s,factory:s.\u0275fac,providedIn:"root"}),s})(),z=(()=>{class s{constructor(e,a){this.store=e,this._initVisaProcess=a,this.selectStaffList=this.store.select(A).pipe((0,p.U)(r=>r))}fetchOfficeStaffsList(e){this.store.dispatch(f({search:e}))}createRequest(e){this.store.dispatch(I({id:e}))}}return s.\u0275fac=function(e){return new(e||s)(t.LFG(o.yh),t.LFG(M))},s.\u0275prov=t.Yz7({token:s,factory:s.\u0275fac}),s})();var Y=i(21799);let Z=(()=>{class s{constructor(e,a,r,n){this._actions=e,this._store=a,this._service=r,this._notificationService=n,this.fetchOfficeStaffsList=(0,m.GW)(()=>this._actions.pipe((0,m.l4)(f),(0,S.w)(l=>this._service.fetchOfficeStaffsList(l.search)),(0,p.U)(l=>h({payload:l})))),this.createRequest=(0,m.GW)(()=>this._actions.pipe((0,m.l4)(I),(0,S.w)(l=>this._service.createRequest(l.id)),(0,p.U)(l=>x()))),this.createRequestSuccess=(0,m.GW)(()=>this._actions.pipe((0,m.l4)(x),(0,J.M)(this._store.selectStaffList),(0,S.w)(([l,re])=>this._notificationService.notifySuccess("Request Created Successfully").pipe((0,p.U)(()=>f({search:Object.assign(Object.assign({},re),{page:0})}))))))}}return s.\u0275fac=function(e){return new(e||s)(t.LFG(m.eX),t.LFG(z),t.LFG(M),t.LFG(Y.zg))},s.\u0275prov=t.Yz7({token:s,factory:s.\u0275fac}),s})(),B=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({imports:[[u.ez,o.Aw.forFeature(L,G),m.sQ.forFeature([Z])]]}),s})();var D=i(1402),b=i(88476),U=i(50727),Q=i(18505),V=i(95698),d=i(93075),O=i(82599),y=i(34378),R=i(43687),k=i(45834),N=i(65868),T=i(62764);function w(s,c){1&s&&(t.TgZ(0,"span"),t._uU(1,"No Result"),t.qZA())}const K=function(){return[10,20,30,40,50]};function W(s,c){if(1&s){const e=t.EpF();t.ynx(0),t.TgZ(1,"div",1)(2,"cc-accordion")(3,"div",2,3)(5,"form",4)(6,"div",5)(7,"div",6)(8,"div",5)(9,"div",7)(10,"cc-input",8)(11,"cc-icon",9),t._uU(12,"search"),t.qZA()()(),t.TgZ(13,"div")(14,"button",10),t.NdJ("click",function(){const n=t.CHM(e).ngIf;return t.oxw().filter(0,n.size)}),t._uU(15," Search "),t.qZA()()()()()(),t.TgZ(16,"div",11)(17,"div",12)(18,"cc-datagrid",13),t.NdJ("page",function(r){return t.CHM(e),t.oxw().fetchStaffsListNextPage(r)}),t.qZA(),t.YNc(19,w,2,0,"ng-template",null,14,t.W1O),t.qZA()()()()(),t.BQk()}if(2&s){const e=c.ngIf,a=t.MAs(4),r=t.MAs(20),n=t.oxw();t.xp6(3),t.Q6J("icon",a.expanded?"expand_less":"expand_more")("expanded",!0),t.xp6(2),t.Q6J("formGroup",n.form),t.xp6(13),t.Q6J("noResultTemplate",r)("data",e.content)("columns",n.columns)("length",e.totalElements)("pageOnFront",!1)("pageIndex",e.number)("pageSize",e.size)("pageSizeOptions",t.DdM(18,K))("showColumnMenuButton",!0)("stickyHeader",!0)("columnMovable",!0)("columnHideable",!0)("showColumnMenuButton",!0)("showColumnMenuHeader",!1)("columnMenuButtonIcon","settings")}}let X=(()=>{class s{constructor(e,a,r){this._store=e,this.formBuilder=a,this._dialog=r,this.form=this.formBuilder.group({search:""}),this.previousData=[],this.subscription=new U.w0,this.createRequestCalled=!1,this.columns=[{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"raised",text:"Create Request",color:"primary",mode:"single",disabled:!1,callback:n=>this.createRequest(n.id)}]}},{field:"name",header:"Name"},{field:"nationality",header:"Nationality",formatter:n=>n.nationality?n.nationality.label:""},{field:"manager",header:"Manager",formatter:n=>n.manager?n.manager.label:""},{field:"phoneNumber",header:"Phone Number"},{field:"employeeType",header:"Employee Type",formatter:n=>n.employeeType?n.employeeType.label:""}]}ngOnInit(){this.vm$=this._store.selectStaffList.pipe((0,Q.b)(e=>{this.createRequestCalled&&this.dataChanged(e.content)&&(this.form.controls.search.setValue(""),this.createRequestCalled=!1),this.previousData=e.content})),this._store.fetchOfficeStaffsList({page:0,size:20,search:this.form.controls.search.value})}ngOnDestroy(){this.subscription.unsubscribe()}filter(e,a){this._store.fetchOfficeStaffsList({page:e,size:a,search:this.form.controls.search.value})}createRequest(e){this.createRequestCalled=!0,this._store.createRequest(e),this.subscription.add(this._store.selectStaffList.pipe((0,V.q)(1)).subscribe(()=>{this._store.fetchOfficeStaffsList({page:0})}))}fetchStaffsListNextPage(e){this._store.fetchOfficeStaffsList({page:e.pageIndex,size:e.pageSize,search:this.form.controls.search.value})}dataChanged(e){return e.length!==this.previousData.length||e.some((a,r)=>a.id!==this.previousData[r].id)}}return s.\u0275fac=function(e){return new(e||s)(t.Y36(z),t.Y36(d.qu),t.Y36(O.uY))},s.\u0275cmp=t.Xpm({type:s,selectors:[["app-initialize-officestaff-processing"]],decls:2,vars:3,consts:[[4,"ngIf"],[1,"fluid-container","m-4"],["cc-panel","","title","",3,"icon","expanded"],["panel","ccPanel"],[3,"formGroup"],[1,"row"],[1,"col-md-10"],[1,"form-group","col-md-4"],["formControlName","search","label","Search","placeholder","Search"],[1,"input-icon-suffix"],["cc-raised-button","",1,"btn","btn-danger",2,"margin-top","8px",3,"click"],[1,"card"],[1,"card-body"],[3,"noResultTemplate","data","columns","length","pageOnFront","pageIndex","pageSize","pageSizeOptions","showColumnMenuButton","stickyHeader","columnMovable","columnHideable","showColumnMenuHeader","columnMenuButtonIcon","page"],["noResultTpl",""]],template:function(e,a){1&e&&(t.YNc(0,W,21,19,"ng-container",0),t.ALo(1,"async")),2&e&&t.Q6J("ngIf",t.lcZ(1,1,a.vm$))},directives:[u.O5,y.I,y.Vx,d._Y,d.JL,d.sg,R.G,d.JJ,d.u,k.Q9,N.uu,T.Ge],pipes:[u.Ov],styles:[""],changeDetection:0}),s})();var $=i(57902),q=i(4882),_=i(26523),ee=i(69202),te=i(92431),se=i(58015),E=i(11523),ie=i(54657),ae=i(467),ne=i(63372);const ce=[{path:"",component:X,data:{label:"Initialize Officestaffs Visa Processing",pageCode:"InitializeOfficeStaffVisaProcessing"}}];let oe=(()=>{class s{}return s.\u0275fac=function(e){return new(e||s)},s.\u0275mod=t.oAB({type:s}),s.\u0275inj=t.cJS({providers:[z],imports:[[u.ez,B,D.Bz.forChild(ce),ee.Ev,d.UX,d.u5,b.er,q.$,_.lK,b.gZ,b.n_.forFeature({defaultPageSize:30}),ie.JC,R.f,$.A,se.YV,N.S6,O.I8,k.L,te.XD,E.D$,y.yU,T.Gz,O.I8,E.bY,ne.N,ae.sJ.forChild({})]]}),s})()},46700:(P,v,i)=>{var u={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function o(f){var h=j(f);return i(h)}function j(f){if(!i.o(u,f)){var h=new Error("Cannot find module '"+f+"'");throw h.code="MODULE_NOT_FOUND",h}return u[f]}o.keys=function(){return Object.keys(u)},o.resolve=j,P.exports=o,o.id=46700}}]);
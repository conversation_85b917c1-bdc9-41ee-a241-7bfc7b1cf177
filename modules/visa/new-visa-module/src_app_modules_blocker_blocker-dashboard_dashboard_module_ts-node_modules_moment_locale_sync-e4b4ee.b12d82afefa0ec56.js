(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_blocker_blocker-dashboard_dashboard_module_ts-node_modules_moment_locale_sync-e4b4ee"],{3419:(F,w,l)=>{"use strict";l.r(w),l.d(w,{DashboardModule:()=>Re});var p=l(69808),M=l(1402),d=l(93075),r=l(82599),_=l(62764),N=l(43687),D=l(26523),I=l(42002),j=l(13859),A=l(65868),O=l(4882),q=l(45834),W=l(92431),e=l(5e3),h=l(97582),E=l(18505),Y=l(63900),P=l(60515),B=l(48966),Z=l(88476),u=l(43604),K=l(40520);let y=(()=>{class n{constructor(t){this._http=t,this.getAllBlockersByResolveStatus=i=>this._http.get(u.b.getBlockersByResolveStatus,{params:{resolveStatus:i}}),this.getBlockersForBroadcastMsg=()=>this._http.get(u.b.getBlockersForBroadcastMessage),this.changeBroadcastStatus=(i,a)=>this._http.post(`${u.b.changeBroadcastStatus}/${i}`,{},{params:{broadcastStatus:a}}),this.resolveBlocker=i=>this._http.post(`${u.b.resolveBlocker}/${i}`,{}),this.createBlocker=i=>this._http.post(u.b.createBlocker,i),this.updateBlocker=i=>this._http.post(u.b.updateBlocker,i),this.getAllBlockerConfigurations=()=>this._http.get(u.b.getAllBlockerConfigurations),this.getBroadcastMessageConfig=i=>this._http.get(`${u.b.getBroadCastMessageConfig}/${i}`),this.createBroadcastMessageConfig=i=>this._http.post(u.b.createBroadCastMessageConfig,i),this.updateBroadcastMessageConfig=i=>this._http.post(u.b.updateBroadCastMessageConfig,i),this.getClientMessageContent=()=>this._http.get(u.b.getClientMessageContent),this.getMaidMessageContent=()=>this._http.get(u.b.getMaidMessageContent),this.getTmpMsgByNameAndChannelType=(i="Email")=>this._http.get(u.b.getTmpMsgByNameAndChannelType,{params:{channelType:i}})}}return n.\u0275fac=function(t){return new(t||n)(e.LFG(K.eN))},n.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var R=l(54004),ee=l(8188);function te(n){return n.toLowerCase().replace(/_/g," ").replace(/\b\w/g,o=>o.toUpperCase())}let $=(()=>{class n{constructor(t,i){this._apiService=t,this._picklist=i,this.blockerConfigurationOptions=this._apiService.getAllBlockerConfigurations().pipe((0,R.U)(a=>a.filter(s=>s.active).map(s=>({id:s.id,text:s.name,data:{config:s}})))),this.nationalitiesPageFetcher=a=>this._picklist.getPicklist({code:"NATIONALITIES",page:a.page,pageSize:a.size,search:a.searchString||""}).pipe((0,R.U)(s=>s.map(c=>({id:c.id,text:c.label})))),this.housemaidTypesOptions=[{id:"Freedom Operator",text:"Freedom Operator"},{id:"MaidVisa",text:"Maid visa"},{id:"Normal",text:"Normal"},{id:"Walk-in",text:"Walkin"},{id:"Maids.at",text:"Maids at"}],this.housemaidStatusOptions=["TRACKED","REJECTED","PASSED_EXIT","UNREACHABLE","IN_EXIT","LANDED_IN_DUBAI","UNREACHABLE_AFTER_EXIT","AVAILABLE","WITH_CLIENT","RESERVED_FOR_PROSPECT","RESERVED_FOR_REPLACEMENT","SICK_WITHOUT_CLIENT","ON_VACATION","EMPLOYEMENT_TERMINATED","NO_SHOW","VISA_UNSUCCESSFUL"].map(a=>({id:a,text:te(a)})),this.addBlockerMsgContentParams=["@arrivalEstimatedDate@","@blocker_steps@","@blocker_name@"].map(a=>({id:a,text:a})),this.broadCastMsgContentParams=["@maid_name@","@client_name@","@issue_visa_eta@","@resolve_blocker_eta@","@x_days@","@designation@"].map(a=>({id:a,text:a}))}parameterTypesOptions(){return{for:{addBlockerMsgContentParams:this.addBlockerMsgContentParams,broadCastMsgContentParams:this.broadCastMsgContentParams}}}}return n.\u0275fac=function(t){return new(t||n)(e.LFG(y),e.LFG(ee.Ab))},n.\u0275prov=e.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var S,H=l(61135);class f{constructor(){S.set(this,new H.X(0)),this.refreshData$=(0,h.Q_)(this,S,"f").asObservable()}refreshData(){(0,h.Q_)(this,S,"f").next(1)}}S=new WeakMap,f.\u0275fac=function(o){return new(o||f)},f.\u0275prov=e.Yz7({token:f,factory:f.\u0275fac,providedIn:"root"});var J=l(21799),L=l(85185),V=l(11523),G=l(79136);function oe(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"cc-checkbox",34),e.NdJ("ngModelChange",function(a){return e.CHM(t).$implicit.checked=a})("change",function(a){const c=e.CHM(t).$implicit;return e.oxw(2).onSelectBlocker(a,c.id)}),e.qZA()}if(2&n){const t=o.$implicit;e.Q6J("checked",t.checked||!1)("ngModel",t.checked)}}const ne=function(n){return{select:n}};function ie(n,o){if(1&n&&(e.TgZ(0,"div",29)(1,"p",30),e._uU(2,"Select Blockers"),e.qZA(),e._UZ(3,"cc-datagrid",31),e.YNc(4,oe,1,2,"ng-template",32,33,e.W1O),e.qZA()),2&n){const t=o.ngIf,i=e.MAs(5),a=e.oxw();e.xp6(3),e.Q6J("columns",a.gridCols)("data",t)("showPaginator",!1)("loading",!1)("cellTemplate",e.VKq(6,ne,i)),e.xp6(1),e.Q6J("ccGridCell",t)}}function ae(n,o){1&n&&e._UZ(0,"div",40)}function se(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"cc-select",41),e.NdJ("valueChange",function(a){return e.CHM(t),e.oxw(2).injectParameterIntoContent(a,"clientMessageContent")}),e.qZA()}if(2&n){const t=e.oxw(2);e.Q6J("data",t.parameterTypesOptions)}}function ce(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",42)(1,"button",43),e.NdJ("click",function(){return e.CHM(t),e.oxw(2).onCancelContentEdit("clientMsgContent")}),e._uU(2," Cancel "),e.qZA(),e.TgZ(3,"button",44),e.NdJ("click",function(){e.CHM(t);const a=e.oxw(2);return a.onSaveContentEdit("clientMsgContent",a.form.value.clientMessageContent)}),e._uU(4," Save "),e.qZA()()}}function le(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",35),e.YNc(1,ae,1,0,"div",36),e.YNc(2,se,1,1,"cc-select",37),e.TgZ(3,"cc-textarea",38),e.NdJ("click",function(a){return e.CHM(t),e.oxw().setCurrentCursorIndex(a,"clientMessageContent")})("keyup.enter",function(a){return e.CHM(t),e.oxw().setCurrentCursorIndex(a,"clientMessageContent")}),e.qZA(),e.YNc(4,ce,5,0,"div",39),e.qZA()}if(2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",!t.editClientMsgContent),e.xp6(1),e.Q6J("ngIf",t.editClientMsgContent),e.xp6(1),e.Q6J("ccAutosizeMinRows",2)("ccAutosizeMaxRows",60),e.xp6(1),e.Q6J("ngIf",t.editClientMsgContent)}}function re(n,o){1&n&&e._UZ(0,"div",40)}function de(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"cc-select",48),e.NdJ("valueChange",function(a){return e.CHM(t),e.oxw(2).injectParameterIntoContent(a,"housemaidMessageContent")}),e.qZA()}if(2&n){const t=e.oxw(2);e.Q6J("data",t.parameterTypesOptions)}}function me(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",42)(1,"button",43),e.NdJ("click",function(){return e.CHM(t),e.oxw(2).onCancelContentEdit("maidMsgContent")}),e._uU(2," Cancel "),e.qZA(),e.TgZ(3,"button",44),e.NdJ("click",function(){e.CHM(t);const a=e.oxw(2);return a.onSaveContentEdit("maidMsgContent",a.form.value.housemaidMessageContent)}),e._uU(4," Save "),e.qZA()()}}function pe(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",45),e.YNc(1,re,1,0,"div",36),e.YNc(2,de,1,1,"cc-select",46),e.TgZ(3,"cc-textarea",47),e.NdJ("click",function(a){return e.CHM(t),e.oxw().setCurrentCursorIndex(a,"housemaidMessageContent")})("keyup.enter",function(a){return e.CHM(t),e.oxw().setCurrentCursorIndex(a,"housemaidMessageContent")}),e.qZA(),e.YNc(4,me,5,0,"div",39),e.qZA()}if(2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",!t.editMaidMsgContent),e.xp6(1),e.Q6J("ngIf",t.editMaidMsgContent),e.xp6(1),e.Q6J("ccAutosizeMinRows",2)("ccAutosizeMaxRows",60),e.xp6(1),e.Q6J("ngIf",t.editMaidMsgContent)}}const ue=function(){return[]};function _e(n,o){if(1&n&&(e.TgZ(0,"div",49),e._UZ(1,"cc-select",50)(2,"cc-select",51)(3,"cc-select",52)(4,"cc-textarea",53),e.TgZ(5,"cc-checkbox",54),e._uU(6,"Exclude from RPA"),e.qZA()()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("lazyPageFetcher",t.nationalitiesPageFetcher)("modelOptions",t.extraNationalitiesOpts||e.DdM(6,ue)),e.xp6(1),e.Q6J("data",t.housemaidStatusOptions),e.xp6(1),e.Q6J("data",t.housemaidTypeOptions),e.xp6(1),e.Q6J("ccAutosizeMinRows",2)("ccAutosizeMaxRows",60)}}const X=function(n){return{visibility:n}};let C=class{constructor(o,t,i,a,s,c,m,v){this._apiService=o,this._dataService=t,this._store=i,this._notification=a,this._dialogRef=s,this._cdr=c,this._fb=m,this.data=v,this.gridCols=[{field:"select",header:"Select"},{field:"visaBlockerConfiguration.label",header:"Blocker Name",width:"80%"}],this.housemaidTypeOptions=this._dataService.housemaidTypesOptions,this.housemaidStatusOptions=this._dataService.housemaidStatusOptions,this.parameterTypesOptions=this._dataService.parameterTypesOptions().for.broadCastMsgContentParams,this.nationalitiesPageFetcher=this._dataService.nationalitiesPageFetcher,this.targetedBlockers=[],this.editClientMsgContent=!1,this.editMaidMsgContent=!1,this.updateClientMessage=!1,this.updateHousemaidMessage=!1,this.dialogTitle="Send Broadcast Message",this.dialogAction="Send Broadcast",this.cursorIndex={},this.defaultMsgsContent={client:"",maid:""},this.form=this._fb.group({informClient:!1,informHousemaid:!1,boadcastThreshold:"",hasExcludedCriteria:!1,excludedNationalityIds:"",excludedMaidStatusIds:"",excludedMaidTypeIds:"",excludedwithStepNote:"",clientMessageContent:"",housemaidMessageContent:"",rpaExecluded:!1,clientParamType:"",maidParamType:""})}ngOnInit(){this.data$=this._apiService.getBlockersForBroadcastMsg(),this.initializeAddPage(),this.informClient$=this.form.get("informClient").valueChanges,this.informHousemaid$=this.form.get("informHousemaid").valueChanges,this.excludeClientAndMaid$=this.form.get("hasExcludedCriteria").valueChanges}onSelectBlocker(o,t){this.targetedBlockers=o.checked?[...this.targetedBlockers,{id:t}]:this.targetedBlockers.filter(i=>i.id!==t),this._cdr.detectChanges()}onEditMaidMsgContent(){this.editMaidMsgContent=!this.editMaidMsgContent}onEditClientMsgContent(){this.editClientMsgContent=!this.editClientMsgContent}onCancelContentEdit(o){"clientMsgContent"===o&&(this.editClientMsgContent=!1,this.updateClientMessage=!1,this.form.patchValue({clientMessageContent:this.defaultMsgsContent.client})),"maidMsgContent"===o&&(this.editMaidMsgContent=!1,this.updateHousemaidMessage=!1,this.form.patchValue({housemaidMessageContent:this.defaultMsgsContent.maid}))}onSaveContentEdit(o,t){"clientMsgContent"===o&&(this.editClientMsgContent=!1,this.updateClientMessage=this.defaultMsgsContent.client!==t),"maidMsgContent"===o&&(this.editMaidMsgContent=!1,this.updateHousemaidMessage=this.defaultMsgsContent.maid!==t)}onSendBroadcast(){this.targetedBlockers.length?this.createBroadcast():this._notification.notifyError("Please select at least one blocker.")}setCurrentCursorIndex(o,t){var i;this.cursorIndex[t]=null!==(i=o.target.selectionStart)&&void 0!==i?i:0}injectParameterIntoContent(o,t){var i,a,s;if(o&&t){const c=null===(i=this.form.get(t))||void 0===i?void 0:i.value,m=this.cursorIndex[t]||(null==c?void 0:c.length),v=/\s|\n/.test(c[m-1])?"":" ",Q=" "===c[m]?"":" ",z=`${c.slice(0,m)}${v}${o}${Q}${c.slice(m)}`;t.startsWith("client")&&(this.updateClientMessage=!0,null===(a=this.form.get("clientParamType"))||void 0===a||a.reset("")),t.startsWith("housemaid")&&(this.updateHousemaidMessage=!0,null===(s=this.form.get("maidParamType"))||void 0===s||s.reset("")),this.form.patchValue({[`${t}`]:z})}}createBroadcast(){const o=this.getPayloadData();this._apiService.createBroadcastMessageConfig(o).pipe((0,E.b)(()=>{this._notification.notifySuccess("Added Successfully"),this._store.refreshData(),this._dialogRef.close()})).subscribe()}initializeAddPage(){this.getClientMessageContent(),this.getMaidMessageContent()}getMaidMessageContent(){this.form.get("informHousemaid").valueChanges.pipe((0,Y.w)(o=>o?this._apiService.getMaidMessageContent():P.E)).subscribe(o=>{this.defaultMsgsContent.maid=o,this.form.patchValue({housemaidMessageContent:o})})}getClientMessageContent(){var o;null===(o=this.form.get("informClient"))||void 0===o||o.valueChanges.pipe((0,Y.w)(t=>t?this._apiService.getClientMessageContent():P.E)).subscribe(t=>{this.defaultMsgsContent.client=t,this.form.patchValue({clientMessageContent:t})})}getPayloadData(){const{informClient:o,informHousemaid:t,boadcastThreshold:i,excludedNationalityIds:a,excludedMaidStatusIds:s,excludedMaidTypeIds:c,excludedwithStepNote:m,clientMessageContent:v,housemaidMessageContent:Q,rpaExecluded:z}=this.form.value;return{targetedBlockers:this.targetedBlockers,excludedNationalityIds:""!==a?null==a?void 0:a.map($e=>({id:$e})):"",updateClientMessage:this.updateClientMessage,updateHousemaidMessage:this.updateHousemaidMessage,informClient:o,informHousemaid:t,boadcastThreshold:i,excludedMaidStatusIds:s,excludedMaidTypeIds:c,excludedwithStepNote:m,clientMessageContent:v,housemaidMessageContent:Q,rpaExecluded:z}}};C.\u0275fac=function(o){return new(o||C)(e.Y36(y),e.Y36($),e.Y36(f),e.Y36(J.zg),e.Y36(B.so),e.Y36(e.sBO),e.Y36(d.qu),e.Y36(B.WI))},C.\u0275cmp=e.Xpm({type:C,selectors:[["ng-component"]],decls:47,vars:28,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["role","button","cc-dialog-close-button","","cc-dialog-close",""],["class","blockers",4,"ngIf"],[3,"formGroup"],[1,"d-flex","flex-row","align-items-center","gap-2","my-5","pr-5"],[1,"col-3","font-weight-medium","px-0"],[1,"d-flex","flex-grow-1","flex-row","col-9","align-items-center","gap-2"],["formControlName","boadcastThreshold","label","Enter Number of Days...",1,"flex-grow-1",3,"required","symbol"],[1,"d-inline-block","font-weight-bold","text-danger",2,"letter-spacing","0.2px"],[1,"d-flex","flex-column","flex-md-row","justify-content-between","my-5"],[1,"inform-client","col-md-6","pl-md-0","pr-md-4","px-0"],["id","title",1,"d-flex","justify-content-between","col-12","px-0"],["formControlName","informClient",1,"col-6","px-0"],[1,"d-block","text-danger","col-6","text-right","px-0",2,"cursor","pointer",3,"ngStyle","click"],["id","content","style","background-color: #f2f2f2da; position: relative","class","p-3 rounded mb-md-0 mb-4",4,"ngIf"],[1,"inform-maid","col-md-6","pr-md-0","pl-md-4","px-0"],["id","title",1,"d-flex","justify-content-center","col-12","px-0"],["formControlName","informHousemaid",1,"col-6","px-0"],[1,"d-block","col-6","text-danger","text-right","px-0",2,"cursor","pointer",3,"ngStyle","click"],["id","content","class","p-3 rounded","style","background-color: #f2f2f2da; position: relative",4,"ngIf"],["id","exclude",1,"col-12","my-4","rounded","py-3","px-4",2,"background-color","#f2f2f2da"],[1,"title","d-flex","justify-content-between"],[1,"font-weight-bold"],["formControlName","hasExcludedCriteria"],["id","content","class","d-flex flex-column flex-md-row flex-md-wrap mt-3",4,"ngIf"],["cc-flat-button","","cc-dialog-close","",1,"px-4"],["cc-raised-button","","color","accent",1,"px-4",3,"disabled","click"],[1,"blockers"],[1,"font-weight-medium"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["selectTmp",""],[3,"checked","ngModel","ngModelChange","change"],["id","content",1,"p-3","rounded","mb-md-0","mb-4",2,"background-color","#f2f2f2da","position","relative"],["style","\n                position: absolute;\n                inset: 0;\n                z-index: 10;\n                cursor: not-allowed;\n              ",4,"ngIf"],["label","Add Parameter","formControlName","clientParamType",3,"data","valueChange",4,"ngIf"],["label","Message Content","formControlName","clientMessageContent",3,"ccAutosizeMinRows","ccAutosizeMaxRows","click","keyup.enter"],["class","actions d-flex justify-content-end gap-2 mt-2",4,"ngIf"],[2,"position","absolute","inset","0","z-index","10","cursor","not-allowed"],["label","Add Parameter","formControlName","clientParamType",3,"data","valueChange"],[1,"actions","d-flex","justify-content-end","gap-2","mt-2"],["cc-flat-button","",3,"click"],["cc-flat-button","","color","accent",3,"click"],["id","content",1,"p-3","rounded",2,"background-color","#f2f2f2da","position","relative"],["label","Add Parameter","formControlName","maidParamType",3,"data","valueChange",4,"ngIf"],["label","Message Content","formControlName","housemaidMessageContent",3,"ccAutosizeMinRows","ccAutosizeMaxRows","click","keyup.enter"],["label","Add Parameter","formControlName","maidParamType",3,"data","valueChange"],["id","content",1,"d-flex","flex-column","flex-md-row","flex-md-wrap","mt-3"],["label","Nationalities","formControlName","excludedNationalityIds","multiple","true",1,"col-md-6","pl-md-0","pr-md-4","px-0",3,"lazyPageFetcher","modelOptions"],["label","Status","formControlName","excludedMaidStatusIds","multiple","true",1,"col-md-6","pr-md-0","pl-md-4","px-0",3,"data"],["label","Type","formControlName","excludedMaidTypeIds","multiple","true",1,"col-md-6","pl-md-0","pr-md-4","px-0",3,"data"],["formControlName","excludedwithStepNote","label","Type Note Here",1,"col-md-6","pr-md-0","pl-md-4","px-0",3,"ccAutosizeMinRows","ccAutosizeMaxRows"],["formControlName","rpaExecluded"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"cc-dialog-content"),e.YNc(6,ie,6,8,"div",4),e.ALo(7,"async"),e.TgZ(8,"form",5)(9,"div",6)(10,"cc-label",7),e._uU(11,"Broadcast Threshold"),e.qZA(),e.TgZ(12,"div",8),e._UZ(13,"cc-amount-input",9),e.TgZ(14,"span",10),e._uU(15,"Days"),e.qZA()()(),e.TgZ(16,"div",11)(17,"div",12)(18,"div",13)(19,"cc-checkbox",14),e._uU(20,"Inform Client"),e.qZA(),e.TgZ(21,"a",15),e.NdJ("click",function(){return t.onEditClientMsgContent()}),e.ALo(22,"async"),e._uU(23,"Edit Message Content"),e.qZA()(),e.YNc(24,le,5,5,"div",16),e.ALo(25,"async"),e.qZA(),e.TgZ(26,"div",17)(27,"div",18)(28,"cc-checkbox",19),e._uU(29,"Inform Maid"),e.qZA(),e.TgZ(30,"a",20),e.NdJ("click",function(){return t.onEditMaidMsgContent()}),e.ALo(31,"async"),e._uU(32,"Edit Message Content"),e.qZA()(),e.YNc(33,pe,5,5,"div",21),e.ALo(34,"async"),e.qZA()(),e.TgZ(35,"div",22)(36,"div",23)(37,"span",24),e._uU(38,"Exclude"),e.qZA(),e._UZ(39,"cc-slide-toggle",25),e.qZA(),e.YNc(40,_e,7,7,"div",26),e.ALo(41,"async"),e.qZA()()(),e.TgZ(42,"cc-dialog-actions")(43,"button",27),e._uU(44,"Cancel"),e.qZA(),e.TgZ(45,"button",28),e.NdJ("click",function(){return t.onSendBroadcast()}),e._uU(46),e.qZA()()()),2&o&&(e.xp6(3),e.Oqu(t.dialogTitle),e.xp6(3),e.Q6J("ngIf",e.lcZ(7,12,t.data$)),e.xp6(2),e.Q6J("formGroup",t.form),e.xp6(5),e.Q6J("required",!0)("symbol",""),e.xp6(8),e.Q6J("ngStyle",e.VKq(24,X,e.lcZ(22,14,t.informClient$)?"visible":"hidden")),e.xp6(3),e.Q6J("ngIf",e.lcZ(25,16,t.informClient$)),e.xp6(6),e.Q6J("ngStyle",e.VKq(26,X,e.lcZ(31,18,t.informHousemaid$)?"visible":"hidden")),e.xp6(3),e.Q6J("ngIf",e.lcZ(34,20,t.informHousemaid$)),e.xp6(7),e.Q6J("ngIf",e.lcZ(41,22,t.excludeClientAndMaid$)),e.xp6(5),e.Q6J("disabled",!t.form.valid||t.editClientMsgContent||t.editMaidMsgContent),e.xp6(1),e.hij(" ",t.dialogAction," "))},directives:[r.iK,r.Cj,r.Zb,r.fX,r.zn,r.kL,p.O5,_.Ge,_.VC,O.E,d.JJ,d.On,d._Y,d.JL,d.sg,L.k_,V.Fi,d.u,d.Q7,p.PC,D.jB,I.Qf,A.uu,G.I,r.Zu],pipes:[p.Ov],encapsulation:2,changeDetection:0}),C=(0,h.gn)([Z.kG],C);var g=l(15439),U=l(28172);function fe(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",3)(1,"p",4)(2,"span"),e._uU(3),e.qZA(),e.TgZ(4,"a",5),e.NdJ("click",function(){const s=e.CHM(t).index;return e.oxw(2).onDelete(s)}),e.O4$(),e.TgZ(5,"svg",6),e._UZ(6,"path",7),e.qZA()()()()}if(2&n){const t=o.$implicit;e.xp6(3),e.Oqu(t)}}function he(n,o){if(1&n&&(e.TgZ(0,"div",1),e.YNc(1,fe,7,1,"div",2),e.qZA()),2&n){const t=e.oxw();e.xp6(1),e.Q6J("ngForOf",t.items)}}let Ce=(()=>{class n{constructor(t){this.cdr=t,this.valueChanges=new e.vpe}ngOnInit(){}onDelete(t){this.items=[...this.items].filter((i,a)=>a!==t),this.cdr.detectChanges(),this.valueChanges.emit(this.items)}}return n.\u0275fac=function(t){return new(t||n)(e.Y36(e.sBO))},n.\u0275cmp=e.Xpm({type:n,selectors:[["chips"]],inputs:{items:"items"},outputs:{valueChanges:"valueChanges"},decls:1,vars:1,consts:[["class","d-flex flex-wrap  gap-1",4,"ngIf"],[1,"d-flex","flex-wrap","gap-1"],["class","d-block px-2 mx-1 my-1 rounded-pill","style","box-sizing: border-box; height: 1.8em; background: #80808092;'",4,"ngFor","ngForOf"],[1,"d-block","px-2","mx-1","my-1","rounded-pill",2,"box-sizing","border-box","height","1.8em","background","#80808092"],[1,"d-flex","gap-1","align-items-center",2,"line-height","1.6"],[3,"click"],["width","12px","height","12px","fill","red","viewBox","0 0 512 512","xmlns","http://www.w3.org/2000/svg"],["d","M289.94,256l95-95A24,24,0,0,0,351,127l-95,95-95-95A24,24,0,0,0,127,161l95,95-95,95A24,24,0,1,0,161,385l95-95,95,95A24,24,0,0,0,385,351Z"]],template:function(t,i){1&t&&e.YNc(0,he,2,1,"div",0),2&t&&e.Q6J("ngIf",i.items.length>0)},directives:[p.O5,p.sg],encapsulation:2,changeDetection:0}),n})();function ve(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"button",17),e.NdJ("click",function(){return e.CHM(t),e.oxw().onEditEmailTemplate()}),e._uU(1," Edit Email Template "),e.qZA()}}function xe(n,o){1&n&&e._UZ(0,"div",22)}function be(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"cc-select",32),e.NdJ("valueChange",function(a){return e.CHM(t),e.oxw(3).injectParameterIntoContent(a)}),e.qZA()}if(2&n){const t=e.oxw(3);e.Q6J("data",t.parameterTypesOptions)}}function ke(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",23)(1,"div",24)(2,"chips",25),e.NdJ("valueChanges",function(a){return e.CHM(t),e.oxw(2).onRecipientsChanges(a)}),e.qZA(),e.TgZ(3,"cc-input",26),e.NdJ("keydown",function(a){return e.CHM(t),e.oxw(2).onAddRecipients(a)}),e.qZA()(),e.TgZ(4,"div",27),e._UZ(5,"cc-textarea",28),e.qZA(),e.TgZ(6,"div",29),e.YNc(7,be,1,1,"cc-select",30),e.TgZ(8,"cc-textarea",31),e.NdJ("click",function(a){return e.CHM(t),e.oxw(2).setCurrentCursorIndex(a)})("keyup.enter",function(a){return e.CHM(t),e.oxw(2).setCurrentCursorIndex(a)}),e.qZA()()()}if(2&n){const t=e.oxw(2);e.xp6(2),e.Q6J("items",t.recipients),e.xp6(3),e.Q6J("required",!0),e.xp6(2),e.Q6J("ngIf",t.editEmailTemplate),e.xp6(1),e.Q6J("ccAutosizeMinRows",10)("ccAutosizeMaxRows",60)("required",!0)}}function Te(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",33)(1,"button",34),e.NdJ("click",function(){return e.CHM(t),e.oxw(2).onCancelEmailTemplateEdit()}),e._uU(2," Cancel "),e.qZA(),e.TgZ(3,"button",17),e.NdJ("click",function(){e.CHM(t);const a=e.oxw(2);return a.onSaveEmailTemplate(a.form.value.emailContent)}),e._uU(4," Save "),e.qZA()()}}function Me(n,o){if(1&n&&(e.TgZ(0,"div",18),e.YNc(1,xe,1,0,"div",19),e.YNc(2,ke,9,6,"div",20),e.YNc(3,Te,5,0,"div",21),e.qZA()),2&n){const t=o.ngIf,i=e.oxw();e.xp6(1),e.Q6J("ngIf",!i.editEmailTemplate),e.xp6(1),e.Q6J("ngIf",t),e.xp6(1),e.Q6J("ngIf",i.editEmailTemplate&&t)}}let x=class{constructor(o,t,i,a,s,c,m){this._apiService=o,this._dataService=t,this._store=i,this._notification=a,this._DialogRef=s,this._cdr=c,this._fb=m,this.blockerConfigurationOptions=this._dataService.blockerConfigurationOptions,this.parameterTypesOptions=this._dataService.parameterTypesOptions().for.addBlockerMsgContentParams,this.informCompany$=new H.X(!1),this.editEmailTemplate=!1,this.updateEmailTemplate=!1,this.defaultEmailContent="",this.recipients=[],this.updateMessage=!1,this.form=this._fb.group({visaBlockerConfiguration:"",arrivalEstimatedDate:"",turnOffRPA:!1,informCompany:!1,emailTitle:"",emailContent:"",recipients:"",paramType:""})}ngOnInit(){var o;null===(o=this.form.get("informCompany"))||void 0===o||o.valueChanges.subscribe({next:t=>{this.informCompany$.next(t),this.onInformCompany(t)}})}onEditEmailTemplate(){this.editEmailTemplate=!this.editEmailTemplate,this._cdr.detectChanges()}onSelectConfig(o){var t,i,a,s;const c=null===(t=null==o?void 0:o.data)||void 0===t?void 0:t.config,m=null===(i=this.form.get("informCompany"))||void 0===i?void 0:i.value;this.selectedBlockerConfig=c,this.editEmailTemplate=!1,this.form.patchValue({arrivalEstimatedDate:g(new Date).add(+c.arrivalEstimatedDaysNum,"days").format("YYYY-MM-DD"),informCompany:c.addEmail}),m&&this.form.patchValue({emailTitle:c.emailTitle,emailContent:c.emailContent}),this.recipients=(null===(a=c.recipients)||void 0===a?void 0:a.trim())?null===(s=c.recipients)||void 0===s?void 0:s.trim().split(","):[],this._cdr.detectChanges()}onInformCompany(o){this._apiService.getTmpMsgByNameAndChannelType().subscribe(t=>{var i;this.defaultEmailContent=t,this.form.patchValue({emailContent:t,emailTitle:null===(i=this.selectedBlockerConfig)||void 0===i?void 0:i.emailTitle})}),o||(["emailTitle","emailContent"].forEach(t=>{var i,a;null===(i=this.form.get(t))||void 0===i||i.clearValidators(),null===(a=this.form.get(t))||void 0===a||a.updateValueAndValidity()}),this.editEmailTemplate&&(this.editEmailTemplate=!1,this._cdr.detectChanges()))}setCurrentCursorIndex(o){var t;this.cursorIndex=null!==(t=o.target.selectionStart)&&void 0!==t?t:0}injectParameterIntoContent(o){var t,i;if(o){const a=null===(t=this.form.get("emailContent"))||void 0===t?void 0:t.value,s=this.cursorIndex||(null==a?void 0:a.length),c=/\s|\n/.test(a[s-1])?"":" ",m=" "===a[s]?"":" ";this.form.patchValue({emailContent:`${a.slice(0,s)}${c}${o}${m}${a.slice(s)}`})}null===(i=this.form.get("paramType"))||void 0===i||i.reset("")}onSaveEmailTemplate(o){this.editEmailTemplate=!1,this.updateEmailTemplate=this.defaultEmailContent!==o,this._cdr.detectChanges()}onCancelEmailTemplateEdit(){var o;this.editEmailTemplate=!1,this.updateEmailTemplate=!1,this.form.patchValue({emailContent:this.defaultEmailContent,emailTitle:null===(o=this.selectedBlockerConfig)||void 0===o?void 0:o.emailTitle}),this._cdr.detectChanges()}onSave(){var o;const t=this.form.value,{visaBlockerConfiguration:i,arrivalEstimatedDate:a}=t,m=(0,h._T)(t,["visaBlockerConfiguration","arrivalEstimatedDate","recipients","paramType"]);if((null===(o=this.form.value)||void 0===o?void 0:o.informCompany)&&!this.recipients.length)return void this._notification.notifyError("You have to input at least one recipient");let v=Object.assign(Object.assign({},m),{arrivalEstimatedDate:g(a).format("YYYY-MM-DD 00:00:00"),visaBlockerConfiguration:{id:i.id},updateEmailTemplate:this.updateEmailTemplate,emailRecipients:this.recipients.join(",")});this.addBlocker(v)}onAddRecipients(o){const t=this.form.get("recipients"),i=function ge(n){return!!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(n.value)}(t);("Enter"===o.key||" "===o.key)&&(o.preventDefault(),t.value&&i?(this.recipients=[...this.recipients,t.value],this._cdr.detectChanges()):this._notification.notifyError("Please Enter A Valid Email Address."),t.reset())}onRecipientsChanges(o){this.recipients=o,this._cdr.detectChanges()}addBlocker(o){this._apiService.createBlocker(o).subscribe({next:()=>{this._notification.notifySuccess("Added Successfully"),this._store.refreshData(),this._DialogRef.close()}})}};x.\u0275fac=function(o){return new(o||x)(e.Y36(y),e.Y36($),e.Y36(f),e.Y36(J.zg),e.Y36(B.so),e.Y36(e.sBO),e.Y36(d.qu))},x.\u0275cmp=e.Xpm({type:x,selectors:[["ng-component"]],decls:28,vars:14,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["role","button","cc-dialog-close-button","","cc-dialog-close",""],[1,"d-flex","align-items-center","flex-column",3,"formGroup"],[1,"col-md-10"],["label","Select Blocker","formControlName","visaBlockerConfiguration",3,"data","emitFullSelectOption","required","valueChange"],["label","ETA to be Solved","formControlName","arrivalEstimatedDate",3,"required"],[1,"col-md-10","my-3"],["formControlName","turnOffRPA"],[1,"col-md-10","mt-3","mb-5"],[1,"col-12","px-0","title","d-flex","justify-content-between"],["formControlName","informCompany"],["cc-raised-button","","color","accent",3,"click",4,"ngIf"],["style","position: relative",4,"ngIf"],["cc-flat-button","","cc-dialog-close","",1,"px-4"],["cc-raised-button","","color","accent","type","submit",1,"px-4",3,"disabled","click"],["cc-raised-button","","color","accent",3,"click"],[2,"position","relative"],["style","\n              position: absolute;\n              inset: 0;\n              z-index: 10;\n              cursor: not-allowed;\n            ",4,"ngIf"],["class","content-tmp",4,"ngIf"],["class","col-12 px-0 mt-2 actions d-flex justify-content-end gap-2",4,"ngIf"],[2,"position","absolute","inset","0","z-index","10","cursor","not-allowed"],[1,"content-tmp"],[1,"mt-4","col-12","px-0"],[3,"items","valueChanges"],["label","Recipients","placeholder","Add recipients by entering an email and pressing Enter.","formControlName","recipients",3,"keydown"],[1,"col-12","px-0"],["label","Email Title","formControlName","emailTitle",3,"required"],[1,"message-content","col-12","px-0","p-3","rounded",2,"background-color","#f2f2f2da","position","relative"],["label","Add Parameter","formControlName","paramType",3,"data","valueChange",4,"ngIf"],["label","Email Content","formControlName","emailContent",3,"ccAutosizeMinRows","ccAutosizeMaxRows","required","click","keyup.enter"],["label","Add Parameter","formControlName","paramType",3,"data","valueChange"],[1,"col-12","px-0","mt-2","actions","d-flex","justify-content-end","gap-2"],["cc-flat-button","",3,"click"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Add Blocker"),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"cc-dialog-content")(6,"form",4)(7,"div",5)(8,"cc-select",6),e.NdJ("valueChange",function(a){return t.onSelectConfig(a)}),e.ALo(9,"async"),e.qZA()(),e.TgZ(10,"div",5),e._UZ(11,"cc-datepicker",7),e.qZA(),e.TgZ(12,"div",8)(13,"cc-checkbox",9),e._uU(14,"Turn-off RPA"),e.qZA()(),e.TgZ(15,"div",10)(16,"div",11)(17,"cc-checkbox",12),e._uU(18," Inform Company "),e.qZA(),e.YNc(19,ve,2,0,"button",13),e.ALo(20,"async"),e.qZA(),e.YNc(21,Me,4,3,"div",14),e.ALo(22,"async"),e.qZA()()(),e.TgZ(23,"cc-dialog-actions")(24,"button",15),e._uU(25,"Cancel"),e.qZA(),e.TgZ(26,"button",16),e.NdJ("click",function(){return t.onSave()}),e._uU(27," Save "),e.qZA()()()),2&o&&(e.xp6(6),e.Q6J("formGroup",t.form),e.xp6(2),e.Q6J("data",e.lcZ(9,8,t.blockerConfigurationOptions))("emitFullSelectOption",!0)("required",!0),e.xp6(3),e.Q6J("required",!0),e.xp6(8),e.Q6J("ngIf",e.lcZ(20,10,t.informCompany$)),e.xp6(2),e.Q6J("ngIf",e.lcZ(22,12,t.informCompany$)),e.xp6(5),e.Q6J("disabled",!t.form.valid||t.editEmailTemplate))},directives:[r.iK,r.Cj,r.Zb,r.fX,r.zn,r.kL,d._Y,d.JL,d.sg,D.jB,d.JJ,d.u,d.Q7,U.AC,O.E,p.O5,A.uu,Ce,N.G,I.Qf,r.Zu],pipes:[p.Ov],encapsulation:2,changeDetection:0}),x=(0,h.gn)([Z.kG],x);let b=class{constructor(o,t,i,a,s){this._apiService=o,this._store=t,this._notification=i,this._dialogRef=a,this.data=s,this.disablePreviousDays=()=>c=>{if(!c)return!1;const m=g().startOf("day");return g(c).startOf("day").isAfter(m)},this.visaBlocker=this.data.visaBlocker,this.arrivalEstimatedDate=g(this.visaBlocker.arrivalEstimatedDate).format("YYYY-MM-DD")}ngOnInit(){}onSave(){const o={id:this.visaBlocker.id,arrivalEstimatedDate:g(this.arrivalEstimatedDate).format("YYYY-MM-DD 00:00:00")};this._apiService.updateBlocker(o).pipe((0,E.b)(()=>{this._notification.notifySuccess("Updated Successfully"),this._store.refreshData(),this._dialogRef.close()})).subscribe()}};function ye(n,o){1&n&&(e.TgZ(0,"span")(1,"cc-icon",10),e._uU(2,"check_circle"),e.qZA()())}function Ae(n,o){1&n&&(e.TgZ(0,"span")(1,"cc-icon",11),e._uU(2,"cancel"),e.qZA()())}function Be(n,o){if(1&n&&(e.YNc(0,ye,3,0,"span",4),e.YNc(1,Ae,3,0,"span",4)),2&n){const t=o.$implicit;e.Q6J("ngIf",t.broadcastMode),e.xp6(1),e.Q6J("ngIf",!t.broadcastMode)}}function Ze(n,o){if(1&n&&(e.TgZ(0,"p",14),e._uU(1),e.qZA()),2&n){const t=o.$implicit;e.xp6(1),e.hij(" ",t.visaStepName," ")}}function je(n,o){if(1&n&&(e.TgZ(0,"div",12),e.YNc(1,Ze,2,1,"p",13),e.qZA()),2&n){const t=o.$implicit;e.xp6(1),e.Q6J("ngForOf",t.blockerSteps)}}function Ee(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"button",23),e.NdJ("click",function(){e.CHM(t);const a=e.oxw(2).$implicit;return e.oxw(2).toggleBroadcastStatus(a.id,!a.broadcastStatus)}),e._uU(1," Disable Broadcast "),e.qZA()}}function Se(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"button",24),e.NdJ("click",function(){e.CHM(t);const a=e.oxw(2).$implicit;return e.oxw(2).toggleBroadcastStatus(a.id,!a.broadcastStatus)}),e._uU(1," Enable Broadcast "),e.qZA()}}function we(n,o){if(1&n&&(e.TgZ(0,"div",20),e.YNc(1,Ee,2,0,"button",21),e.YNc(2,Se,2,0,"button",22),e.qZA()),2&n){const t=e.oxw().$implicit;e.xp6(1),e.Q6J("ngIf",t.broadcastStatus),e.xp6(1),e.Q6J("ngIf",!t.broadcastStatus)}}function Ne(n,o){if(1&n){const t=e.EpF();e.TgZ(0,"div",15)(1,"div",16)(2,"button",17),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw(2).openEditBlockerDialog(s)}),e._uU(3," Edit "),e.qZA(),e.TgZ(4,"button",18),e.NdJ("click",function(){const s=e.CHM(t).$implicit;return e.oxw(2).onResolveBlocker(s.id)}),e._uU(5," Resolved "),e.qZA()(),e.YNc(6,we,3,2,"div",19),e.qZA()}if(2&n){const t=o.$implicit;e.xp6(6),e.Q6J("ngIf",t.broadcastMode)}}b.\u0275fac=function(o){return new(o||b)(e.Y36(y),e.Y36(f),e.Y36(J.zg),e.Y36(B.so),e.Y36(B.WI))},b.\u0275cmp=e.Xpm({type:b,selectors:[["app-edit-blocker"]],decls:17,vars:4,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["role","button","cc-dialog-close","","cc-dialog-close-button",""],[1,"d-flex","flex-column","align-items-center"],[1,"col-md-10",2,"width","0","height","0","position","absolute","left","-100%"],[1,"col-md-10"],["label","ETA to be Solved",3,"ngModel","disableFilterFunc","required","ngModelChange"],["date","ngModel"],["cc-flat-button","","cc-dialog-close","",1,"px-5"],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"]],template:function(o,t){if(1&o&&(e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Edit Blocker"),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"cc-dialog-content")(6,"div",4)(7,"div",5),e._UZ(8,"cc-input"),e.qZA(),e.TgZ(9,"div",6)(10,"cc-datepicker",7,8),e.NdJ("ngModelChange",function(a){return t.arrivalEstimatedDate=a}),e.qZA()()()(),e.TgZ(12,"cc-dialog-actions")(13,"button",9),e._uU(14,"Cancel"),e.qZA(),e.TgZ(15,"button",10),e.NdJ("click",function(){return t.onSave()}),e._uU(16," Save "),e.qZA()()()),2&o){const i=e.MAs(11);e.xp6(10),e.Q6J("ngModel",t.arrivalEstimatedDate)("disableFilterFunc",t.disablePreviousDays())("required",!0),e.xp6(5),e.Q6J("disabled",!i.valid)}},directives:[r.iK,r.Cj,r.Zb,r.zn,r.fX,r.kL,N.G,U.AC,d.JJ,d.On,d.Q7,r.Zu,A.uu],encapsulation:2,changeDetection:0}),b=(0,h.gn)([Z.kG],b);const De=function(n,o,t){return{broadcastMode:n,blockerSteps:o,actions:t}};function Ie(n,o){if(1&n&&(e.ynx(0),e._UZ(1,"cc-datagrid",5),e.YNc(2,Be,2,2,"ng-template",6,7,e.W1O),e.YNc(4,je,2,1,"ng-template",6,8,e.W1O),e.YNc(6,Ne,7,1,"ng-template",6,9,e.W1O),e.BQk()),2&n){const t=o.ngIf,i=e.MAs(3),a=e.MAs(5),s=e.MAs(7),c=e.oxw();e.xp6(1),e.Q6J("columns",c.gridCols)("data",t)("showPaginator",!1)("loading",!1)("cellTemplate",e.kEZ(8,De,i,a,s)),e.xp6(1),e.Q6J("ccGridCell",t),e.xp6(2),e.Q6J("ccGridCell",t),e.xp6(2),e.Q6J("ccGridCell",t)}}const Oe=[{field:"visaBlockerConfiguration.label",header:"Blocker"},{field:"blockerType",header:"Type"},{field:"creationDate",header:"Creation Date",formatter:n=>g(n.creationDate).format("YYYY-MM-DD")},{field:"arrivalEstimatedDate",header:"EDA to be Solved",formatter:n=>g(n.arrivalEstimatedDate).format("YYYY-MM-DD")},{field:"broadcastMode",header:"Broadcast Mode"},{field:"blockerSteps",header:"Affected Steps",width:"300px"},{field:"actions",header:"Actions",width:"200px"}];let k=class{constructor(o,t,i){this._dialog=o,this._apiService=t,this._store=i,this.gridCols=Oe}ngOnInit(){this.data$=this._store.refreshData$.pipe((0,Y.w)(()=>this._apiService.getAllBlockersByResolveStatus(!1)))}openBroadcastMessageDialog(){this._dialog.originalOpen(C,{panelClass:["col-md-10"],data:{}})}openAddBlockerDialog(){this._dialog.originalOpen(x,{panelClass:["col-md-10"],data:{}})}openEditBlockerDialog(o){this._dialog.originalOpen(b,{panelClass:["col-md-6"],data:{visaBlocker:o}})}toggleBroadcastStatus(o,t){this._apiService.changeBroadcastStatus(o,t).pipe((0,E.b)(()=>this._store.refreshData())).subscribe()}onResolveBlocker(o){this._apiService.resolveBlocker(o).pipe((0,E.b)(()=>this._store.refreshData())).subscribe()}onEditBroadcastMessageConfig(o){this._dialog.originalOpen(C,{panelClass:["col-md-10"],data:{id:o}})}};function Ye(n,o){if(1&n&&(e.TgZ(0,"p",7),e._uU(1),e.qZA()),2&n){const t=o.$implicit;e.xp6(1),e.hij(" ",t.visaStepName," ")}}function Je(n,o){if(1&n&&(e.TgZ(0,"div",5),e.YNc(1,Ye,2,1,"p",6),e.qZA()),2&n){const t=o.$implicit;e.xp6(1),e.Q6J("ngForOf",t.blockerSteps)}}k.\u0275fac=function(o){return new(o||k)(e.Y36(r.uY),e.Y36(y),e.Y36(f))},k.\u0275cmp=e.Xpm({type:k,selectors:[["ongoing-blockers"]],decls:8,vars:3,consts:[[1,"p-3"],[1,"cols-12","d-flex","justify-content-end","gap-2","mb-3"],["cc-raised-button","",2,"background-color","#7a7a7a","color","#fff",3,"click"],["cc-raised-button","","color","accent",3,"click"],[4,"ngIf"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["broadcastModeTmp",""],["affectedStepsTmp",""],["actionsTmp",""],[1,"text-success"],[1,"text-danger"],[1,"d-flex","flex-row","flex-wrap","justify-content-center"],["class","mb-2 mr-2","style","background: #8080805a; padding: 2px 10px",4,"ngFor","ngForOf"],[1,"mb-2","mr-2",2,"background","#8080805a","padding","2px 10px"],[1,"py-1","btn-wrapper"],[1,"d-flex","flex-row","justify-content-between","mb-2",2,"width","100%"],["cc-flat-button","",1,"mr-2",2,"background-color","#990000","color","#fff",3,"click"],["cc-flat-button","",2,"background-color","#38761d","color","#fff",3,"click"],["class","d-flex flex-column gap-1","style","width: 100%",4,"ngIf"],[1,"d-flex","flex-column","gap-1",2,"width","100%"],["cc-flat-button","","style","background-color: #7a7a7a; color: #fff; width: 100%",3,"click",4,"ngIf"],["cc-flat-button","","style","background-color: #93c47d; width: 100%",3,"click",4,"ngIf"],["cc-flat-button","",2,"background-color","#7a7a7a","color","#fff","width","100%",3,"click"],["cc-flat-button","",2,"background-color","#93c47d","width","100%",3,"click"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"div",1)(2,"button",2),e.NdJ("click",function(){return t.openBroadcastMessageDialog()}),e._uU(3," Send Broadcast Message "),e.qZA(),e.TgZ(4,"button",3),e.NdJ("click",function(){return t.openAddBlockerDialog()}),e._uU(5," Add Blocker "),e.qZA()(),e.YNc(6,Ie,8,12,"ng-container",4),e.ALo(7,"async"),e.qZA()),2&o&&(e.xp6(6),e.Q6J("ngIf",e.lcZ(7,1,t.data$)))},directives:[A.uu,p.O5,_.Ge,_.VC,q.Q9,p.sg],pipes:[p.Ov],encapsulation:2,changeDetection:0}),k=(0,h.gn)([Z.kG],k);const Ue=function(n){return{affectedSteps:n}};function Qe(n,o){if(1&n&&(e.ynx(0),e._UZ(1,"cc-datagrid",2),e.YNc(2,Je,2,1,"ng-template",3,4,e.W1O),e.BQk()),2&n){const t=o.ngIf,i=e.MAs(3),a=e.oxw();e.xp6(1),e.Q6J("columns",a.gridCols)("data",t)("showPaginator",!1)("loading",!1)("cellTemplate",e.VKq(6,Ue,i)),e.xp6(1),e.Q6J("ccGridCell",t)}}const ze=[{field:"visaBlockerConfiguration.label",header:"Blocker Name"},{field:"blockerType",header:"Type"},{field:"creationDate",header:"Creation Date",formatter:n=>g(n.creationDate).format("YYYY-MM-DD h:mm:A")},{field:"Resolution Timefr",header:"Resolution Timefr",formatter:n=>{const o=g(n.creationDate),t=g(n.resolvedDate),i=o.from(t,!0);return"Invalid date"!==i?i:"--"}},{field:"resolvedDate",header:"Resolved Date",formatter:n=>g(n.resolvedDate).format("YYYY-MM-DD hh:mm:A")},{field:"affectedSteps",header:"Affected Steps",width:"350px"}];let T=class{constructor(o){this._apiService=o,this.gridCols=ze}ngOnInit(){this.data$=this._apiService.getAllBlockersByResolveStatus(!0)}};function Fe(n,o){1&n&&e._UZ(0,"ongoing-blockers")}function qe(n,o){1&n&&e._UZ(0,"blockers-history")}T.\u0275fac=function(o){return new(o||T)(e.Y36(y))},T.\u0275cmp=e.Xpm({type:T,selectors:[["blockers-history"]],decls:3,vars:3,consts:[[1,"p-3"],[4,"ngIf"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["affectedStepsTmp",""],[1,"d-flex","flex-row","flex-wrap","justify-content-center"],["class","mb-2 mr-2","style","background: #8080805a; padding: 2px 10px",4,"ngFor","ngForOf"],[1,"mb-2","mr-2",2,"background","#8080805a","padding","2px 10px"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0),e.YNc(1,Qe,4,8,"ng-container",1),e.ALo(2,"async"),e.qZA()),2&o&&(e.xp6(1),e.Q6J("ngIf",e.lcZ(2,1,t.data$)))},directives:[p.O5,_.Ge,_.VC,p.sg],pipes:[p.Ov],encapsulation:2}),T=(0,h.gn)([Z.kG],T);const Pe=[{path:"",component:(()=>{class n{constructor(){}ngOnInit(){}}return n.\u0275fac=function(t){return new(t||n)},n.\u0275cmp=e.Xpm({type:n,selectors:[["app-shell"]],decls:6,vars:0,consts:[[1,"dashboard","col-12","mt-2"],["animationDuration","0ms","cc-align-tabs","start"],["label","Ongoing Blockers"],["ccTabContent",""],["label","Blockers History"]],template:function(t,i){1&t&&(e.TgZ(0,"div",0)(1,"cc-tab-group",1)(2,"cc-tab",2),e.YNc(3,Fe,1,0,"ng-template",3),e.qZA(),e.TgZ(4,"cc-tab",4),e.YNc(5,qe,1,0,"ng-template",3),e.qZA()()())},directives:[j.e6,j.eF,j.ST,k,T],encapsulation:2}),n})(),data:{label:"Dashboard"}}];let Re=(()=>{class n{}return n.\u0275fac=function(t){return new(t||n)},n.\u0275mod=e.oAB({type:n}),n.\u0275inj=e.cJS({imports:[[p.ez,d.u5,d.UX,M.Bz.forChild(Pe),r.I8,_.Gz,N.f,D.lK,I.l2,j.wA,A.S6,O.$,W.XD,L.C6,q.L,G.B,U.Bp,V.SZ]]}),n})()},46700:(F,w,l)=>{var p={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function M(r){var _=d(r);return l(_)}function d(r){if(!l.o(p,r)){var _=new Error("Cannot find module '"+r+"'");throw _.code="MODULE_NOT_FOUND",_}return p[r]}M.keys=function(){return Object.keys(p)},M.resolve=d,F.exports=M,M.id=46700}}]);
"use strict";(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["vendors-node_modules_angular_cdk_fesm2015_clipboard_mjs-node_modules_maids_cc-lib_fesm2015_ma-278b3e"],{69287:(J,U,v)=>{v.d(U,{Iq:()=>z,TU:()=>R,i3:()=>T});var s=v(69808),a=v(5e3);class b{constructor(_,d){this._document=d;const l=this._textarea=this._document.createElement("textarea"),C=l.style;C.position="fixed",C.top=C.opacity="0",C.left="-999em",l.setAttribute("aria-hidden","true"),l.value=_,this._document.body.appendChild(l)}copy(){const _=this._textarea;let d=!1;try{if(_){const l=this._document.activeElement;_.select(),_.setSelectionRange(0,_.value.length),d=this._document.execCommand("copy"),l&&l.focus()}}catch(l){}return d}destroy(){const _=this._textarea;_&&(_.remove(),this._textarea=void 0)}}let R=(()=>{class g{constructor(d){this._document=d}copy(d){const l=this.beginCopy(d),C=l.copy();return l.destroy(),C}beginCopy(d){return new b(d,this._document)}}return g.\u0275fac=function(d){return new(d||g)(a.LFG(s.K0))},g.\u0275prov=a.Yz7({token:g,factory:g.\u0275fac,providedIn:"root"}),g})();const E=new a.OlP("CDK_COPY_TO_CLIPBOARD_CONFIG");let T=(()=>{class g{constructor(d,l,C){this._clipboard=d,this._ngZone=l,this.text="",this.attempts=1,this.copied=new a.vpe,this._pending=new Set,C&&null!=C.attempts&&(this.attempts=C.attempts)}copy(d=this.attempts){if(d>1){let l=d;const C=this._clipboard.beginCopy(this.text);this._pending.add(C);const D=()=>{const Q=C.copy();Q||!--l||this._destroyed?(this._currentTimeout=null,this._pending.delete(C),C.destroy(),this.copied.emit(Q)):this._currentTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(D,1))};D()}else this.copied.emit(this._clipboard.copy(this.text))}ngOnDestroy(){this._currentTimeout&&clearTimeout(this._currentTimeout),this._pending.forEach(d=>d.destroy()),this._pending.clear(),this._destroyed=!0}}return g.\u0275fac=function(d){return new(d||g)(a.Y36(R),a.Y36(a.R0b),a.Y36(E,8))},g.\u0275dir=a.lG2({type:g,selectors:[["","cdkCopyToClipboard",""]],hostBindings:function(d,l){1&d&&a.NdJ("click",function(){return l.copy()})},inputs:{text:["cdkCopyToClipboard","text"],attempts:["cdkCopyToClipboardAttempts","attempts"]},outputs:{copied:"cdkCopyToClipboardCopied"}}),g})(),z=(()=>{class g{}return g.\u0275fac=function(d){return new(d||g)},g.\u0275mod=a.oAB({type:g}),g.\u0275inj=a.cJS({}),g})()},81770:(J,U,v)=>{v.d(U,{m:()=>D});var s=v(5e3),a=v(69808),b=v(48966),R=v(21799),E=v(40520),T=v(65868),z=v(45834),g=v(82599);function _(M,y){1&M&&(s.TgZ(0,"cc-icon",16),s._uU(1," done "),s.qZA()),2&M&&s.Q6J("inline",!0)}function d(M,y){1&M&&(s.TgZ(0,"cc-icon",17),s._uU(1," content_copy "),s.qZA()),2&M&&s.Q6J("inline",!0)}function l(M,y){if(1&M){const m=s.EpF();s.TgZ(0,"div",9)(1,"label",10),s._uU(2),s.qZA(),s.TgZ(3,"div",11)(4,"span"),s._uU(5),s.qZA()(),s.TgZ(6,"div",12)(7,"button",13),s.NdJ("click",function(){s.CHM(m);const H=s.oxw().$implicit;return s.oxw().copy(H)}),s.YNc(8,_,2,1,"cc-icon",14),s.YNc(9,d,2,1,"cc-icon",15),s.qZA()()()}if(2&M){const m=s.oxw().$implicit;s.xp6(2),s.hij("",m.label,":"),s.xp6(3),s.Oqu(m.value),s.xp6(2),s.Q6J("color",m.copied?"green":"accent")("disabled",!m.value),s.xp6(1),s.Q6J("ngIf",m.copied),s.xp6(1),s.Q6J("ngIf",!m.copied)}}function C(M,y){if(1&M&&(s.ynx(0),s.YNc(1,l,10,6,"div",8),s.BQk()),2&M){const m=y.$implicit;s.xp6(1),s.Q6J("ngIf",m.value)}}let D=(()=>{class M{constructor(m,A,H,L){this._dialogRef=m,this.data=A,this._notificationService=H,this._http=L,this.numbers=[],this._timer=3e3}ngOnInit(){this._timer=this.data.successTime||this._timer,this._http.request(this.data.apiMethod||"get",this.data.api).subscribe({next:m=>{this.numbers=this.data.phonesMap?Object.entries(this.data.phonesMap).map(([A,H])=>({label:A,value:m[H]})):Object.keys(m).map(A=>m[A])},error:m=>{console.error("Error fetching data",m)}})}copy(m){navigator.clipboard.writeText(m.value),m.copied=!0,setTimeout(()=>{m.copied=!1},this._timer)}close(){this._dialogRef.close()}}return M.\u0275fac=function(m){return new(m||M)(s.Y36(b.so),s.Y36(b.WI),s.Y36(R.zg),s.Y36(E.eN))},M.\u0275cmp=s.Xpm({type:M,selectors:[["app-preview-phones"]],decls:14,vars:2,consts:[["cc-std-dialog",""],["cc-dialog-title","",2,"text-align","center"],["role","button","type","button","cc-icon-button","",3,"click"],[2,"overflow","visible"],[1,"row"],[1,"col-sm-12"],[4,"ngFor","ngForOf"],["cc-button","",3,"click"],["class","row mt-4 align-items-sm-center",4,"ngIf"],[1,"row","mt-4","align-items-sm-center"],[1,"custom-label","col-md-5","m-0",2,"color","rgba(0, 0, 0, 0.87)"],[1,"col-md-5","number"],[1,"col-md-2"],["cc-mini-fab","",3,"color","disabled","click"],["color","primary",3,"inline",4,"ngIf"],["class","done-icon",3,"inline",4,"ngIf"],["color","primary",3,"inline"],[1,"done-icon",3,"inline"]],template:function(m,A){1&m&&(s.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),s._uU(3),s.qZA(),s.TgZ(4,"a",2),s.NdJ("click",function(){return A.close()}),s.TgZ(5,"cc-icon"),s._uU(6,"close"),s.qZA()()(),s.TgZ(7,"cc-dialog-content",3)(8,"div",4)(9,"div",5),s.YNc(10,C,2,1,"ng-container",6),s.qZA()()(),s.TgZ(11,"cc-dialog-actions")(12,"button",7),s.NdJ("click",function(){return A.close()}),s._uU(13,"Close"),s.qZA()()()),2&m&&(s.xp6(3),s.Oqu(A.data.name),s.xp6(7),s.Q6J("ngForOf",A.numbers))},directives:[T.uu,z.Q9,g.iK,g.Cj,g.Zb,g.kL,a.sg,a.O5,g.Zu],styles:[""]}),M})()},83877:(J,U,v)=>{v.d(U,{QG:()=>rt,ap:()=>nt});var s=v(97582),a=v(5e3),b=v(22313),R=v(69808);const E=["wrapper"],T=["sourceImage"];function z(c,h){if(1&c){const t=a.EpF();a.TgZ(0,"img",4,5),a.NdJ("load",function(){return a.CHM(t),a.oxw().imageLoadedInView()})("mousedown",function(i){a.CHM(t);const o=a.oxw();return o.startMove(i,o.moveTypes.Drag)})("touchstart",function(i){a.CHM(t);const o=a.oxw();return o.startMove(i,o.moveTypes.Drag)})("error",function(i){return a.CHM(t),a.oxw().loadImageError(i)}),a.qZA()}if(2&c){const t=a.oxw();a.Udp("visibility",t.imageVisible?"visible":"hidden")("transform",t.safeTransformStyle),a.ekj("ngx-ic-draggable",!t.disabled&&t.allowMoveImage),a.Q6J("src",t.safeImgDataUrl,a.LSH),a.uIk("alt",t.imageAltText)}}function g(c,h){if(1&c){const t=a.EpF();a.ynx(0),a.TgZ(1,"span",9),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"topleft")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"topleft")}),a._UZ(2,"span",10),a.qZA(),a.TgZ(3,"span",11),a._UZ(4,"span",10),a.qZA(),a.TgZ(5,"span",12),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"topright")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"topright")}),a._UZ(6,"span",10),a.qZA(),a.TgZ(7,"span",13),a._UZ(8,"span",10),a.qZA(),a.TgZ(9,"span",14),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"bottomright")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"bottomright")}),a._UZ(10,"span",10),a.qZA(),a.TgZ(11,"span",15),a._UZ(12,"span",10),a.qZA(),a.TgZ(13,"span",16),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"bottomleft")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"bottomleft")}),a._UZ(14,"span",10),a.qZA(),a.TgZ(15,"span",17),a._UZ(16,"span",10),a.qZA(),a.TgZ(17,"span",18),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"top")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"top")}),a.qZA(),a.TgZ(18,"span",19),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"right")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"right")}),a.qZA(),a.TgZ(19,"span",20),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"bottom")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"bottom")}),a.qZA(),a.TgZ(20,"span",21),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"left")})("touchstart",function(i){a.CHM(t);const o=a.oxw(2);return o.startMove(i,o.moveTypes.Resize,"left")}),a.qZA(),a.BQk()}}function _(c,h){if(1&c){const t=a.EpF();a.TgZ(0,"div",6),a.NdJ("keydown",function(i){return a.CHM(t),a.oxw().keyboardAccess(i)}),a.TgZ(1,"div",7),a.NdJ("mousedown",function(i){a.CHM(t);const o=a.oxw();return o.startMove(i,o.moveTypes.Move)})("touchstart",function(i){a.CHM(t);const o=a.oxw();return o.startMove(i,o.moveTypes.Move)}),a.qZA(),a.YNc(2,g,21,0,"ng-container",8),a.qZA()}if(2&c){const t=a.oxw();a.Udp("top",t.cropper.y1,"px")("left",t.cropper.x1,"px")("width",t.cropper.x2-t.cropper.x1,"px")("height",t.cropper.y2-t.cropper.y1,"px")("margin-left","center"===t.alignImage?t.marginLeft:null)("visibility",t.imageVisible?"visible":"hidden"),a.ekj("ngx-ic-round",t.roundCropper),a.uIk("aria-label",t.cropperFrameAriaLabel),a.xp6(2),a.Q6J("ngIf",!t.hideResizeSquares)}}class d{constructor(){this.format="png",this.output="blob",this.maintainAspectRatio=!0,this.transform={},this.aspectRatio=1,this.resetCropOnAspectRatioChange=!0,this.resizeToWidth=0,this.resizeToHeight=0,this.cropperMinWidth=0,this.cropperMinHeight=0,this.cropperMaxHeight=0,this.cropperMaxWidth=0,this.cropperStaticWidth=0,this.cropperStaticHeight=0,this.canvasRotation=0,this.initialStepSize=3,this.roundCropper=!1,this.onlyScaleDown=!1,this.imageQuality=92,this.autoCrop=!0,this.backgroundColor=null,this.containWithinAspectRatio=!1,this.hideResizeSquares=!1,this.alignImage="center",this.cropperFrameAriaLabel="Crop photo",this.cropperScaledMinWidth=20,this.cropperScaledMinHeight=20,this.cropperScaledMaxWidth=20,this.cropperScaledMaxHeight=20,this.stepSize=this.initialStepSize}setOptions(h){Object.keys(h).filter(t=>t in this).forEach(t=>this[t]=h[t]),this.validateOptions()}setOptionsFromChanges(h){Object.keys(h).filter(t=>t in this).forEach(t=>this[t]=h[t].currentValue),this.validateOptions()}validateOptions(){if(this.maintainAspectRatio&&!this.aspectRatio)throw new Error("`aspectRatio` should > 0 when `maintainAspectRatio` is enabled")}}var l=(()=>{return(c=l||(l={})).Drag="drag",c.Move="move",c.Resize="resize",c.Pinch="pinch",l;var c})();function M(c,h){return c/100*h}let y=(()=>{class c{crop(t,e,i,o,n){const r=this.getImagePosition(t,e,i,o),p=r.x2-r.x1,f=r.y2-r.y1,x=document.createElement("canvas");x.width=p,x.height=f;const u=x.getContext("2d");if(!u)return null;null!=o.backgroundColor&&(u.fillStyle=o.backgroundColor,u.fillRect(0,0,p,f));const I=(o.transform.scale||1)*(o.transform.flipH?-1:1),k=(o.transform.scale||1)*(o.transform.flipV?-1:1),{translateH:W,translateV:B}=this.getCanvasTranslate(t,e,o),O=e.transformed;u.setTransform(I,0,0,k,O.size.width/2+W,O.size.height/2+B),u.translate(-r.x1/I,-r.y1/k),u.rotate((o.transform.rotate||0)*Math.PI/180),u.drawImage(O.image,-O.size.width/2,-O.size.height/2);const S={width:p,height:f,imagePosition:r,cropperPosition:Object.assign({},i)};o.containWithinAspectRatio&&(S.offsetImagePosition=this.getOffsetImagePosition(t,e,i,o));const w=this.getResizeRatio(p,f,o);return 1!==w&&(S.width=Math.round(p*w),S.height=o.maintainAspectRatio?Math.round(S.width/o.aspectRatio):Math.round(f*w),function N(c,h,t){const e=c.width,i=c.height,o=e/(h=Math.round(h)),n=i/(t=Math.round(t)),r=Math.ceil(o/2),p=Math.ceil(n/2),f=c.getContext("2d");if(f){const x=f.getImageData(0,0,e,i),u=f.createImageData(h,t),I=x.data,k=u.data;for(let W=0;W<t;W++)for(let B=0;B<h;B++){const O=4*(B+W*h),S=W*n;let w=0,Z=0,K=0,q=0,G=0,$=0,tt=0;const st=Math.floor(B*o),ct=Math.floor(W*n);let X=Math.ceil((B+1)*o),V=Math.ceil((W+1)*n);X=Math.min(X,e),V=Math.min(V,i);for(let Y=ct;Y<V;Y++){const et=Math.abs(S-Y)/p,ht=B*o,pt=et*et;for(let j=st;j<X;j++){const it=Math.abs(ht-j)/r,P=Math.sqrt(pt+it*it);if(P>=1)continue;w=2*P*P*P-3*P*P+1;const F=4*(j+Y*e);tt+=w*I[F+3],K+=w,I[F+3]<255&&(w=w*I[F+3]/250),q+=w*I[F],G+=w*I[F+1],$+=w*I[F+2],Z+=w}}k[O]=q/Z,k[O+1]=G/Z,k[O+2]=$/Z,k[O+3]=tt/K}c.width=h,c.height=t,f.putImageData(u,0,0)}}(x,S.width,S.height)),"blob"===n?this.cropToBlob(S,x,o):(S.base64=x.toDataURL("image/"+o.format,this.getQuality(o)),S)}cropToBlob(t,e,i){return(0,s.mG)(this,void 0,void 0,function*(){return t.blob=yield new Promise(o=>e.toBlob(o,"image/"+i.format,this.getQuality(i))),t.blob&&(t.objectUrl=URL.createObjectURL(t.blob)),t})}getCanvasTranslate(t,e,i){if("px"===i.transform.translateUnit){const o=this.getRatio(t,e);return{translateH:(i.transform.translateH||0)*o,translateV:(i.transform.translateV||0)*o}}return{translateH:i.transform.translateH?M(i.transform.translateH,e.transformed.size.width):0,translateV:i.transform.translateV?M(i.transform.translateV,e.transformed.size.height):0}}getRatio(t,e){return e.transformed.size.width/t.nativeElement.offsetWidth}getImagePosition(t,e,i,o){const n=this.getRatio(t,e),r={x1:Math.round(i.x1*n),y1:Math.round(i.y1*n),x2:Math.round(i.x2*n),y2:Math.round(i.y2*n)};return o.containWithinAspectRatio||(r.x1=Math.max(r.x1,0),r.y1=Math.max(r.y1,0),r.x2=Math.min(r.x2,e.transformed.size.width),r.y2=Math.min(r.y2,e.transformed.size.height)),r}getOffsetImagePosition(t,e,i,o){const p=e.transformed.size.width/t.nativeElement.offsetWidth;let f,x;(o.canvasRotation+e.exifTransform.rotate)%2?(f=(e.transformed.size.width-e.original.size.height)/2,x=(e.transformed.size.height-e.original.size.width)/2):(f=(e.transformed.size.width-e.original.size.width)/2,x=(e.transformed.size.height-e.original.size.height)/2);const u={x1:Math.round(i.x1*p)-f,y1:Math.round(i.y1*p)-x,x2:Math.round(i.x2*p)-f,y2:Math.round(i.y2*p)-x};return o.containWithinAspectRatio||(u.x1=Math.max(u.x1,0),u.y1=Math.max(u.y1,0),u.x2=Math.min(u.x2,e.transformed.size.width),u.y2=Math.min(u.y2,e.transformed.size.height)),u}getResizeRatio(t,e,i){const o=i.resizeToWidth/t,n=i.resizeToHeight/e,r=new Array;i.resizeToWidth>0&&r.push(o),i.resizeToHeight>0&&r.push(n);const p=0===r.length?1:Math.min(...r);return p>1&&!i.onlyScaleDown?p:Math.min(p,1)}getQuality(t){return Math.min(1,Math.max(0,t.imageQuality/100))}}return c.\u0275fac=function(t){return new(t||c)},c.\u0275prov=a.Yz7({token:c,factory:c.\u0275fac,providedIn:"root"}),c})(),m=(()=>{class c{resetCropperPosition(t,e,i){if(!(null==t?void 0:t.nativeElement))return;const o=t.nativeElement;if(i.cropperStaticHeight&&i.cropperStaticWidth)e.x1=0,e.x2=o.offsetWidth>i.cropperStaticWidth?i.cropperStaticWidth:o.offsetWidth,e.y1=0,e.y2=o.offsetHeight>i.cropperStaticHeight?i.cropperStaticHeight:o.offsetHeight;else{const n=Math.min(i.cropperScaledMaxWidth,o.offsetWidth),r=Math.min(i.cropperScaledMaxHeight,o.offsetHeight);if(i.maintainAspectRatio)if(o.offsetWidth/i.aspectRatio<o.offsetHeight){e.x1=0,e.x2=n;const p=n/i.aspectRatio;e.y1=(o.offsetHeight-p)/2,e.y2=e.y1+p}else{e.y1=0,e.y2=r;const p=r*i.aspectRatio;e.x1=(o.offsetWidth-p)/2,e.x2=e.x1+p}else e.x1=0,e.x2=n,e.y1=0,e.y2=r}}move(t,e,i){const o=this.getClientX(t)-e.clientX,n=this.getClientY(t)-e.clientY;i.x1=e.x1+o,i.y1=e.y1+n,i.x2=e.x2+o,i.y2=e.y2+n}resize(t,e,i,o,n){const r=this.getClientX(t)-e.clientX,p=this.getClientY(t)-e.clientY;switch(e.position){case"left":i.x1=Math.min(Math.max(e.x1+r,i.x2-n.cropperScaledMaxWidth),i.x2-n.cropperScaledMinWidth);break;case"topleft":i.x1=Math.min(Math.max(e.x1+r,i.x2-n.cropperScaledMaxWidth),i.x2-n.cropperScaledMinWidth),i.y1=Math.min(Math.max(e.y1+p,i.y2-n.cropperScaledMaxHeight),i.y2-n.cropperScaledMinHeight);break;case"top":i.y1=Math.min(Math.max(e.y1+p,i.y2-n.cropperScaledMaxHeight),i.y2-n.cropperScaledMinHeight);break;case"topright":i.x2=Math.max(Math.min(e.x2+r,i.x1+n.cropperScaledMaxWidth),i.x1+n.cropperScaledMinWidth),i.y1=Math.min(Math.max(e.y1+p,i.y2-n.cropperScaledMaxHeight),i.y2-n.cropperScaledMinHeight);break;case"right":i.x2=Math.max(Math.min(e.x2+r,i.x1+n.cropperScaledMaxWidth),i.x1+n.cropperScaledMinWidth);break;case"bottomright":i.x2=Math.max(Math.min(e.x2+r,i.x1+n.cropperScaledMaxWidth),i.x1+n.cropperScaledMinWidth),i.y2=Math.max(Math.min(e.y2+p,i.y1+n.cropperScaledMaxHeight),i.y1+n.cropperScaledMinHeight);break;case"bottom":i.y2=Math.max(Math.min(e.y2+p,i.y1+n.cropperScaledMaxHeight),i.y1+n.cropperScaledMinHeight);break;case"bottomleft":i.x1=Math.min(Math.max(e.x1+r,i.x2-n.cropperScaledMaxWidth),i.x2-n.cropperScaledMinWidth),i.y2=Math.max(Math.min(e.y2+p,i.y1+n.cropperScaledMaxHeight),i.y1+n.cropperScaledMinHeight);break;case"center":const f=t.scale,x=Math.min(Math.max(n.cropperScaledMinWidth,Math.abs(e.x2-e.x1)*f),n.cropperScaledMaxWidth),u=Math.min(Math.max(n.cropperScaledMinHeight,Math.abs(e.y2-e.y1)*f),n.cropperScaledMaxHeight);i.x1=e.clientX-x/2,i.x2=e.clientX+x/2,i.y1=e.clientY-u/2,i.y2=e.clientY+u/2,i.x1<0?(i.x2-=i.x1,i.x1=0):i.x2>o.width&&(i.x1-=i.x2-o.width,i.x2=o.width),i.y1<0?(i.y2-=i.y1,i.y1=0):i.y2>o.height&&(i.y1-=i.y2-o.height,i.y2=o.height)}n.maintainAspectRatio&&this.checkAspectRatio(e.position,i,o,n)}checkAspectRatio(t,e,i,o){let n=0,r=0;switch(t){case"top":e.x2=e.x1+(e.y2-e.y1)*o.aspectRatio,n=Math.max(e.x2-i.width,0),r=Math.max(0-e.y1,0),(n>0||r>0)&&(e.x2-=r*o.aspectRatio>n?r*o.aspectRatio:n,e.y1+=r*o.aspectRatio>n?r:n/o.aspectRatio);break;case"bottom":e.x2=e.x1+(e.y2-e.y1)*o.aspectRatio,n=Math.max(e.x2-i.width,0),r=Math.max(e.y2-i.height,0),(n>0||r>0)&&(e.x2-=r*o.aspectRatio>n?r*o.aspectRatio:n,e.y2-=r*o.aspectRatio>n?r:n/o.aspectRatio);break;case"topleft":e.y1=e.y2-(e.x2-e.x1)/o.aspectRatio,n=Math.max(0-e.x1,0),r=Math.max(0-e.y1,0),(n>0||r>0)&&(e.x1+=r*o.aspectRatio>n?r*o.aspectRatio:n,e.y1+=r*o.aspectRatio>n?r:n/o.aspectRatio);break;case"topright":e.y1=e.y2-(e.x2-e.x1)/o.aspectRatio,n=Math.max(e.x2-i.width,0),r=Math.max(0-e.y1,0),(n>0||r>0)&&(e.x2-=r*o.aspectRatio>n?r*o.aspectRatio:n,e.y1+=r*o.aspectRatio>n?r:n/o.aspectRatio);break;case"right":case"bottomright":e.y2=e.y1+(e.x2-e.x1)/o.aspectRatio,n=Math.max(e.x2-i.width,0),r=Math.max(e.y2-i.height,0),(n>0||r>0)&&(e.x2-=r*o.aspectRatio>n?r*o.aspectRatio:n,e.y2-=r*o.aspectRatio>n?r:n/o.aspectRatio);break;case"left":case"bottomleft":e.y2=e.y1+(e.x2-e.x1)/o.aspectRatio,n=Math.max(0-e.x1,0),r=Math.max(e.y2-i.height,0),(n>0||r>0)&&(e.x1+=r*o.aspectRatio>n?r*o.aspectRatio:n,e.y2-=r*o.aspectRatio>n?r:n/o.aspectRatio);break;case"center":e.x2=e.x1+(e.y2-e.y1)*o.aspectRatio,e.y2=e.y1+(e.x2-e.x1)/o.aspectRatio;const p=Math.max(0-e.x1,0),f=Math.max(e.x2-i.width,0),x=Math.max(e.y2-i.height,0),u=Math.max(0-e.y1,0);(p>0||f>0||x>0||u>0)&&(e.x1+=x*o.aspectRatio>p?x*o.aspectRatio:p,e.x2-=u*o.aspectRatio>f?u*o.aspectRatio:f,e.y1+=u*o.aspectRatio>f?u:f/o.aspectRatio,e.y2-=x*o.aspectRatio>p?x:p/o.aspectRatio)}}getClientX(t){var e;return(null===(e=t.touches)||void 0===e?void 0:e[0].clientX)||t.clientX||0}getClientY(t){var e;return(null===(e=t.touches)||void 0===e?void 0:e[0].clientY)||t.clientY||0}}return c.\u0275fac=function(t){return new(t||c)},c.\u0275prov=a.Yz7({token:c,factory:c.\u0275fac,providedIn:"root"}),c})();let at=(()=>{class c{constructor(){this.autoRotateSupported=function H(){return new Promise(c=>{const h=new Image;h.onload=()=>{c(1===h.width&&2===h.height)},h.src="data:image/jpeg;base64,/9j/4QAiRXhpZgAATU0AKgAAAAgAAQESAAMAAAABAAYAAAAAAAD/2wCEAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAf/AABEIAAEAAgMBEQACEQEDEQH/xABKAAEAAAAAAAAAAAAAAAAAAAALEAEAAAAAAAAAAAAAAAAAAAAAAQEAAAAAAAAAAAAAAAAAAAAAEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8H//2Q=="})}()}loadImageFile(t,e){return t.arrayBuffer().then(i=>this.checkImageTypeAndLoadImageFromArrayBuffer(i,t.type,e))}checkImageTypeAndLoadImageFromArrayBuffer(t,e,i){return this.isValidImageType(e)?this.loadImageFromArrayBuffer(t,i):Promise.reject(new Error("Invalid image type"))}isValidImageType(t){return/image\/(png|jpg|jpeg|bmp|gif|tiff|webp|x-icon|vnd.microsoft.icon)/.test(t)}loadImageFromURL(t,e){return fetch(t).then(i=>i.arrayBuffer()).then(i=>this.loadImageFromArrayBuffer(i,e))}loadBase64Image(t,e){const i=this.base64ToArrayBuffer(t);return this.loadImageFromArrayBuffer(i,e)}base64ToArrayBuffer(t){t=t.replace(/^data\:([^\;]+)\;base64,/gim,"");const e=atob(t),i=e.length,o=new Uint8Array(i);for(let n=0;n<i;n++)o[n]=e.charCodeAt(n);return o.buffer}loadImageFromArrayBuffer(t,e){return new Promise((i,o)=>{const n=new Blob([t]),r=URL.createObjectURL(n),p=new Image;p.onload=()=>i({originalImage:p,originalObjectUrl:r,originalArrayBuffer:t}),p.onerror=o,p.src=r}).then(i=>this.transformImageFromArrayBuffer(i,e))}transformImageFromArrayBuffer(t,e){return(0,s.mG)(this,void 0,void 0,function*(){const i=yield this.autoRotateSupported,o=yield function L(c){switch("object"==typeof c&&(c=function ot(c){const h=new DataView(c);if(65496!==h.getUint16(0,!1))return-2;const t=h.byteLength;let e=2;for(;e<t;){if(h.getUint16(e+2,!1)<=8)return-1;const i=h.getUint16(e,!1);if(e+=2,65505==i){if(1165519206!==h.getUint32(e+=2,!1))return-1;const o=18761==h.getUint16(e+=6,!1);e+=h.getUint32(e+4,o);const n=h.getUint16(e,o);e+=2;for(let r=0;r<n;r++)if(274==h.getUint16(e+12*r,o))return h.getUint16(e+12*r+8,o)}else{if(65280!=(65280&i))break;e+=h.getUint16(e,!1)}}return-1}(c)),c){case 2:return{rotate:0,flip:!0};case 3:return{rotate:2,flip:!1};case 4:return{rotate:2,flip:!0};case 5:return{rotate:1,flip:!0};case 6:return{rotate:1,flip:!1};case 7:return{rotate:3,flip:!0};case 8:return{rotate:3,flip:!1};default:return{rotate:0,flip:!1}}}(i?-1:t.originalArrayBuffer);return t.originalImage&&t.originalImage.complete?this.transformLoadedImage({original:{objectUrl:t.originalObjectUrl,image:t.originalImage,size:{width:t.originalImage.naturalWidth,height:t.originalImage.naturalHeight}},exifTransform:o},e):Promise.reject(new Error("No image loaded"))})}transformLoadedImage(t,e){return(0,s.mG)(this,void 0,void 0,function*(){const i=e.canvasRotation+t.exifTransform.rotate,o={width:t.original.image.naturalWidth,height:t.original.image.naturalHeight};if(0===i&&!t.exifTransform.flip&&!e.containWithinAspectRatio)return{original:{objectUrl:t.original.objectUrl,image:t.original.image,size:Object.assign({},o)},transformed:{objectUrl:t.original.objectUrl,image:t.original.image,size:Object.assign({},o)},exifTransform:t.exifTransform};const n=this.getTransformedSize(o,t.exifTransform,e),r=document.createElement("canvas");r.width=n.width,r.height=n.height;const p=r.getContext("2d");null==p||p.setTransform(t.exifTransform.flip?-1:1,0,0,1,r.width/2,r.height/2),null==p||p.rotate(Math.PI*(i/2)),null==p||p.drawImage(t.original.image,-o.width/2,-o.height/2);const f=yield new Promise(I=>r.toBlob(I,e.format));if(!f)throw new Error("Failed to get Blob for transformed image.");const x=URL.createObjectURL(f),u=yield this.loadImageFromObjectUrl(x);return{original:{objectUrl:t.original.objectUrl,image:t.original.image,size:Object.assign({},o)},transformed:{objectUrl:x,image:u,size:{width:u.width,height:u.height}},exifTransform:t.exifTransform}})}loadImageFromObjectUrl(t){return new Promise((e,i)=>{const o=new Image;o.onload=()=>e(o),o.onerror=i,o.src=t})}getTransformedSize(t,e,i){const o=i.canvasRotation+e.rotate;if(i.containWithinAspectRatio){if(o%2){const r=t.height/i.aspectRatio;return{width:Math.max(t.height,t.width*i.aspectRatio),height:Math.max(t.width,r)}}{const r=t.width/i.aspectRatio;return{width:Math.max(t.width,t.height*i.aspectRatio),height:Math.max(t.height,r)}}}return o%2?{height:t.width,width:t.height}:{width:t.width,height:t.height}}}return c.\u0275fac=function(t){return new(t||c)},c.\u0275prov=a.Yz7({token:c,factory:c.\u0275fac,providedIn:"root"}),c})(),nt=(()=>{class c{constructor(t,e,i,o,n,r){this.cropService=t,this.cropperPositionService=e,this.loadImageService=i,this.sanitizer=o,this.cd=n,this.hammerLoader=r,this.settings=new d,this.setImageMaxSizeRetries=0,this.resizedWhileHidden=!1,this.marginLeft="0px",this.maxSize={width:0,height:0},this.moveTypes=l,this.imageVisible=!1,this.cropperFrameAriaLabel=this.settings.cropperFrameAriaLabel,this.output=this.settings.output,this.format=this.settings.format,this.transform={},this.maintainAspectRatio=this.settings.maintainAspectRatio,this.aspectRatio=this.settings.aspectRatio,this.resetCropOnAspectRatioChange=this.settings.resetCropOnAspectRatioChange,this.resizeToWidth=this.settings.resizeToWidth,this.resizeToHeight=this.settings.resizeToHeight,this.cropperMinWidth=this.settings.cropperMinWidth,this.cropperMinHeight=this.settings.cropperMinHeight,this.cropperMaxHeight=this.settings.cropperMaxHeight,this.cropperMaxWidth=this.settings.cropperMaxWidth,this.cropperStaticWidth=this.settings.cropperStaticWidth,this.cropperStaticHeight=this.settings.cropperStaticHeight,this.canvasRotation=this.settings.canvasRotation,this.initialStepSize=this.settings.initialStepSize,this.roundCropper=this.settings.roundCropper,this.onlyScaleDown=this.settings.onlyScaleDown,this.imageQuality=this.settings.imageQuality,this.autoCrop=this.settings.autoCrop,this.backgroundColor=this.settings.backgroundColor,this.containWithinAspectRatio=this.settings.containWithinAspectRatio,this.hideResizeSquares=this.settings.hideResizeSquares,this.allowMoveImage=!1,this.cropper={x1:-100,y1:-100,x2:1e4,y2:1e4},this.alignImage=this.settings.alignImage,this.disabled=!1,this.hidden=!1,this.imageCropped=new a.vpe,this.startCropImage=new a.vpe,this.imageLoaded=new a.vpe,this.cropperReady=new a.vpe,this.loadImageFailed=new a.vpe,this.transformChange=new a.vpe,this.reset()}ngOnChanges(t){var e;this.onChangesUpdateSettings(t),this.onChangesInputImage(t),(null===(e=this.loadedImage)||void 0===e?void 0:e.original.image.complete)&&(t.containWithinAspectRatio||t.canvasRotation)&&this.loadImageService.transformLoadedImage(this.loadedImage,this.settings).then(i=>this.setLoadedImage(i)).catch(i=>this.loadImageError(i)),(t.cropper||t.maintainAspectRatio||t.aspectRatio)&&(this.setMaxSize(),this.setCropperScaledMinSize(),this.setCropperScaledMaxSize(),!this.maintainAspectRatio||!this.resetCropOnAspectRatioChange&&this.aspectRatioIsCorrect()||!t.maintainAspectRatio&&!t.aspectRatio?t.cropper&&(this.checkCropperPosition(!1),this.doAutoCrop()):this.resetCropperPosition(),this.cd.markForCheck()),t.transform&&(this.transform=this.transform||{},this.setCssTransform(),this.doAutoCrop(),this.cd.markForCheck()),t.hidden&&this.resizedWhileHidden&&!this.hidden&&setTimeout(()=>{this.onResize(),this.resizedWhileHidden=!1})}onChangesUpdateSettings(t){this.settings.setOptionsFromChanges(t),this.settings.cropperStaticHeight&&this.settings.cropperStaticWidth&&this.settings.setOptions({hideResizeSquares:!0,cropperMinWidth:this.settings.cropperStaticWidth,cropperMinHeight:this.settings.cropperStaticHeight,cropperMaxHeight:this.settings.cropperStaticHeight,cropperMaxWidth:this.settings.cropperStaticWidth,maintainAspectRatio:!1})}onChangesInputImage(t){(t.imageChangedEvent||t.imageURL||t.imageBase64||t.imageFile)&&this.reset(),t.imageChangedEvent&&this.isValidImageChangedEvent()&&this.loadImageFile(this.imageChangedEvent.target.files[0]),t.imageURL&&this.imageURL&&this.loadImageFromURL(this.imageURL),t.imageBase64&&this.imageBase64&&this.loadBase64Image(this.imageBase64),t.imageFile&&this.imageFile&&this.loadImageFile(this.imageFile)}isValidImageChangedEvent(){var t,e,i;return(null===(i=null===(e=null===(t=this.imageChangedEvent)||void 0===t?void 0:t.target)||void 0===e?void 0:e.files)||void 0===i?void 0:i.length)>0}setCssTransform(){var t;const e=(null===(t=this.transform)||void 0===t?void 0:t.translateUnit)||"%";this.safeTransformStyle=this.sanitizer.bypassSecurityTrustStyle(`translate(${this.transform.translateH||0}${e}, ${this.transform.translateV||0}${e}) scaleX(`+(this.transform.scale||1)*(this.transform.flipH?-1:1)+") scaleY("+(this.transform.scale||1)*(this.transform.flipV?-1:1)+") rotate("+(this.transform.rotate||0)+"deg)")}ngOnInit(){this.settings.stepSize=this.initialStepSize,this.activatePinchGesture()}reset(){this.imageVisible=!1,this.loadedImage=void 0,this.safeImgDataUrl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAC0lEQVQYV2NgAAIAAAUAAarVyFEAAAAASUVORK5CYII=",this.moveStart={active:!1,type:null,position:null,x1:0,y1:0,x2:0,y2:0,clientX:0,clientY:0},this.maxSize={width:0,height:0},this.cropper.x1=-100,this.cropper.y1=-100,this.cropper.x2=1e4,this.cropper.y2=1e4}loadImageFile(t){this.loadImageService.loadImageFile(t,this.settings).then(e=>this.setLoadedImage(e)).catch(e=>this.loadImageError(e))}loadBase64Image(t){this.loadImageService.loadBase64Image(t,this.settings).then(e=>this.setLoadedImage(e)).catch(e=>this.loadImageError(e))}loadImageFromURL(t){this.loadImageService.loadImageFromURL(t,this.settings).then(e=>this.setLoadedImage(e)).catch(e=>this.loadImageError(e))}setLoadedImage(t){this.loadedImage=t,this.safeImgDataUrl=this.sanitizer.bypassSecurityTrustResourceUrl(t.transformed.objectUrl),this.cd.markForCheck()}loadImageError(t){console.error(t),this.loadImageFailed.emit()}imageLoadedInView(){null!=this.loadedImage&&(this.imageLoaded.emit(this.loadedImage),this.setImageMaxSizeRetries=0,setTimeout(()=>this.checkImageMaxSizeRecursively()))}checkImageMaxSizeRecursively(){this.setImageMaxSizeRetries>40?this.loadImageFailed.emit():this.sourceImageLoaded()?(this.setMaxSize(),this.setCropperScaledMinSize(),this.setCropperScaledMaxSize(),this.resetCropperPosition(),this.cropperReady.emit(Object.assign({},this.maxSize)),this.cd.markForCheck()):(this.setImageMaxSizeRetries++,setTimeout(()=>this.checkImageMaxSizeRecursively(),50))}sourceImageLoaded(){var t,e;return(null===(e=null===(t=this.sourceImage)||void 0===t?void 0:t.nativeElement)||void 0===e?void 0:e.offsetWidth)>0}onResize(){!this.loadedImage||(this.hidden?this.resizedWhileHidden=!0:(this.resizeCropperPosition(),this.setMaxSize(),this.setCropperScaledMinSize(),this.setCropperScaledMaxSize()))}activatePinchGesture(){var t;return(0,s.mG)(this,void 0,void 0,function*(){yield null===(t=this.hammerLoader)||void 0===t?void 0:t.call(this);const e=(null==window?void 0:window.Hammer)||null;if(e){const i=new e(this.wrapper.nativeElement);i.get("pinch").set({enable:!0}),i.on("pinchmove",this.onPinch.bind(this)),i.on("pinchend",this.pinchStop.bind(this)),i.on("pinchstart",this.startPinch.bind(this))}else(0,a.X6Q)()&&console.warn("[NgxImageCropper] Could not find HammerJS - Pinch Gesture won't work")})}resizeCropperPosition(){const t=this.sourceImage.nativeElement;(this.maxSize.width!==t.offsetWidth||this.maxSize.height!==t.offsetHeight)&&(this.cropper.x1=this.cropper.x1*t.offsetWidth/this.maxSize.width,this.cropper.x2=this.cropper.x2*t.offsetWidth/this.maxSize.width,this.cropper.y1=this.cropper.y1*t.offsetHeight/this.maxSize.height,this.cropper.y2=this.cropper.y2*t.offsetHeight/this.maxSize.height)}resetCropperPosition(){this.cropperPositionService.resetCropperPosition(this.sourceImage,this.cropper,this.settings),this.doAutoCrop(),this.imageVisible=!0}keyboardAccess(t){this.changeKeyboardStepSize(t),this.keyboardMoveCropper(t)}changeKeyboardStepSize(t){const e=+t.key;e>=1&&e<=9&&(this.settings.stepSize=e)}keyboardMoveCropper(t){if(!["ArrowUp","ArrowDown","ArrowRight","ArrowLeft"].includes(t.key))return;const i=t.shiftKey?l.Resize:l.Move,o=t.altKey?function D(c){switch(c){case"ArrowUp":return"bottom";case"ArrowRight":return"left";case"ArrowDown":return"top";default:return"right"}}(t.key):function C(c){switch(c){case"ArrowUp":return"top";case"ArrowRight":return"right";case"ArrowDown":return"bottom";default:return"left"}}(t.key),n=function Q(c,h){switch(c){case"ArrowUp":return{clientX:0,clientY:-1*h};case"ArrowRight":return{clientX:h,clientY:0};case"ArrowDown":return{clientX:0,clientY:h};default:return{clientX:-1*h,clientY:0}}}(t.key,this.settings.stepSize);t.preventDefault(),t.stopPropagation(),this.startMove({clientX:0,clientY:0},i,o),this.moveImg(n),this.moveStop()}startMove(t,e,i=null){var o,n;this.disabled||(null===(o=this.moveStart)||void 0===o?void 0:o.active)&&(null===(n=this.moveStart)||void 0===n?void 0:n.type)===l.Pinch||e===l.Drag&&!this.allowMoveImage||(t.preventDefault&&t.preventDefault(),this.moveStart=Object.assign({active:!0,type:e,position:i,transform:Object.assign({},this.transform),clientX:this.cropperPositionService.getClientX(t),clientY:this.cropperPositionService.getClientY(t)},this.cropper))}startPinch(t){!this.safeImgDataUrl||(t.preventDefault&&t.preventDefault(),this.moveStart=Object.assign({active:!0,type:l.Pinch,position:"center",clientX:this.cropper.x1+(this.cropper.x2-this.cropper.x1)/2,clientY:this.cropper.y1+(this.cropper.y2-this.cropper.y1)/2},this.cropper))}moveImg(t){var e,i;if(this.moveStart.active){if(t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),this.moveStart.type===l.Move)this.cropperPositionService.move(t,this.moveStart,this.cropper),this.checkCropperPosition(!0);else if(this.moveStart.type===l.Resize)!this.cropperStaticWidth&&!this.cropperStaticHeight&&this.cropperPositionService.resize(t,this.moveStart,this.cropper,this.maxSize,this.settings),this.checkCropperPosition(!1);else if(this.moveStart.type===l.Drag){const o=this.cropperPositionService.getClientX(t)-this.moveStart.clientX,n=this.cropperPositionService.getClientY(t)-this.moveStart.clientY;this.transform=Object.assign(Object.assign({},this.transform),{translateH:((null===(e=this.moveStart.transform)||void 0===e?void 0:e.translateH)||0)+o,translateV:((null===(i=this.moveStart.transform)||void 0===i?void 0:i.translateV)||0)+n}),this.setCssTransform()}this.cd.detectChanges()}}onPinch(t){this.moveStart.active&&(t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),this.moveStart.type===l.Pinch&&(this.cropperPositionService.resize(t,this.moveStart,this.cropper,this.maxSize,this.settings),this.checkCropperPosition(!1)),this.cd.detectChanges())}setMaxSize(){if(this.sourceImage){const t=this.sourceImage.nativeElement;this.maxSize.width=t.offsetWidth,this.maxSize.height=t.offsetHeight,this.marginLeft=this.sanitizer.bypassSecurityTrustStyle("calc(50% - "+this.maxSize.width/2+"px)")}}setCropperScaledMinSize(){var t,e;(null===(e=null===(t=this.loadedImage)||void 0===t?void 0:t.transformed)||void 0===e?void 0:e.image)?(this.setCropperScaledMinWidth(),this.setCropperScaledMinHeight()):(this.settings.cropperScaledMinWidth=20,this.settings.cropperScaledMinHeight=20)}setCropperScaledMinWidth(){this.settings.cropperScaledMinWidth=this.cropperMinWidth>0?Math.max(20,this.cropperMinWidth/this.loadedImage.transformed.image.width*this.maxSize.width):20}setCropperScaledMinHeight(){this.settings.cropperScaledMinHeight=this.maintainAspectRatio?Math.max(20,this.settings.cropperScaledMinWidth/this.aspectRatio):this.cropperMinHeight>0?Math.max(20,this.cropperMinHeight/this.loadedImage.transformed.image.height*this.maxSize.height):20}setCropperScaledMaxSize(){var t,e;if(null===(e=null===(t=this.loadedImage)||void 0===t?void 0:t.transformed)||void 0===e?void 0:e.image){const i=this.loadedImage.transformed.size.width/this.maxSize.width;this.settings.cropperScaledMaxWidth=this.cropperMaxWidth>20?this.cropperMaxWidth/i:this.maxSize.width,this.settings.cropperScaledMaxHeight=this.cropperMaxHeight>20?this.cropperMaxHeight/i:this.maxSize.height,this.maintainAspectRatio&&(this.settings.cropperScaledMaxWidth>this.settings.cropperScaledMaxHeight*this.aspectRatio?this.settings.cropperScaledMaxWidth=this.settings.cropperScaledMaxHeight*this.aspectRatio:this.settings.cropperScaledMaxWidth<this.settings.cropperScaledMaxHeight*this.aspectRatio&&(this.settings.cropperScaledMaxHeight=this.settings.cropperScaledMaxWidth/this.aspectRatio))}else this.settings.cropperScaledMaxWidth=this.maxSize.width,this.settings.cropperScaledMaxHeight=this.maxSize.height}checkCropperPosition(t=!1){this.cropper.x1<0&&(this.cropper.x2-=t?this.cropper.x1:0,this.cropper.x1=0),this.cropper.y1<0&&(this.cropper.y2-=t?this.cropper.y1:0,this.cropper.y1=0),this.cropper.x2>this.maxSize.width&&(this.cropper.x1-=t?this.cropper.x2-this.maxSize.width:0,this.cropper.x2=this.maxSize.width),this.cropper.y2>this.maxSize.height&&(this.cropper.y1-=t?this.cropper.y2-this.maxSize.height:0,this.cropper.y2=this.maxSize.height)}moveStop(){var t;this.moveStart.active&&(this.moveStart.active=!1,(null===(t=this.moveStart)||void 0===t?void 0:t.type)===l.Drag?this.transformChange.emit(this.transform):this.doAutoCrop())}pinchStop(){this.moveStart.active&&(this.moveStart.active=!1,this.doAutoCrop())}doAutoCrop(){this.autoCrop&&this.crop()}crop(t=this.settings.output){var e,i;if(null!=(null===(i=null===(e=this.loadedImage)||void 0===e?void 0:e.transformed)||void 0===i?void 0:i.image)){if(this.startCropImage.emit(),"blob"===t)return this.cropToBlob();if("base64"===t)return this.cropToBase64()}return null}cropToBlob(){const t=this.cropService.crop(this.sourceImage,this.loadedImage,this.cropper,this.settings,"blob");return t?Promise.resolve(t).then(e=>(this.imageCropped.emit(e),t)):null}cropToBase64(){const t=this.cropService.crop(this.sourceImage,this.loadedImage,this.cropper,this.settings,"base64");return t?(this.imageCropped.emit(t),t):null}aspectRatioIsCorrect(){return(this.cropper.x2-this.cropper.x1)/(this.cropper.y2-this.cropper.y1)===this.aspectRatio}}return c.\u0275fac=function(t){return new(t||c)(a.Y36(y),a.Y36(m),a.Y36(at),a.Y36(b.H7),a.Y36(a.sBO),a.Y36(b.vm,8))},c.\u0275cmp=a.Xpm({type:c,selectors:[["image-cropper"]],viewQuery:function(t,e){if(1&t&&(a.Gf(E,7),a.Gf(T,5)),2&t){let i;a.iGM(i=a.CRH())&&(e.wrapper=i.first),a.iGM(i=a.CRH())&&(e.sourceImage=i.first)}},hostVars:6,hostBindings:function(t,e){1&t&&a.NdJ("resize",function(){return e.onResize()},!1,a.Jf7)("mousemove",function(o){return e.moveImg(o)},!1,a.evT)("touchmove",function(o){return e.moveImg(o)},!1,a.evT)("mouseup",function(){return e.moveStop()},!1,a.evT)("touchend",function(){return e.moveStop()},!1,a.evT),2&t&&(a.Udp("text-align",e.alignImage),a.ekj("disabled",e.disabled)("ngx-ix-hidden",e.hidden))},inputs:{imageChangedEvent:"imageChangedEvent",imageURL:"imageURL",imageBase64:"imageBase64",imageFile:"imageFile",imageAltText:"imageAltText",cropperFrameAriaLabel:"cropperFrameAriaLabel",output:"output",format:"format",transform:"transform",maintainAspectRatio:"maintainAspectRatio",aspectRatio:"aspectRatio",resetCropOnAspectRatioChange:"resetCropOnAspectRatioChange",resizeToWidth:"resizeToWidth",resizeToHeight:"resizeToHeight",cropperMinWidth:"cropperMinWidth",cropperMinHeight:"cropperMinHeight",cropperMaxHeight:"cropperMaxHeight",cropperMaxWidth:"cropperMaxWidth",cropperStaticWidth:"cropperStaticWidth",cropperStaticHeight:"cropperStaticHeight",canvasRotation:"canvasRotation",initialStepSize:"initialStepSize",roundCropper:"roundCropper",onlyScaleDown:"onlyScaleDown",imageQuality:"imageQuality",autoCrop:"autoCrop",backgroundColor:"backgroundColor",containWithinAspectRatio:"containWithinAspectRatio",hideResizeSquares:"hideResizeSquares",allowMoveImage:"allowMoveImage",cropper:"cropper",alignImage:"alignImage",disabled:"disabled",hidden:"hidden"},outputs:{imageCropped:"imageCropped",startCropImage:"startCropImage",imageLoaded:"imageLoaded",cropperReady:"cropperReady",loadImageFailed:"loadImageFailed",transformChange:"transformChange"},features:[a.TTD],decls:5,vars:10,consts:[["wrapper",""],["class","ngx-ic-source-image","role","presentation",3,"src","visibility","transform","ngx-ic-draggable","load","mousedown","touchstart","error",4,"ngIf"],[1,"ngx-ic-overlay"],["class","ngx-ic-cropper","tabindex","0",3,"ngx-ic-round","top","left","width","height","margin-left","visibility","keydown",4,"ngIf"],["role","presentation",1,"ngx-ic-source-image",3,"src","load","mousedown","touchstart","error"],["sourceImage",""],["tabindex","0",1,"ngx-ic-cropper",3,"keydown"],["role","presentation",1,"ngx-ic-move",3,"mousedown","touchstart"],[4,"ngIf"],["role","presentation",1,"ngx-ic-resize","ngx-ic-topleft",3,"mousedown","touchstart"],[1,"ngx-ic-square"],[1,"ngx-ic-resize","ngx-ic-top"],["role","presentation",1,"ngx-ic-resize","ngx-ic-topright",3,"mousedown","touchstart"],[1,"ngx-ic-resize","ngx-ic-right"],["role","presentation",1,"ngx-ic-resize","ngx-ic-bottomright",3,"mousedown","touchstart"],[1,"ngx-ic-resize","ngx-ic-bottom"],["role","presentation",1,"ngx-ic-resize","ngx-ic-bottomleft",3,"mousedown","touchstart"],[1,"ngx-ic-resize","ngx-ic-left"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-top",3,"mousedown","touchstart"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-right",3,"mousedown","touchstart"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-bottom",3,"mousedown","touchstart"],["role","presentation",1,"ngx-ic-resize-bar","ngx-ic-left",3,"mousedown","touchstart"]],template:function(t,e){1&t&&(a.TgZ(0,"div",null,0),a.YNc(2,z,2,8,"img",1),a._UZ(3,"div",2),a.YNc(4,_,3,16,"div",3),a.qZA()),2&t&&(a.Udp("background",e.imageVisible&&e.backgroundColor),a.xp6(2),a.Q6J("ngIf",e.safeImgDataUrl),a.xp6(1),a.Udp("width",e.maxSize.width,"px")("height",e.maxSize.height,"px")("margin-left","center"===e.alignImage?e.marginLeft:null),a.xp6(1),a.Q6J("ngIf",e.imageVisible))},directives:[R.O5],styles:['[_nghost-%COMP%]{display:flex;position:relative;width:100%;max-width:100%;max-height:100%;overflow:hidden;padding:5px;text-align:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]{width:100%;position:relative}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image[_ngcontent-%COMP%]{max-width:100%;max-height:100%;transform-origin:center}[_nghost-%COMP%] > div[_ngcontent-%COMP%]   img.ngx-ic-source-image.ngx-ic-draggable[_ngcontent-%COMP%]{user-drag:none;-webkit-user-drag:none;user-select:none;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;cursor:grab}[_nghost-%COMP%]   .ngx-ic-overlay[_ngcontent-%COMP%]{position:absolute;pointer-events:none;touch-action:none;outline:var(--cropper-overlay-color, white) solid 100vw;top:0;left:0}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{position:absolute;display:flex;color:#53535c;background:transparent;outline:rgba(255,255,255,.3) solid 100vw;outline:var(--cropper-outline-color, rgba(255, 255, 255, .3)) solid 100vw;touch-action:none}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]{outline-width:100vh}}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:after{position:absolute;content:"";inset:0;pointer-events:none;border:dashed 1px;opacity:.75;color:inherit;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{width:100%;cursor:move;border:1px solid rgba(255,255,255,.5)}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]:focus   .ngx-ic-move[_ngcontent-%COMP%]{border-color:#1e90ff;border-width:2px}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]{position:absolute;display:inline-block;line-height:6px;padding:8px;opacity:.85;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%]   .ngx-ic-square[_ngcontent-%COMP%]{display:inline-block;background:#53535C;width:6px;height:6px;border:1px solid rgba(255,255,255,.5);box-sizing:content-box}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topleft[_ngcontent-%COMP%]{top:-12px;left:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-top[_ngcontent-%COMP%]{top:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-topright[_ngcontent-%COMP%]{top:-12px;right:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-right[_ngcontent-%COMP%]{top:calc(50% - 12px);right:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomright[_ngcontent-%COMP%]{bottom:-12px;right:-12px;cursor:nwse-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-12px;left:calc(50% - 12px);cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-bottomleft[_ngcontent-%COMP%]{bottom:-12px;left:-12px;cursor:nesw-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize.ngx-ic-left[_ngcontent-%COMP%]{top:calc(50% - 12px);left:-12px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%]{position:absolute;z-index:1}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-top[_ngcontent-%COMP%]{top:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-right[_ngcontent-%COMP%]{top:11px;right:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-bottom[_ngcontent-%COMP%]{bottom:-11px;left:11px;width:calc(100% - 22px);height:22px;cursor:ns-resize}[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar.ngx-ic-left[_ngcontent-%COMP%]{top:11px;left:-11px;height:calc(100% - 22px);width:22px;cursor:ew-resize}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]{outline-color:transparent}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{border-radius:100%;box-shadow:0 0 0 100vw #ffffff4d;box-shadow:0 0 0 100vw var(--cropper-outline-color, rgba(255, 255, 255, .3))}@media (orientation: portrait){[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]:after{box-shadow:0 0 0 100vh #ffffff4d;box-shadow:0 0 0 100vh var(--cropper-outline-color, rgba(255, 255, 255, .3))}}[_nghost-%COMP%]   .ngx-ic-cropper.ngx-ic-round[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{border-radius:100%}.disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-resize-bar[_ngcontent-%COMP%], .disabled[_nghost-%COMP%]   .ngx-ic-cropper[_ngcontent-%COMP%]   .ngx-ic-move[_ngcontent-%COMP%]{display:none}.ngx-ix-hidden[_nghost-%COMP%]{display:none}'],changeDetection:0}),c})(),rt=(()=>{class c{}return c.\u0275fac=function(t){return new(t||c)},c.\u0275mod=a.oAB({type:c}),c.\u0275inj=a.cJS({imports:[[R.ez]]}),c})()},15861:(J,U,v)=>{function s(b,R,E,T,z,g,_){try{var d=b[g](_),l=d.value}catch(C){return void E(C)}d.done?R(l):Promise.resolve(l).then(T,z)}function a(b){return function(){var R=this,E=arguments;return new Promise(function(T,z){var g=b.apply(R,E);function _(l){s(g,T,z,_,d,"next",l)}function d(l){s(g,T,z,_,d,"throw",l)}_(void 0)})}}v.d(U,{Z:()=>a})}}]);
(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_blocker_visa-blockers-configuration_configurations_module_ts-node_modules_mom-ab5844"],{44343:(J,E,c)=>{"use strict";c.r(E),c.d(E,{ConfigurationsModule:()=>Bt});var p=c(69808),f=c(88476),C=c(1402),l=c(62764),g=c(65868),k=c(45834),r=c(93075),h=c(82599),T=c(69202),w=c(43687),V=c(26523),P=c(28172),N=c(4882),D=c(42002),y=c(34378),z=c(58015),m=c(97582),M=c(63900),O=c(18505),t=c(5e3),u=c(43604),$=c(40520);let A=(()=>{class i{constructor(n){this._http=n,this.getAllBlockerConfigurations=()=>this._http.get(u.b.getAllBlockerConfigurations),this.getBlocker=s=>this._http.get(`${u.b.getBlocker}/${s}`),this.changeConfigStatus=(s,a)=>this._http.post(`${u.b.changeConfigStatus}/${s}`,{},{params:{status:a}}),this.getVisaStepsByProcessType=s=>this._http.get(u.b.getVisaStepsByProcessType,{params:Object.assign({},s)}),this.getVisaStepThresholds=()=>this._http.get(u.b.getVisaStepThresholds),this.createVisaStepThreshold=s=>this._http.post(u.b.createVisaStepThreshold,s),this.createVisaStepThresholdList=s=>this._http.post(u.b.createVisaStepThresholds,s),this.createBlockerConfig=s=>this._http.post(u.b.createBlockerConfiguration,s),this.updateBlockerConfiguration=(s,a)=>this._http.post(`${u.b.updateBlockerConfiguration}/${s}`,a),this.getTmpMsgByNameAndChannelType=(s="Email")=>this._http.get(u.b.getTmpMsgByNameAndChannelType,{params:{channelType:s}})}}return i.\u0275fac=function(n){return new(n||i)(t.LFG($.eN))},i.\u0275prov=t.Yz7({token:i,factory:i.\u0275fac,providedIn:"root"}),i})();var Z,S,j,I=c(61135),G=c(77579);const q={search:""};class _{constructor(){Z.set(this,new I.X(0)),S.set(this,new G.x),j.set(this,new I.X([])),this.refreshData$=(0,m.Q_)(this,Z,"f").asObservable(),this.search$=(0,m.Q_)(this,S,"f").asObservable(),this.AffectedVisaSteps$=(0,m.Q_)(this,j,"f").asObservable()}refreshData(){(0,m.Q_)(this,Z,"f").next(1)}updateSearchState(e){(0,m.Q_)(this,S,"f").next(e)}resetSearchState(){(0,m.Q_)(this,S,"f").next(q)}updateAffectedVisaStepsState(e){const n=[...e].filter((s,a,o)=>a===o.findIndex(d=>d.visaProcessType===s.visaProcessType&&d.visaStepName===s.visaStepName));(0,m.Q_)(this,j,"f").next(n)}resetAffectedVisaStepsState(){(0,m.Q_)(this,j,"f").next([])}}function R(i,e){1&i&&(t.TgZ(0,"span")(1,"cc-icon",9),t._uU(2,"check_circle"),t.qZA()())}function H(i,e){1&i&&(t.TgZ(0,"span")(1,"cc-icon",10),t._uU(2,"cancel"),t.qZA()())}function W(i,e){if(1&i&&(t.YNc(0,R,3,0,"span",8),t.YNc(1,H,3,0,"span",8)),2&i){const n=e.$implicit;t.Q6J("ngIf",n.addEmail),t.xp6(1),t.Q6J("ngIf",!n.addEmail)}}function X(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"button",15),t.NdJ("click",function(){t.CHM(n);const a=t.oxw().$implicit;return t.oxw(2).changeConfigStatus(a.id,!a.active)}),t._uU(1," Disable "),t.qZA()}}function K(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"button",16),t.NdJ("click",function(){t.CHM(n);const a=t.oxw().$implicit;return t.oxw(2).changeConfigStatus(a.id,!a.active)}),t._uU(1," Enable "),t.qZA()}}function tt(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"div",11)(1,"button",12),t.NdJ("click",function(){const o=t.CHM(n).$implicit;return t.oxw(2).navToEditBlockerPage(o.id)}),t._uU(2," Edit "),t.qZA(),t.YNc(3,X,2,0,"button",13),t.YNc(4,K,2,0,"button",14),t.qZA()}if(2&i){const n=e.$implicit;t.xp6(3),t.Q6J("ngIf",n.active),t.xp6(1),t.Q6J("ngIf",!n.active)}}Z=new WeakMap,S=new WeakMap,j=new WeakMap,_.\u0275fac=function(e){return new(e||_)},_.\u0275prov=t.Yz7({token:_,factory:_.\u0275fac,providedIn:"root"});const et=function(i,e){return{addEmail:i,actions:e}};function nt(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"div",1)(1,"div",2)(2,"button",3),t.NdJ("click",function(){return t.CHM(n),t.oxw().navToAddNewBlockerPage()}),t._uU(3," Create Visa Blocker "),t.qZA()(),t._UZ(4,"cc-datagrid",4),t.YNc(5,W,2,2,"ng-template",5,6,t.W1O),t.YNc(7,tt,5,2,"ng-template",5,7,t.W1O),t.qZA()}if(2&i){const n=e.ngIf,s=t.MAs(6),a=t.MAs(8),o=t.oxw();t.xp6(4),t.Q6J("columns",o.gridCols)("data",n)("showPaginator",!1)("loading",!1)("cellTemplate",t.WLB(7,et,s,a)),t.xp6(1),t.Q6J("ccGridCell",n),t.xp6(2),t.Q6J("ccGridCell",n)}}const it=[{field:"name",header:"Blocker Name",width:"20%"},{field:"blockerType.label",header:"Type",width:"20%"},{field:"arrivalEstimatedDaysNum",header:"Days to Resolve",width:"20%",formatter:i=>+i.arrivalEstimatedDaysNum+" Days"},{field:"addEmail",header:"Email",width:"20%"},{field:"actions",header:"Actions",width:"20%"}];let x=class{constructor(e,n,s,a){this.apiService=e,this.store=n,this.router=s,this.dialog=a,this.gridCols=it}ngOnInit(){this.data$=this.store.refreshData$.pipe((0,M.w)(()=>this.apiService.getAllBlockerConfigurations()))}navToAddNewBlockerPage(){this.router.navigateByUrl(["visa","v2","blocker","configurations","add-new-blocker"].join("/"))}navToEditBlockerPage(e){this.router.navigateByUrl(["visa","v2","blocker","configurations","edit-blocker",e].join("/"))}changeConfigStatus(e,n){this.apiService.changeConfigStatus(e,n).pipe((0,O.b)(()=>this.store.refreshData())).subscribe()}};x.\u0275fac=function(e){return new(e||x)(t.Y36(A),t.Y36(_),t.Y36(C.F0),t.Y36(h.uY))},x.\u0275cmp=t.Xpm({type:x,selectors:[["visa-blockers"]],decls:2,vars:3,consts:[["class","my-3",4,"ngIf"],[1,"my-3"],[1,"d-flex","col-12","justify-content-end","justify-content-md-end","gap-2","pr-0","mt-5","mb-3"],["cc-raised-button","","color","accent",3,"click"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["emailTmp",""],["actionsTmp",""],[4,"ngIf"],[1,"text-success"],[1,"text-danger"],[1,"d-flex","flex-column","flex-md-row","gap-1"],["cc-flat-button","","color","accent",3,"click"],["cc-flat-button","","style","background-color: #7a7a7a; color: #fff;",3,"click",4,"ngIf"],["cc-flat-button","","style","background-color: #008000c7; color: #fff",3,"click",4,"ngIf"],["cc-flat-button","",2,"background-color","#7a7a7a","color","#fff",3,"click"],["cc-flat-button","",2,"background-color","#008000c7","color","#fff",3,"click"]],template:function(e,n){1&e&&(t.YNc(0,nt,9,10,"div",0),t.ALo(1,"async")),2&e&&t.Q6J("ngIf",t.lcZ(1,1,n.data$))},directives:[p.O5,g.uu,l.Ge,l.VC,k.Q9],pipes:[p.Ov],encapsulation:2,changeDetection:0}),x=(0,m.gn)([f.kG],x);var st=c(60515),at=c(39841),ot=c(54482);const{isArray:ct}=Array;function rt(i){return 1===i.length&&ct(i[0])?i[0]:i}var lt=c(83268),dt=c(89635),pt=c(63269);function Y(...i){const e=(0,pt.jO)(i);return e?(0,dt.z)(Y(...i),(0,lt.Z)(e)):(0,ot.e)((n,s)=>{(0,at.l)([n,...rt(i)])(s)})}function mt(...i){return Y(...i)}var L=c(54004),ut=c(15439);function U(i){return i.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())}var gt=c(8188);let F=(()=>{class i{constructor(n,s){this.apiService=n,this.picklist=s,this.visaProcessTypeOpts=["newRequest","renewRequest","cancelRequest"].map(a=>({id:a,text:U(a)})),this.blockerTypeOpts=this.picklist.getPicklist({code:"visa_blocker_type"}).pipe((0,L.U)(a=>a.map(o=>({id:o.id,text:o.label})))),this.addConfigMsgContentParams=["@arrivalEstimatedDate@","@blocker_steps@","@blocker_name@"].map(a=>({id:a,text:a}))}}return i.\u0275fac=function(n){return new(n||i)(t.LFG(A),t.LFG(gt.Ab))},i.\u0275prov=t.Yz7({token:i,factory:i.\u0275fac,providedIn:"root"}),i})();var ht=c(48966);function _t(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"form",19,20)(2,"cc-checkbox",21,22),t.NdJ("ngModelChange",function(a){return t.CHM(n).$implicit.isSelected=a})("ngModelChange",function(){const o=t.CHM(n).$implicit;return t.oxw(2).onSelectStep(o)}),t.qZA()()}if(2&i){const n=e.$implicit,s=e.index;t.xp6(2),t.Q6J("ngModel",n.isSelected)("name",n.workFlowId+s+n.taskName+n.id)}}function vt(i,e){if(1&i&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&i){const n=e.index;t.xp6(1),t.Oqu(n+1)}}const Ct=function(i,e){return{select:i,index:e}};function xt(i,e){if(1&i&&(t.ynx(0),t._UZ(1,"cc-datagrid",15),t.YNc(2,_t,4,2,"ng-template",16,17,t.W1O),t.YNc(4,vt,2,1,"ng-template",16,18,t.W1O),t.BQk()),2&i){const n=e.ngIf,s=t.MAs(3),a=t.MAs(5),o=t.oxw();t.xp6(1),t.Q6J("columns",o.gridCols)("data",n)("showPaginator",!1)("loading",!1)("cellTemplate",t.WLB(7,Ct,s,a)),t.xp6(1),t.Q6J("ccGridCell",n),t.xp6(2),t.Q6J("ccGridCell",n)}}const bt=[{field:"select",header:"Select"},{field:"index",header:"Index"},{field:"workFlowId",header:"Visa Process Type",formatter:i=>U(i.workFlowId)},{field:"taskLabel",header:"Visa Step"}];let b=class{constructor(e,n,s,a,o,d){this.apiService=e,this.store=n,this.dataService=s,this.dialogRef=a,this.cdr=o,this.fb=d,this.gridCols=bt,this.processTypeOpts=this.dataService.visaProcessTypeOpts,this.affectedVisaSteps=[],this.form=this.fb.group({processType:"",search:""})}ngOnInit(){this.data$=this.form.valueChanges.pipe((0,M.w)(({processType:e,search:n})=>e?this.apiService.getVisaStepsByProcessType({processType:e,search:n}):st.E),mt(this.store.AffectedVisaSteps$),(0,L.U)(([e,n])=>this.updateStepSelectionState(e,n))),this.store.AffectedVisaSteps$.subscribe(e=>{this.affectedVisaSteps=e||[],this.cdr.detectChanges()})}onSelectStep(e){e.isSelected?this.addStep(e):this.removeStep(e)}onSave(){this.store.updateAffectedVisaStepsState(this.affectedVisaSteps),this.dialogRef.close()}addStep(e){this.affectedVisaSteps=[...this.affectedVisaSteps,{id:e.id,visaProcessType:e.workFlowId,visaStepName:e.taskLabel}],this.cdr.detectChanges()}removeStep(e){this.affectedVisaSteps=this.affectedVisaSteps.filter(n=>n.visaStepName!==e.taskLabel&&n.visaProcessType!==e.workFlowId),this.cdr.detectChanges()}updateStepSelectionState(e,n){return e.map(s=>{const a=n.some(o=>o.visaStepName===s.taskLabel&&o.visaProcessType===s.workFlowId);return Object.assign(Object.assign({},s),{isSelected:a})})}};b.\u0275fac=function(e){return new(e||b)(t.Y36(A),t.Y36(_),t.Y36(F),t.Y36(ht.so),t.Y36(t.sBO),t.Y36(r.qu))},b.\u0275cmp=t.Xpm({type:b,selectors:[["app-include-visa-steps"]],decls:26,vars:6,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title",""],["cc-dialog-content",""],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[2,"margin-right","2px"],[1,"row","d-flex","justify-content-around","px-4",3,"formGroup"],[1,"col-md-6"],["label","Visa Process Type","formControlName","processType",3,"required","data"],["label","Visa Step Name","formControlName","search"],[4,"ngIf"],["cc-dialog-actions",""],["cc-flat-button","","cc-dialog-close",""],["cc-raised-button","","color","primary",3,"click"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["selectTmp",""],["indexTmp",""],[1,"visa-step-checkbox","mb-0"],["form","ngForm"],[3,"ngModel","name","ngModelChange"],["m","ngModel"]],template:function(e,n){1&e&&(t.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),t._uU(3,"Include Visa Steps"),t.qZA()(),t.TgZ(4,"div",3)(5,"cc-accordion")(6,"cc-panel",4)(7,"cc-panel-title",5)(8,"cc-icon",6),t._uU(9,"filter_alt"),t.qZA(),t.TgZ(10,"span"),t._uU(11,"Filter By Visa Process Type"),t.qZA()(),t.TgZ(12,"cc-panel-body")(13,"div")(14,"form",7)(15,"div",8),t._UZ(16,"cc-select",9),t.qZA(),t.TgZ(17,"div",8),t._UZ(18,"cc-input",10),t.qZA()()()()()(),t.YNc(19,xt,6,10,"ng-container",11),t.ALo(20,"async"),t.qZA(),t.TgZ(21,"div",12)(22,"button",13),t._uU(23,"Cancel"),t.qZA(),t.TgZ(24,"button",14),t.NdJ("click",function(){return n.onSave()}),t._uU(25," Save Changes "),t.qZA()()()),2&e&&(t.xp6(14),t.Q6J("formGroup",n.form),t.xp6(2),t.Q6J("required",!0)("data",n.processTypeOpts),t.xp6(3),t.Q6J("ngIf",t.lcZ(20,4,n.data$)))},directives:[h.iK,h.Cj,h.Zb,h.kL,y.I,y.CW,y.LL,k.Q9,y.G9,r._Y,r.JL,r.sg,V.jB,r.JJ,r.u,r.Q7,w.G,p.O5,l.Ge,l.VC,r.F,N.E,r.On,h.Zu,g.uu,h.zn],pipes:[p.Ov],encapsulation:2,changeDetection:0}),b=(0,m.gn)([f.kG],b);var Tt=c(21799),Q=c(11523);function yt(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"div",2)(1,"p",3)(2,"span"),t._uU(3),t.qZA(),t.TgZ(4,"a",4),t.NdJ("click",function(){const o=t.CHM(n).index;return t.oxw().onDelete(o)}),t.O4$(),t.TgZ(5,"svg",5),t._UZ(6,"path",6),t.qZA()()()()}if(2&i){const n=e.$implicit;t.xp6(3),t.Oqu(n)}}let St=(()=>{class i{constructor(n){this.cdr=n,this.valueChanges=new t.vpe}ngOnInit(){}onDelete(n){this.items=[...this.items].filter((s,a)=>a!==n),this.cdr.detectChanges(),this.valueChanges.emit(this.items)}}return i.\u0275fac=function(n){return new(n||i)(t.Y36(t.sBO))},i.\u0275cmp=t.Xpm({type:i,selectors:[["chips"]],inputs:{items:"items"},outputs:{valueChanges:"valueChanges"},decls:2,vars:1,consts:[[1,"d-flex","flex-wrap","gap-1"],["class","d-block px-2 mx-1 my-1 rounded-pill","style","box-sizing: border-box; height: 1.8em; background: #80808092;'",4,"ngFor","ngForOf"],[1,"d-block","px-2","mx-1","my-1","rounded-pill",2,"box-sizing","border-box","height","1.8em","background","#80808092"],[1,"d-flex","gap-1","align-items-center",2,"line-height","1.6"],[3,"click"],["width","12px","height","12px","fill","red","viewBox","0 0 512 512","xmlns","http://www.w3.org/2000/svg"],["d","M289.94,256l95-95A24,24,0,0,0,351,127l-95,95-95-95A24,24,0,0,0,127,161l95,95-95,95A24,24,0,1,0,161,385l95-95,95,95A24,24,0,0,0,385,351Z"]],template:function(n,s){1&n&&(t.TgZ(0,"div",0),t.YNc(1,yt,7,1,"div",1),t.qZA()),2&n&&(t.xp6(1),t.Q6J("ngForOf",s.items))},directives:[p.sg],encapsulation:2,changeDetection:0}),i})();function jt(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"button",21),t.NdJ("click",function(){const o=t.CHM(n).index;return t.oxw(2).onDeleteStep(o)}),t.TgZ(1,"cc-icon"),t._uU(2,"delete"),t.qZA()()}}function kt(i,e){if(1&i&&(t.TgZ(0,"span"),t._uU(1),t.qZA()),2&i){const n=e.index;t.xp6(1),t.Oqu(n+1)}}const At=function(i,e){return{actions:i,index:e}};function Zt(i,e){if(1&i&&(t.TgZ(0,"cc-card-content"),t._UZ(1,"cc-datagrid",17),t.YNc(2,jt,3,0,"ng-template",18,19,t.W1O),t.YNc(4,kt,2,1,"ng-template",18,20,t.W1O),t.qZA()),2&i){const n=e.ngIf,s=t.MAs(3),a=t.MAs(5),o=t.oxw();t.xp6(1),t.Q6J("columns",o.gridCols)("data",n)("showPaginator",!1)("loading",!1)("cellTemplate",t.WLB(7,At,s,a)),t.xp6(1),t.Q6J("ccGridCell",n),t.xp6(2),t.Q6J("ccGridCell",n)}}function Et(i,e){1&i&&t._UZ(0,"div",32)}function wt(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"cc-select",33),t.NdJ("valueChange",function(a){return t.CHM(n),t.oxw(2).injectParameterIntoContent(a)}),t.qZA()}if(2&i){const n=t.oxw(2);t.Q6J("data",n.parameterTypesOptions)}}function Vt(i,e){if(1&i){const n=t.EpF();t.TgZ(0,"div",34)(1,"button",35),t.NdJ("click",function(){return t.CHM(n),t.oxw(2).onCancelContentEdit()}),t._uU(2,"Cancel"),t.qZA(),t.TgZ(3,"button",36),t.NdJ("click",function(){t.CHM(n);const a=t.oxw(2);return a.onSaveContentEdit(a.form.value.emailContent)}),t._uU(4," Save "),t.qZA()()}}function Nt(i,e){if(1&i){const n=t.EpF();t.ynx(0),t.TgZ(1,"div",2)(2,"div")(3,"chips",22),t.NdJ("valueChanges",function(a){return t.CHM(n),t.oxw().onRecipientsChanges(a)}),t.qZA()(),t.TgZ(4,"div")(5,"cc-input",23),t.NdJ("keydown",function(a){return t.CHM(n),t.oxw().onAddRecipients(a)}),t.qZA()()(),t.TgZ(6,"div",2),t._UZ(7,"cc-textarea",24),t.qZA(),t.TgZ(8,"div",2)(9,"a",25),t.NdJ("click",function(){return t.CHM(n),t.oxw().onEditEmailContent()}),t._uU(10,"Edit Message Content"),t.qZA(),t.TgZ(11,"div",26),t.YNc(12,Et,1,0,"div",27),t.TgZ(13,"div",28),t.YNc(14,wt,1,1,"cc-select",29),t.TgZ(15,"cc-textarea",30),t.NdJ("click",function(a){return t.CHM(n),t.oxw().setCurrentCursorIndex(a)}),t.qZA()(),t.YNc(16,Vt,5,0,"div",31),t.qZA()(),t.BQk()}if(2&i){const n=t.oxw();t.xp6(3),t.Q6J("items",n.recipients),t.xp6(4),t.Q6J("required",!0),t.xp6(5),t.Q6J("ngIf",!n.editEmailContent),t.xp6(2),t.Q6J("ngIf",n.editEmailContent),t.xp6(1),t.Q6J("required",!0),t.xp6(1),t.Q6J("ngIf",n.editEmailContent)}}const Mt=[{field:"index",header:"#"},{field:"visaProcessType",header:"Visa Process Type",formatter:i=>i.visaProcessType.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase()).trim()},{field:"visaStepName",header:"Step"},{field:"actions",header:"Actions"}];let v=class{constructor(e,n,s,a,o,d,B,Jt,Dt){this._store=e,this._dataService=n,this._notification=s,this._apiService=a,this._fb=o,this._dialog=d,this._router=B,this._route=Jt,this._cdr=Dt,this.gridCols=Mt,this.blockerTypeOpts=this._dataService.blockerTypeOpts,this.parameterTypesOptions=this._dataService.addConfigMsgContentParams,this.isAddEmail$=new I.X(!0),this.affectedVisaSteps=[],this.recipients=[],this.page="Add",this.editEmailContent=!1,this.defaultEmailContent="",this.updateEmailContent=!1,this.form=this._fb.group({name:"",blockerType:"",arrivalEstimatedDaysNum:"",recipients:"",emailTitle:"",emailContent:"",addEmail:!0,paramType:""})}ngOnInit(){this.setupPage(),this.loadVisaSteps(),this.setupAddEmailListener();const e=this.form.get("addEmail").valueChanges,s=["emailContent","emailTitle"].map(a=>this.form.get(a));e.subscribe(a=>{s.forEach(a?o=>{o.addValidators(r.kI.required),o.updateValueAndValidity()}:o=>{o.clearValidators(),o.updateValueAndValidity()})})}setCurrentCursorIndex(e){var n;this.cursorIndex=null!==(n=e.target.selectionStart)&&void 0!==n?n:0}injectParameterIntoContent(e){var n,s;if(e){const a=null===(n=this.form.get("emailContent"))||void 0===n?void 0:n.value,o=this.cursorIndex||(null==a?void 0:a.length),d=/\s|\n/.test(a[o-1])?"":" ",B=" "===a[o]?"":" ";this.form.patchValue({emailContent:`${a.slice(0,o)}${d}${e}${B}${a.slice(o)}`})}null===(s=this.form.get("paramType"))||void 0===s||s.reset("")}openIncludeStepsDialog(){this._dialog.originalOpen(b,{panelClass:["col-md-10"],data:{}})}onEditEmailContent(){this.editEmailContent=!this.editEmailContent,this._cdr.detectChanges()}onCancelContentEdit(){this.editEmailContent=!1,this.updateEmailContent=!1,this.form.patchValue({emailContent:this.defaultEmailContent}),this._cdr.detectChanges()}onSaveContentEdit(e){this.editEmailContent=!1,this.updateEmailContent=this.defaultEmailContent!==e,this._cdr.detectChanges()}onSave(){this.affectedVisaSteps.length?!this.isAddEmail$.getValue()||this.recipients.length?this.blockerId?this.update():this.add():this._notification.notifyError("You have to input at least one recipient"):this._notification.notifyError("You have to select at least one visa step.")}add(){let n=this.form.value.addEmail?this.mapFullBlockerDto():this.mapBasicBlockerDto();this._apiService.createBlockerConfig(n).subscribe(s=>{"success"===s.toLocaleLowerCase()?(this._notification.notifySuccess("Added Successfully"),this._router.navigateByUrl("visa/v2/blocker/configurations"),this._store.refreshData()):this._notification.notifyError(s)})}update(){let n=this.form.value.addEmail?this.mapFullBlockerDto():this.mapBasicBlockerDto();this._apiService.updateBlockerConfiguration(this.blockerId,n).subscribe(s=>{"success"===s.toLocaleLowerCase()?(this._notification.notifySuccess("Updated Successfully"),this._router.navigateByUrl("visa/v2/blocker/configurations"),this._store.refreshData()):this._notification.notifyError(s)})}onCancel(){this._router.navigateByUrl("visa/v2/blocker/configurations")}onDeleteStep(e){this.affectedVisaSteps=this.affectedVisaSteps.filter((n,s)=>s!==e),this._store.updateAffectedVisaStepsState(this.affectedVisaSteps),this._cdr.detectChanges()}onAddRecipients(e){const n=this.form.get("recipients"),s=function ft(i){return!!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(i.value)}(n);("Enter"===e.key||" "===e.key)&&(e.preventDefault(),n.value&&s?(this.recipients=[...this.recipients,n.value],this._cdr.detectChanges()):this._notification.notifyError("Please Enter A Valid Email Address."),n.reset())}onRecipientsChanges(e){this.recipients=e,this._cdr.detectChanges()}ngOnDestroy(){this._store.resetAffectedVisaStepsState()}setupPage(){this.blockerId=+this._route.snapshot.paramMap.get("id"),this.loadEmailContent(),this._store.resetAffectedVisaStepsState(),this.blockerId&&(this.page="Edit",this._store.refreshData$.pipe((0,M.w)(()=>this._apiService.getBlocker(this.blockerId))).subscribe(e=>{const n=e.visaBlockerSteps.map(s=>({id:s.workFlowTask,visaProcessType:s.visaProcessType.toLowerCase()+"Request",visaStepName:s.visaStepName}));this._store.updateAffectedVisaStepsState(n),this.populateFormFields(e),this._cdr.detectChanges()}))}loadVisaSteps(){this._store.AffectedVisaSteps$.subscribe({next:e=>{this.affectedVisaSteps=e,this._cdr.detectChanges()}})}setupAddEmailListener(){var e;null===(e=this.form.get("addEmail"))||void 0===e||e.valueChanges.pipe((0,O.b)(n=>this.isAddEmail$.next(n))).subscribe()}populateFormFields(e){this.form.patchValue({name:e.name,blockerType:e.blockerType.id,arrivalEstimatedDaysNum:e.arrivalEstimatedDaysNum,addEmail:e.addEmail}),this.patchEmailFields(e)}patchEmailFields(e){this.recipients=e.recipients?e.recipients.split(","):[],this.form.patchValue({emailTitle:e.emailTitle})}mapBasicBlockerDto(){var e;return{name:this.form.value.name,arrivalEstimatedDaysNum:this.form.value.arrivalEstimatedDaysNum,blockerType:{id:this.form.value.blockerType},addEmail:this.form.value.addEmail,visaBlockerSteps:null===(e=this.affectedVisaSteps)||void 0===e?void 0:e.map(n=>({visaProcessType:n.visaProcessType,workFlowTask:{id:n.id}}))}}mapFullBlockerDto(){var e;return{name:this.form.value.name,arrivalEstimatedDaysNum:this.form.value.arrivalEstimatedDaysNum,blockerType:{id:this.form.value.blockerType},addEmail:this.form.value.addEmail,visaBlockerSteps:null===(e=this.affectedVisaSteps)||void 0===e?void 0:e.map(n=>({visaProcessType:n.visaProcessType,workFlowTask:{id:n.id}})),recipients:this.recipients?this.recipients.join(","):"",emailTitle:this.form.value.emailTitle,emailContent:this.form.value.emailContent,updateEmailTemplate:this.updateEmailContent}}loadEmailContent(){this._apiService.getTmpMsgByNameAndChannelType().subscribe(e=>{e&&(this.defaultEmailContent=e,this.form.patchValue({emailContent:e}))})}formatDate(e){return e?ut(e).format("YYYY-MM-DD"):""}};v.\u0275fac=function(e){return new(e||v)(t.Y36(_),t.Y36(F),t.Y36(Tt.zg),t.Y36(A),t.Y36(r.qu),t.Y36(h.uY),t.Y36(C.F0),t.Y36(C.gz),t.Y36(t.sBO))},v.\u0275cmp=t.Xpm({type:v,selectors:[["ng-component"]],decls:34,vars:13,consts:[[1,"my-2"],[1,"visa-blocker","d-flex","flex-column","align-items-center","mt-5",3,"formGroup"],[1,"col-md-8"],["label","Blocker Name","formControlName","name","required","true"],["label","Blocker Type","formControlName","blockerType","required","true",3,"data"],["label","EDA to be Solved","formControlName","arrivalEstimatedDaysNum",3,"symbol","required"],[1,"d-flex","flex-column","align-items-center","my-5"],[1,"d-flex","justify-content-between","col-12","px-0"],[1,"font-weight-bold"],["cc-raised-button","","color","accent",1,"d-flex","align-items-center",2,"box-sizing","border-box","height","2.5rem",3,"click"],[2,"font-size","36px","margin-right","1px"],[4,"ngIf"],[1,"d-flex","flex-column","align-items-center",3,"formGroup"],["formControlName","addEmail"],[1,"d-flex","justify-content-end","gap-2","col-12","my-4","px-4"],["cc-button","",1,"px-5",3,"click"],["cc-raised-button","","color","accent",1,"px-5",3,"disabled","click"],[3,"columns","data","showPaginator","loading","cellTemplate"],[3,"ccGridCell"],["actionsTmp",""],["indexTmp",""],["cc-icon-button","","color","accent",3,"click"],[3,"items","valueChanges"],["label","Recipients","placeholder","Add recipients by entering an email and pressing Enter.","formControlName","recipients",3,"keydown"],["label","Email Title","formControlName","emailTitle",3,"required"],[1,"d-block","text-danger","col-12","text-right","px-0",2,"cursor","pointer",3,"click"],[2,"position","relative"],["style","position: absolute; inset: 0; z-index: 10; cursor: not-allowed",4,"ngIf"],[1,"message-content","col-12","px-0","p-3","rounded",2,"background-color","#f2f2f2da","position","relative"],["label","Add Parameter","formControlName","paramType",3,"data","valueChange",4,"ngIf"],["label","Email Content","formControlName","emailContent",3,"required","click"],["class","actions d-flex justify-content-end gap-2 mt-2",4,"ngIf"],[2,"position","absolute","inset","0","z-index","10","cursor","not-allowed"],["label","Add Parameter","formControlName","paramType",3,"data","valueChange"],[1,"actions","d-flex","justify-content-end","gap-2","mt-2"],["cc-flat-button","",3,"click"],["cc-flat-button","","color","accent",3,"click"]],template:function(e,n){1&e&&(t._UZ(0,"div",0),t.TgZ(1,"form",1)(2,"div",2),t._UZ(3,"cc-input",3),t.qZA(),t.TgZ(4,"div",2),t._UZ(5,"cc-select",4),t.ALo(6,"async"),t.qZA(),t.TgZ(7,"div",2),t.ynx(8),t._UZ(9,"cc-amount-input",5),t.BQk(),t.qZA()(),t.TgZ(10,"div",6)(11,"div",2)(12,"cc-card")(13,"cc-card-header",7)(14,"cc-card-title")(15,"span",8),t._uU(16,"Affected Visa Steps"),t.qZA()(),t.TgZ(17,"button",9),t.NdJ("click",function(){return n.openIncludeStepsDialog()}),t.TgZ(18,"cc-icon",10),t._uU(19,"add_circle"),t.qZA(),t.TgZ(20,"span"),t._uU(21,"Include Step"),t.qZA()()(),t.YNc(22,Zt,6,10,"cc-card-content",11),t.qZA()()(),t.TgZ(23,"form",12)(24,"div",2)(25,"cc-checkbox",13),t._uU(26,"Add Email"),t.qZA()(),t.YNc(27,Nt,17,6,"ng-container",11),t.ALo(28,"async"),t.qZA(),t.TgZ(29,"div",14)(30,"button",15),t.NdJ("click",function(){return n.onCancel()}),t._uU(31,"Cancel"),t.qZA(),t.TgZ(32,"button",16),t.NdJ("click",function(){return n.onSave()}),t._uU(33),t.qZA()()),2&e&&(t.xp6(1),t.Q6J("formGroup",n.form),t.xp6(4),t.Q6J("data",t.lcZ(6,9,n.blockerTypeOpts)),t.xp6(4),t.Q6J("symbol","")("required",!0),t.xp6(13),t.Q6J("ngIf",n.affectedVisaSteps),t.xp6(1),t.Q6J("formGroup",n.form),t.xp6(4),t.Q6J("ngIf",t.lcZ(28,11,n.isAddEmail$)),t.xp6(5),t.Q6J("disabled",!n.form.valid),t.xp6(1),t.hij(" ","Add"===n.page?"Save":"Update"," "))},directives:[r._Y,r.JL,r.sg,w.G,r.JJ,r.u,r.Q7,V.jB,Q.Fi,T.Dt,T.oJ,T.K9,g.uu,k.Q9,p.O5,T.uw,l.Ge,l.VC,N.E,St,D.Qf],pipes:[p.Ov],encapsulation:2,changeDetection:0}),v=(0,m.gn)([f.kG],v);const It=[{path:"",component:x,data:{label:"Configuration List"}},{path:"add-new-blocker",component:v,data:{label:"Create New Blocker"}},{path:"edit-blocker/:id",component:v,data:{label:"Edit Blocker"}}];let Bt=(()=>{class i{}return i.\u0275fac=function(n){return new(n||i)},i.\u0275mod=t.oAB({type:i}),i.\u0275inj=t.cJS({imports:[[p.ez,r.UX,r.u5,C.Bz.forChild(It),f.n_.forFeature({defaultPageSize:20}),z.YV,T.Ev,h.I8,l.Gz,y.yU,g.S6,k.L,w.f,V.lK,P.Bp,N.$,D.l2,Q.SZ]]}),i})()},46700:(J,E,c)=>{var p={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function f(l){var g=C(l);return c(g)}function C(l){if(!c.o(p,l)){var g=new Error("Cannot find module '"+l+"'");throw g.code="MODULE_NOT_FOUND",g}return p[l]}f.keys=function(){return Object.keys(p)},f.resolve=C,J.exports=f,f.id=46700}}]);
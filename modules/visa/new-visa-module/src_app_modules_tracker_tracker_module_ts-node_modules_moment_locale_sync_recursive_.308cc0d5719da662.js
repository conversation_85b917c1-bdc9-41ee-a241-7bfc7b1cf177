(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_tracker_tracker_module_ts-node_modules_moment_locale_sync_recursive_"],{72015:(fe,le,g)=>{"use strict";g.r(le),g.d(le,{TrackerModule:()=>cr});var h=g(69808),A=g(1402),T=g(88476),N=g(62764),B=g(88087),Nt=g(57902),l=g(93075),ie=g(4882),P=g(26523),_=g(69202),ne=g(43687),pe=g(92431),Zt=g(58015),k=g(65868),j=g(82599),de=g(11523),O=g(45834),At=g(34378),Ot=g(54657),Jt=g(467),Mt=g(63372),M=g(83278),b=g(97582),e=g(5e3);const he={viewOnly:!1,mainScreens:[],issueScreens:[],config:{Colors:[],GraphicComponents:[],MaidSituations:[],WorkFlowTasks:[],PredefinedLinks:[],ScreenTypes:[],FunctionTypes:[],Parameters:[]},visaSteps:[],setupPage:{id:null,screenEnabled:!1,screenOrder:null,maidSituations:[],hideFromRemainingSteps:!1,priority:1,screenName:null,visaGroupsSteps:[],conditionsToggle:!1,conditionJson:null,screenType:"Normal",screenMultiType:"Multi Normals",screenTitle:{text:null},screenSubTitle:{text:null},gptPolicy:{text:null},graphicComponent:null,informative:[{showInformativeIcon:!0,text:{text:null},nationalities:[]}],progressBarColor:null,mainTrackerPage:{showCallToAction:!1,showBottomHyperLinks:!1,components:[{text:{text:null},hyperLinks:[],notes:[],componentType:"Normal"}],callToActions:[],bottomHyperLinks:[]},homeTrackerPage:{showCallToAction:!1,hideFullDetailsCTA:!1,screenText:{text:""},callToActions:[]},issueType:{text:null},issue:!1,underTrackerTextChecked:!1,underTrackerText:{text:null}}};var s=g(65620);const ae=(0,s.PH)("[Tracker Screens List | Store Service] fetch Screens Lists"),Te=(0,s.PH)("[Tracker | Effect ] fetch Screens Lists success",(0,s.Ky)()),ve=(0,s.PH)("[Tracker Screens List | Store Service] update screen request",(0,s.Ky)()),Ce=(0,s.PH)("[Tracker Screens List | Store Service] update screen order request",(0,s.Ky)()),ce=((0,s.PH)("[Tracker Update Screen Request | Store Service] update screen success"),(0,s.PH)("[Tracker Page Setup | Store Service] fetch configurations")),xe=(0,s.PH)("[Tracker | Effect ] fetch configurations success",(0,s.Ky)()),ke=(0,s.PH)("[Tracker Add Step Pop Up | Store Service] add step",(0,s.Ky)()),be=(0,s.PH)("[Tracker Delete Step From Page Setup | Store Service] delete step",(0,s.Ky)()),Se=(0,s.PH)("[Tracker Add informative Section from setup page | Store Service] add informative section",(0,s.Ky)()),ye=(0,s.PH)("[Tracker Delete informative Section from setup page | Store Service] delete informative section",(0,s.Ky)()),we=(0,s.PH)("[Tracker Add HyperLink from a component in setup page | Store Service] Add hyperlink from component",(0,s.Ky)()),Pe=(0,s.PH)("[Tracker Delete HyperLink from a component in setup page | Store Service] delete hyperlink from component",(0,s.Ky)()),Ne=(0,s.PH)("[Tracker Add Note from a component in setup page | Store Service] Add Note from component",(0,s.Ky)()),Ze=(0,s.PH)("[Tracker Delete Note from a component in setup page | Store Service] delete Note from component",(0,s.Ky)()),Ae=(0,s.PH)("[Tracker Add HyperLink from a Step component in setup page | Store Service] Add hyperlink from Step component",(0,s.Ky)()),Oe=(0,s.PH)("[Tracker Delete HyperLink from a Step component in setup page | Store Service] delete hyperlink from Step component",(0,s.Ky)()),Je=(0,s.PH)("[Tracker Add Note from a Step component in setup page | Store Service] Add Note from Step component",(0,s.Ky)()),Me=(0,s.PH)("[Tracker Delete Note from a Step component in setup page | Store Service] delete Note from Step component",(0,s.Ky)()),je=(0,s.PH)("[Tracker Activate Separator component in setup page | Store Service] Activate Separator component",(0,s.Ky)()),Le=(0,s.PH)("[Tracker deActivate Separator component in setup page | Store Service] deActivate Separator component",(0,s.Ky)()),Ie=(0,s.PH)("[Tracker Save Step Component in Main Tracker setup page | Store Service] Save Step Component",(0,s.Ky)()),He=(0,s.PH)("[Tracker Save Component in Main Tracker setup page | Store Service] Save Component",(0,s.Ky)()),qe=(0,s.PH)("[Tracker Save Hyperlink in Main Tracker setup page | Store Service] Save Hyperlink",(0,s.Ky)()),Qe=(0,s.PH)("[Tracker Save Note in Main Tracker setup page | Store Service] Save Note",(0,s.Ky)()),Ue=(0,s.PH)("[Tracker Save the Setup page state | Store Service] Save the Setup page state",(0,s.Ky)()),Fe=(0,s.PH)("[Tracker delete Component in Main Tracker setup page | Store Service] delete Component",(0,s.Ky)()),Ge=(0,s.PH)("[Tracker delete Step Component in Main Tracker setup page | Store Service] delete Step Component",(0,s.Ky)()),Be=(0,s.PH)("[Tracker Add Step Component in Main Tracker setup page | Store Service] Add Step Component",(0,s.Ky)()),Ye=(0,s.PH)("[Tracker Add Component in Main Tracker setup page | Store Service] Add Component"),De=(0,s.PH)("[Tracker Add CTA in Main Tracker setup page | Store Service] Add CTA"),$e=(0,s.PH)("[Tracker Save CTA Component in Main Tracker setup page | Store Service] Save CTA Component",(0,s.Ky)()),Ee=(0,s.PH)("[Tracker Delete CTA Component in Main Tracker setup page | Store Service] Delete CTA Component",(0,s.Ky)()),Re=(0,s.PH)("[Tracker Show/Hide Call To Action in Main Tracker setup page | Store Service] Show/Hide Call To Action",(0,s.Ky)()),Ke=(0,s.PH)("[Tracker Show/Hide Conditions in Tracker setup page | Store Service] Show/Hide Conditions",(0,s.Ky)()),ze=(0,s.PH)("[Tracker Show/Hide Under Tracker Text in Success Tracker setup page | Store Service] Show/Hide Under Tracker Text",(0,s.Ky)()),Ve=(0,s.PH)("[Tracker Show/Hide Bottom HyperLink in Main Tracker setup page | Store Service] Show/Hide Bottom HyperLink",(0,s.Ky)()),We=(0,s.PH)("[Tracker Add Bottom HyperLink in Main Tracker setup page | Store Service] Add Bottom HyperLink"),Xe=(0,s.PH)("[Tracker Save Bottom HyperLink Component in Main Tracker setup page | Store Service] Save Bottom HyperLink Component",(0,s.Ky)()),et=(0,s.PH)("[Tracker Delete Bottom HyperLink Component in Main Tracker setup page | Store Service] Delete Bottom HyperLink Component",(0,s.Ky)()),tt=(0,s.PH)("[Tracker Add Home CTA in Main Tracker setup page | Store Service] Add Home CTA"),nt=(0,s.PH)("[Tracker Save Home CTA Component in Main Tracker setup page | Store Service] Save Home CTA Component",(0,s.Ky)()),ot=(0,s.PH)("[Tracker Delete Home CTA Component in Main Tracker setup page | Store Service] Delete Home CTA Component",(0,s.Ky)()),rt=(0,s.PH)("[Tracker Show/Hide Home Call To Action in Main Tracker setup page | Store Service] Show/Hide Home Call To Action",(0,s.Ky)()),it=(0,s.PH)("[Tracker Show/Hide Full Details CTA in Main Tracker setup page | Store Service] Show/Hide Full Details CTA",(0,s.Ky)()),at=(0,s.PH)("[Tracker Change Component Type in Main Tracker setup page | Store Service] Change Component Type",(0,s.Ky)()),ct=(0,s.PH)("[Tracker Request Page Creation | Store Service] Request Page Creation",(0,s.Ky)()),st=(0,s.PH)("[Tracker Request Page Edit | Store Service] Request Page Edit",(0,s.Ky)()),lt=(0,s.PH)("[Tracker Request Multi Page Edit | Store Service] Request Multi Page Edit",(0,s.Ky)()),pt=(0,s.PH)("[Tracker Request Page Creation Succeeded| Store Service] Request Page Creation Succeeded"),dt=(0,s.PH)("[Tracker Request Page Edit Succeeded| Store Service] Request Page Edit Succeeded"),mt=(0,s.PH)("[Tracker Request Multi Page Edit Succeeded| Store Service] Request Multi Page Edit Succeeded"),ut=(0,s.PH)("[Tracker Get Page Details | Store Service] Request Page Details",(0,s.Ky)()),gt=(0,s.PH)("[Tracker Get Page Details Success| Store Service] Request Page Details Success",(0,s.Ky)()),_t=(0,s.PH)("[Tracker Get Multi Page Details | Store Service] Request Multi Page Details",(0,s.Ky)()),ft=(0,s.PH)("[Tracker Get Multi Page Details Success| Store Service] Request Multi Page Details Success",(0,s.Ky)()),ht=(0,s.PH)("[Tracker Setup Multi set multi type| Store Service] set multi type",(0,s.Ky)()),Tt=(0,s.PH)("[Tracker Setup Page as View Only| Store Service] set view only",(0,s.Ky)()),me=(0,s.PH)("[Tracker pages | Store Service] Reset State"),vt=(0,s.PH)("[Tracker pages | Store Service] Check Informative"),Ct="tracker",xt=i=>"Separator"===i.componentType,jt=(0,s.Lq)(he,(0,s.on)(Te,(i,{payload:n})=>Object.assign(Object.assign({},i),{mainScreens:n["Main Screens"],issueScreens:n["Issue Screens"]})),(0,s.on)(xe,(i,{payload:n})=>Object.assign(Object.assign({},i),{config:n})),(0,s.on)(ke,(i,{data:n})=>{const t=i.setupPage.visaGroupsSteps?[...i.setupPage.visaGroupsSteps]:[];t.push(n);const o=Object.assign({},i),r=Object.assign({},o.setupPage);return r.visaGroupsSteps=t,o.setupPage=r,o}),(0,s.on)(be,(i,{id:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),a=(o.visaGroupsSteps?[...o.visaGroupsSteps]:[]).filter(c=>c.workFlowTask.id!==n);return o.visaGroupsSteps=a,t.setupPage=o,t}),(0,s.on)(Se,(i,n)=>{const t=Object.assign({},i),o=[...n.data];return o.push({showInformativeIcon:!0,text:{text:null},nationalities:[]}),t.setupPage=Object.assign(Object.assign({},t.setupPage),{informative:o}),t}),(0,s.on)(ye,(i,{index:n})=>{const t=Object.assign({},i),o=[...t.setupPage.informative];return o.splice(n,1),t.setupPage=Object.assign(Object.assign({},t.setupPage),{informative:o}),t}),(0,s.on)(Pe,(i,{index:n,componentIndex:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.components],d=Object.assign({},c[t]);if(d){const m=[...d.hyperLinks];m.splice(n,1);const u=Object.assign(Object.assign({},d),{hyperLinks:m});c[t]=u,a.components=c,r.mainTrackerPage=a,o.setupPage=r}return o}),(0,s.on)(we,(i,{componentIndex:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components],p=Object.assign({},a[n]);if(p){const d=[...p.hyperLinks,{text:{text:""},hyperLinkText:{text:""},urlText:{text:""}}],m=Object.assign(Object.assign({},p),{hyperLinks:d});a[n]=m,r.components=a,o.mainTrackerPage=r,t.setupPage=o}return t}),(0,s.on)(Ze,(i,{index:n,componentIndex:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.components],d=Object.assign({},c[t]);if(d){const m=[...d.notes];m.splice(n,1);const u=Object.assign(Object.assign({},d),{notes:m});c[t]=u,a.components=c,r.mainTrackerPage=a,o.setupPage=r}return o}),(0,s.on)(Ne,(i,{componentIndex:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components],p=Object.assign({},a[n]);if(p){const d=[...p.notes,{text:{text:""}}],m=Object.assign(Object.assign({},p),{notes:d});a[n]=m,r.components=a,o.mainTrackerPage=r,t.setupPage=o}return t}),(0,s.on)(Oe,(i,{index:n,componentIndex:t,stepIndex:o})=>{const r=Object.assign({},i),a=Object.assign({},r.setupPage),c=Object.assign({},a.mainTrackerPage),p=[...c.components],m=Object.assign({},p[o]);if(m){const u=[...m.normalComponents],C=Object.assign({},m.normalComponents[t]);if(C){const S=[...C.hyperLinks];S.splice(n,1);const Q=Object.assign(Object.assign({},C),{hyperLinks:S});u[t]=Q,m.normalComponents=u,p[o]=m,c.components=p,a.mainTrackerPage=c,r.setupPage=a}}return r}),(0,s.on)(Me,(i,{index:n,componentIndex:t,stepIndex:o})=>{const r=Object.assign({},i),a=Object.assign({},r.setupPage),c=Object.assign({},a.mainTrackerPage),p=[...c.components],m=Object.assign({},p[o]);if(m){const u=[...m.normalComponents],C=Object.assign({},m.normalComponents[t]);if(C){const S=[...C.notes];S.splice(n,1);const Q=Object.assign(Object.assign({},C),{notes:S});u[t]=Q,m.normalComponents=u,p[o]=m,c.components=p,a.mainTrackerPage=c,r.setupPage=a}}return r}),(0,s.on)(Ae,(i,{componentIndex:n,stepIndex:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.components],d=Object.assign({},c[t]);if(d){const m=[...d.normalComponents],u=Object.assign({},d.normalComponents[n]);if(u){const C=[...u.hyperLinks,{id:null,text:{id:null,text:""},hyperLinkText:{id:null,text:""},urlText:{id:null,text:""}}],S=Object.assign(Object.assign({},u),{hyperLinks:C});m[n]=S,d.normalComponents=m,c[t]=d,a.components=c,r.mainTrackerPage=a,o.setupPage=r}}return o}),(0,s.on)(Je,(i,{componentIndex:n,stepIndex:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.components],d=Object.assign({},c[t]);if(d){const m=[...d.normalComponents],u=Object.assign({},d.normalComponents[n]);if(u){const C=[...u.notes,{text:{id:null,text:""}}],S=Object.assign(Object.assign({},u),{notes:C});m[n]=S,d.normalComponents=m,c[t]=d,a.components=c,r.mainTrackerPage=a,o.setupPage=r}}return o}),(0,s.on)(je,(i,{index:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components],c=a[n];if(Object.assign({},c)){const d=Object.assign(Object.assign({},c),{added:!0});a[n]=d,r.components=a,o.mainTrackerPage=r,t.setupPage=o}return t}),(0,s.on)(Le,(i,{index:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components],c=a[n];if(Object.assign({},c)){const d=Object.assign(Object.assign({},c),{added:!1,text:{text:null}});a[n]=d,r.components=a,o.mainTrackerPage=r,t.setupPage=o}return t}),(0,s.on)(He,(i,{component:n,componentIndex:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.components];return c[t]=n,a.components=c,r.mainTrackerPage=a,o.setupPage=r,o}),(0,s.on)(Ie,(i,{component:n,componentIndex:t,stepIndex:o})=>{const r=Object.assign({},i),a=Object.assign({},r.setupPage),c=Object.assign({},a.mainTrackerPage),p=[...c.components],d=Object.assign({},p[o]),m=d,u=[...m.normalComponents];return u[t]=n,m.normalComponents=u,p[o]=m,p[o]=d,c.components=p,a.mainTrackerPage=c,r.setupPage=a,r}),(0,s.on)(Ue,(i,{state:n})=>{const t=Object.assign({},i);let r,o=n.conditionJson;try{"object"==typeof n.conditionJson&&"string"!=typeof n.conditionJson&&n.conditionJson&&(o=JSON.stringify(n.conditionJson))}catch(a){o=n.conditionJson}return r=Object.assign(Object.assign({},n),{conditionJson:o}),t.setupPage=r,t}),(0,s.on)(Fe,(i,{componentIndex:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components];return a.splice(n,1),a.splice(n-1,1),r.components=a,o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(Ge,(i,{componentIndex:n,stepIndex:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.components],p=Object.assign({},c[t]),d=[...p.normalComponents];return d.splice(n,1),p.normalComponents=d,c[t]=p,a.components=c,r.mainTrackerPage=a,o.setupPage=r,o}),(0,s.on)(Be,(i,{stepIndex:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components],c=Object.assign({},a[n]),p=[...c.normalComponents];return p.push({text:{text:null},hyperLinks:[],notes:[],componentType:"Normal"}),c.normalComponents=p,a[n]=c,r.components=a,o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(qe,(i,{linkIndex:n,componentIndex:t,stepIndex:o,link:r})=>{const a=Object.assign({},i),c=Object.assign({},a.setupPage),p=Object.assign({},c.mainTrackerPage),d=[...p.components];if(null!=o){const m=Object.assign({},d[o]),u=m,C=[...u.normalComponents],S=Object.assign({},C[t]),Q=[...S.hyperLinks];Q[n]=r,S.hyperLinks=Q,C[t]=S,u.normalComponents=C,d[o]=u,d[o]=m,p.components=d,c.mainTrackerPage=p,a.setupPage=c}else{const u=Object.assign({},d[t]),C=[...u.hyperLinks];C[n]=r,u.hyperLinks=C,d[t]=u,p.components=d,c.mainTrackerPage=p,a.setupPage=c}return a}),(0,s.on)(Qe,(i,{noteIndex:n,componentIndex:t,stepIndex:o,note:r})=>{const a=Object.assign({},i),c=Object.assign({},a.setupPage),p=Object.assign({},c.mainTrackerPage),d=[...p.components];if(null!=o){const m=Object.assign({},d[o]),u=m,C=[...u.normalComponents],S=Object.assign({},C[t]),Q=[...S.notes];Q[n]=r,S.notes=Q,C[t]=S,u.normalComponents=C,d[o]=u,d[o]=m,p.components=d,c.mainTrackerPage=p,a.setupPage=c}else{const u=Object.assign({},d[t]),C=[...u.notes];C[r]=r,u.notes=C,d[t]=u,p.components=d,c.mainTrackerPage=p,a.setupPage=c}return a}),(0,s.on)(Ye,i=>{const n=Object.assign({},i),t=Object.assign({},n.setupPage),o=Object.assign({},t.mainTrackerPage),r=[...o.components];return r.push({text:{text:""},added:!1,componentType:"Separator"}),r.push({text:{text:null},hyperLinks:[],notes:[],componentType:"Normal"}),o.components=r,t.mainTrackerPage=o,n.setupPage=t,n}),(0,s.on)(Re,(i,{showCallToActionToggle:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign(Object.assign({},o.mainTrackerPage),{showCallToAction:n});return o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(Ke,(i,{showConditionsToggle:n})=>{const t=Object.assign({},i),o=Object.assign(Object.assign({},t.setupPage),{conditionsToggle:n});return t.setupPage=o,t}),(0,s.on)(ze,(i,{showUnderTrackerText:n})=>{const t=Object.assign({},i),o=Object.assign(Object.assign({},t.setupPage),{showUnderTrackerText:n});return t.setupPage=o,t}),(0,s.on)($e,(i,{index:n,cta:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.callToActions],p=Object.assign({},t);return c[n]=p,a.callToActions=c,r.mainTrackerPage=a,o.setupPage=r,o}),(0,s.on)(Ee,(i,{index:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.callToActions];return a.splice(n,1),r.callToActions=a,o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(De,i=>{const n=Object.assign({},i),t=Object.assign({},n.setupPage),o=Object.assign({},t.mainTrackerPage),r=[...o.callToActions];return r.push({buttonName:{text:""},functionType:"",primaryButton:!0}),o.callToActions=r,t.mainTrackerPage=o,n.setupPage=t,n}),(0,s.on)(Ve,(i,{showButtomHyperlinkToggle:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign(Object.assign({},o.mainTrackerPage),{showBottomHyperLinks:n});return o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(Xe,(i,{index:n,link:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.mainTrackerPage),c=[...a.bottomHyperLinks],p=Object.assign({},t);return c[n]=p,a.bottomHyperLinks=c,r.mainTrackerPage=a,o.setupPage=r,o}),(0,s.on)(et,(i,{index:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.bottomHyperLinks];return a.splice(n,1),r.bottomHyperLinks=a,o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(We,i=>{const n=Object.assign({},i),t=Object.assign({},n.setupPage),o=Object.assign({},t.mainTrackerPage),r=[...o.bottomHyperLinks];return r.push({hyperLinkText:{text:""},text:{text:""},urlText:{text:""}}),o.bottomHyperLinks=r,t.mainTrackerPage=o,n.setupPage=t,n}),(0,s.on)(nt,(i,{index:n,cta:t})=>{const o=Object.assign({},i),r=Object.assign({},o.setupPage),a=Object.assign({},r.homeTrackerPage),c=[...a.callToActions],p=Object.assign({},t);return c[n]=p,a.callToActions=c,r.homeTrackerPage=a,o.setupPage=r,o}),(0,s.on)(ot,(i,{index:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.homeTrackerPage),a=[...r.callToActions];return a.splice(n,1),r.callToActions=a,o.homeTrackerPage=r,t.setupPage=o,t}),(0,s.on)(tt,i=>{const n=Object.assign({},i),t=Object.assign({},n.setupPage),o=Object.assign({},t.homeTrackerPage),r=[...o.callToActions];return r.push({buttonName:{text:""},functionType:"",primaryButton:!0}),o.callToActions=r,t.homeTrackerPage=o,n.setupPage=t,n}),(0,s.on)(rt,(i,{showCallToActionToggle:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign(Object.assign({},o.homeTrackerPage),{showCallToAction:n});return o.homeTrackerPage=r,t.setupPage=o,t}),(0,s.on)(it,(i,{hideFullDetailsCTAToggle:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign(Object.assign({},o.homeTrackerPage),{hideFullDetailsCTA:n});return o.homeTrackerPage=r,t.setupPage=o,t}),(0,s.on)(at,(i,{index:n})=>{const t=Object.assign({},i),o=Object.assign({},t.setupPage),r=Object.assign({},o.mainTrackerPage),a=[...r.components];return a[n]="Stepper"==Object.assign({},a[n]).componentType?{text:{text:null},hyperLinks:[],notes:[],componentType:"Normal"}:{normalComponents:[{text:{text:null},hyperLinks:[],notes:[],componentType:"Normal"}],componentType:"Stepper"},r.components=a,o.mainTrackerPage=r,t.setupPage=o,t}),(0,s.on)(gt,(i,{data:n})=>{const t=Object.assign({},i),o=Object.assign({},n);if(o.mainTrackerPage&&Array.isArray(o.mainTrackerPage.components)){const r=[],a=[...o.mainTrackerPage.components];for(let c=0;c<a.length;c++)r.push(Object.assign({},a[c])),c<a.length-1&&!xt(a[c])&&!xt(a[c+1])&&r.push({text:{text:""},added:!1,componentType:"Separator"});o.mainTrackerPage=Object.assign(Object.assign({},o.mainTrackerPage),{components:r})}return t.setupPage=o,t}),(0,s.on)(ft,(i,{data:n})=>{const t=Object.assign({},i),o=Object.assign({},n);return t.setupPage=o,t}),(0,s.on)(ht,(i,{pageType:n})=>{const t=Object.assign({},i),o=Object.assign(Object.assign({},t.setupPage),{screenMultiType:n,screenType:""});return t.setupPage=o,t}),(0,s.on)(Tt,(i,{viewOnly:n})=>Object.assign(Object.assign({},i),{viewOnly:n})),(0,s.on)(me,i=>Object.assign(Object.assign({},i),{setupPage:he.setupPage})),(0,s.on)(vt,i=>{const n=Object.assign({},i);if(0==i.setupPage.informative.length){const t=[...n.setupPage.informative];t.push({showInformativeIcon:!0,text:{text:null},nationalities:[]}),n.setupPage=Object.assign(Object.assign({},n.setupPage),{informative:t})}return n})),w=(0,s.ZF)(Ct),Lt=((0,s.P1)(w,i=>i),(0,s.P1)(w,i=>i.mainScreens)),It=(0,s.P1)(w,i=>i.issueScreens),Ht=(0,s.P1)(w,i=>i.config.Colors),qt=(0,s.P1)(w,i=>i.config.GraphicComponents),Qt=(0,s.P1)(w,i=>i.config.MaidSituations),Ut=(0,s.P1)(w,i=>i.config.PredefinedLinks),Ft=(0,s.P1)(w,i=>i.config.WorkFlowTasks),Gt=(0,s.P1)(w,i=>i.config.ScreenTypes),Bt=(0,s.P1)(w,i=>i.config.FunctionTypes),Yt=(0,s.P1)(w,i=>i.setupPage.visaGroupsSteps),Dt=(0,s.P1)(w,i=>i.config.Parameters),Et=(0,s.P1)(w,i=>i.setupPage),Rt=(0,s.P1)(w,i=>({screenType:i.setupPage.screenType,screenTitle:i.setupPage.screenTitle,screenSubTitle:i.setupPage.screenSubTitle,graphicComponent:i.setupPage.graphicComponent,informative:i.setupPage.informative,progressBarColor:i.setupPage.progressBarColor,mainTrackerPage:i.setupPage.mainTrackerPage,homeTrackerPage:i.setupPage.homeTrackerPage})),Kt=(0,s.P1)(w,i=>i.viewOnly);var kt=g(40520),bt=g(8188),f=g(54004),oe=g(39646),J=g(43604);let ue=(()=>{class i{constructor(t,o){this._api=t,this._http=o}fetchScreensList(){return this._http.get([this._api,J.b.trackerScreens].join("/"),{}).pipe()}fetchConfigurations(){return this._http.get([this._api,J.b.config].join("/"),{}).pipe()}fetchConditionsList(){return this._http.get([this._api,J.b.conditionsList].join("/"),{}).pipe()}updateConditionName(t,o){return this._http.post([this._api,J.b.updatePicklist].join("/"),{id:t,name:o}).pipe()}changeEditState(t){return this._http.get([this._api,J.b.changeStateRequest+"?vg_id="+t.id].join("/")).pipe()}changeOrder(t){return this._http.get([this._api,J.b.changeOrderRequest+"?vg_id="+t.id+"&screenOrder="+t.screenOrder].join("/")).pipe()}fetchNationalities(){return this._http.get([this._api,J.b.nationalities].join("/"),{context:(new kt.qT).set(bt.hG,!1)}).pipe()}removeTypeField(t){if(Array.isArray(t))return t.map(o=>this.removeTypeField(o));if("object"==typeof t&&null!==t){const o={};for(const r in t)t.hasOwnProperty(r)&&"type"!==r&&(o[r]=this.removeTypeField(t[r]));return o}return t}shapeRequest(t){let o=this.makeRequest(t);if(delete o.screenMultiType,o.maidSituations=t.maidSituations.map(r=>({id:r.id})),t.conditionsToggle&&!t.conditionJson)o.conditionJson="";else if(t.conditionJson){let r=JSON.parse(t.conditionJson);r=this.removeTypeField(r);const a=JSON.stringify(r);o.conditionJson=a}else o.conditionJson="";return"Issue"==t.screenType?delete o.informative:delete o.issueType,"Success"==t.screenType&&(delete o.screenSubTitle,delete o.informative),"Success"!=t.screenType&&(delete o.underTrackerTextChecked,delete o.underTrackerText),null==t.mainTrackerPage&&delete o.mainTrackerPage,o}createPageRequest(t){let o=this.shapeRequest(t);return this._http.post([this._api,J.b.createPageRequest].join("/"),o).pipe()}editPageRequest(t){let o=this.shapeRequest(t);return this._http.post([this._api,J.b.updatePageRequest].join("/"),o).pipe()}isSeparatorComponent(t){return"Separator"===t.componentType}isStepperComponent(t){return"Stepper"===t.componentType}shapeMultiRequest(t){let o=this.makeRequest(t);return delete o.screenType,delete o.conditionsToggle,delete o.screenName,delete o.conditionJson,delete o.hideFromRemainingSteps,delete o.underTrackerTextChecked,delete o.underTrackerText,"Multi Issue"==t.screenMultiType&&delete o.informative,o.maidSituations=t.maidSituations,o}editMultiPageRequest(t){let o=this.shapeMultiRequest(t);return this._http.post([this._api,J.b.updateMultiPageRequest].join("/"),o).pipe()}getPage(t){return this._http.get([this._api,J.b.getPageRequest,t].join("/")).pipe((0,f.U)(o=>this.mapResponseToSetupPage(o)))}getMultiPage(t){return"normal"==t?this._http.get([this._api,J.b.getMultiNormalsPageRequest].join("/")).pipe((0,f.U)(o=>this.mapResponseToSetupPage(o))):"issue"==t?this._http.get([this._api,J.b.getMultiIssuesPageRequest].join("/")).pipe((0,f.U)(o=>this.mapResponseToSetupPage(o))):(0,oe.of)()}mapHyperLink(t){return{text:{text:t.text.text},hyperLinkText:{text:t.hyperLinkText.text},urlText:{text:t.urlText.text}}}mapComponent(t){var o,r;return"SeparatorComponent"===t.entityType?{text:{text:t.text.text},added:!0,componentType:"Separator"}:"NormalComponent"===t.entityType?{text:{text:t.text.text},hyperLinks:(null===(o=t.hyperLinks)||void 0===o?void 0:o.map(function(a){return{hyperLinkText:{text:a.hyperLinkText.text},text:{text:a.text.text},urlText:{text:a.urlText.text}}}))||[],notes:t.notes.map(function(a){return{text:{text:a.text.text}}})||[],componentType:"Normal"}:"StepperComponent"===t.entityType?{normalComponents:(null===(r=t.normalComponents)||void 0===r?void 0:r.map(function(a){var c;return{text:{text:a.text.text},hyperLinks:(null===(c=a.hyperLinks)||void 0===c?void 0:c.map(function(p){return{hyperLinkText:{text:p.hyperLinkText.text},text:{text:p.text.text},urlText:{text:p.urlText.text}}}))||[],notes:a.notes.map(function(p){return{text:{text:p.text.text}}})||[],componentType:"Normal"}}))||[],componentType:"Stepper"}:{}}mapMainTrackerPage(t){var o,r,a;return{showCallToAction:t.showCallToAction,showBottomHyperLinks:t.showBottomHyperLinks,components:null===(o=t.components)||void 0===o?void 0:o.map(this.mapComponent),callToActions:(null===(r=t.callToActions)||void 0===r?void 0:r.map(this.mapCallToAction))||[],bottomHyperLinks:(null===(a=t.bottomHyperLinks)||void 0===a?void 0:a.map(this.mapHyperLink))||[]}}mapCallToAction(t){return{buttonName:{text:t.buttonName.text},functionType:t.functionType,primaryButton:t.primaryButton}}mapHomeTrackerPage(t){var o;return{showCallToAction:t.showCallToAction,hideFullDetailsCTA:t.hideFullDetailsCTA,screenText:{text:t.screenText.text},callToActions:null===(o=t.callToActions)||void 0===o?void 0:o.map(this.mapCallToAction)}}mapResponseToSetupPage(t){var o,r,a;return{id:t.id,screenEnabled:t.screenEnabled,screenOrder:t.screenOrder,screenName:t.screenName||null,priority:t.priority,hideFromRemainingSteps:t.hideFromRemainingSteps,conditionsToggle:t.conditionsToggle,conditionJson:t.conditionJson?t.conditionJson:null,screenType:t.screenType,screenMultiType:t.screenMultiType,issueType:t.issueType||{text:null},visaGroupsSteps:null===(o=t.visaGroupsSteps)||void 0===o?void 0:o.map(c=>({priority:c.priority,workFlowTask:c.workFlowTask})),issue:t.issue,underTrackerTextChecked:t.underTrackerTextChecked,underTrackerText:t.underTrackerText,mainTrackerPage:this.mapMainTrackerPage(t.mainTrackerPage),homeTrackerPage:this.mapHomeTrackerPage(t.homeTrackerPage),maidSituations:null===(r=t.maidSituations)||void 0===r?void 0:r.map(c=>Object.assign(Object.assign({},c),{text:c.name||c.label,label:c.name||c.label,name:c.name||c.label})),graphicComponent:t.graphicComponent.id,progressBarColor:t.progressBarColor.id,screenTitle:{text:t.screenTitle.text},screenSubTitle:{text:t.screenSubTitle.text},gptPolicy:{text:t.gptPolicy.text||""},informative:null===(a=t.informative)||void 0===a?void 0:a.map(c=>({showInformativeIcon:c.showInformativeIcon,text:{text:c.text.text},nationalities:c.nationalities}))}}uriEncodeTextFields(t){if(null!==t&&"object"==typeof t)for(const o in t)if(Object.prototype.hasOwnProperty.call(t,o)){const r=t[o];"urlText"===o&&r&&"object"==typeof r&&"string"==typeof r.text?(r.text=this.stripHtmlTags(r.text),r.text=encodeURIComponent(r.text)):"text"===o&&"string"==typeof r?t[o]=encodeURIComponent(r):"object"==typeof r&&null!==r&&this.uriEncodeTextFields(r)}}stripHtmlTags(t){return t.replace(/<[^>]*>/g,"")}deepClone(t){if(null===t||"object"!=typeof t)return t;const o=Array.isArray(t)?[]:{};for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(o[r]=this.deepClone(t[r]));return o}makeRequest(t){var o,r;const a=this.deepClone(t);a.mainTrackerPage.components=(null===(o=a.mainTrackerPage.components)||void 0===o?void 0:o.filter(d=>!(this.isSeparatorComponent(d)&&!d.added)))||[];const c=null===(r=a.mainTrackerPage.components)||void 0===r?void 0:r.map(d=>this.isSeparatorComponent(d)&&d.added?(0,b._T)(d,["added"]):d);a.mainTrackerPage.components=c,this.uriEncodeTextFields(a);const p=Object.assign({},a);return p.mainTrackerPage.components.forEach(d=>{var m;this.isStepperComponent(d)&&(d.normalComponents=null===(m=d.normalComponents)||void 0===m?void 0:m.map(u=>(0,b._T)(u,["componentType"]))),this.isSeparatorComponent(d)&&""==d.text.text&&(d.text=Object.assign(Object.assign({},d.text),{text:null}))}),this.removeNull(p)}removeNull(t){return Object.keys(t).forEach(o=>{var r;t[o]&&"object"==typeof t[o]?this.removeNull(t[o]):null==t[o]&&(null===(r=Object.getOwnPropertyDescriptor(t,o))||void 0===r?void 0:r.configurable)&&delete t[o]}),t}}return i.\u0275fac=function(t){return new(t||i)(e.LFG(bt.JV),e.LFG(kt.eN))},i.\u0275prov=e.Yz7({token:i,factory:i.\u0275fac,providedIn:"root"}),i})(),x=(()=>{class i extends T.il{constructor(t,o){super(t),this._trackerService=o,this.selectMainScreens=this.store.select(Lt),this.selectIssueScreens=this.store.select(It),this.selectColors=this.store.select(Ht),this.selectGraphicalComponents=this.store.select(qt),this.selectMaidSituations=this.store.select(Qt),this.selectPredefinedLinks=this.store.select(Ut),this.selectWorkFlowTasks=this.store.select(Ft),this.selectScreenTypes=this.store.select(Gt),this.selectFunctionTypes=this.store.select(Bt),this.selectParameters=this.store.select(Dt),this.selectVisaSteps=this.store.select(Yt),this.selectVisaStepById=r=>this.store.select((i=>(0,s.P1)(w,n=>n.config.WorkFlowTasks.find(t=>t.id==i)))(r)),this.selectSetupPage=this.store.select(Et),this.selectNormalStepPage=this.store.select(Rt),this.selectView=this.store.select(Kt),this.fetchNationalities=()=>this._trackerService.fetchNationalities()}pickListsCodes(){return[]}resetState(){this.store.dispatch(me())}fetchScreensList(){this.store.dispatch(ae())}fetchConfigurations(){this.store.dispatch(ce())}changeEditState(t){this.store.dispatch(ve({data:t}))}changeOrder(t){this.store.dispatch(Ce({data:t}))}addStep(t){this.store.dispatch(ke({data:t}))}deleteStep(t){this.store.dispatch(be({id:t}))}addInformative(t){this.store.dispatch(Se({data:t}))}deleteInformative(t){this.store.dispatch(ye({index:t}))}deleteHyperLink(t,o){this.store.dispatch(Pe({index:t,componentIndex:o}))}addHyperLink(t){this.store.dispatch(we({componentIndex:t}))}deleteNote(t,o){this.store.dispatch(Ze({index:t,componentIndex:o}))}addNote(t){this.store.dispatch(Ne({componentIndex:t}))}deleteStepHyperLink(t,o,r){this.store.dispatch(Oe({index:t,componentIndex:o,stepIndex:r}))}addStepHyperLink(t,o){this.store.dispatch(Ae({componentIndex:t,stepIndex:o}))}deleteStepNote(t,o,r){this.store.dispatch(Me({index:t,componentIndex:o,stepIndex:r}))}addStepNote(t,o){this.store.dispatch(Je({componentIndex:t,stepIndex:o}))}activateSeperator(t){this.store.dispatch(je({index:t}))}deActivateSeperator(t){this.store.dispatch(Le({index:t}))}saveStepComponent(t,o,r){this.store.dispatch(Ie({component:t,componentIndex:o,stepIndex:r}))}saveHyperLink(t,o,r,a){this.store.dispatch(qe({linkIndex:t,componentIndex:o,stepIndex:r,link:a}))}saveNote(t,o,r,a){this.store.dispatch(Qe({noteIndex:t,componentIndex:o,stepIndex:r,note:a}))}saveComponent(t,o){this.store.dispatch(He({component:t,componentIndex:o}))}checkInformative(){this.store.dispatch(vt())}savePageState(t){this.store.dispatch(Ue({state:t}))}deleteComponent(t){this.store.dispatch(Fe({componentIndex:t}))}deleteStepComponent(t,o){this.store.dispatch(Ge({componentIndex:t,stepIndex:o}))}addStepComponent(t){this.store.dispatch(Be({stepIndex:t}))}addComponent(){this.store.dispatch(Ye())}toggleShowCallToAction(t,o){this.store.dispatch(o?rt({showCallToActionToggle:t}):Re({showCallToActionToggle:t}))}showConditionsToggle(t){this.store.dispatch(Ke({showConditionsToggle:t}))}underTrackerTextToggle(t){this.store.dispatch(ze({showUnderTrackerText:t}))}toggleHideFullDetailsCTA(t){this.store.dispatch(it({hideFullDetailsCTAToggle:t}))}addCTA(t){this.store.dispatch(t?tt():De())}saveCTA(t,o,r){this.store.dispatch(r?nt({index:t,cta:o}):$e({index:t,cta:o}))}deleteCTA(t,o){this.store.dispatch(o?ot({index:t}):Ee({index:t}))}toggleShowButtomHyperlink(t){this.store.dispatch(Ve({showButtomHyperlinkToggle:t}))}addBottomLink(){this.store.dispatch(We())}saveBottomLink(t,o){this.store.dispatch(Xe({index:t,link:o}))}deleteBottomLink(t){this.store.dispatch(et({index:t}))}changeComponentType(t){this.store.dispatch(at({index:t}))}createPage(t){this.store.dispatch(ct({page:t}))}editPage(t){this.store.dispatch(st({page:t}))}editMultiPage(t){this.store.dispatch(lt({page:t}))}getPage(t){return this.store.dispatch(ut({id:t}))}getMultiPage(t){return this.store.dispatch(_t({screenType:t}))}setMultipleType(t){return this.store.dispatch(ht({pageType:t}))}setView(t){return this.store.dispatch(Tt({viewOnly:t}))}}return i.\u0275fac=function(t){return new(t||i)(e.LFG(s.yh),e.LFG(ue))},i.\u0275prov=e.Yz7({token:i,factory:i.\u0275fac}),i})();function zt(i,n){if(1&i&&e._UZ(0,"div",16),2&i){const t=e.oxw().$implicit;e.Q6J("innerHTML",t.screenType,e.oJD)}}function Vt(i,n){if(1&i&&e._UZ(0,"div",17),2&i){const t=e.oxw().$implicit;e.Q6J("innerHTML",t.screenType,e.oJD)}}function Wt(i,n){if(1&i&&(e.YNc(0,zt,1,1,"div",14),e.YNc(1,Vt,1,1,"div",15)),2&i){const t=n.$implicit;e.Q6J("ngIf","Success"!=t.screenType),e.xp6(1),e.Q6J("ngIf","Success"==t.screenType)}}function Xt(i,n){1&i&&e._UZ(0,"div",16),2&i&&e.Q6J("innerHTML",n.$implicit.screenName,e.oJD)}function en(i,n){if(1&i){const t=e.EpF();e.ynx(0),e.TgZ(1,"span",18),e.NdJ("click",function(){e.CHM(t);const r=e.oxw().$implicit;return e.oxw(2).updateOrder(r.id,r.screenOrder+1)}),e._uU(2,"\u2b9d"),e.qZA(),e._uU(3),e.TgZ(4,"span",18),e.NdJ("click",function(){e.CHM(t);const r=e.oxw().$implicit;return e.oxw(2).updateOrder(r.id,r.screenOrder-1)}),e._uU(5,"\u2b9f"),e.qZA(),e.BQk()}if(2&i){const t=e.oxw().$implicit;e.xp6(3),e.hij(" ",t.screenOrder," ")}}function tn(i,n){if(1&i&&(e.ynx(0),e._uU(1),e.BQk()),2&i){const t=e.oxw().$implicit;e.xp6(1),e.hij(" ",t.screenOrder," ")}}function nn(i,n){if(1&i&&(e.YNc(0,en,6,1,"ng-container",7),e.YNc(1,tn,2,1,"ng-container",7)),2&i){const t=n.$implicit;e.Q6J("ngIf",!t.hideFromRemainingSteps),e.xp6(1),e.Q6J("ngIf",t.hideFromRemainingSteps)}}const on=function(i,n,t){return{screenOrder:i,screenName:n,screenType:t}};function rn(i,n){if(1&i&&(e.ynx(0),e._UZ(1,"cc-datagrid",9),e.YNc(2,Wt,2,2,"ng-template",10,11,e.W1O),e.YNc(4,Xt,1,1,"ng-template",10,12,e.W1O),e.YNc(6,nn,2,2,"ng-template",10,13,e.W1O),e.BQk()),2&i){const t=n.ngIf,o=e.MAs(3),r=e.MAs(5),a=e.MAs(7),c=e.oxw(),p=e.MAs(22);e.xp6(1),e.Q6J("noResultTemplate",p)("data",t)("columns",c.mainScreensColumns)("length",t.length)("pageOnFront",!0)("cellTemplate",e.kEZ(9,on,a,r,o)),e.xp6(1),e.Q6J("ccGridCell",t),e.xp6(2),e.Q6J("ccGridCell",t),e.xp6(2),e.Q6J("ccGridCell",t)}}function an(i,n){1&i&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}function cn(i,n){1&i&&e._UZ(0,"div",16),2&i&&e.Q6J("innerHTML",n.$implicit.issueType,e.oJD)}function sn(i,n){1&i&&e._UZ(0,"div",16),2&i&&e.Q6J("innerHTML",n.$implicit.screenName,e.oJD)}const ln=function(i){return{screenName:i}};function pn(i,n){if(1&i&&(e.ynx(0),e._UZ(1,"cc-datagrid",9),e.YNc(2,cn,1,1,"ng-template",10,19,e.W1O),e.YNc(4,sn,1,1,"ng-template",10,12,e.W1O),e._uU(6," >"),e.BQk()),2&i){const t=n.ngIf,o=e.MAs(5),r=e.oxw(),a=e.MAs(22);e.xp6(1),e.Q6J("noResultTemplate",a)("data",t)("columns",r.issueScreensColumns)("length",t.length)("pageOnFront",!0)("cellTemplate",e.VKq(8,ln,o)),e.xp6(1),e.Q6J("ccGridCell",t),e.xp6(2),e.Q6J("ccGridCell",t)}}function dn(i,n){1&i&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}let Y=class{constructor(n,t,o){this._store=n,this.router=t,this.route=o,this.mainScreens$=this._store.selectMainScreens,this.issueScreens$=this._store.selectIssueScreens,this.mainScreensColumns=[{field:"id",header:"Screen Code",sortable:!0},{field:"priority",header:"Priority"},{field:"screenOrder",header:"Order",sortable:!0},{field:"screenName",header:"Screen Name",type:"tag"},{field:"screenType",header:"Screen Type",sortable:!0},{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"raised",text:"View",color:"accent",mode:"single",disabled:!1,hidden:r=>!1,callback:r=>this.view(r.id)},{type:"raised",text:"Edit",color:"accent",mode:"single",disabled:!1,hidden:r=>!1,callback:r=>this.edit(r.id)},{type:"raised",text:"Enable",class:"green",mode:"single",disabled:!1,hidden:r=>0!=r.screenEnabled,callback:r=>this.enable(r.id)},{type:"raised",text:"Disable",class:"gray",mode:"single",disabled:!1,hidden:r=>0==r.screenEnabled,callback:r=>this.disable(r.id)}]}}],this.issueScreensColumns=[{field:"id",header:"Screen Code",sortable:!0},{field:"priority",header:"Priority"},{field:"screenName",header:"Screen Name",sortable:!0},{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"raised",text:"View",color:"accent",mode:"single",disabled:!1,hidden:r=>!1,callback:r=>this.view(r.id)},{type:"raised",text:"Edit",color:"accent",mode:"single",disabled:!1,hidden:r=>!1,callback:r=>this.edit(r.id)},{type:"raised",text:"Enable",class:"green",mode:"single",disabled:!1,hidden:r=>0!=r.screenEnabled,callback:r=>this.enable(r.id)},{type:"raised",text:"Disable",class:"gray",mode:"single",disabled:!1,hidden:r=>0==r.screenEnabled,callback:r=>this.disable(r.id)}]}}]}enable(n){this._store.changeEditState({id:n,screenEnabled:!0})}disable(n){this._store.changeEditState({id:n,screenEnabled:!1})}updateOrder(n,t){this._store.changeOrder({id:n,screenOrder:t})}edit(n){this._store.resetState(),this.router.navigateByUrl("/visa/tracker/setup/"+n)}view(n){this._store.resetState(),this.router.navigateByUrl("/visa/tracker/setup/view/"+n)}gotToMultiIssue(){this._store.resetState(),this.router.navigateByUrl("/visa/tracker/setup-multiple/issue")}gotToMultiNormal(){this._store.resetState(),this.router.navigateByUrl("/visa/tracker/setup-multiple/normal")}gotToSetup(){this.router.navigateByUrl("/visa/tracker/setup")}ngOnInit(){this._store.fetchScreensList()}};Y.\u0275fac=function(n){return new(n||Y)(e.Y36(x),e.Y36(A.F0),e.Y36(A.gz))},Y.\u0275cmp=e.Xpm({type:Y,selectors:[["app-tracker"]],decls:32,vars:6,consts:[[1,"fluid-container","m-4"],[2,"color","red"],[1,"row"],[1,"col-md-7"],[1,"col-md-5"],["cc-raised-button","","color","accent",1,"m-1",3,"click"],[1,"float-right"],[4,"ngIf"],["noResultTpl",""],[3,"noResultTemplate","data","columns","length","pageOnFront","cellTemplate"],[3,"ccGridCell"],["typeTpl",""],["titleTpl",""],["orderTpl",""],[3,"innerHTML",4,"ngIf"],["style","color: green",3,"innerHTML",4,"ngIf"],[3,"innerHTML"],[2,"color","green",3,"innerHTML"],[2,"cursor","pointer",3,"click"],["issueTpl",""]],template:function(n,t){1&n&&(e.TgZ(0,"div",0)(1,"h3",1),e._uU(2,"Client Tracker"),e.qZA(),e.TgZ(3,"cc-card")(4,"cc-card-header",2)(5,"div",3)(6,"h3"),e._uU(7,"Main Screens"),e.qZA()(),e.TgZ(8,"div",4)(9,"button",5),e.NdJ("click",function(){return t.gotToMultiIssue()}),e._uU(10," Multi Issue Screen "),e.qZA(),e.TgZ(11,"button",5),e.NdJ("click",function(){return t.gotToMultiNormal()}),e._uU(12," Multi Normal Screen "),e.qZA(),e.TgZ(13,"button",5),e.NdJ("click",function(){return t.gotToSetup()}),e.TgZ(14,"span"),e._uU(15,"+"),e.qZA(),e._uU(16," Add Visa Step "),e.qZA()()(),e._UZ(17,"cc-card-actions",6),e.TgZ(18,"cc-card-content"),e.YNc(19,rn,8,13,"ng-container",7),e.ALo(20,"async"),e.YNc(21,an,2,0,"ng-template",null,8,e.W1O),e.qZA()(),e.TgZ(23,"cc-card")(24,"cc-card-header")(25,"h3"),e._uU(26,"Issue Screens"),e.qZA()(),e.TgZ(27,"cc-card-content"),e.YNc(28,pn,7,10,"ng-container",7),e.ALo(29,"async"),e.YNc(30,dn,2,0,"ng-template",null,8,e.W1O),e.qZA()()()),2&n&&(e.xp6(19),e.Q6J("ngIf",e.lcZ(20,2,t.mainScreens$)),e.xp6(9),e.Q6J("ngIf",e.lcZ(29,4,t.issueScreens$)))},directives:[_.Dt,_.oJ,k.uu,_.zM,_.uw,h.O5,N.Ge,N.VC],pipes:[h.Ov],styles:[""],changeDetection:0}),Y=(0,b.gn)([T.kG],Y);var v=g(26991),y=g(63900),ge=g(11365),H=g(21799);let mn=(()=>{class i{constructor(t,o,r,a,c){this._actions=t,this._store=o,this._service=r,this._router=a,this._notificationService=c,this.fetchScreensList=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(ae),(0,y.w)(p=>this._service.fetchScreensList()),(0,f.U)(p=>Te({payload:p})))),this.changeEditState=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(ve),(0,y.w)(p=>this._service.changeEditState(p.data)),(0,f.U)(p=>ae()))),this.changeOrder=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(Ce),(0,y.w)(p=>this._service.changeOrder(p.data)),(0,f.U)(p=>ae()))),this.fetchConfiguration=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(ce),(0,y.w)(p=>this._service.fetchConfigurations()),(0,f.U)(p=>xe({payload:p})))),this.createRequest=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(ct),(0,y.w)(p=>this._service.createPageRequest(p.page)),(0,f.U)(p=>pt()))),this.createRequestSuccess=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(pt),(0,ge.M)(this._store.selectSetupPage),(0,y.w)(([p,d])=>(this._router.navigateByUrl("/visa/tracker"),this._notificationService.notifySuccess("Page Created Successfully").pipe((0,f.U)(()=>me())))))),this.getPage=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(ut),(0,y.w)(p=>this._service.getPage(p.id)),(0,f.U)(p=>gt({data:p})))),this.getMultiPage=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(_t),(0,y.w)(p=>this._service.getMultiPage(p.screenType)),(0,f.U)(p=>ft({data:p})))),this.editRequest=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(st),(0,y.w)(p=>this._service.editPageRequest(p.page)),(0,f.U)(p=>dt()))),this.editMultiPageRequest=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(lt),(0,y.w)(p=>this._service.editMultiPageRequest(p.page)),(0,f.U)(p=>mt()))),this.editMultiRequestSuccess=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(mt),(0,ge.M)(this._store.selectSetupPage),(0,y.w)(([p,d])=>this._notificationService.notifySuccess("Page Edited Successfully").pipe((0,y.w)(()=>(this._router.navigateByUrl("/visa/tracker"),(0,oe.of)(ce()))))))),this.editRequestSuccess=(0,v.GW)(()=>this._actions.pipe((0,v.l4)(dt),(0,ge.M)(this._store.selectSetupPage),(0,y.w)(([p,d])=>this._notificationService.notifySuccess("Page Edited Successfully").pipe((0,y.w)(()=>(this._router.navigateByUrl("/visa/tracker"),(0,oe.of)(ce())))))))}}return i.\u0275fac=function(t){return new(t||i)(e.LFG(v.eX),e.LFG(x),e.LFG(ue),e.LFG(A.F0),e.LFG(H.zg))},i.\u0275prov=e.Yz7({token:i,factory:i.\u0275fac}),i})(),un=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.oAB({type:i}),i.\u0275inj=e.cJS({imports:[[h.ez,s.Aw.forFeature(Ct,jt),v.sQ.forFeature([mn])]]}),i})();var _e=g(39841),St=g(48966),U=g(85185);function gn(i,n){if(1&i){const t=e.EpF();e.ynx(0),e.TgZ(1,"form")(2,"div",1)(3,"div",2)(4,"h5"),e._uU(5,"Add New Step"),e.qZA()(),e.TgZ(6,"div",3)(7,"cc-icon",4),e.NdJ("click",function(){return e.CHM(t),e.oxw().cancel()}),e._uU(8,"highlight_off"),e.qZA()()(),e.TgZ(9,"div",5)(10,"form",6)(11,"div",7)(12,"cc-label",8),e._uU(13," Step "),e.qZA(),e._UZ(14,"cc-select",9),e.qZA()()(),e.TgZ(15,"div",10)(16,"button",11),e.NdJ("click",function(){return e.CHM(t),e.oxw().cancel()}),e._uU(17," Cancel "),e.qZA(),e.TgZ(18,"button",12),e.NdJ("click",function(){return e.CHM(t),e.oxw().save()}),e._uU(19," Done "),e.qZA()()(),e.BQk()}if(2&i){const t=n.ngIf,o=e.oxw();e.xp6(10),e.Q6J("formGroup",o.form),e.xp6(4),e.Q6J("data",t)}}let _n=(()=>{class i{constructor(t,o,r){this._store=t,this.formBuilder=o,this.dialogRef=r,this.selectedSteps$=this._store.selectVisaSteps.pipe((0,f.U)(a=>a.map(c=>({id:c.workFlowTask.id,text:c.workFlowTask.taskLabel})))),this.visaSteps$=(0,_e.a)([this._store.selectWorkFlowTasks,this.selectedSteps$]).pipe((0,f.U)(([a,c])=>a.map(p=>{const d=c.some(m=>m.id===p.id);return{id:p.id,text:p.taskLabel,disabled:d}}))),this.form=this.formBuilder.group({visaStep:""})}ngOnInit(){this._store.fetchScreensList()}save(){let t;this._store.selectVisaStepById(this.form.value.visaStep).subscribe(o=>t={priority:null,workFlowTask:o}),this._store.addStep(t),this.dialogRef.close()}cancel(){this.dialogRef.close()}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(x),e.Y36(l.qu),e.Y36(St.so))},i.\u0275cmp=e.Xpm({type:i,selectors:[["add-step-dialog"]],decls:2,vars:3,consts:[[4,"ngIf"],["mat-dialog-title","",1,"d-inline-block","w-100"],[1,"float-left","w-75"],[1,"float-right"],[2,"cursor","pointer",3,"click"],["mat-dialog-content",""],[1,"row",3,"formGroup"],[1,"col-md-12","row"],[1,"col-md-2"],["formControlName","visaStep","search","true","required","true",1,"col-md-10",3,"data"],["mat-dialog-actions","",1,"col-12","text-center","d-flex","justify-content-center","mt-3"],["cc-raised-button","","color","primary",1,"m-1",3,"click"],["cc-raised-button","","color","accent",1,"m-1",3,"click"]],template:function(t,o){1&t&&(e.YNc(0,gn,20,2,"ng-container",0),e.ALo(1,"async")),2&t&&e.Q6J("ngIf",e.lcZ(1,1,o.visaSteps$))},directives:[h.O5,l._Y,l.JL,l.F,O.Q9,l.sg,U.k_,P.jB,l.JJ,l.u,l.Q7,k.uu],pipes:[h.Ov],styles:[""],changeDetection:0}),i})();var re=g(38646);function fn(i,n){1&i&&e._UZ(0,"div",13),2&i&&e.Q6J("innerHTML",n.$implicit.tags[0].value,e.oJD)}function hn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"cc-input",14),e.NdJ("ngModelChange",function(r){const c=e.CHM(t).index;return e.oxw().selectedConditions[c]=r}),e.qZA()()}if(2&i){const t=n.index,o=e.oxw();e.xp6(1),e.Q6J("name","input-"+t)("ngModel",o.selectedConditions[t])}}function Tn(i,n){1&i&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}const vn=function(){return[]},Cn=function(i,n){return{condition:i,name:n}};let xn=(()=>{class i{constructor(t,o,r){this._service=t,this.dialogRef=o,this._cdr=r,this.conditionsColumns=[{field:"condition",header:"The Condition"},{field:"name",header:"Name",sortable:!0},{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"raised",text:"Save",color:"accent",mode:"single",disabled:!1,hidden:a=>!1,callback:(a,c)=>this.save(a,c)}]}}]}ngOnInit(){this.conditions$=this._service.fetchConditionsList(),this.conditions$.subscribe(t=>{this.conditions=t,this.selectedConditions=t.map(o=>o.name),this._cdr.detectChanges()})}save(t,o){this._service.updateConditionName(t.id,this.selectedConditions[o]).subscribe()}cancel(){this.dialogRef.close()}}return i.\u0275fac=function(t){return new(t||i)(e.Y36(ue),e.Y36(St.so),e.Y36(e.sBO))},i.\u0275cmp=e.Xpm({type:i,selectors:[["conditions-list-dialog"]],decls:21,vars:12,consts:[["mat-dialog-title","",1,"d-inline-block","w-100"],[1,"float-left","w-75"],[1,"float-right"],[2,"cursor","pointer",3,"click"],["mat-dialog-content","",1,"d-flex"],[1,"col-md-12"],[3,"noResultTemplate","data","columns","length","showPaginator","cellTemplate"],[3,"ccGridCell"],["conditionTpl",""],["nameTpl",""],["noResultTpl",""],["mat-dialog-actions","",1,"col-12","text-center","d-flex","justify-content-center","mt-3"],["cc-raised-button","","color","primary",1,"m-1",3,"click"],[3,"innerHTML"],[3,"name","ngModel","ngModelChange"]],template:function(t,o){if(1&t&&(e.ynx(0),e.TgZ(1,"form")(2,"div",0)(3,"div",1)(4,"h5"),e._uU(5,"Conditions List"),e.qZA()(),e.TgZ(6,"div",2)(7,"cc-icon",3),e.NdJ("click",function(){return o.cancel()}),e._uU(8,"highlight_off"),e.qZA()()(),e.TgZ(9,"div",4)(10,"div",5),e._UZ(11,"cc-datagrid",6),e.YNc(12,fn,1,1,"ng-template",7,8,e.W1O),e.YNc(14,hn,2,2,"ng-template",7,9,e.W1O),e.YNc(16,Tn,2,0,"ng-template",null,10,e.W1O),e.qZA()(),e.TgZ(18,"div",11)(19,"button",12),e.NdJ("click",function(){return o.cancel()}),e._uU(20," Close "),e.qZA()()(),e.BQk()),2&t){const r=e.MAs(13),a=e.MAs(15),c=e.MAs(17);e.xp6(11),e.Q6J("noResultTemplate",c)("data",o.conditions?o.conditions:e.DdM(8,vn))("columns",o.conditionsColumns)("length",o.conditions?o.conditions.length:0)("showPaginator",!1)("cellTemplate",e.WLB(9,Cn,r,a)),e.xp6(1),e.Q6J("ccGridCell",o.conditions),e.xp6(2),e.Q6J("ccGridCell",o.conditions)}},directives:[l._Y,l.JL,l.F,O.Q9,N.Ge,N.VC,ne.G,l.JJ,l.On,k.uu],styles:[""],changeDetection:0}),i})();var se=g(79136),Z=g(13859);let D=class{constructor(n,t,o,r){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(a=>a.map(c=>({id:c.id,text:c.name,data:c})))),this.onLinkDeleted=new e.vpe,this.predefinedLinks$=this._store.selectPredefinedLinks.pipe((0,f.U)(a=>a.map(c=>({value:c.code.replace(/@/g,""),text:c.name}))))}ngOnInit(){this.predefinedLinks$.subscribe(n=>{this.links=n,this.urlConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!0,parameters:this.links,predefinedLinks:[]}}),this.hyperConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!1,parameters:[],predefinedLinks:[]},this.textConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.form.valueChanges.subscribe(n=>{var t,o,r;this.text=null===(t=n.text)||void 0===t?void 0:t.text,this.link=null===(o=n.hyperLinkText)||void 0===o?void 0:o.text,this.url=null===(r=n.urlText)||void 0===r?void 0:r.text})}deleteHyperLink(){this.onLinkDeleted.emit()}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}};D.\u0275fac=function(n){return new(n||D)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ))},D.\u0275cmp=e.Xpm({type:D,selectors:[["hyperlink"]],inputs:{viewOnly:"viewOnly",stepIndex:"stepIndex",componentIndex:"componentIndex",linkIndex:"linkIndex",component:"component",link:"link",url:"url",text:"text",form:"form",parameters:"parameters"},outputs:{onLinkDeleted:"onLinkDeleted"},decls:17,vars:15,consts:[[1,"row",3,"formGroup"],[1,"col-md-8"],[2,"display","inline-block",3,"innerHTML"],["target","_blank",2,"display","inline-block",3,"innerHTML","href"],[1,"col-md-4","pb-4"],[1,"float-right"],["cc-mini-fab","","color","accent",3,"disabled","click"],["formGroupName","text",1,"form-group","col-md-3"],["formControlName","text","required","true",1,"w-100",3,"label","config","ccdisabled"],["formGroupName","hyperLinkText",1,"form-group","col-md-4"],["formGroupName","urlText",1,"form-group","col-md-5"],["formControlName","text","required","true",1,"w-100",3,"label","config","ccdisabled","parametrButtonName"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0)(1,"div",1),e._uU(2," \u2b9e "),e._UZ(3,"div",2)(4,"a",3),e.qZA(),e.TgZ(5,"div",4)(6,"div",5)(7,"button",6),e.NdJ("click",function(){return t.deleteHyperLink()}),e.TgZ(8,"cc-icon"),e._uU(9,"delete"),e.qZA()()()(),e._UZ(10,"br"),e.TgZ(11,"div",7),e._UZ(12,"cc-new-editor",8),e.qZA(),e.TgZ(13,"div",9),e._UZ(14,"cc-new-editor",8),e.qZA(),e.TgZ(15,"div",10),e._UZ(16,"cc-new-editor",11),e.qZA()()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(3),e.Q6J("innerHTML",t.text,e.oJD),e.xp6(1),e.s9C("href",t.url,e.LSH),e.Q6J("innerHTML",t.link,e.oJD),e.xp6(3),e.Q6J("disabled",t.viewOnly),e.xp6(5),e.Q6J("label","Text")("config",t.textConfig)("ccdisabled",t.viewOnly),e.xp6(2),e.Q6J("label","Hyperlink Text")("config",t.hyperConfig)("ccdisabled",t.viewOnly),e.xp6(2),e.Q6J("label","URL")("config",t.urlConfig)("ccdisabled",t.viewOnly)("parametrButtonName","Add Link"))},directives:[l.JL,l.sg,k.uu,O.Q9,l.x0,M.F,l.JJ,l.u,l.Q7],styles:[""],changeDetection:0}),D=(0,b.gn)([T.kG],D);let $=class{constructor(n,t,o,r){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(a=>a.map(c=>({id:c.id,text:c.name,data:c})))),this.predefinedLinks$=this._store.selectPredefinedLinks.pipe((0,f.U)(a=>a.map(c=>({value:c.code,text:c.name})))),this.onNoteDeleted=new e.vpe}ngOnInit(){this.predefinedLinks$.subscribe(n=>{this.noteConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!0,addParameter:!0,parameters:this.parameters.map(t=>({text:t,value:t})),predefinedLinks:n}}),this.form.valueChanges.subscribe(n=>{var t,o;n.text&&this.text!=(null===(t=n.text)||void 0===t?void 0:t.text)&&(this.text=null===(o=n.text)||void 0===o?void 0:o.text)})}deleteNote(){this.onNoteDeleted.emit()}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}textChanged(n){this._store.saveNote(this.noteIndex,this.componentIndex,this.stepIndex,{text:n})}};function kn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"hyperlink",12),e.NdJ("onLinkDeleted",function(){const a=e.CHM(t).index,c=e.oxw(3);return c.deleteHyperLink(a,c.j)}),e.qZA()()}if(2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("form",r.form.controls.hyperLinks.controls[o])("linkIndex",o)("stepIndex",r.stepIndex)("componentIndex",r.j)("parameters",r.parameters)("component",t)("link",t.hyperLinkText.text)("url",t.urlText.text)("text",t.text.text)("viewOnly",r.viewOnly)}}function bn(i,n){if(1&i&&(e.ynx(0,10),e.YNc(1,kn,2,10,"div",11),e.BQk()),2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.component.hyperLinks)}}function Sn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"note",14),e.NdJ("onNoteDeleted",function(){const a=e.CHM(t).index,c=e.oxw(3);return c.deleteNote(a,c.j)}),e.qZA()()}if(2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("form",r.form.controls.notes.controls[o])("noteIndex",o)("parameters",r.parameters)("stepIndex",r.stepIndex)("componentIndex",r.j)("component",t)("text",t.text.text)("viewOnly",r.viewOnly)}}function yn(i,n){if(1&i&&(e.ynx(0,13),e.YNc(1,Sn,2,8,"div",11),e.BQk()),2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.component.notes)}}function wn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"div",2),e._UZ(2,"cc-new-editor",3),e.qZA(),e.YNc(3,bn,2,1,"ng-container",4),e.YNc(4,yn,2,1,"ng-container",5),e.TgZ(5,"div",6)(6,"button",7),e.NdJ("click",function(){e.CHM(t);const r=e.oxw();return r.addLink(r.j)}),e.TgZ(7,"span",8),e._uU(8," \u2b9e Add Hyperlink"),e.qZA(),e.TgZ(9,"span",9),e._uU(10,"+"),e.qZA()(),e.TgZ(11,"button",7),e.NdJ("click",function(){e.CHM(t);const r=e.oxw();return r.addNote(r.j)}),e.TgZ(12,"span",8),e._uU(13," | Add Note"),e.qZA(),e.TgZ(14,"span",9),e._uU(15,"+"),e.qZA()()()()}if(2&i){const t=e.oxw();e.xp6(2),e.Q6J("id","nomral"+t.getRandomNumber(0,1e4))("config",t.normalConfig)("ccdisabled",t.viewOnly),e.xp6(1),e.Q6J("ngIf",t.isNormalComponent(t.component)),e.xp6(1),e.Q6J("ngIf",t.isNormalComponent(t.component)),e.xp6(2),e.Q6J("disabled",t.viewOnly),e.xp6(5),e.Q6J("disabled",t.viewOnly)}}function Pn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"hyperlink",12),e.NdJ("onLinkDeleted",function(){const a=e.CHM(t).index,c=e.oxw(3);return c.deleteHyperLink(a,c.j)}),e.qZA()()}if(2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("form",r.form.controls.hyperLinks.controls[o])("linkIndex",o)("stepIndex",r.stepIndex)("componentIndex",r.j)("parameters",r.parameters)("component",t)("link",t.hyperLinkText.text)("url",t.urlText.text)("text",t.text.text)("viewOnly",r.viewOnly)}}function Nn(i,n){if(1&i&&(e.ynx(0,10),e.YNc(1,Pn,2,10,"div",11),e.BQk()),2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.component.hyperLinks)}}function Zn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"note",14),e.NdJ("onNoteDeleted",function(){const a=e.CHM(t).index,c=e.oxw(3);return c.deleteNote(a,c.j)}),e.qZA()()}if(2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("form",r.form.controls.notes.controls[o])("noteIndex",o)("parameters",r.parameters)("stepIndex",r.stepIndex)("componentIndex",r.j)("component",t)("text",t.text.text)("viewOnly",r.viewOnly)}}function An(i,n){if(1&i&&(e.ynx(0,13),e.YNc(1,Zn,2,8,"div",11),e.BQk()),2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.component.notes)}}function On(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"div",2),e._UZ(2,"cc-new-editor",3),e.qZA(),e.YNc(3,Nn,2,1,"ng-container",4),e.YNc(4,An,2,1,"ng-container",5),e.TgZ(5,"div",6)(6,"button",7),e.NdJ("click",function(){e.CHM(t);const r=e.oxw();return r.addLink(r.j)}),e.TgZ(7,"span",8),e._uU(8," \u2b9e Add Hyperlink"),e.qZA(),e.TgZ(9,"span",9),e._uU(10,"+"),e.qZA()(),e.TgZ(11,"button",7),e.NdJ("click",function(){e.CHM(t);const r=e.oxw();return r.addNote(r.j)}),e.TgZ(12,"span",8),e._uU(13," | Add Note"),e.qZA(),e.TgZ(14,"span",9),e._uU(15,"+"),e.qZA()()()()}if(2&i){const t=e.oxw();e.xp6(2),e.Q6J("id","nomral"+t.getRandomNumber(0,1e4))("config",t.normalConfig)("ccdisabled",t.viewOnly),e.xp6(1),e.Q6J("ngIf",t.isNormalComponent(t.component)),e.xp6(1),e.Q6J("ngIf",t.isNormalComponent(t.component)),e.xp6(2),e.Q6J("disabled",t.viewOnly),e.xp6(5),e.Q6J("disabled",t.viewOnly)}}$.\u0275fac=function(n){return new(n||$)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ))},$.\u0275cmp=e.Xpm({type:$,selectors:[["note"]],inputs:{viewOnly:"viewOnly",stepIndex:"stepIndex",componentIndex:"componentIndex",noteIndex:"noteIndex",component:"component",text:"text",form:"form",parameters:"parameters"},outputs:{onNoteDeleted:"onNoteDeleted"},decls:15,vars:6,consts:[[1,"row",3,"formGroup"],[1,"col-md-8"],[2,"color","orange"],[2,"font-weight","500"],[2,"display","inline-block",3,"innerHTML"],[1,"col-md-4","pb-4"],[1,"float-right"],["cc-mini-fab","","color","accent",3,"disabled","click"],[1,"col-md-12"],["formGroupName","text",1,"form-group","col-md-12"],["formControlName","text","required","true",3,"label","config","ccdisabled"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0)(1,"div",1)(2,"span",2),e._uU(3,"|"),e.qZA(),e.TgZ(4,"span",3),e._uU(5,"Note:"),e.qZA(),e._UZ(6,"p",4),e.qZA(),e.TgZ(7,"div",5)(8,"div",6)(9,"button",7),e.NdJ("click",function(){return t.deleteNote()}),e.TgZ(10,"cc-icon"),e._uU(11,"delete"),e.qZA()()()(),e.TgZ(12,"div",8)(13,"div",9),e._UZ(14,"cc-new-editor",10),e.qZA()()()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(6),e.Q6J("innerHTML",t.text,e.oJD),e.xp6(3),e.Q6J("disabled",t.viewOnly),e.xp6(5),e.Q6J("label","Note")("config",t.noteConfig)("ccdisabled",t.viewOnly))},directives:[l.JL,l.sg,k.uu,O.Q9,l.x0,M.F,l.JJ,l.u,l.Q7],styles:[""],changeDetection:0}),$=(0,b.gn)([T.kG],$);let F=class{constructor(n){this._store=n,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(t=>t.map(o=>({id:o.id,text:o.name,data:o})))),this.onLinkAdded=new e.vpe,this.onLinkDeleted=new e.vpe,this.onNoteAdded=new e.vpe,this.onNoteDeleted=new e.vpe}ngOnInit(){this.form.valueChanges.subscribe(n=>{this.encodedText=encodeURIComponent(this.component.text.text)})}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}isNormalComponent(n){return"Normal"===n.componentType}addLink(n){this.onLinkAdded.emit({component:this.form.value,componentIndex:n,stepIndex:-1})}deleteHyperLink(n,t){this.onLinkDeleted.emit({component:this.form.value,index:n,componentIndex:t,stepIndex:-1})}addNote(n){this.onNoteAdded.emit({component:this.form.value,componentIndex:n,stepIndex:-1})}deleteNote(n,t){this.onNoteDeleted.emit({component:this.form.value,index:n,componentIndex:t,stepIndex:-1})}};function Jn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",6),e.NdJ("click",function(){e.CHM(t);const r=e.oxw().index,a=e.oxw(2);return a.deleteStep(r,a.j)}),e.TgZ(1,"cc-icon"),e._uU(2,"delete"),e.qZA()()}if(2&i){const t=e.oxw(3);e.Q6J("disabled",t.viewOnly)}}function Mn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-card",4)(1,"cc-card-title",5)(2,"strong"),e._uU(3),e.qZA(),e.YNc(4,Jn,3,1,"button",7),e.qZA(),e.TgZ(5,"cc-card-content")(6,"normal-component",8),e.NdJ("onLinkAdded",function(r){e.CHM(t);const a=e.oxw(2);return a.addLink(r,a.j)})("onLinkDeleted",function(r){e.CHM(t);const a=e.oxw(2);return a.deleteHyperLink(r,a.j)})("onNoteAdded",function(r){e.CHM(t);const a=e.oxw(2);return a.addNote(r,a.j)})("onNoteDeleted",function(r){e.CHM(t);const a=e.oxw(2);return a.deleteNote(r,a.j)}),e.qZA()()()}if(2&i){const t=n.$implicit,o=n.index,r=e.oxw(2);e.xp6(3),e.hij("Step ",o+1,""),e.xp6(1),e.Q6J("ngIf",o>0),e.xp6(2),e.Q6J("parameters",r.parameters)("form",r.form.controls.normalComponents.controls[o])("component",t)("j",o)("stepIndex",r.index)("normalConfig",r.normalConfig)("urlConfig",r.urlConfig)("noteConfig",r.noteConfig)("viewOnly",r.viewOnly)}}function jn(i,n){if(1&i){const t=e.EpF();e.ynx(0,2),e.YNc(1,Mn,7,11,"cc-card",3),e.TgZ(2,"cc-card",4)(3,"cc-card-title",5)(4,"strong"),e._uU(5),e.qZA(),e.TgZ(6,"button",6),e.NdJ("click",function(){e.CHM(t);const r=e.oxw();return r.addStep(r.j)}),e.TgZ(7,"cc-icon"),e._uU(8,"add"),e.qZA()()()(),e.BQk()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("ngForOf",t.component.normalComponents),e.xp6(4),e.hij("Step ",t.component.normalComponents.length+1,""),e.xp6(1),e.Q6J("disabled",t.viewOnly)}}F.\u0275fac=function(n){return new(n||F)(e.Y36(x))},F.\u0275cmp=e.Xpm({type:F,selectors:[["normal-component"]],inputs:{viewOnly:"viewOnly",stepIndex:"stepIndex",component:"component",j:"j",normalConfig:"normalConfig",urlConfig:"urlConfig",noteConfig:"noteConfig",form:"form",parameters:"parameters"},outputs:{onLinkAdded:"onLinkAdded",onLinkDeleted:"onLinkDeleted",onNoteAdded:"onNoteAdded",onNoteDeleted:"onNoteDeleted"},decls:3,vars:3,consts:[[1,"col-md-12",3,"formGroup"],[4,"ngIf"],["formGroupName","text"],["formControlName","text","required","true",1,"m-2",3,"id","config","ccdisabled"],["formArrayName","hyperLinks",4,"ngIf"],["formArrayName","notes",4,"ngIf"],[1,"row"],[2,"background-color","lightgray","border-radius","5px","border","none","padding","5px","width","25%","margin","0 5px","font-size","small",3,"disabled","click"],[1,"float-left"],[1,"float-right",2,"color","gray","border","2px solid gray","border-radius","50%","width","22px","height","22px","display","inline-block"],["formArrayName","hyperLinks"],[4,"ngFor","ngForOf"],[3,"form","linkIndex","stepIndex","componentIndex","parameters","component","link","url","text","viewOnly","onLinkDeleted"],["formArrayName","notes"],[3,"form","noteIndex","parameters","stepIndex","componentIndex","component","text","viewOnly","onNoteDeleted"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0),e.YNc(1,wn,16,7,"div",1),e.YNc(2,On,16,7,"div",1),e.qZA()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("ngIf",null!=t.stepIndex),e.xp6(1),e.Q6J("ngIf",null==t.stepIndex))},directives:[l.JL,l.sg,h.O5,l.x0,M.F,l.JJ,l.u,l.Q7,l.CE,h.sg,D,$],styles:[""],changeDetection:0}),F=(0,b.gn)([T.kG],F);let E=class{constructor(n,t,o,r){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(a=>a.map(c=>({id:c.id,text:c.name,data:c})))),this.onLinkAdded=new e.vpe,this.onLinkDeleted=new e.vpe,this.onNoteAdded=new e.vpe,this.onNoteDeleted=new e.vpe,this.onSave=new e.vpe}ngOnInit(){}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}isStepperComponent(n){return"Stepper"===n.componentType}addLink(n,t){this.onLinkAdded.emit(Object.assign(Object.assign({},n),{stepIndex:t}))}deleteHyperLink(n,t){this.onLinkDeleted.emit(Object.assign(Object.assign({},n),{stepIndex:t}))}addNote(n,t){this.onNoteAdded.emit(Object.assign(Object.assign({},n),{stepIndex:t}))}deleteNote(n,t){this.onNoteDeleted.emit(Object.assign(Object.assign({},n),{stepIndex:t}))}addStep(n){this.onSave.emit(),this._store.addStepComponent(n)}deleteStep(n,t){this.onSave.emit(),this._store.deleteStepComponent(n,t)}};function Ln(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",5),e.NdJ("click",function(){e.CHM(t);const r=e.oxw(2);return r.addSeperator(r.index)}),e.TgZ(1,"span",6),e._uU(2," -- Add Seperation --"),e.qZA(),e.TgZ(3,"span",7),e._uU(4,"+"),e.qZA()()}}function In(i,n){if(1&i&&(e.TgZ(0,"div",3),e.YNc(1,Ln,5,0,"button",4),e.qZA()),2&i){const t=e.oxw();e.xp6(1),e.Q6J("ngIf",!t.viewOnly)}}function Hn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-input",17),e.NdJ("change",function(){return e.CHM(t),e.oxw(2).textChange()}),e.qZA()}if(2&i){const t=e.oxw(2);e.Q6J("disabled",t.viewOnly)}}function qn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",18),e.NdJ("click",function(){e.CHM(t);const r=e.oxw(2);return r.deleteSeperator(r.index)}),e.TgZ(1,"cc-icon"),e._uU(2,"delete"),e.qZA()()}if(2&i){const t=e.oxw(2);e.Q6J("disabled",t.viewOnly)}}function Qn(i,n){if(1&i&&(e.TgZ(0,"div",8)(1,"div",9),e.YNc(2,Hn,1,1,"cc-input",10),e.qZA(),e.TgZ(3,"div",11)(4,"div",12),e._UZ(5,"div",13),e.TgZ(6,"div",14),e._uU(7),e.qZA(),e._UZ(8,"div",13),e.qZA()(),e.TgZ(9,"div",15),e.YNc(10,qn,3,1,"button",16),e.qZA()()),2&i){const t=e.oxw();e.xp6(2),e.Q6J("ngIf",t.component.added),e.xp6(5),e.hij(" ",t.component.text.text," "),e.xp6(3),e.Q6J("ngIf",0!=t.index)}}E.\u0275fac=function(n){return new(n||E)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ))},E.\u0275cmp=e.Xpm({type:E,selectors:[["stepper-component"]],inputs:{viewOnly:"viewOnly",index:"index",component:"component",j:"j",normalConfig:"normalConfig",urlConfig:"urlConfig",noteConfig:"noteConfig",form:"form",parameters:"parameters"},outputs:{onLinkAdded:"onLinkAdded",onLinkDeleted:"onLinkDeleted",onNoteAdded:"onNoteAdded",onNoteDeleted:"onNoteDeleted",onSave:"onSave"},decls:2,vars:2,consts:[[1,"col-md-12",3,"formGroup"],["formArrayName","normalComponents",4,"ngIf"],["formArrayName","normalComponents"],["class","m-2",4,"ngFor","ngForOf"],[1,"m-2"],[1,"align-content-center"],["cc-mini-fab","","color","accent",1,"float-right",3,"disabled","click"],["class","float-right","cc-mini-fab","","color","accent",3,"disabled","click",4,"ngIf"],[1,"w-100",3,"parameters","form","component","j","stepIndex","normalConfig","urlConfig","noteConfig","viewOnly","onLinkAdded","onLinkDeleted","onNoteAdded","onNoteDeleted"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0),e.YNc(1,jn,9,3,"ng-container",1),e.qZA()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("ngIf",t.isStepperComponent(t.component)))},directives:[l.JL,l.sg,h.O5,l.CE,h.sg,_.Dt,_.K9,k.uu,O.Q9,_.uw,F],styles:[""],changeDetection:0}),E=(0,b.gn)([T.kG],E);let R=class{constructor(n,t,o,r){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(a=>a.map(c=>({id:c.id,text:c.name,data:c})))),this.onSave=new e.vpe}ngOnInit(){}isSeperatorComponent(n){return"Normal"===n.componentType}addSeperator(n){this.onSave.emit(),this._store.activateSeperator(n)}deleteSeperator(n){this.onSave.emit(),this._store.deActivateSeperator(n)}textChange(){this.onSave.emit()}};R.\u0275fac=function(n){return new(n||R)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ))},R.\u0275cmp=e.Xpm({type:R,selectors:[["seperator-component"]],inputs:{viewOnly:"viewOnly",component:"component",index:"index",form:"form",parameters:"parameters"},outputs:{onSave:"onSave"},decls:3,vars:3,consts:[[1,"w-100",3,"formGroup"],["class","row",4,"ngIf"],["class","row","formGroupName","text",4,"ngIf"],[1,"row"],["style","\n        background-color: lightgray;\n        border-radius: 5px;\n        border: none;\n        padding: 5px;\n        width: 25%;\n        margin: 0 5px;\n        font-size: small;\n      ",3,"click",4,"ngIf"],[2,"background-color","lightgray","border-radius","5px","border","none","padding","5px","width","25%","margin","0 5px","font-size","small",3,"click"],[1,"float-left"],[1,"float-right",2,"color","gray","border","2px solid gray","border-radius","50%","width","22px","height","22px","display","inline-block"],["formGroupName","text",1,"row"],[1,"col-md-7","align-content-lg-end"],["placeholder","Separator","label","Separator","formControlName","text",3,"disabled","change",4,"ngIf"],[1,"col-md-4","align-content-lg-end"],[1,"separator-container"],[1,"separator-line"],[1,"separator-text"],[1,"col-md-1","align-content-center"],["class","float-right","cc-mini-fab","","color","accent",3,"disabled","click",4,"ngIf"],["placeholder","Separator","label","Separator","formControlName","text",3,"disabled","change"],["cc-mini-fab","","color","accent",1,"float-right",3,"disabled","click"]],template:function(n,t){1&n&&(e.TgZ(0,"cc-card",0),e.YNc(1,In,2,1,"div",1),e.YNc(2,Qn,11,3,"div",2),e.qZA()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("ngIf",!t.component.added),e.xp6(1),e.Q6J("ngIf",t.component.added))},directives:[_.Dt,l.JL,l.sg,h.O5,l.x0,ne.G,l.JJ,l.u,k.uu,O.Q9],styles:[".separator-container[_ngcontent-%COMP%]{display:flex;align-items:center;justify-content:center;margin:20px 0}.separator-line[_ngcontent-%COMP%]{flex-grow:1;height:1px;background-color:#ccc}.separator-text[_ngcontent-%COMP%]{margin:0 10px;font-weight:700;color:#000}"],changeDetection:0}),R=(0,b.gn)([T.kG],R);const Un=function(){return[]},Fn=function(){return{id:!0,text:"Primary CTA"}},Gn=function(){return{id:!1,text:"Secondary CTA"}},Bn=function(i,n){return[i,n]};let G=class{constructor(n,t){this._store=n,this.formBuilder=t,this.functionTypes$=this._store.selectFunctionTypes.pipe((0,f.U)(o=>o.map(r=>({id:r.label,text:r.label,data:r})))),this.isHome=!1}ngOnInit(){this.config={inline:!1,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]}}deleteCTA(){this._store.deleteCTA(this.index,this.isHome)}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}textChanged(){}functionTypesChanged(n){this._store.saveCTA(this.index,Object.assign(Object.assign({},this.form.value),{functionType:n}),this.isHome)}primaryButtonChanged(n){this._store.saveCTA(this.index,Object.assign(Object.assign({},this.form.value),{primaryButton:n}),this.isHome)}};G.\u0275fac=function(n){return new(n||G)(e.Y36(x),e.Y36(l.qu))},G.\u0275cmp=e.Xpm({type:G,selectors:[["call-to-action"]],inputs:{viewOnly:"viewOnly",index:"index",component:"component",isHome:"isHome",parameters:"parameters",colors:"colors",predefinedLinks:"predefinedLinks",form:"form"},decls:14,vars:17,consts:[[1,"row",3,"formGroup"],[1,"col-md-12"],[1,"float-right"],["cc-mini-fab","","color","accent",3,"disabled","click"],[1,"col-md-12","row"],["formGroupName","buttonName",1,"form-group","col-md-12"],["formControlName","text","required","true",3,"label","config","ccdisabled","input"],[1,"form-group","col-md-6"],["formControlName","functionType","label","Function Type","search","false","required","true","required","true",3,"data","disabled","valueChange"],["formControlName","primaryButton","label","Primary Button","search","false","required","true",3,"data","disabled","valueChange"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0)(1,"div",1)(2,"div",2)(3,"button",3),e.NdJ("click",function(){return t.deleteCTA()}),e.TgZ(4,"cc-icon"),e._uU(5,"delete"),e.qZA()()()(),e.TgZ(6,"div",4)(7,"div",5)(8,"cc-new-editor",6),e.NdJ("input",function(){return t.textChanged()}),e.qZA()(),e.TgZ(9,"div",7)(10,"cc-select",8),e.NdJ("valueChange",function(r){return t.functionTypesChanged(r)}),e.ALo(11,"async"),e.qZA()(),e.TgZ(12,"div",7)(13,"cc-select",9),e.NdJ("valueChange",function(r){return t.primaryButtonChanged(r)}),e.qZA()()()()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(3),e.Q6J("disabled",t.viewOnly),e.xp6(5),e.Q6J("label","Button Name")("config",t.config)("ccdisabled",t.viewOnly),e.xp6(2),e.Q6J("data",e.lcZ(11,9,t.functionTypes$)||e.DdM(11,Un))("disabled",t.viewOnly),e.xp6(3),e.Q6J("data",e.WLB(14,Bn,e.DdM(12,Fn),e.DdM(13,Gn)))("disabled",t.viewOnly))},directives:[l.JL,l.sg,k.uu,O.Q9,l.x0,M.F,l.JJ,l.u,l.Q7,P.jB],pipes:[h.Ov],styles:[""],changeDetection:0}),G=(0,b.gn)([T.kG],G);let K=class{constructor(n,t){this._store=n,this.formBuilder=t,this.functionTypes$=this._store.selectFunctionTypes.pipe((0,f.U)(o=>o.map(r=>({id:r.code,text:r.label,data:r})))),this.predefinedLinks$=this._store.selectPredefinedLinks.pipe((0,f.U)(o=>o.map(r=>({value:r.code.replace(/@/g,""),text:r.name}))))}ngOnInit(){this.predefinedLinks$.subscribe(n=>{this.links=n,this.urlConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!0,parameters:this.links,predefinedLinks:[]}}),this.hyperConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!1,parameters:[],predefinedLinks:[]},this.textConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.form.valueChanges.subscribe(n=>{var t,o,r;this.text=null===(t=n.text)||void 0===t?void 0:t.text,this.link=null===(o=n.hyperLinkText)||void 0===o?void 0:o.text,this.url=null===(r=n.urlText)||void 0===r?void 0:r.text.replace(/<[^>]*>/g,"")})}deleteHyperLink(){this._store.deleteBottomLink(this.index)}};function Yn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",22),e.NdJ("click",function(){e.CHM(t);const r=e.oxw(2).index;return e.oxw(3).deleteComponent(r)}),e.TgZ(1,"cc-icon"),e._uU(2,"delete"),e.qZA()()}if(2&i){const t=e.oxw(5);e.Q6J("disabled",t.viewOnly)}}function Dn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-card",14)(1,"cc-card-title")(2,"div",15)(3,"cc-radio-group",16),e.NdJ("change",function(){e.CHM(t);const r=e.oxw().index;return e.oxw(3).changeComponentType(r)}),e.TgZ(4,"cc-radio-button",17),e._uU(5,"Normal"),e.qZA(),e.TgZ(6,"cc-radio-button",18),e._uU(7,"Stepper"),e.qZA()(),e.TgZ(8,"div",19),e.YNc(9,Yn,3,1,"button",20),e.qZA()()(),e.TgZ(10,"cc-card-content")(11,"normal-component",21),e.NdJ("onLinkAdded",function(r){return e.CHM(t),e.oxw(4).addLink(r)})("onLinkDeleted",function(r){return e.CHM(t),e.oxw(4).deleteHyperLink(r)})("onNoteAdded",function(r){return e.CHM(t),e.oxw(4).addNote(r)})("onNoteDeleted",function(r){return e.CHM(t),e.oxw(4).deleteNote(r)}),e.qZA()()()}if(2&i){const t=e.oxw(),o=t.index,r=t.$implicit,a=e.oxw(3);e.xp6(3),e.Q6J("disabled",a.viewOnly),e.xp6(6),e.Q6J("ngIf",0!=o),e.xp6(2),e.Q6J("parameters",a.parameters)("form",a.form.controls.components.controls[o])("component",r)("j",o)("normalConfig",a.normalConfig)("urlConfig",a.urlConfig)("noteConfig",a.noteConfig)("viewOnly",a.viewOnly)}}function $n(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",25),e.NdJ("click",function(){e.CHM(t);const r=e.oxw(2).index;return e.oxw(3).deleteComponent(r)}),e.TgZ(1,"cc-icon"),e._uU(2,"delete"),e.qZA()()}}function En(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-card",14)(1,"cc-card-title")(2,"div",15)(3,"cc-radio-group",16),e.NdJ("change",function(){e.CHM(t);const r=e.oxw().index;return e.oxw(3).changeComponentType(r)}),e.TgZ(4,"cc-radio-button",17),e._uU(5,"Normal"),e.qZA(),e.TgZ(6,"cc-radio-button",18),e._uU(7,"Stepper"),e.qZA()(),e.TgZ(8,"div",19),e.YNc(9,$n,3,0,"button",23),e.qZA()()(),e.TgZ(10,"cc-card-content")(11,"stepper-component",24),e.NdJ("onLinkAdded",function(r){return e.CHM(t),e.oxw(4).addLink(r)})("onLinkDeleted",function(r){return e.CHM(t),e.oxw(4).deleteHyperLink(r)})("onNoteAdded",function(r){return e.CHM(t),e.oxw(4).addNote(r)})("onNoteDeleted",function(r){return e.CHM(t),e.oxw(4).deleteNote(r)})("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()()()}if(2&i){const t=e.oxw(),o=t.index,r=t.$implicit,a=e.oxw(3);e.xp6(3),e.Q6J("disabled",a.viewOnly),e.xp6(6),e.Q6J("ngIf",0!=o),e.xp6(2),e.Q6J("parameters",a.parameters)("index",o)("form",a.form.controls.components.controls[o])("component",r)("j",o)("normalConfig",a.normalConfig)("urlConfig",a.urlConfig)("noteConfig",a.noteConfig)("viewOnly",a.viewOnly)}}function Rn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",14)(1,"seperator-component",26),e.NdJ("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()()}if(2&i){const t=e.oxw(),o=t.index,r=t.$implicit,a=e.oxw(3);e.xp6(1),e.Q6J("parameters",a.parameters)("form",a.form.controls.components.controls[o])("component",r)("index",o)("viewOnly",a.viewOnly)}}function Kn(i,n){if(1&i&&(e.TgZ(0,"cc-card-content",11)(1,"div",12),e.YNc(2,Dn,12,10,"cc-card",13),e.YNc(3,En,12,11,"cc-card",13),e.YNc(4,Rn,2,5,"div",13),e.qZA()()),2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.Q6J("formGroupName",o),e.xp6(2),e.Q6J("ngIf",r.isNormalComponent(t)),e.xp6(1),e.Q6J("ngIf",r.isStepperComponent(t)),e.xp6(1),e.Q6J("ngIf",!(r.isNormalComponent(t)||r.isStepperComponent(t)))}}function zn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-card",8)(1,"cc-card-title"),e._uU(2," Screen Text "),e.qZA(),e.YNc(3,Kn,5,4,"cc-card-content",9),e.TgZ(4,"cc-card-footer")(5,"button",10),e.NdJ("click",function(){return e.CHM(t),e.oxw(2).addComponent()}),e._uU(6," Add Text Box "),e.qZA()()()}if(2&i){const t=e.oxw(2);e.xp6(3),e.Q6J("ngForOf",t.mainTrackerPage.components),e.xp6(2),e.Q6J("disabled",t.viewOnly)}}K.\u0275fac=function(n){return new(n||K)(e.Y36(x),e.Y36(l.qu))},K.\u0275cmp=e.Xpm({type:K,selectors:[["bottom-hyperlink"]],inputs:{viewOnly:"viewOnly",index:"index",component:"component",form:"form",parameters:"parameters",link:"link",url:"url",text:"text"},decls:17,vars:15,consts:[[1,"row",3,"formGroup"],[1,"col-md-8"],[2,"display","inline-block",3,"innerHTML"],["target","_blank",2,"display","inline-block",3,"innerHTML","href"],[1,"col-md-4","pb-4"],[1,"float-right"],["cc-mini-fab","","color","accent",3,"disabled","click"],["formGroupName","text",1,"form-group","col-md-6"],["formControlName","text","required","true",1,"w-100",3,"label","config","ccdisabled"],["formGroupName","hyperLinkText",1,"form-group","col-md-6"],["formGroupName","urlText",1,"form-group","col-md-12"],["formControlName","text","required","true",1,"w-100",3,"label","config","ccdisabled","parametrButtonName"]],template:function(n,t){1&n&&(e.TgZ(0,"div",0)(1,"div",1),e._uU(2," \u2b9e "),e._UZ(3,"div",2)(4,"a",3),e.qZA(),e.TgZ(5,"div",4)(6,"div",5)(7,"button",6),e.NdJ("click",function(){return t.deleteHyperLink()}),e.TgZ(8,"cc-icon"),e._uU(9,"delete"),e.qZA()()()(),e._UZ(10,"br"),e.TgZ(11,"div",7),e._UZ(12,"cc-new-editor",8),e.qZA(),e.TgZ(13,"div",9),e._UZ(14,"cc-new-editor",8),e.qZA(),e.TgZ(15,"div",10),e._UZ(16,"cc-new-editor",11),e.qZA()()),2&n&&(e.Q6J("formGroup",t.form),e.xp6(3),e.Q6J("innerHTML",t.text,e.oJD),e.xp6(1),e.s9C("href",t.url,e.LSH),e.Q6J("innerHTML",t.link,e.oJD),e.xp6(3),e.Q6J("disabled",t.viewOnly),e.xp6(5),e.Q6J("label","Text")("config",t.textConfig)("ccdisabled",t.viewOnly),e.xp6(2),e.Q6J("label","Hyperlink Text")("config",t.hyperConfig)("ccdisabled",t.viewOnly),e.xp6(2),e.Q6J("label","URL")("config",t.urlConfig)("ccdisabled",t.viewOnly)("parametrButtonName","Add Link"))},directives:[l.JL,l.sg,k.uu,O.Q9,l.x0,M.F,l.JJ,l.u,l.Q7],styles:[""],changeDetection:0}),K=(0,b.gn)([T.kG],K);const yt=function(){return[]};function Vn(i,n){if(1&i&&(e.TgZ(0,"cc-card",29),e._UZ(1,"call-to-action",30),e.ALo(2,"async"),e.ALo(3,"async"),e.qZA()),2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("form",r.form.controls.callToActions.controls[o])("parameters",r.parameters)("index",o)("component",t)("predefinedLinks",e.lcZ(2,7,r.predefinedLinks$)||e.DdM(11,yt))("colors",e.lcZ(3,9,r.colors$)||e.DdM(12,yt))("viewOnly",r.viewOnly)}}function Wn(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-card-content"),e.YNc(1,Vn,4,13,"cc-card",27),e.TgZ(2,"button",28),e.NdJ("click",function(){return e.CHM(t),e.oxw(2).addCTA()}),e._uU(3," Add CTA "),e.qZA()()}if(2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.mainTrackerPage.callToActions),e.xp6(1),e.Q6J("disabled",t.viewOnly)}}function Xn(i,n){if(1&i&&(e.TgZ(0,"cc-card",29),e._UZ(1,"bottom-hyperlink",31),e.qZA()),2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("parameters",r.parameters)("index",o)("form",r.form.controls.bottomHyperLinks.controls[o])("component",t)("link",t.hyperLinkText.text)("url",t.urlText.text)("text",t.text.text)("viewOnly",r.viewOnly)}}function eo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"cc-card-content"),e.YNc(1,Xn,2,8,"cc-card",27),e.TgZ(2,"button",28),e.NdJ("click",function(){return e.CHM(t),e.oxw(2).addBottomLink()}),e._uU(3," Add Hyperlink "),e.qZA()()}if(2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.mainTrackerPage.bottomHyperLinks),e.xp6(1),e.Q6J("disabled",t.viewOnly)}}function to(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",1),e.YNc(1,zn,7,2,"cc-card",2),e.TgZ(2,"div",3)(3,"div",4)(4,"cc-card",5)(5,"cc-card-title"),e._uU(6," Show Call To Action "),e.TgZ(7,"cc-slide-toggle",6),e.NdJ("change",function(r){return e.CHM(t),e.oxw().showCallToActionToggle(r)}),e.qZA()(),e.YNc(8,Wn,4,2,"cc-card-content",7),e.qZA(),e.TgZ(9,"cc-card",5)(10,"cc-card-title"),e._uU(11," Show Bottom Hyperlink "),e.TgZ(12,"cc-slide-toggle",6),e.NdJ("change",function(r){return e.CHM(t),e.oxw().showBottomHyperLinksToggle(r)}),e.qZA()(),e.YNc(13,eo,4,2,"cc-card-content",7),e.qZA()()()()}if(2&i){const t=e.oxw();e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("ngIf","single"==t.type),e.xp6(6),e.Q6J("formControl",t.form.get("showCallToAction"))("disabled",t.viewOnly),e.xp6(1),e.Q6J("ngIf",t.mainTrackerPage.showCallToAction),e.xp6(4),e.Q6J("formControl",t.form.get("showBottomHyperLinks"))("disabled",t.viewOnly),e.xp6(1),e.Q6J("ngIf",t.mainTrackerPage.showBottomHyperLinks)}}let L=class{constructor(n,t){this._store=n,this.formBuilder=t,this.viewOnly$=this._store.selectView,this.colors$=this._store.selectColors,this.predefinedLinks$=this._store.selectPredefinedLinks.pipe((0,f.U)(o=>o.map(r=>({value:r.code,text:r.name})))),this.type="single",this.onSave=new e.vpe}ngOnInit(){this.textEditorObservable$=(0,_e.a)([this.colors$,this.predefinedLinks$]).pipe((0,f.U)(([n,t])=>{console.log(t),this.normalConfig={inline:!1,italic:!1,underline:!1,bold:!0,list:!0,changeColor:!0,colors:n.map(o=>({value:o.tags[0].value,text:o.code})),addLink:!0,addParameter:!0,parameters:this.parameters.map(o=>({text:o,value:o})),predefinedLinks:t},this.urlConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!0,parameters:this.parameters.map(o=>({text:o,value:o})),predefinedLinks:[]},this.noteConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:[],addLink:!1,addParameter:!0,parameters:this.parameters.map(o=>({text:o,value:o})),predefinedLinks:[]}})),this.textEditorObservable$.subscribe(()=>{}),this.viewOnly$.subscribe(n=>this.viewOnly=n)}isNormalComponent(n){return"Normal"===n.componentType}isStepperComponent(n){return"Stepper"===n.componentType}addLink(n){n.stepIndex>-1?(this.onSave.emit(),this._store.addStepHyperLink(n.componentIndex,n.stepIndex)):(this.onSave.emit(),this._store.addHyperLink(n.componentIndex))}deleteHyperLink(n){n.stepIndex>-1?(this.onSave.emit(),this._store.deleteStepHyperLink(n.index,n.componentIndex,n.stepIndex)):(this.onSave.emit(),this._store.deleteHyperLink(n.index,n.componentIndex))}addNote(n){n.stepIndex>-1?(this.onSave.emit(),this._store.addStepNote(n.componentIndex,n.stepIndex)):(this.onSave.emit(),this._store.addNote(n.componentIndex))}deleteNote(n){n.stepIndex>-1?(this.onSave.emit(),this._store.deleteStepNote(n.index,n.componentIndex,n.stepIndex)):(this.onSave.emit(),this._store.saveComponent(n.component,n.componentIndex),this._store.deleteNote(n.index,n.componentIndex))}deleteComponent(n){this.onSave.emit(),this._store.deleteComponent(n)}addComponent(){this.onSave.emit(),this._store.addComponent()}addCTA(){this.onSave.emit(),this._store.addCTA(!1)}addBottomLink(){this.onSave.emit(),this._store.addBottomLink()}showCallToActionToggle(n){this.form.controls.showCallToAction.setValue(n.checked),this.onSave.emit(),this._store.toggleShowCallToAction(n.checked,!1)}showBottomHyperLinksToggle(n){this.form.controls.showBottomHyperLinks.setValue(n.checked),this.onSave.emit(),this._store.toggleShowButtomHyperlink(n.checked)}changeComponentType(n){this._store.changeComponentType(n)}saveState(){this.onSave.emit()}};function no(i,n){if(1&i&&(e.TgZ(0,"cc-card",11),e._UZ(1,"call-to-action",12),e.qZA()),2&i){const t=n.$implicit,o=n.index,r=e.oxw(3);e.xp6(1),e.Q6J("form",r.form.controls.callToActions.controls[o])("parameters",r.parameters)("index",o)("isHome",!0)("component",t)("viewOnly",r.viewOnly)}}function oo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",13),e.NdJ("click",function(){return e.CHM(t),e.oxw(3).addCTA()}),e._uU(1," Add CTA "),e.qZA()}}function ro(i,n){if(1&i&&(e.TgZ(0,"cc-card-content"),e.YNc(1,no,2,6,"cc-card",9),e.YNc(2,oo,2,0,"button",10),e.qZA()),2&i){const t=e.oxw(2);e.xp6(1),e.Q6J("ngForOf",t.homeTrackerPage.callToActions),e.xp6(1),e.Q6J("ngIf",!t.viewOnly)}}function io(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",1)(1,"div")(2,"div")(3,"cc-card",2)(4,"cc-card-title"),e._uU(5," Screen Text "),e.qZA(),e.TgZ(6,"cc-card-content"),e._UZ(7,"cc-new-editor",3),e.qZA()()(),e.TgZ(8,"div",4)(9,"div",5)(10,"cc-card",6)(11,"cc-card-title"),e._uU(12,"hide Full Details CTA "),e.TgZ(13,"cc-slide-toggle",7),e.NdJ("change",function(r){return e.CHM(t),e.oxw().hideFullDetailsCTAToggle(r)}),e.qZA()()(),e.TgZ(14,"cc-card",6)(15,"cc-card-title"),e._uU(16," Show Call To Action "),e.TgZ(17,"cc-slide-toggle",7),e.NdJ("change",function(r){return e.CHM(t),e.oxw().showCallToActionToggle(r)}),e.qZA()(),e.YNc(18,ro,3,2,"cc-card-content",8),e.qZA()()()()()}if(2&i){const t=e.oxw();e.Q6J("formGroup",t.form),e.xp6(3),e.Q6J("formGroupName","screenText"),e.xp6(4),e.Q6J("formControlName","text")("config",t.normalConfig)("ccdisabled",t.viewOnly),e.xp6(6),e.Q6J("formControl",t.form.get("hideFullDetailsCTA"))("disabled",t.viewOnly),e.xp6(4),e.Q6J("formControl",t.form.get("showCallToAction"))("disabled",t.viewOnly),e.xp6(1),e.Q6J("ngIf",t.homeTrackerPage.showCallToAction)}}L.\u0275fac=function(n){return new(n||L)(e.Y36(x),e.Y36(l.qu))},L.\u0275cmp=e.Xpm({type:L,selectors:[["main-tracker-page"]],inputs:{mainTrackerPage:"mainTrackerPage",form:"form",type:"type",parameters:"parameters"},outputs:{onSave:"onSave"},decls:4,vars:5,consts:[[3,"formGroup",4,"ngIf"],[3,"formGroup"],["formArrayName","components",4,"ngIf"],[1,"mt-4"],[1,"row",2,"margin-right","0"],[1,"col-md-6"],[1,"float-right",3,"formControl","disabled","change"],[4,"ngIf"],["formArrayName","components"],[3,"formGroupName",4,"ngFor","ngForOf"],["cc-flat-button","","color","accent",3,"disabled","click"],[3,"formGroupName"],[1,"row"],["class","w-100",4,"ngIf"],[1,"w-100"],[1,"col-md-12","row"],["formControlName","componentType","name","componentType",1,"col-11","d-flex","gap-4",3,"disabled","change"],["value","Normal"],["value","Stepper"],[1,"col-md-1"],["class","float-right","cc-mini-fab","","color","accent",3,"disabled","click",4,"ngIf"],[1,"w-100",3,"parameters","form","component","j","normalConfig","urlConfig","noteConfig","viewOnly","onLinkAdded","onLinkDeleted","onNoteAdded","onNoteDeleted"],["cc-mini-fab","","color","accent",1,"float-right",3,"disabled","click"],["class","float-right","cc-mini-fab","","color","accent",3,"click",4,"ngIf"],[1,"w-100",3,"parameters","index","form","component","j","normalConfig","urlConfig","noteConfig","viewOnly","onLinkAdded","onLinkDeleted","onNoteAdded","onNoteDeleted","onSave"],["cc-mini-fab","","color","accent",1,"float-right",3,"click"],[1,"w-100",3,"parameters","form","component","index","viewOnly","onSave"],["class","mt-2",4,"ngFor","ngForOf"],["cc-flat-button","","color","accent",1,"mt-2",3,"disabled","click"],[1,"mt-2"],[3,"form","parameters","index","component","predefinedLinks","colors","viewOnly"],[3,"parameters","index","form","component","link","url","text","viewOnly"]],template:function(n,t){1&n&&(e.ynx(0),e.YNc(1,to,14,8,"div",0),e.ALo(2,"async"),e.ALo(3,"async"),e.BQk()),2&n&&(e.xp6(1),e.Q6J("ngIf",e.lcZ(2,1,t.predefinedLinks$)&&e.lcZ(3,3,t.colors$)&&t.parameters.length>0))},directives:[h.O5,l.JL,l.sg,_.Dt,l.CE,_.K9,h.sg,_.uw,l.x0,pe.u6,l.JJ,l.u,pe.UF,k.uu,O.Q9,F,E,R,_.uC,se.I,l.oH,G,K],pipes:[h.Ov],styles:[""],changeDetection:0}),L=(0,b.gn)([T.kG],L);let I=class{constructor(n,t){this._store=n,this.formBuilder=t,this.colors$=this._store.selectColors,this.viewOnly$=this._store.selectView,this.onSave=new e.vpe,this.normalConfig={}}ngOnInit(){this.textEditorObservable$=(0,_e.a)([this.colors$]).pipe((0,f.U)(([n])=>{let t=n.map(o=>({value:o.tags[0].value,text:o.code}));this.normalConfig={inline:!1,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:t,addLink:!1,addParameter:!0,parameters:this.parameters.map(o=>({text:o,value:o})),predefinedLinks:[]}})).subscribe(()=>{}),this.viewOnly$.subscribe(n=>this.viewOnly=n)}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}isNormalComponent(n){return"Normal"===n.componentType}isStepperComponent(n){return"Stepper"===n.componentType}addCTA(){this.onSave.emit(),this._store.addCTA(!0)}showCallToActionToggle(n){this.form.controls.showCallToAction.setValue(n.checked),this.onSave.emit(),this._store.toggleShowCallToAction(n.checked,!0)}hideFullDetailsCTAToggle(n){this.form.controls.hideFullDetailsCTA.setValue(n.checked),this.onSave.emit(),this._store.toggleHideFullDetailsCTA(n.checked)}};function ao(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",32)(1,"button",18),e.NdJ("click",function(){e.CHM(t);const r=e.oxw().index;return e.oxw().deleteInformative(r)}),e.TgZ(2,"cc-icon"),e._uU(3,"delete"),e.qZA()()()}}I.\u0275fac=function(n){return new(n||I)(e.Y36(x),e.Y36(l.qu))},I.\u0275cmp=e.Xpm({type:I,selectors:[["home-tracker-page"]],inputs:{parameters:"parameters",homeTrackerPage:"homeTrackerPage",form:"form"},outputs:{onSave:"onSave"},decls:3,vars:3,consts:[[3,"formGroup",4,"ngIf"],[3,"formGroup"],[3,"formGroupName"],["required","true",1,"m-2",3,"formControlName","config","ccdisabled"],[1,"mt-4"],[1,"row",2,"margin-right","0"],[1,"col-md-6"],[1,"float-right",3,"formControl","disabled","change"],[4,"ngIf"],["class","mt-2",4,"ngFor","ngForOf"],["class","mt-2","cc-flat-button","","color","accent",3,"click",4,"ngIf"],[1,"mt-2"],[3,"form","parameters","index","isHome","component","viewOnly"],["cc-flat-button","","color","accent",1,"mt-2",3,"click"]],template:function(n,t){1&n&&(e.ynx(0),e.YNc(1,io,19,10,"div",0),e.ALo(2,"async"),e.BQk()),2&n&&(e.xp6(1),e.Q6J("ngIf",e.lcZ(2,1,t.colors$)))},directives:[h.O5,l.JL,l.sg,_.Dt,l.x0,_.K9,_.uw,M.F,l.Q7,l.JJ,l.u,se.I,l.oH,h.sg,G,k.uu],pipes:[h.Ov],styles:[""],changeDetection:0}),I=(0,b.gn)([T.kG],I);const wt=function(){return[]};function co(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",25)(1,"cc-card-header")(2,"cc-checkbox",26),e.NdJ("change",function(){return e.CHM(t),e.oxw().saveState()}),e._uU(3,"Show Icon "),e.qZA(),e.TgZ(4,"div",27)(5,"cc-label",28),e._uU(6," Nationalities "),e.qZA(),e.TgZ(7,"cc-select",29),e.NdJ("valueChange",function(r){const c=e.CHM(t).index;return e.oxw().onNationalitySelected(r,c.toString())}),e.ALo(8,"async"),e.qZA()(),e.YNc(9,ao,4,0,"div",30),e.qZA(),e.TgZ(10,"cc-card-content",25),e._UZ(11,"cc-new-editor",31),e.qZA()()}if(2&i){const t=n.index,o=e.oxw();let r;e.Q6J("formGroupName",t),e.xp6(7),e.Q6J("data",null!==(r=e.lcZ(8,9,o.nationalities$))&&void 0!==r?r:e.DdM(11,wt))("value",o.page.informative[t].nationalities)("multiple",!0),e.xp6(2),e.Q6J("ngIf",0!=t),e.xp6(1),e.Q6J("formGroupName","text"),e.xp6(1),e.Q6J("config",o.config)("ccdisabled",o.viewOnly)("required",o.page.informative[t].showInformativeIcon)}}function so(i,n){if(1&i&&(e.TgZ(0,"span"),e._uU(1),e.TgZ(2,"button",38),e._uU(3),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw().$implicit;e.xp6(1),e.hij("",t," "),e.xp6(2),e.hij(" ",o.text," ")}}function lo(i,n){if(1&i&&(e.TgZ(0,"div",37),e.YNc(1,so,4,2,"span",23),e.qZA()),2&i){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",null==t.data||null==t.data.tags[0]?null:t.data.tags[0].value)}}function po(i,n){if(1&i&&(e.TgZ(0,"div",33)(1,"label",34),e._uU(2," Tracker Color "),e.qZA(),e.TgZ(3,"cc-select",35),e.YNc(4,lo,2,1,"div",36),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw();e.xp6(1),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t))("color","transparent"!=o.getBackgroundColor(o.form.value.progressBarColor,t)?"white":"Black"),e.xp6(2),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t)),e.Q6J("data",t)}}function mo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"main-tracker-page",39),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("mainTrackerPage",t.page.mainTrackerPage)("form",t.form.controls.mainTrackerPage)}}function uo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"home-tracker-page",40),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("form",t.form.controls.homeTrackerPage)("homeTrackerPage",t.page.homeTrackerPage)}}let z=class{constructor(n,t,o,r,a){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.cdRef=a,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(c=>c.map(p=>({id:p.id,text:p.name,data:p})))),this.viewOnly$=this._store.selectView,this.homeClicked=!1,this.setupPage$=this._store.selectNormalStepPage,this.changed=!1,this.onSave=new e.vpe}homeClick(){this.onSave.emit(),this.homeClicked=!this.homeClicked}ngOnChanges(n){n.page&&(this.page=n.page.currentValue)}ngOnInit(){this.refreshNationalitiesSelects(),this.nationalities$.subscribe(n=>this.nationalities=n),this.titleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.subTitleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.gptPolicyConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!0,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.config={inline:!1,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!1,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.viewOnly$.subscribe(n=>this.viewOnly=n)}onNationalitySelected(n,t){const o=this.form.get("informative").get(t).get("nationalities");o.clear(),n&&n.forEach(r=>{const a=new l.NI(this.nationalities.find(c=>c.id==r));o.push(a)}),this.refreshNationalitiesSelects()}addInformative(){this.onSave.emit(),this._store.addInformative(this.form.get("informative").value)}deleteInformative(n){this.onSave.emit(),this._store.deleteInformative(n)}refreshNationalitiesSelects(){this.nationalities$=this._store.fetchNationalities().pipe((0,y.w)(n=>{const t=this.form.get("informative"),o=Object.keys(t.controls),r=[];o.forEach(c=>{var p;const d=null===(p=null==t?void 0:t.get(`${c}.nationalities`))||void 0===p?void 0:p.value;r.push(...d)});const a=n.map(c=>{const p=r.some(d=>d.id===c.id);return{id:c.id,text:c.name,disabled:p}});return(0,oe.of)(a)}))}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}getBackgroundColor(n,t){var o;if(n){const r=t.find(a=>a.id==n);if(r)return(null===(o=r.data.tags[0])||void 0===o?void 0:o.value)||"transparent"}return"transparent"}saveState(){this.onSave.emit()}};function go(i,n){if(1&i&&(e.TgZ(0,"span"),e._uU(1),e.TgZ(2,"button",28),e._uU(3),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw().$implicit;e.xp6(1),e.hij("",t," "),e.xp6(2),e.hij(" ",o.text," ")}}function _o(i,n){if(1&i&&(e.TgZ(0,"div",27),e.YNc(1,go,4,2,"span",21),e.qZA()),2&i){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",null==t.data||null==t.data.tags[0]?null:t.data.tags[0].value)}}function fo(i,n){if(1&i&&(e.TgZ(0,"div",23)(1,"label",24),e._uU(2," Tracker Color "),e.qZA(),e.TgZ(3,"cc-select",25),e.YNc(4,_o,2,1,"div",26),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw();e.xp6(1),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t))("color","transparent"!=o.getBackgroundColor(o.form.value.progressBarColor,t)?"white":"Black"),e.xp6(2),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t)),e.Q6J("data",t)("disabled",o.viewOnly)}}function ho(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"main-tracker-page",29),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("mainTrackerPage",t.page.mainTrackerPage)("form",t.form.controls.mainTrackerPage)}}function To(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"home-tracker-page",30),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("form",t.form.controls.homeTrackerPage)("homeTrackerPage",t.page.homeTrackerPage)}}z.\u0275fac=function(n){return new(n||z)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ),e.Y36(e.sBO))},z.\u0275cmp=e.Xpm({type:z,selectors:[["normal-content"]],inputs:{colors:"colors",parameters:"parameters",colors$:"colors$",links:"links",form:"form",page:"page"},outputs:{onSave:"onSave"},features:[e.TTD],decls:36,vars:28,consts:[[1,"fluid-container"],[3,"formGroup"],[1,""],[1,"mt-4","mb-4"],[1,"row"],[1,"col-md-12"],["formControlName","screenName","required","true",3,"label","disabled"],[1,"col-md-8",3,"formGroupName"],["required","true",3,"formControlName","label","config","ccdisabled"],[1,"col-md-4"],["formControlName","graphicComponent","name","graphicComponent","search","false","required","true",3,"data"],[1,"col-md-12",3,"formGroupName"],["name","screenSubTitle","required","true",3,"label","formControlName","config","ccdisabled"],["name","gptPolicy",3,"label","formControlName","config","ccdisabled"],[1,"col-md-12","mt-4","row"],["formArrayName","informative",1,"col-md-10"],[3,"formGroupName",4,"ngFor","ngForOf"],[2,"text-align","right"],["cc-mini-fab","","color","accent",3,"click"],["class","col-md-2",4,"ngIf"],[1,"row","p-4"],["cc-align-tabs","left",1,"w-100",3,"selectedTabChange"],["label","Main Tracker Page","formGroupName","mainTrackerPage"],[4,"ngIf"],["label","Home Traker Page","formGroupName","homeTrackerPage"],[3,"formGroupName"],["formControlName","showInformativeIcon",1,"col-md-2","mt-3",3,"change"],[1,"col-md-9","row"],[1,"col-md-2","mt-3"],["label","Nationalities","formArrayName","nationalities","idProperty","id","textProperty","label",1,"col-md-10",3,"data","value","multiple","valueChange"],["class","col-md-1 align-content-center",4,"ngIf"],["label","(i) pop up text","formControlName","text",1,"m-2",3,"config","ccdisabled","required"],[1,"col-md-1","align-content-center"],[1,"col-md-2"],[2,"padding","7px","border-radius","5px"],["formControlName","progressBarColor","name","progressBarColor","label","Tracker Color","search","false","required","true",3,"data"],["class","cc-selected-item",4,"ccSelectTrigger"],[1,"cc-selected-item"],["cc-button",""],[3,"parameters","mainTrackerPage","form","onSave"],[3,"parameters","form","homeTrackerPage","onSave"]],template:function(n,t){if(1&n&&(e.TgZ(0,"div",0)(1,"form",1)(2,"cc-card",2)(3,"cc-card-title")(4,"h3",3),e._uU(5,"Page Setup"),e.qZA()(),e.TgZ(6,"cc-card-content")(7,"div",4)(8,"div",5),e._UZ(9,"cc-input",6),e.qZA(),e.TgZ(10,"div",7),e._UZ(11,"cc-new-editor",8),e.qZA(),e.TgZ(12,"div",9)(13,"cc-label"),e._uU(14," Select Graphic Component "),e.qZA(),e._UZ(15,"cc-select",10),e.ALo(16,"async"),e.qZA(),e.TgZ(17,"div",11),e._UZ(18,"cc-new-editor",12),e.qZA(),e.TgZ(19,"div",11),e._UZ(20,"cc-new-editor",13),e.qZA(),e.TgZ(21,"div",14)(22,"cc-card",15),e.YNc(23,co,12,12,"div",16),e.TgZ(24,"cc-card-footer",17)(25,"button",18),e.NdJ("click",function(){return t.addInformative()}),e.TgZ(26,"cc-icon"),e._uU(27,"add"),e.qZA()()()(),e.YNc(28,po,5,7,"div",19),e.ALo(29,"async"),e.qZA()(),e.TgZ(30,"div",20)(31,"cc-tab-group",21),e.NdJ("selectedTabChange",function(){return t.homeClick()}),e.TgZ(32,"cc-tab",22),e.YNc(33,mo,2,3,"div",23),e.qZA(),e.TgZ(34,"cc-tab",24),e.YNc(35,uo,2,3,"div",23),e.qZA()()()()()()()),2&n){let o;e.xp6(1),e.Q6J("formGroup",t.form),e.xp6(8),e.Q6J("label","Screen Name")("disabled",t.viewOnly),e.xp6(1),e.Q6J("formGroupName","screenTitle"),e.xp6(1),e.Q6J("formControlName","text")("label","Title")("config",t.titleConfig)("ccdisabled",t.viewOnly),e.xp6(4),e.Q6J("data",null!==(o=e.lcZ(16,23,t.graphicComponents))&&void 0!==o?o:e.DdM(27,wt)),e.xp6(2),e.Q6J("formGroupName","screenSubTitle"),e.xp6(1),e.Q6J("label","Sub Title")("formControlName","text")("config",t.subTitleConfig)("ccdisabled",t.viewOnly),e.xp6(1),e.Q6J("formGroupName","gptPolicy"),e.xp6(1),e.Q6J("label","GPT Policy")("formControlName","text")("config",t.gptPolicyConfig)("ccdisabled",t.viewOnly),e.xp6(3),e.Q6J("ngForOf",t.page.informative),e.xp6(5),e.Q6J("ngIf",e.lcZ(29,25,t.colors$)),e.xp6(5),e.Q6J("ngIf",!t.homeClicked),e.xp6(2),e.Q6J("ngIf",t.homeClicked)}},directives:[l._Y,l.JL,l.sg,_.Dt,_.K9,_.uw,ne.G,l.JJ,l.u,l.Q7,l.x0,M.F,U.k_,P.jB,l.CE,h.sg,_.oJ,ie.E,h.O5,k.uu,O.Q9,_.uC,P.hV,Z.e6,Z.eF,L,I],pipes:[h.Ov],styles:[".radio-group[_ngcontent-%COMP%]{display:flex;gap:10px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{appearance:none;border:2px solid #ccc;border-radius:8px;width:20px;height:20px;margin-right:5px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff;color:#fff}.radio-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;font-family:Arial,sans-serif;font-size:14px}"],changeDetection:0}),z=(0,b.gn)([T.kG],z);const vo=function(){return[]};let V=class{constructor(n){this._store=n,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(t=>t.map(o=>({id:o.id,text:o.name,data:o})))),this.viewOnly$=this._store.selectView,this.homeClicked=!1,this.setupPage$=this._store.selectNormalStepPage,this.onSave=new e.vpe}homeClick(){this.onSave.emit(),this.homeClicked=!this.homeClicked}ngOnInit(){this.issueConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!1,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.titleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.subTitleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.config={inline:!1,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!1,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.gptPolicyConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!0,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.viewOnly$.subscribe(n=>this.viewOnly=n)}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}getBackgroundColor(n,t){var o;if(n){const r=t.find(a=>a.id==n);if(r)return(null===(o=r.data.tags[0])||void 0===o?void 0:o.value)||"transparent"}return"transparent"}saveState(){this.onSave.emit()}};function Co(i,n){if(1&i&&(e.TgZ(0,"div",13),e._UZ(1,"cc-new-editor",23),e.qZA()),2&i){const t=e.oxw();e.Q6J("formGroupName","underTrackerText"),e.xp6(1),e.Q6J("id","underTrackerText"+t.getRandomNumber(0,1e4))("label","Under Tracker Text")("config",t.trackerConfig)("ccdisabled",t.viewOnly)}}function xo(i,n){if(1&i&&(e.TgZ(0,"span"),e._uU(1),e.TgZ(2,"button",29),e._uU(3),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw().$implicit;e.xp6(1),e.hij("",t," "),e.xp6(2),e.hij(" ",o.text," ")}}function ko(i,n){if(1&i&&(e.TgZ(0,"div",28),e.YNc(1,xo,4,2,"span",21),e.qZA()),2&i){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",null==t.data||null==t.data.tags[0]?null:t.data.tags[0].value)}}function bo(i,n){if(1&i&&(e.TgZ(0,"div",24)(1,"label",25),e._uU(2," Tracker Color "),e.qZA(),e.TgZ(3,"cc-select",26),e.YNc(4,ko,2,1,"div",27),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw();e.xp6(1),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t))("color","transparent"!=o.getBackgroundColor(o.form.value.progressBarColor,t)?"white":"Black"),e.xp6(2),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t)),e.Q6J("data",t)("disabled",o.viewOnly)}}function So(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"main-tracker-page",30),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("mainTrackerPage",t.page.mainTrackerPage)("form",t.form.controls.mainTrackerPage)}}function yo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"home-tracker-page",31),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("form",t.form.controls.homeTrackerPage)("homeTrackerPage",t.page.homeTrackerPage)}}V.\u0275fac=function(n){return new(n||V)(e.Y36(x))},V.\u0275cmp=e.Xpm({type:V,selectors:[["issue-content"]],inputs:{colors:"colors",parameters:"parameters",colors$:"colors$",links:"links",form:"form",page:"page"},outputs:{onSave:"onSave"},decls:33,vars:27,consts:[[1,"fluid-container"],[3,"formGroup"],[1,""],[1,"mt-4","mb-4"],[1,"row"],[1,"col-md-12"],["formControlName","screenName","required","true",3,"label","disabled"],[1,"col-md-12",3,"formGroupName"],["label","Issue Type","formControlName","text","required","true",3,"config","ccdisabled"],[1,"col-md-8",3,"formGroupName"],["label","Title","formControlName","text","required","true",3,"config","ccdisabled"],[1,"col-md-4"],["formControlName","graphicComponent","name","graphicComponent","search","false","required","true",3,"data","disabled"],["label","Sub Title","formControlName","text","name","screenSubTitle","required","true",3,"config","ccdisabled"],["name","gptPolicy",3,"label","formControlName","config","ccdisabled"],[1,"col-md-12","mt-4","row"],[1,"col-md-10"],["class","col-md-2",4,"ngIf"],[1,"row","p-4"],["cc-align-tabs","left",1,"w-100",3,"selectedTabChange"],["label","Main Tracker Page","formGroupName","mainTrackerPage"],[4,"ngIf"],["label","Home Traker Page","formGroupName","homeTrackerPage"],[1,"col-md-2"],[2,"padding","7px","border-radius","5px"],["formControlName","progressBarColor","name","progressBarColor","label","Tracker Color","search","false","required","true",3,"data","disabled"],["class","cc-selected-item",4,"ccSelectTrigger"],[1,"cc-selected-item"],["cc-button",""],[3,"parameters","mainTrackerPage","form","onSave"],[3,"parameters","form","homeTrackerPage","onSave"]],template:function(n,t){if(1&n&&(e.TgZ(0,"div",0)(1,"form",1)(2,"cc-card",2)(3,"cc-card-title")(4,"h3",3),e._uU(5,"Page Setup"),e.qZA()(),e.TgZ(6,"cc-card-content")(7,"div",4)(8,"div",5),e._UZ(9,"cc-input",6),e.qZA(),e.TgZ(10,"div",7),e._UZ(11,"cc-new-editor",8),e.qZA(),e.TgZ(12,"div",9),e._UZ(13,"cc-new-editor",10),e.qZA(),e.TgZ(14,"div",11)(15,"cc-label"),e._uU(16," Select Graphic Component "),e.qZA(),e._UZ(17,"cc-select",12),e.ALo(18,"async"),e.qZA(),e.TgZ(19,"div",7),e._UZ(20,"cc-new-editor",13),e.qZA(),e.TgZ(21,"div",7),e._UZ(22,"cc-new-editor",14),e.qZA(),e.TgZ(23,"div",15),e._UZ(24,"div",16),e.YNc(25,fo,5,8,"div",17),e.ALo(26,"async"),e.qZA()(),e.TgZ(27,"div",18)(28,"cc-tab-group",19),e.NdJ("selectedTabChange",function(){return t.homeClick()}),e.TgZ(29,"cc-tab",20),e.YNc(30,ho,2,3,"div",21),e.qZA(),e.TgZ(31,"cc-tab",22),e.YNc(32,To,2,3,"div",21),e.qZA()()()()()()()),2&n){let o;e.xp6(1),e.Q6J("formGroup",t.form),e.xp6(8),e.Q6J("label","Screen Name")("disabled",t.viewOnly),e.xp6(1),e.Q6J("formGroupName","issueType"),e.xp6(1),e.Q6J("config",t.issueConfig)("ccdisabled",t.viewOnly),e.xp6(1),e.Q6J("formGroupName","screenTitle"),e.xp6(1),e.Q6J("config",t.titleConfig)("ccdisabled",t.viewOnly),e.xp6(4),e.Q6J("data",null!==(o=e.lcZ(18,22,t.graphicComponents))&&void 0!==o?o:e.DdM(26,vo))("disabled",t.viewOnly),e.xp6(2),e.Q6J("formGroupName","screenSubTitle"),e.xp6(1),e.Q6J("config",t.subTitleConfig)("ccdisabled",t.viewOnly),e.xp6(1),e.Q6J("formGroupName","gptPolicy"),e.xp6(1),e.Q6J("label","GPT Policy")("formControlName","text")("config",t.gptPolicyConfig)("ccdisabled",t.viewOnly),e.xp6(3),e.Q6J("ngIf",e.lcZ(26,24,t.colors$)),e.xp6(5),e.Q6J("ngIf",!t.homeClicked),e.xp6(2),e.Q6J("ngIf",t.homeClicked)}},directives:[l._Y,l.JL,l.sg,_.Dt,_.K9,_.uw,ne.G,l.JJ,l.u,l.Q7,l.x0,M.F,U.k_,P.jB,h.O5,P.hV,k.uu,Z.e6,Z.eF,L,I],pipes:[h.Ov],styles:[".radio-group[_ngcontent-%COMP%]{display:flex;gap:10px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{appearance:none;border:2px solid #ccc;border-radius:8px;width:20px;height:20px;margin-right:5px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff;color:#fff}.radio-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;font-family:Arial,sans-serif;font-size:14px}"],changeDetection:0}),V=(0,b.gn)([T.kG],V);const wo=function(){return[]};let W=class{constructor(n){this._store=n,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(t=>t.map(o=>({id:o.id,text:o.name,data:o})))),this.viewOnly$=this._store.selectView,this.homeClicked=!1,this.setupPage$=this._store.selectNormalStepPage,this.onSave=new e.vpe}ngOnChanges(n){}homeClick(){this.onSave.emit(),this.homeClicked=!this.homeClicked}ngOnInit(){this.issueConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.titleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.trackerConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.gptPolicyConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!0,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.viewOnly$.subscribe(n=>this.viewOnly=n)}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}getBackgroundColor(n,t){var o;if(n){const r=t.find(a=>a.id==n);if(r)return(null===(o=r.data.tags[0])||void 0===o?void 0:o.value)||"transparent"}return"transparent"}underTrackerTextToggle(n){this.onSave.emit(),this._store.underTrackerTextToggle(n.checked)}saveState(){this.onSave.emit()}};function Po(i,n){if(1&i&&e._UZ(0,"cc-advanced-search",23),2&i){const t=e.oxw(3).ngIf,o=e.oxw();e.Q6J("title","Conditions")("mode","developer")("cardTitle","Conditions")("panelExpanded",!0)("formControl",o.form.controls.conditionJson)("metaApi","/visa/visa-group/get-conditions")("fieldOverrides",o.fieldOverrides)("required",t.conditionsToggle)("disabled",o.viewOnly)}}function No(i,n){1&i&&e._uU(0),2&i&&e.hij(" ",(n.index+1).toString().padStart(3,"0")," ")}W.\u0275fac=function(n){return new(n||W)(e.Y36(x))},W.\u0275cmp=e.Xpm({type:W,selectors:[["success-content"]],inputs:{colors:"colors",parameters:"parameters",colors$:"colors$",links:"links",form:"form",page:"page"},outputs:{onSave:"onSave"},features:[e.TTD],decls:33,vars:23,consts:[[1,"fluid-container"],[3,"formGroup"],[1,""],[1,"mt-4","mb-4"],[1,"row"],[1,"col-md-12"],["formControlName","screenName","required","true",3,"label","disabled"],[1,"col-md-8",3,"formGroupName"],["label","Title","formControlName","text","required","true",3,"config","ccdisabled"],[1,"col-md-4"],["formControlName","graphicComponent","name","graphicComponent","search","false","required","true",3,"data","disabled"],["formControlName","underTrackerTextChecked",1,"col-md-2","mt-3",3,"disabled","change"],["class","col-md-12",3,"formGroupName",4,"ngIf"],[1,"col-md-12",3,"formGroupName"],["name","gptPolicy",3,"label","formControlName","config","ccdisabled"],[1,"col-md-12","mt-4","row"],[1,"col-md-10"],["class","col-md-2",4,"ngIf"],[1,"row","p-4"],["cc-align-tabs","left",1,"w-100",3,"selectedTabChange"],["label","Main Tracker Page","formGroupName","mainTrackerPage"],[4,"ngIf"],["label","Home Traker Page","formGroupName","homeTrackerPage"],["formControlName","text","name","text","required","true",3,"id","label","config","ccdisabled"],[1,"col-md-2"],[2,"padding","7px","border-radius","5px"],["formControlName","progressBarColor","name","progressBarColor","label","Tracker Color","search","false","required","true",3,"data","disabled"],["class","cc-selected-item",4,"ccSelectTrigger"],[1,"cc-selected-item"],["cc-button",""],[3,"parameters","mainTrackerPage","form","onSave"],[3,"parameters","form","homeTrackerPage","onSave"]],template:function(n,t){if(1&n&&(e.TgZ(0,"div",0)(1,"form",1)(2,"cc-card",2)(3,"cc-card-title")(4,"h3",3),e._uU(5,"Page Setup"),e.qZA()(),e.TgZ(6,"cc-card-content")(7,"div",4)(8,"div",5),e._UZ(9,"cc-input",6),e.qZA(),e.TgZ(10,"div",7),e._UZ(11,"cc-new-editor",8),e.qZA(),e.TgZ(12,"div",9)(13,"cc-label"),e._uU(14," Select Graphic Component "),e.qZA(),e._UZ(15,"cc-select",10),e.ALo(16,"async"),e.qZA(),e.TgZ(17,"div",5)(18,"cc-checkbox",11),e.NdJ("change",function(r){return t.underTrackerTextToggle(r)}),e._uU(19,"Under Tracker Text "),e.qZA()(),e.YNc(20,Co,2,5,"div",12),e.TgZ(21,"div",13),e._UZ(22,"cc-new-editor",14),e.qZA(),e.TgZ(23,"div",15),e._UZ(24,"div",16),e.YNc(25,bo,5,8,"div",17),e.ALo(26,"async"),e.qZA()(),e.TgZ(27,"div",18)(28,"cc-tab-group",19),e.NdJ("selectedTabChange",function(){return t.homeClick()}),e.TgZ(29,"cc-tab",20),e.YNc(30,So,2,3,"div",21),e.qZA(),e.TgZ(31,"cc-tab",22),e.YNc(32,yo,2,3,"div",21),e.qZA()()()()()()()),2&n){let o;e.xp6(1),e.Q6J("formGroup",t.form),e.xp6(8),e.Q6J("label","Screen Name")("disabled",t.viewOnly),e.xp6(1),e.Q6J("formGroupName","screenTitle"),e.xp6(1),e.Q6J("config",t.titleConfig)("ccdisabled",t.viewOnly),e.xp6(4),e.Q6J("data",null!==(o=e.lcZ(16,18,t.graphicComponents))&&void 0!==o?o:e.DdM(22,wo))("disabled",t.viewOnly),e.xp6(3),e.Q6J("disabled",t.viewOnly),e.xp6(2),e.Q6J("ngIf",t.page.underTrackerTextChecked),e.xp6(1),e.Q6J("formGroupName","gptPolicy"),e.xp6(1),e.Q6J("label","GPT Policy")("formControlName","text")("config",t.gptPolicyConfig)("ccdisabled",t.viewOnly),e.xp6(3),e.Q6J("ngIf",e.lcZ(26,20,t.colors$)),e.xp6(5),e.Q6J("ngIf",!t.homeClicked),e.xp6(2),e.Q6J("ngIf",t.homeClicked)}},directives:[l._Y,l.JL,l.sg,_.Dt,_.K9,_.uw,ne.G,l.JJ,l.u,l.Q7,l.x0,M.F,U.k_,P.jB,ie.E,h.O5,P.hV,k.uu,Z.e6,Z.eF,L,I],pipes:[h.Ov],styles:[".radio-group[_ngcontent-%COMP%]{display:flex;gap:10px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{appearance:none;border:2px solid #ccc;border-radius:8px;width:20px;height:20px;margin-right:5px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff;color:#fff}.radio-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;font-family:Arial,sans-serif;font-size:14px}"],changeDetection:0}),W=(0,b.gn)([T.kG],W);const Zo=function(i){return{index:i}};function Ao(i,n){if(1&i&&(e.ynx(0),e._UZ(1,"cc-datagrid",24),e.YNc(2,No,1,1,"ng-template",25,26,e.W1O),e.BQk()),2&i){const t=n.ngIf,o=e.MAs(3);e.oxw();const r=e.MAs(38),a=e.oxw(3);e.xp6(1),e.Q6J("noResultTemplate",r)("data",t)("columns",a.visaStepsColumns)("length",t.length)("pageOnFront",!0)("cellTemplate",e.VKq(7,Zo,o)),e.xp6(1),e.Q6J("ccGridCell",t)}}function Oo(i,n){1&i&&(e.TgZ(0,"span"),e._uU(1,"No Result"),e.qZA())}function Jo(i,n){if(1&i){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",7)(2,"div",8)(3,"cc-label"),e._uU(4," Maid Situation "),e.qZA(),e.TgZ(5,"cc-select",9),e.NdJ("valueChange",function(r){return e.CHM(t),e.oxw(3).situatuationChange(r)}),e.qZA()(),e.TgZ(6,"div",8)(7,"cc-label"),e._uU(8," Priority "),e.qZA(),e._UZ(9,"cc-input-mask",10),e.qZA(),e.TgZ(10,"div",11)(11,"cc-checkbox",12),e.NdJ("change",function(){return e.CHM(t),e.oxw(3).saveState()}),e._uU(12," Hide From Remaining Steps"),e.qZA()()(),e.TgZ(13,"div",13)(14,"cc-card-title"),e._uU(15," Conditions "),e.TgZ(16,"cc-slide-toggle",12),e.NdJ("change",function(r){return e.CHM(t),e.oxw(3).showConditionsToggle(r)}),e.qZA(),e.TgZ(17,"button",14),e.NdJ("click",function(){return e.CHM(t),e.oxw(3).showConditionsPopUp()}),e.TgZ(18,"span",15),e._uU(19," Conditions List "),e.TgZ(20,"cc-icon",16),e._uU(21,"info"),e.qZA()()()(),e.TgZ(22,"cc-card-content",17),e.YNc(23,Po,1,9,"cc-advanced-search",18),e.qZA()(),e.TgZ(24,"div",11)(25,"cc-card")(26,"cc-card-header",19)(27,"h3",8),e._uU(28,"Visa Steps"),e.qZA(),e.TgZ(29,"div",20)(30,"button",21),e.NdJ("click",function(){return e.CHM(t),e.oxw(3).addStepDialog()}),e.TgZ(31,"span"),e._uU(32,"+"),e.qZA(),e._uU(33," Add Step "),e.qZA()()(),e.TgZ(34,"cc-card-content"),e.YNc(35,Ao,4,9,"ng-container",6),e.ALo(36,"async"),e.YNc(37,Oo,2,0,"ng-template",null,22,e.W1O),e.qZA()()(),e.BQk()}if(2&i){const t=n.ngIf,o=e.oxw(2).ngIf,r=e.oxw();e.xp6(5),e.Q6J("data",t)("value",o.maidSituations)("disabled",r.viewOnly),e.xp6(4),e.Q6J("formControl",r.form.controls.priority)("mask","0*")("disabled",r.viewOnly),e.xp6(2),e.Q6J("formControl",r.form.controls.hideFromRemainingSteps)("disabled",r.viewOnly),e.xp6(5),e.Q6J("formControl",r.form.get("conditionsToggle"))("disabled",r.viewOnly),e.xp6(7),e.Q6J("ngIf",o.conditionsToggle),e.xp6(7),e.Q6J("disabled",r.viewOnly),e.xp6(5),e.Q6J("ngIf",e.lcZ(36,13,r.visaSteps$))}}function Mo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"normal-content",36),e.NdJ("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw().ngIf,r=e.oxw();e.Q6J("form",r.form)("page",o)("colors",r.colors)("colors$",r.colors$)("parameters",r.parameters)("links",t)}}function jo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"issue-content",36),e.NdJ("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw().ngIf,r=e.oxw();e.Q6J("form",r.form)("page",o)("colors",r.colors)("colors$",r.colors$)("parameters",r.parameters)("links",t)}}function Lo(i,n){1&i&&(e.TgZ(0,"span",37),e._uU(1," Success"),e.qZA())}function Io(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"success-content",36),e.NdJ("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw().ngIf,r=e.oxw();e.Q6J("form",r.form)("page",o)("colors",r.colors)("colors$",r.colors$)("parameters",r.parameters)("links",t)}}function Ho(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"button",38),e.NdJ("click",function(){return e.CHM(t),e.oxw(4).save()}),e._uU(1," Save "),e.qZA()}if(2&i){const t=e.oxw(3).ngIf,o=e.oxw();e.Q6J("disabled",!o.form.valid||0==t.maidSituations.length||!t.priority)}}function qo(i,n){if(1&i){const t=e.EpF();e.ynx(0),e.TgZ(1,"cc-card")(2,"cc-card-title")(3,"h3"),e._uU(4,"Page Content"),e.qZA()(),e.TgZ(5,"cc-card-content")(6,"cc-tab-group",27,28),e.NdJ("selectedTabChange",function(r){return e.CHM(t),e.oxw(3).typeChange(r)})("selectedIndexChange",function(r){return e.CHM(t),e.oxw(3).selectedIndex=r}),e.TgZ(8,"cc-tab",29),e.YNc(9,Mo,1,6,"normal-content",30),e.qZA(),e.TgZ(10,"cc-tab",31),e.YNc(11,jo,1,6,"issue-content",30),e.qZA(),e.TgZ(12,"cc-tab"),e.YNc(13,Lo,2,0,"ng-template",32),e.YNc(14,Io,1,6,"success-content",30),e.qZA()()(),e.TgZ(15,"cc-card-footer")(16,"div",33)(17,"button",34),e.NdJ("click",function(){return e.CHM(t),e.oxw(3).gotMainPage()}),e._uU(18," Cancel "),e.qZA(),e.YNc(19,Ho,2,1,"button",35),e.qZA()()(),e.BQk()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw();e.xp6(6),e.Q6J("selectedIndex",o.selectedIndex),e.xp6(3),e.Q6J("ngIf","Normal"==t.screenType),e.xp6(2),e.Q6J("ngIf","Issue"==t.screenType),e.xp6(3),e.Q6J("ngIf","Success"==t.screenType),e.xp6(5),e.Q6J("ngIf",!o.viewOnly)}}function Qo(i,n){if(1&i&&(e.TgZ(0,"cc-card",4)(1,"cc-card-title")(2,"h3",5),e._uU(3,"Page Setup"),e.qZA()(),e.TgZ(4,"cc-card-content"),e.YNc(5,Jo,39,15,"ng-container",6),e.ALo(6,"async"),e.qZA(),e.YNc(7,qo,20,5,"ng-container",6),e.qZA()),2&i){const t=e.oxw(2);e.xp6(5),e.Q6J("ngIf",e.lcZ(6,2,t.maidSituations$)),e.xp6(2),e.Q6J("ngIf",t.parameters.length>0)}}function Uo(i,n){if(1&i&&(e.TgZ(0,"div",1)(1,"form",2),e.ALo(2,"async"),e.YNc(3,Qo,8,4,"cc-card",3),e.ALo(4,"async"),e.qZA()()),2&i){const t=e.oxw();e.xp6(1),e.Q6J("formGroup",t.form)("ccConnectForm",e.lcZ(2,3,t.setupPage$)),e.xp6(2),e.Q6J("ngIf",e.lcZ(4,5,t.predefinedLinks$))}}let q=class{constructor(n,t,o,r,a,c,p){var d;this._store=n,this.formBuilder=t,this._dialog=o,this.router=r,this._route=a,this._notifications=c,this.cdRef=p,this.fullSearch={},this.visaSteps$=this._store.selectVisaSteps,this.maidSituations$=this._store.selectMaidSituations.pipe((0,f.U)(m=>m.map(u=>({id:u.id,text:u.name})))),this.colors$=this._store.selectColors.pipe((0,f.U)(m=>m.map(u=>({id:u.id,text:u.name,data:u})))),this.Editorcolors$=this._store.selectColors,this.predefinedLinks$=this._store.selectPredefinedLinks.pipe((0,f.U)(m=>m.map(u=>({value:u.code,text:u.name})))),this.setupPage$=this._store.selectSetupPage,this.parameters$=this._store.selectParameters,this.viewOnly$=this._store.selectView,this.form=this.formBuilder.group({}),this.selectedIndex=0,this.visaStepsColumns=[{field:"index",header:"Index"},{field:"workFlowTask.taskLabel",header:"Title"},{field:"operations",header:"Actions",sortable:!1,type:"button",buttonConfig:{mode:"multiple",disabled:!1,buttons:[{type:"icon",icon:"delete",text:"Delete",color:"accent",mode:"single",disabled:this.viewOnly,hidden:m=>!1,callback:m=>this.delete(m.workFlowTask.id)}]}}],this.fieldOverrides={typeOfPreviousVisa:{fieldType:(m,u)=>re.a.multipleSelect},clientCity:{fieldType:(m,u)=>re.a.multipleSelect},MaidInStep:{fieldType:(m,u)=>re.a.multipleSelect},MaidCompletedStep:{fieldType:(m,u)=>re.a.multipleSelect},MaidNotInStep:{fieldType:(m,u)=>re.a.multipleSelect},MaidNotCompletedStep:{fieldType:(m,u)=>re.a.multipleSelect}},this.currentRoute=(null===(d=this._route.snapshot.routeConfig)||void 0===d?void 0:d.path)||"",this.currentRoute.includes("view")?this._store.setView(!0):this._store.setView(!1)}get conditionctrl(){return this.form.get("conditionJson")}ngOnChanges(n){n.form&&(this.setupPage$=this._store.selectSetupPage)}ngViewAfterInit(){}ngOnInit(){var n;this._store.resetState(),this.id=+(null!==(n=this._route.snapshot.paramMap.get("id"))&&void 0!==n?n:"")||null,this._store.fetchConfigurations(),this.Editorcolors$.subscribe(t=>{this.colors=t.map(o=>({value:o.tags[0].value,text:o.code}))}),this.setupPage$.subscribe(t=>{switch(this.currentState=t,t.screenType){case"Issue":this.selectedIndex=1;break;case"Success":this.selectedIndex=2;break;default:this.selectedIndex=0}}),this.parameters$.subscribe(t=>{this.parameters=t}),this.maidSituations$.subscribe(t=>{this.maidSituations=t}),this.viewOnly$.subscribe(t=>{this.viewOnly=t}),this.id&&(console.log(this.form),this._store.getPage(this.id),this.cdRef.markForCheck())}addStepDialog(){this._store.savePageState(this.form.value),this._dialog.originalOpen(_n,{width:"550px"})}gotMainPage(){this._store.resetState(),this.router.navigateByUrl("/visa/tracker")}delete(n){this._store.savePageState(this.form.value),this._store.deleteStep(n)}saveState(){this._store.savePageState(this.form.value)}removeNull(n){return Object.keys(n).forEach(t=>{var o;n[t]&&"object"==typeof n[t]?this.removeNull(n[t]):null==n[t]&&(null===(o=Object.getOwnPropertyDescriptor(n,t))||void 0===o?void 0:o.configurable)&&delete n[t]}),n}save(){if(this._store.savePageState(Object.assign({},this.form.value)),this.id){let n=this.removeNull(Object.assign(Object.assign({},this.form.value),{id:this.id,informative:this.form.value.informative.map(r=>({id:r.id,showInformativeIcon:r.showInformativeIcon,text:r.text,nationalities:r.nationalities.map(a=>({id:a.id,name:a.text}))})),graphicComponent:{id:this.form.value.graphicComponent},progressBarColor:{id:this.form.value.progressBarColor}})),t=n.mainTrackerPage.callToActions.filter(r=>r.primaryButton).length,o=n.homeTrackerPage.callToActions.filter(r=>r.primaryButton).length;if(t>1||o>1)return void this._notifications.notifyError("Only One Primary CTA can be added in home page or main page");if(""==n.homeTrackerPage.screenText.text)return void this._notifications.notifyError("Home Tracker Page is not filled");this._store.editPage(n)}else{let n=this.removeNull(Object.assign(Object.assign({},this.form.value),{informative:this.form.value.informative.map(r=>({showInformativeIcon:r.showInformativeIcon,text:r.text,nationalities:r.nationalities.map(a=>({id:a.id,name:a.text}))})),graphicComponent:{id:this.form.value.graphicComponent},progressBarColor:{id:this.form.value.progressBarColor}})),t=n.mainTrackerPage.callToActions.filter(r=>r.primaryButton).length,o=n.homeTrackerPage.callToActions.filter(r=>r.primaryButton).length;if(t>1||o>1)return void this._notifications.notifyError("Only One Primary CTA can be added in home page or main page");if(""==n.homeTrackerPage.screenText.text)return void this._notifications.notifyError("Home Tracker Page is not filled");this._store.createPage(n)}}situatuationChange(n){const t=this.form.get("maidSituations");t.clear(),n&&n.forEach(o=>{const r=new l.NI(this.maidSituations.find(a=>a.id==o));t.push(r)}),this._store.savePageState(Object.assign({},this.form.getRawValue()))}showConditionsToggle(n){this.form.controls.conditionsToggle.setValue(n.checked);let t=this.form.controls.conditionJson.value;this._store.savePageState(Object.assign(Object.assign({},this.form.getRawValue()),{conditionJson:t})),this._store.showConditionsToggle(this.form.controls.conditionsToggle.value)}typeChange(n){0==n.index&&(this._store.savePageState(Object.assign(Object.assign({},this.form.value),{screenType:"Normal",issue:!1})),this._store.checkInformative()),1==n.index&&this._store.savePageState(Object.assign(Object.assign({},this.form.value),{screenType:"Issue",issue:!0})),2==n.index&&(this._store.savePageState(Object.assign(Object.assign({},this.form.value),{screenType:"Success",issue:!1})),this._store.checkInformative())}showConditionsPopUp(){this._store.savePageState(this.form.value),this._dialog.originalOpen(xn,{width:"800px"}).afterClosed().subscribe(()=>{this._store.showConditionsToggle(!1)})}};function Fo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",29)(1,"button",15),e.NdJ("click",function(){e.CHM(t);const r=e.oxw().index;return e.oxw().deleteInformative(r)}),e.TgZ(2,"cc-icon"),e._uU(3,"delete"),e.qZA()()()}}q.\u0275fac=function(n){return new(n||q)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(A.F0),e.Y36(A.gz),e.Y36(H.zg),e.Y36(e.sBO))},q.\u0275cmp=e.Xpm({type:q,selectors:[["app-setup-page"]],features:[e.TTD],decls:3,vars:3,consts:[["class","fluid-container m-4",4,"ngIf"],[1,"fluid-container","m-4"],[3,"formGroup","ccConnectForm"],["class","",4,"ngIf"],[1,""],[1,"mt-4","mb-4"],[4,"ngIf"],[1,"row"],[1,"col-md-6"],["formArrayName","maidSituations","multiple","true","search","false","required","true",3,"data","value","disabled","valueChange"],["required","true",3,"formControl","mask","disabled"],[1,"col-md-12"],[3,"formControl","disabled","change"],[1,"col-md-12","mt-4","mb-4"],["cc-raised-button","",2,"float","right",3,"click"],[1,"d-flex","align-items-center","px-1"],[2,"font-size","36px"],[1,"mt-4"],["class","mt-5",3,"title","mode","cardTitle","panelExpanded","formControl","metaApi","fieldOverrides","required","disabled",4,"ngIf"],[1,"mb-2"],[1,"col-md-6","text-right"],["cc-raised-button","","color","accent",2,"z-index","999",3,"disabled","click"],["noResultTpl",""],[1,"mt-5",3,"title","mode","cardTitle","panelExpanded","formControl","metaApi","fieldOverrides","required","disabled"],[3,"noResultTemplate","data","columns","length","pageOnFront","cellTemplate"],[3,"ccGridCell"],["indexTpl",""],["cc-align-tabs","center",3,"selectedIndex","selectedTabChange","selectedIndexChange"],["tabGroup",""],["label","Normal"],[3,"form","page","colors","colors$","parameters","links","onSave",4,"ngIf"],["label","Issue"],["cc-tab-label",""],[1,"row","justify-content-end"],["cc-raised-button","",1,"m-2",2,"width","20%",3,"click"],["cc-raised-button","","class","m-2","style","width: 20%","color","accent",3,"disabled","click",4,"ngIf"],[3,"form","page","colors","colors$","parameters","links","onSave"],[2,"color","green"],["cc-raised-button","","color","accent",1,"m-2",2,"width","20%",3,"disabled","click"]],template:function(n,t){1&n&&(e.ynx(0),e.YNc(1,Uo,5,7,"div",0),e.ALo(2,"async"),e.BQk()),2&n&&(e.xp6(1),e.Q6J("ngIf",e.lcZ(2,1,t.setupPage$)))},directives:[h.O5,l._Y,l.JL,l.sg,T.Ls,_.Dt,_.K9,_.uw,U.k_,P.jB,l.CE,de.ts,l.Q7,l.JJ,l.oH,ie.E,se.I,k.uu,O.Q9,B.G2,_.oJ,N.Ge,N.VC,Z.e6,Z.eF,z,V,Z.L3,W,_.uC],pipes:[h.Ov],styles:["[cc-align-tabs=center][_ngcontent-%COMP%] > .cc-tab-header[_ngcontent-%COMP%]   .cc-tab-labels[_ngcontent-%COMP%]{justify-content:space-around!important}  .dynamic-search-compare-to-field{display:none!important}  .advanced-search-action-filter{display:none!important}"],changeDetection:0}),q=(0,b.gn)([T.kG],q);const Pt=function(){return[]};function Go(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div",22)(1,"cc-card-header")(2,"cc-checkbox",23),e.NdJ("change",function(){return e.CHM(t),e.oxw().saveState()}),e._uU(3,"Show Icon "),e.qZA(),e.TgZ(4,"div",24)(5,"cc-label",25),e._uU(6," Nationalities "),e.qZA(),e._UZ(7,"cc-select",26),e.ALo(8,"async"),e.qZA(),e.YNc(9,Fo,4,0,"div",27),e.qZA(),e.TgZ(10,"cc-card-content",22),e._UZ(11,"cc-new-editor",28),e.qZA()()}if(2&i){const t=n.index,o=e.oxw();let r;e.Q6J("formGroupName",t),e.xp6(7),e.Q6J("data",null!==(r=e.lcZ(8,8,o.nationalities$))&&void 0!==r?r:e.DdM(10,Pt))("value",o.page.informative[t].nationalities)("multiple",!0),e.xp6(2),e.Q6J("ngIf",0!=t),e.xp6(1),e.Q6J("formGroupName","text"),e.xp6(1),e.Q6J("config",o.config)("required",o.page.informative[t].showInformativeIcon)}}function Bo(i,n){if(1&i&&(e.TgZ(0,"span"),e._uU(1),e.TgZ(2,"button",35),e._uU(3),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw().$implicit;e.xp6(1),e.hij("",t," "),e.xp6(2),e.hij(" ",o.text," ")}}function Yo(i,n){if(1&i&&(e.TgZ(0,"div",34),e.YNc(1,Bo,4,2,"span",20),e.qZA()),2&i){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",null==t.data||null==t.data.tags[0]?null:t.data.tags[0].value)}}function Do(i,n){if(1&i&&(e.TgZ(0,"div",30)(1,"label",31),e._uU(2," Tracker Color "),e.qZA(),e.TgZ(3,"cc-select",32),e.YNc(4,Yo,2,1,"div",33),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw();e.xp6(1),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t))("color","transparent"!=o.getBackgroundColor(o.form.value.progressBarColor,t)?"white":"Black"),e.xp6(2),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t)),e.Q6J("data",t)}}function $o(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"main-tracker-page",36),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("mainTrackerPage",t.page.mainTrackerPage)("type","multiple")("form",t.form.controls.mainTrackerPage)}}function Eo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"home-tracker-page",37),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("form",t.form.controls.homeTrackerPage)("homeTrackerPage",t.page.homeTrackerPage)}}let X=class{constructor(n,t,o,r){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(a=>a.map(c=>({id:c.id,text:c.name,data:c})))),this.homeClicked=!1,this.setupPage$=this._store.selectNormalStepPage,this.onSave=new e.vpe}ngOnInit(){this.refreshNationalitiesSelects(),this.nationalities$.subscribe(n=>this.nationalities=n),this.titleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.subTitleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.config={inline:!1,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!1,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]}}homeClick(){this.onSave.emit(),this.homeClicked=!this.homeClicked}onNationalitySelected(n,t){const o=this.form.get("informative").get(t).get("nationalities");o.clear(),n&&n.forEach(r=>{const a=new l.NI(this.nationalities.find(c=>c.id==r));o.push(a)}),this.refreshNationalitiesSelects()}addInformative(){this.onSave.emit(),this._store.addInformative(this.form.get("informative").value)}deleteInformative(n){this.onSave.emit(),this._store.deleteInformative(n)}refreshNationalitiesSelects(){this.nationalities$=this._store.fetchNationalities().pipe((0,y.w)(n=>{const t=this.form.get("informative"),o=Object.keys(t.controls),r=[];o.forEach(c=>{var p;const d=null===(p=null==t?void 0:t.get(`${c}.nationalities`))||void 0===p?void 0:p.value;r.push(...d)});const a=n.map(c=>{const p=r.some(d=>d.id===c.id);return{id:c.id,text:c.name,disabled:p}});return(0,oe.of)(a)}))}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}getBackgroundColor(n,t){var o;if(n){const r=t.find(a=>a.id==n);if(r)return(null===(o=r.data.tags[0])||void 0===o?void 0:o.value)||"transparent"}return"transparent"}saveState(){this.onSave.emit()}};function Ro(i,n){if(1&i&&(e.TgZ(0,"span"),e._uU(1),e.TgZ(2,"button",24),e._uU(3),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw().$implicit;e.xp6(1),e.hij("",t," "),e.xp6(2),e.hij(" ",o.text," ")}}function Ko(i,n){if(1&i&&(e.TgZ(0,"div",23),e.YNc(1,Ro,4,2,"span",17),e.qZA()),2&i){const t=n.$implicit;e.xp6(1),e.Q6J("ngIf",null==t.data||null==t.data.tags[0]?null:t.data.tags[0].value)}}function zo(i,n){if(1&i&&(e.TgZ(0,"div",19)(1,"label",20),e._uU(2," Tracker Color "),e.qZA(),e.TgZ(3,"cc-select",21),e.YNc(4,Ko,2,1,"div",22),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw();e.xp6(1),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t))("color","transparent"!=o.getBackgroundColor(o.form.value.progressBarColor,t)?"white":"Black"),e.xp6(2),e.Udp("background",o.getBackgroundColor(o.form.value.progressBarColor,t)),e.Q6J("data",t)}}function Vo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"main-tracker-page",25),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("type","multiple")("mainTrackerPage",t.page.mainTrackerPage)("form",t.form.controls.mainTrackerPage)}}function Wo(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"div")(1,"home-tracker-page",26),e.NdJ("onSave",function(){return e.CHM(t),e.oxw().saveState()}),e.qZA()()}if(2&i){const t=e.oxw();e.xp6(1),e.Q6J("parameters",t.parameters)("form",t.form.controls.homeTrackerPage)("homeTrackerPage",t.page.homeTrackerPage)}}X.\u0275fac=function(n){return new(n||X)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ))},X.\u0275cmp=e.Xpm({type:X,selectors:[["multi-normal-content"]],inputs:{colors:"colors",parameters:"parameters",colors$:"colors$",links:"links",form:"form",page:"page"},outputs:{onSave:"onSave"},decls:32,vars:15,consts:[[1,"fluid-container"],[3,"formGroup"],[1,""],[1,"mt-4","mb-4"],[1,"row"],[1,"col-md-8",3,"formGroupName"],["label","Title","formControlName","text","required","true",3,"config"],[1,"col-md-4"],["formControlName","graphicComponent","name","graphicComponent","search","false","required","true",3,"data"],[1,"col-md-12",3,"formGroupName"],["label","Sub Title","formControlName","text","name","screenSubTitle","required","true",3,"config"],[1,"col-md-12","mt-4","row"],["formArrayName","informative",1,"col-md-10"],[3,"formGroupName",4,"ngFor","ngForOf"],[2,"text-align","right"],["cc-mini-fab","","color","accent",3,"click"],["class","col-md-2",4,"ngIf"],[1,"row","p-4"],["cc-align-tabs","left",1,"w-100",3,"selectedTabChange"],["label","Main Tracker Page","formGroupName","mainTrackerPage"],[4,"ngIf"],["label","Home Traker Page","formGroupName","homeTrackerPage"],[3,"formGroupName"],["formControlName","showInformativeIcon",1,"col-md-2","mt-3",3,"change"],[1,"col-md-9","row"],[1,"col-md-2","mt-3"],["label","Nationalities","formArrayName","nationalities","idProperty","id","textProperty","label",1,"col-md-10",3,"data","value","multiple"],["class","col-md-1 align-content-center",4,"ngIf"],["label","(i) pop up text","formControlName","text",1,"m-2",3,"config","required"],[1,"col-md-1","align-content-center"],[1,"col-md-2"],[2,"padding","7px","border-radius","5px"],["formControlName","progressBarColor","name","progressBarColor","label","Tracker Color","search","false","required","true",3,"data"],["class","cc-selected-item",4,"ccSelectTrigger"],[1,"cc-selected-item"],["cc-button",""],[3,"parameters","mainTrackerPage","type","form","onSave"],[3,"parameters","form","homeTrackerPage","onSave"]],template:function(n,t){if(1&n&&(e.TgZ(0,"div",0)(1,"form",1)(2,"cc-card",2)(3,"cc-card-title")(4,"h3",3),e._uU(5,"Page Setup"),e.qZA()(),e.TgZ(6,"cc-card-content")(7,"div",4)(8,"div",5),e._UZ(9,"cc-new-editor",6),e.qZA(),e.TgZ(10,"div",7)(11,"cc-label"),e._uU(12," Select Graphic Component "),e.qZA(),e._UZ(13,"cc-select",8),e.ALo(14,"async"),e.qZA(),e.TgZ(15,"div",9),e._UZ(16,"cc-new-editor",10),e.qZA(),e.TgZ(17,"div",11)(18,"cc-card",12),e.YNc(19,Go,12,11,"div",13),e.TgZ(20,"cc-card-footer",14)(21,"button",15),e.NdJ("click",function(){return t.addInformative()}),e.TgZ(22,"cc-icon"),e._uU(23,"add"),e.qZA()()()(),e.YNc(24,Do,5,7,"div",16),e.ALo(25,"async"),e.qZA()(),e.TgZ(26,"div",17)(27,"cc-tab-group",18),e.NdJ("selectedTabChange",function(){return t.homeClick()}),e.TgZ(28,"cc-tab",19),e.YNc(29,$o,2,4,"div",20),e.qZA(),e.TgZ(30,"cc-tab",21),e.YNc(31,Eo,2,3,"div",20),e.qZA()()()()()()()),2&n){let o;e.xp6(1),e.Q6J("formGroup",t.form),e.xp6(7),e.Q6J("formGroupName","screenTitle"),e.xp6(1),e.Q6J("config",t.titleConfig),e.xp6(4),e.Q6J("data",null!==(o=e.lcZ(14,10,t.graphicComponents))&&void 0!==o?o:e.DdM(14,Pt)),e.xp6(2),e.Q6J("formGroupName","screenSubTitle"),e.xp6(1),e.Q6J("config",t.subTitleConfig),e.xp6(3),e.Q6J("ngForOf",t.page.informative),e.xp6(5),e.Q6J("ngIf",e.lcZ(25,12,t.colors$)),e.xp6(5),e.Q6J("ngIf",!t.homeClicked),e.xp6(2),e.Q6J("ngIf",t.homeClicked)}},directives:[l._Y,l.JL,l.sg,_.Dt,_.K9,_.uw,l.x0,M.F,l.JJ,l.u,l.Q7,U.k_,P.jB,l.CE,h.sg,_.oJ,ie.E,h.O5,k.uu,O.Q9,_.uC,P.hV,Z.e6,Z.eF,L,I],pipes:[h.Ov],styles:[".radio-group[_ngcontent-%COMP%]{display:flex;gap:10px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{appearance:none;border:2px solid #ccc;border-radius:8px;width:20px;height:20px;margin-right:5px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff;color:#fff}.radio-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;font-family:Arial,sans-serif;font-size:14px}"],changeDetection:0}),X=(0,b.gn)([T.kG],X);const Xo=function(){return[]};let ee=class{constructor(n,t,o,r){this._store=n,this.formBuilder=t,this._dialog=o,this.mediaService=r,this.graphicComponents=this._store.selectGraphicalComponents.pipe((0,f.U)(a=>a.map(c=>({id:c.id,text:c.name,data:c})))),this.homeClicked=!1,this.setupPage$=this._store.selectNormalStepPage,this.onSave=new e.vpe}homeClick(){this.onSave.emit(),this.homeClicked=!this.homeClicked}ngOnInit(){this.refreshNationalitiesSelects(),this.nationalities$.subscribe(n=>this.nationalities=n),this.titleConfig={inline:!0,italic:!1,underline:!1,bold:!1,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!1,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.subTitleConfig={inline:!0,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!0,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]},this.config={inline:!1,italic:!1,underline:!1,bold:!0,list:!1,changeColor:!1,colors:this.colors,addLink:!1,addParameter:!0,parameters:this.parameters.map(n=>({text:n,value:n})),predefinedLinks:[]}}onNationalitySelected(n,t){const o=this.form.get("informative").get(t).get("nationalities");o.clear(),n&&n.forEach(r=>{const a=new l.NI(this.nationalities.find(c=>c.id==r));o.push(a)}),this.refreshNationalitiesSelects()}addInformative(){this.onSave.emit(),this._store.addInformative(this.form.get("informative").value)}deleteInformative(n){this.onSave.emit(),this._store.deleteInformative(n)}refreshNationalitiesSelects(){this.nationalities$=this._store.fetchNationalities().pipe((0,y.w)(n=>{const t=this.form.get("informative"),o=Object.keys(t.controls),r=[];o.forEach(c=>{var p;const d=null===(p=null==t?void 0:t.get(`${c}.nationalities`))||void 0===p?void 0:p.value;r.push(...d)});const a=n.map(c=>{const p=r.some(d=>d.id===c.id);return{id:c.id,text:c.name,disabled:p}});return(0,oe.of)(a)}))}getRandomNumber(n,t){return Math.floor(Math.random()*(t-n+1))+n}getBackgroundColor(n,t){var o;if(n){const r=t.find(a=>a.id==n);if(r)return(null===(o=r.data.tags[0])||void 0===o?void 0:o.value)||"transparent"}return"transparent"}saveState(){this.onSave.emit()}};function er(i,n){if(1&i){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",7)(2,"div",8)(3,"cc-label"),e._uU(4," Maid Situation "),e.qZA(),e.TgZ(5,"cc-select",9),e.NdJ("valueChange",function(r){return e.CHM(t),e.oxw(3).situatuationChange(r)}),e.qZA()()(),e.BQk()}if(2&i){const t=n.ngIf,o=e.oxw(2).ngIf;e.xp6(5),e.Q6J("data",t)("value",o.maidSituations)}}function tr(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"multi-normal-content",14),e.NdJ("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw().ngIf,r=e.oxw();e.Q6J("form",r.form)("page",o)("colors",r.colors)("colors$",r.colors$)("parameters",r.parameters)("links",t)}}function nr(i,n){if(1&i){const t=e.EpF();e.TgZ(0,"multi-issue-content",14),e.NdJ("onSave",function(){return e.CHM(t),e.oxw(4).saveState()}),e.qZA()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw().ngIf,r=e.oxw();e.Q6J("form",r.form)("page",o)("colors",r.colors)("colors$",r.colors$)("parameters",r.parameters)("links",t)}}function or(i,n){if(1&i){const t=e.EpF();e.ynx(0),e.TgZ(1,"cc-card")(2,"cc-card-title")(3,"h3"),e._uU(4,"Page Content"),e.qZA()(),e.TgZ(5,"cc-card-content"),e.YNc(6,tr,1,6,"multi-normal-content",10),e.YNc(7,nr,1,6,"multi-issue-content",10),e.qZA(),e.TgZ(8,"cc-card-footer")(9,"div",11)(10,"button",12),e.NdJ("click",function(){return e.CHM(t),e.oxw(3).gotMainPage()}),e._uU(11," Cancel "),e.qZA(),e.TgZ(12,"button",13),e.NdJ("click",function(){return e.CHM(t),e.oxw(3).save()}),e._uU(13," Save "),e.qZA()()()(),e.BQk()}if(2&i){const t=e.oxw(2).ngIf,o=e.oxw();e.xp6(6),e.Q6J("ngIf","Multi Normals"==t.screenMultiType),e.xp6(1),e.Q6J("ngIf","Multi Issues"==t.screenMultiType),e.xp6(5),e.Q6J("disabled",!o.form.valid)}}function rr(i,n){if(1&i&&(e.TgZ(0,"cc-card",4)(1,"cc-card-title")(2,"h3",5),e._uU(3,"Page Setup"),e.qZA()(),e.TgZ(4,"cc-card-content"),e.YNc(5,er,6,2,"ng-container",6),e.ALo(6,"async"),e.qZA(),e.YNc(7,or,14,3,"ng-container",6),e.qZA()),2&i){const t=e.oxw(2);e.xp6(5),e.Q6J("ngIf",e.lcZ(6,2,t.maidSituations$)),e.xp6(2),e.Q6J("ngIf",t.parameters.length>0)}}function ir(i,n){if(1&i&&(e.TgZ(0,"div",1)(1,"form",2),e.YNc(2,rr,8,4,"cc-card",3),e.ALo(3,"async"),e.qZA()()),2&i){const t=n.ngIf,o=e.oxw();e.xp6(1),e.Q6J("formGroup",o.form)("ccConnectForm",t),e.xp6(1),e.Q6J("ngIf",e.lcZ(3,3,o.predefinedLinks$))}}ee.\u0275fac=function(n){return new(n||ee)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(H.yJ))},ee.\u0275cmp=e.Xpm({type:ee,selectors:[["multi-issue-content"]],inputs:{colors:"colors",parameters:"parameters",colors$:"colors$",links:"links",form:"form",page:"page"},outputs:{onSave:"onSave"},decls:27,vars:15,consts:[[1,"fluid-container"],[3,"formGroup"],[1,""],[1,"mt-4","mb-4"],[1,"row"],[1,"col-md-8",3,"formGroupName"],["label","Title","formControlName","text","required","true",3,"config"],[1,"col-md-4"],["formControlName","graphicComponent","name","graphicComponent","search","false","required","true",3,"data"],[1,"col-md-12",3,"formGroupName"],["formControlName","text","name","screenSubTitle","required","true",3,"label","config"],[1,"col-md-12","mt-4","row"],[1,"col-md-10"],["class","col-md-2",4,"ngIf"],[1,"row","p-4"],["cc-align-tabs","left",1,"w-100",3,"selectedTabChange"],["label","Main Tracker Page","formGroupName","mainTrackerPage"],[4,"ngIf"],["label","Home Traker Page","formGroupName","homeTrackerPage"],[1,"col-md-2"],[2,"padding","7px","border-radius","5px"],["formControlName","progressBarColor","name","progressBarColor","label","Tracker Color","search","false","required","true",3,"data"],["class","cc-selected-item",4,"ccSelectTrigger"],[1,"cc-selected-item"],["cc-button",""],[3,"parameters","type","mainTrackerPage","form","onSave"],[3,"parameters","form","homeTrackerPage","onSave"]],template:function(n,t){if(1&n&&(e.TgZ(0,"div",0)(1,"form",1)(2,"cc-card",2)(3,"cc-card-title")(4,"h3",3),e._uU(5,"Page Setup"),e.qZA()(),e.TgZ(6,"cc-card-content")(7,"div",4)(8,"div",5),e._UZ(9,"cc-new-editor",6),e.qZA(),e.TgZ(10,"div",7)(11,"cc-label"),e._uU(12," Select Graphic Component "),e.qZA(),e._UZ(13,"cc-select",8),e.ALo(14,"async"),e.qZA(),e.TgZ(15,"div",9),e._UZ(16,"cc-new-editor",10),e.qZA(),e.TgZ(17,"div",11),e._UZ(18,"div",12),e.YNc(19,zo,5,7,"div",13),e.ALo(20,"async"),e.qZA()(),e.TgZ(21,"div",14)(22,"cc-tab-group",15),e.NdJ("selectedTabChange",function(){return t.homeClick()}),e.TgZ(23,"cc-tab",16),e.YNc(24,Vo,2,4,"div",17),e.qZA(),e.TgZ(25,"cc-tab",18),e.YNc(26,Wo,2,3,"div",17),e.qZA()()()()()()()),2&n){let o;e.xp6(1),e.Q6J("formGroup",t.form),e.xp6(7),e.Q6J("formGroupName","screenTitle"),e.xp6(1),e.Q6J("config",t.titleConfig),e.xp6(4),e.Q6J("data",null!==(o=e.lcZ(14,10,t.graphicComponents))&&void 0!==o?o:e.DdM(14,Xo)),e.xp6(2),e.Q6J("formGroupName","screenSubTitle"),e.xp6(1),e.Q6J("label","Sub Title")("config",t.subTitleConfig),e.xp6(3),e.Q6J("ngIf",e.lcZ(20,12,t.colors$)),e.xp6(5),e.Q6J("ngIf",!t.homeClicked),e.xp6(2),e.Q6J("ngIf",t.homeClicked)}},directives:[l._Y,l.JL,l.sg,_.Dt,_.K9,_.uw,l.x0,M.F,l.JJ,l.u,l.Q7,U.k_,P.jB,h.O5,P.hV,k.uu,Z.e6,Z.eF,L,I],pipes:[h.Ov],styles:[".radio-group[_ngcontent-%COMP%]{display:flex;gap:10px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]{appearance:none;border:2px solid #ccc;border-radius:8px;width:20px;height:20px;margin-right:5px}.radio-group[_ngcontent-%COMP%]   input[type=radio][_ngcontent-%COMP%]:checked{background-color:#007bff;border-color:#007bff;color:#fff}.radio-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{display:flex;align-items:center;font-family:Arial,sans-serif;font-size:14px}"],changeDetection:0}),ee=(0,b.gn)([T.kG],ee);let te=class{constructor(n,t,o,r,a,c,p){this._store=n,this.formBuilder=t,this._dialog=o,this.router=r,this._route=a,this.cdRef=c,this._notifications=p,this.maidSituations$=this._store.selectMaidSituations.pipe((0,f.U)(d=>d.map(m=>({id:m.id,text:m.name})))),this.colors$=this._store.selectColors.pipe((0,f.U)(d=>d.map(m=>({id:m.id,text:m.name,data:m})))),this.Editorcolors$=this._store.selectColors,this.predefinedLinks$=this._store.selectPredefinedLinks.pipe((0,f.U)(d=>d.map(m=>({value:m.code,text:m.name})))),this.setupPage$=this._store.selectSetupPage,this.parameters$=this._store.selectParameters,this.form=this.formBuilder.group({}),this.selectedIndex=0}ngOnChanges(n){n.form&&(this.setupPage$=this._store.selectSetupPage)}ngViewAfterInit(){this.cdRef.markForCheck()}ngOnInit(){var n;this._store.resetState(),this.type=(null!==(n=this._route.snapshot.paramMap.get("type"))&&void 0!==n?n:"")||null,this.type&&this._store.getMultiPage(this.type),this._store.fetchConfigurations(),this.Editorcolors$.subscribe(t=>{this.colors=t.map(o=>({value:o.tags[0].value,text:o.code}))}),this.setupPage$.subscribe(t=>{this.currentState=t}),this.parameters$.subscribe(t=>{this.parameters=t}),this.maidSituations$.subscribe(t=>{this.maidSituations=t})}saveState(){this._store.savePageState(this.form.value)}gotoMainPage(){this._store.resetState(),this.router.navigateByUrl("/visa/tracker")}removeNull(n){return Object.keys(n).forEach(t=>{var o;n[t]&&"object"==typeof n[t]?this.removeNull(n[t]):null==n[t]&&(null===(o=Object.getOwnPropertyDescriptor(n,t))||void 0===o?void 0:o.configurable)&&delete n[t]}),n}save(){this._store.savePageState(Object.assign({},this.form.value));let n=this.removeNull(Object.assign(Object.assign({},this.form.value),{informative:this.form.value.informative.map(r=>({id:r.id,showInformativeIcon:r.showInformativeIcon,text:r.text,nationalities:r.nationalities.map(a=>({id:a.id,name:a.text}))})),graphicComponent:{id:this.form.value.graphicComponent},progressBarColor:{id:this.form.value.progressBarColor}})),t=n.mainTrackerPage.callToActions.filter(r=>r.primaryButton).length,o=n.homeTrackerPage.callToActions.filter(r=>r.primaryButton).length;t>1||o>1?this._notifications.notifyError("Only One Primary CTA can be added in home page or main page"):""!=n.homeTrackerPage.screenText.text?this._store.editMultiPage(n):this._notifications.notifyError("Home Tracker Page is not filled")}gotMainPage(){this.router.navigateByUrl("/visa/tracker")}situatuationChange(n){const t=this.form.get("maidSituations");t.clear(),n&&n.forEach(o=>{const r=new l.NI(this.maidSituations.find(a=>a.id==o));t.push(r)}),this._store.savePageState(Object.assign({},this.form.value))}};te.\u0275fac=function(n){return new(n||te)(e.Y36(x),e.Y36(l.qu),e.Y36(j.uY),e.Y36(A.F0),e.Y36(A.gz),e.Y36(e.sBO),e.Y36(H.zg))},te.\u0275cmp=e.Xpm({type:te,selectors:[["app-setup-multiple-page"]],features:[e.TTD],decls:2,vars:3,consts:[["class","fluid-container m-4",4,"ngIf"],[1,"fluid-container","m-4"],[3,"formGroup","ccConnectForm"],["class","",4,"ngIf"],[1,""],[1,"mt-4","mb-4"],[4,"ngIf"],[1,"row"],[1,"col-md-8"],["formArrayName","maidSituations","multiple","true","search","false","required","true",3,"data","value","valueChange"],[3,"form","page","colors","colors$","parameters","links","onSave",4,"ngIf"],[1,"row","justify-content-end"],["cc-raised-button","",1,"m-2",2,"width","20%",3,"click"],["cc-raised-button","","color","accent",1,"m-2",2,"width","20%",3,"disabled","click"],[3,"form","page","colors","colors$","parameters","links","onSave"]],template:function(n,t){1&n&&(e.YNc(0,ir,4,5,"div",0),e.ALo(1,"async")),2&n&&e.Q6J("ngIf",e.lcZ(1,1,t.setupPage$))},directives:[h.O5,l._Y,l.JL,l.sg,T.Ls,_.Dt,_.K9,_.uw,U.k_,P.jB,l.CE,X,ee,_.uC,k.uu],pipes:[h.Ov],styles:[""],changeDetection:0}),te=(0,b.gn)([T.kG],te);const ar=[{path:"",component:Y,data:{pageCode:"tracker"}},{path:"setup",component:q,data:{pageCode:"tracker"}},{path:"setup-multiple/:type",component:te,data:{pageCode:"tracker"}},{path:"setup/:id",component:q,data:{pageCode:"tracker"}},{path:"setup/view/:id",component:q,data:{pageCode:"tracker"}}];let cr=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=e.oAB({type:i}),i.\u0275inj=e.cJS({providers:[x],imports:[[h.ez,un,A.Bz.forChild(ar),_.Ev,l.UX,l.u5,T.er,ie.$,P.lK,T.gZ,T.n_.forFeature({defaultPageSize:30}),Ot.JC,ne.f,Nt.A,Zt.YV,k.S6,j.I8,O.L,pe.XD,de.D$,At.yU,N.Gz,j.I8,de.bY,Mt.N,M.k,se.B,Z.wA,B.pS,Jt.sJ.forChild({})]]}),i})()},46700:(fe,le,g)=>{var h={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function A(N){var B=T(N);return g(B)}function T(N){if(!g.o(h,N)){var B=new Error("Cannot find module '"+N+"'");throw B.code="MODULE_NOT_FOUND",B}return h[N]}A.keys=function(){return Object.keys(h)},A.resolve=T,fe.exports=A,A.id=46700}}]);
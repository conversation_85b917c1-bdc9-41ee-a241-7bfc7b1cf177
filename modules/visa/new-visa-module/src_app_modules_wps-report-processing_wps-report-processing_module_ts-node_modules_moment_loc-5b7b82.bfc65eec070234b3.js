(self["webpackJsonpvisa-angular"]=self["webpackJsonpvisa-angular"]||[]).push([["src_app_modules_wps-report-processing_wps-report-processing_module_ts-node_modules_moment_loc-5b7b82"],{58460:(k,w,r)=>{"use strict";r.r(w),r.d(w,{WpsReportProcessingModule:()=>xe});var m=r(69808),e=r(5e3),v=r(13859),p=r(97582),d=r(48966),G=r(18505),R=r(88476),O=r(15439),I=r(40520),u=r(43604),L=r(8188),B=r(92340);let h=(()=>{class s{constructor(t){this.http=t,this.extractPayrollData=n=>this.http.post(u.b.extractPayrollData,{},{params:{sheetName:n},context:(new I.qT).set(L.hG,!1)}),this.existReportDate=n=>this.http.get(u.b.existReportDate,{params:{reportDate:n}}),this.processWpsReport=(n,a)=>this.http.post(u.b.processWpsReport,a,{params:{attachmentId:n}}),this.extractViolatorsReport=()=>this.http.get(u.b.extractViolatorsReport),this.getWpsViolatorsReportRecipients=()=>this.http.get(`${B.N.apiBase}/${u.b.publicParameter}`,{params:{code:"WPS_VIOLATORS_REPORT_RECEPIENTS"}}),this.getWpsFulfillmentOptions=this.http.get(u.b.getWpsFulfillmentOptions),this.getWpsEmployeeRecords=n=>this.http.get(u.b.getWpsEmployeeByUniqueId,{params:{employeeUniqueId:n}}),this.deleteWpsEmployee=n=>this.http.post(u.b.deleteWpsEmployee,{},{params:{employeeUniqueId:n}}),this.getAllEmployees=n=>this.http.get(u.b.getWpsAllEmployees,{params:{page:n.params.page||0,size:n.params.size||20,sort:n.params.sort||""}})}getUnknownEmployees(t){const n=t.search.wpsSearchCriteria||[];return console.log("payload",n),this.http.post(u.b.getUnknownEmployees,n,{params:{page:t.params.page||0,size:t.params.size||20,trashed:t.search.trashed||"",sort:t.params.sort||""}})}}return s.\u0275fac=function(t){return new(t||s)(e.LFG(I.eN))},s.\u0275prov=e.Yz7({token:s,factory:s.\u0275fac}),s})();var M=r(61135),V=r(77579);const D={params:{page:0,size:20,sort:""},search:{}};let S=(()=>{class s{constructor(){this.refreshSubject=new M.X(0),this.refresh$=this.refreshSubject.asObservable(),this.searchSubject=new M.X(D),this.search$=this.searchSubject.asObservable(),this.isWpsReportProcessedSubject=new V.x,this.isWpsReportProcessed$=this.isWpsReportProcessedSubject.asObservable()}refresh(){this.refreshSubject.next(1)}updateSearchState(t){const a=Object.assign(Object.assign({},this.searchSubject.value),t);this.searchSubject.next(a)}resetSearchState(){this.searchSubject.next(D)}setIsWpsReportProcessedState(t){this.isWpsReportProcessedSubject.next(t)}}return s.\u0275fac=function(t){return new(t||s)},s.\u0275prov=e.Yz7({token:s,factory:s.\u0275fac}),s})();var l=r(93075),E=r(21799),X=r(54004);let F=(()=>{class s{constructor(t){this.apiService=t,this.monthNames=["January","February","March","April","May","June","July","August","September","October","November","December"],this.wpsFulfillmentOptions=()=>this.apiService.getWpsFulfillmentOptions.pipe((0,X.U)(n=>Object.entries(n).map(([a,i])=>({id:a,text:i}))))}get monthOptions(){return this.monthNames.map(t=>({id:t.charAt(0).toUpperCase()+t.slice(1).toLowerCase(),text:t.charAt(0).toUpperCase()+t.slice(1).toLowerCase()}))}get yearOptions(){const t=(new Date).getFullYear();return Array.from({length:10},(n,a)=>{const i=t-a;return{id:i.toString(),text:i.toString()}})}getPreviousMonthsOptions(){const t=new Date,n=t.getMonth(),a=t.getFullYear();return Array.from({length:4},(i,f)=>{let _=n-(f+1),T=a;return _<0&&(_=12+_,T--),{id:`${T}-${(_+1).toString().padStart(2,"0")}-01`,text:`${this.monthNames[_]} - ${T}`}})}}return s.\u0275fac=function(t){return new(t||s)(e.LFG(h))},s.\u0275prov=e.Yz7({token:s,factory:s.\u0275fac}),s})();var c=r(82599),P=r(26523),q=r(28172),g=r(65868);function H(s,o){1&s&&(e.TgZ(0,"div",10)(1,"span"),e._uU(2,"Warning: "),e.qZA(),e.TgZ(3,"span"),e._uU(4,"A report with the exact same "),e.TgZ(5,"strong"),e._uU(6,"report date"),e.qZA(),e._uU(7," was uploaded already"),e.qZA()())}let b=class{constructor(o,t,n,a,i,f,_,T){this.apiService=o,this.stateService=t,this.formBuilder=n,this.notifications=a,this.dataService=i,this.cdRef=f,this.dialogRef=_,this.data=T,this.existReportDate=!1,this.monthOptions=this.dataService.monthOptions,this.yearOptions=this.dataService.yearOptions,this.form=this.formBuilder.group({payrollMonthLabel:"",payrollYear:"",reportDate:""})}ngOnInit(){var o;this.form.patchValue(Object.assign({},this.data));const t=O(this.data.reportDate).format("YYYY-MM-DD");this.isExistReportData("Invalid date"!==t?t:""),null===(o=this.form.get("reportDate"))||void 0===o||o.valueChanges.subscribe(n=>{const a=O(n).format("YYYY-MM-DD");this.isExistReportData(a)}),this.dialogRef.afterClosed().subscribe(()=>{this.stateService.setIsWpsReportProcessedState({value:!1,key:"Cancelled"})})}processWpsReport(){const{payrollMonthLabel:o,payrollYear:t,reportDate:n}=this.form.value,a={payrollMonthLabel:o,payrollYear:t.toString(),reportDate:O(n).format("YYYY-MM-DD 00:00:00")};this.apiService.processWpsReport(this.data.attachmentId,a).subscribe(()=>{this.notifications.notifySuccess("Processed Successfully"),this.stateService.setIsWpsReportProcessedState({value:!1,key:"Processed"}),this.dialogRef.close()})}isExistReportData(o){this.apiService.existReportDate(o).subscribe(t=>{this.existReportDate=t,this.cdRef.detectChanges()})}};b.\u0275fac=function(o){return new(o||b)(e.Y36(h),e.Y36(S),e.Y36(l.qu),e.Y36(E.zg),e.Y36(F),e.Y36(e.sBO),e.Y36(d.so),e.Y36(d.WI))},b.\u0275cmp=e.Xpm({type:b,selectors:[["ng-component"]],decls:16,vars:9,consts:[["cc-std-dialog",""],["cc-dialog-title","",3,"align"],["cc-dialog-close","","cc-dialog-close-button",""],[3,"formGroup"],["formControlName","payrollYear","label","Payroll year",3,"data","required"],["formControlName","payrollMonthLabel","label","Payroll month",3,"data","required"],["formControlName","reportDate","label","Report date",3,"required"],["class","py-3 d-flex gap-1","style","color: red",4,"ngIf"],["cc-dialog-close","","cc-flat-button",""],["cc-raised-button","","color","primary",3,"disabled","click"],[1,"py-3","d-flex","gap-1",2,"color","red"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),e._uU(3,"WPS Records"),e.qZA(),e._UZ(4,"a",2),e.qZA(),e.TgZ(5,"cc-dialog-content")(6,"form",3),e._UZ(7,"cc-select",4)(8,"cc-select",5)(9,"cc-datepicker",6),e.YNc(10,H,8,0,"div",7),e.qZA()(),e.TgZ(11,"cc-dialog-actions")(12,"button",8),e._uU(13,"Cancel"),e.qZA(),e.TgZ(14,"button",9),e.NdJ("click",function(){return t.processWpsReport()}),e._uU(15," Confirm "),e.qZA()()()),2&o&&(e.xp6(2),e.Q6J("align","left"),e.xp6(4),e.Q6J("formGroup",t.form),e.xp6(1),e.Q6J("data",t.yearOptions)("required",!0),e.xp6(1),e.Q6J("data",t.monthOptions)("required",!0),e.xp6(1),e.Q6J("required",!0),e.xp6(1),e.Q6J("ngIf",t.existReportDate),e.xp6(4),e.Q6J("disabled",!t.form.valid))},directives:[c.iK,c.Cj,c.Zb,c.zn,c.fX,c.kL,l._Y,l.JL,l.sg,P.jB,l.JJ,l.u,l.Q7,q.AC,m.O5,c.Zu,g.uu],encapsulation:2,changeDetection:0}),b=(0,p.gn)([R.kG],b);const K=[[["","alert-content",""]],[["","alert-action",""]]],ee=["[alert-content]","[alert-action]"];let z=(()=>{class s{constructor(){}ngOnInit(){}}return s.\u0275fac=function(t){return new(t||s)},s.\u0275cmp=e.Xpm({type:s,selectors:[["alert-popup"]],ngContentSelectors:ee,decls:11,vars:2,consts:[["cc-std-dialog",""],["cc-dialog-header",""],["cc-dialog-title","",3,"align"],["cc-dialog-close","","cc-dialog-close-button",""],["cc-dialog-content",""],["cc-dialog-actions","",3,"align"],["cc-flat-button","","color","accent","cc-dialog-close",""]],template:function(t,n){1&t&&(e.F$t(K),e.TgZ(0,"div",0)(1,"div",1)(2,"h1",2),e._uU(3,"Alert"),e.qZA(),e._UZ(4,"a",3),e.qZA(),e.TgZ(5,"div",4),e.Hsn(6),e.qZA(),e.TgZ(7,"div",5)(8,"button",6),e._uU(9,"No"),e.qZA(),e.Hsn(10,1),e.qZA()()),2&t&&(e.xp6(2),e.Q6J("align","center"),e.xp6(5),e.Q6J("align","end"))},directives:[c.iK,c.Cj,c.Zb,c.zn,c.fX,c.kL,c.Zu,g.uu],encapsulation:2,changeDetection:0}),s})(),j=class{constructor(o,t,n,a,i){this.apiService=o,this.stateService=t,this.dialog=n,this.dialogRef=a,this.data=i}ngOnInit(){this.dialogRef.afterClosed().subscribe(()=>{this.stateService.setIsWpsReportProcessedState({value:!1,key:"Cancelled"})})}onApply(){this.apiService.extractPayrollData(this.data.fileName).pipe((0,G.b)(o=>{this.dialog.originalOpen(b,{panelClass:["col-md-6"],data:Object.assign(Object.assign({},o),{attachmentId:this.data.attachmentId})})})).subscribe(()=>{this.stateService.setIsWpsReportProcessedState({value:!0,key:"Processed"})})}};j.\u0275fac=function(o){return new(o||j)(e.Y36(h),e.Y36(S),e.Y36(c.uY),e.Y36(d.so),e.Y36(d.WI))},j.\u0275cmp=e.Xpm({type:j,selectors:[["ng-component"]],decls:7,vars:0,consts:[["alert-content",""],["alert-action",""],["cc-raised-button","","color","primary","cc-dialog-close","",3,"click"]],template:function(o,t){1&o&&(e.TgZ(0,"alert-popup"),e.ynx(1,0),e.TgZ(2,"p"),e._uU(3,"Are you sure you want to apply the WPS report"),e.qZA(),e.BQk(),e.ynx(4,1),e.TgZ(5,"button",2),e.NdJ("click",function(){return t.onApply()}),e._uU(6," Yes "),e.qZA(),e.BQk(),e.qZA())},directives:[z,g.uu,c.zn],encapsulation:2,changeDetection:0}),j=(0,p.gn)([R.kG],j);let te=(()=>{class s{constructor(t,n,a,i){this.apiService=t,this.notifications=n,this.dialogRef=a,this.data=i}ngOnInit(){}onApply(){this.apiService.extractViolatorsReport().subscribe(t=>{this.notifications.notifySuccess("Sent Successfully"),this.dialogRef.close()})}}return s.\u0275fac=function(t){return new(t||s)(e.Y36(h),e.Y36(E.zg),e.Y36(d.so),e.Y36(d.WI))},s.\u0275cmp=e.Xpm({type:s,selectors:[["ng-component"]],decls:9,vars:1,consts:[["alert-content",""],[2,"text-wrap","pretty"],[2,"text-wrap","pretty","color","red"],["alert-action",""],["cc-raised-button","","color","primary",3,"click"]],template:function(t,n){1&t&&(e.TgZ(0,"alert-popup"),e.ynx(1,0),e.TgZ(2,"p",1),e._uU(3," This will send a list of violator employees to the following emails: "),e.qZA(),e.TgZ(4,"p",2),e._uU(5),e.qZA(),e.BQk(),e.ynx(6,3),e.TgZ(7,"button",4),e.NdJ("click",function(){return n.onApply()}),e._uU(8," Yes "),e.qZA(),e.BQk(),e.qZA()),2&t&&(e.xp6(5),e.Oqu(n.data.content))},directives:[z,g.uu],encapsulation:2,changeDetection:0}),s})();var J=r(467);const oe={acceptedFiles:".xlsx, .xls, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel",maxFiles:1,maxFilesize:20};let se=(()=>{class s{constructor(t,n,a,i,f){this.apiService=t,this.stateService=n,this.dialog=a,this.formBuilder=i,this.cdRef=f,this.isWpsReportProcessed$=this.stateService.isWpsReportProcessed$,this.dropzoneConfig=oe,this.isWpsReportProcessed=!1,this.form=this.formBuilder.group({wpsReportFile:""})}ngOnInit(){this.stateService.isWpsReportProcessed$.subscribe(t=>{this.isWpsReportProcessed=t.value&&"Processed"===t.key,!t.value&&"Processed"===t.key&&this.form.reset(),this.cdRef.detectChanges()})}applyWPSReport(){this.stateService.setIsWpsReportProcessedState({value:!0,key:"Processed"}),this.dialog.originalOpen(j,{panelClass:["col-md-4"],data:{fileName:this.form.value.wpsReportFile[0].name,attachmentId:this.form.value.wpsReportFile[0].id}})}extractWPSViolatorsReport(){this.apiService.getWpsViolatorsReportRecipients().subscribe(t=>{this.dialog.originalOpen(te,{panelClass:["col-md-4"],data:{content:t.map(n=>n.value).toString().split(",").join(", ")}})})}}return s.\u0275fac=function(t){return new(t||s)(e.Y36(h),e.Y36(S),e.Y36(c.uY),e.Y36(l.qu),e.Y36(e.sBO))},s.\u0275cmp=e.Xpm({type:s,selectors:[["upload-and-manage-wps-report"]],decls:9,vars:5,consts:[[1,"px-2","my-4","py-1"],[1,"d-flex","col-md-12","justify-content-md-between","gap-2","gap-md-0","flex-column","flex-md-row",3,"formGroup"],[1,"col-md-5"],["formControlName","wpsReportFile","label","Upload WPS Report",1,"d-inline",3,"dropzoneConfig","required"],[1,"col-md-5","d-flex","flex-column","justify-content-center","align-items-end","gap-2"],["cc-raised-button","","color","warn",1,"w-100",3,"disabled","click"]],template:function(t,n){1&t&&(e.TgZ(0,"div",0)(1,"form",1)(2,"div",2),e._UZ(3,"cc-file-uploader",3),e.qZA(),e.TgZ(4,"div",4)(5,"button",5),e.NdJ("click",function(){return n.applyWPSReport()}),e._uU(6," Apply WPS Report "),e.qZA(),e.TgZ(7,"button",5),e.NdJ("click",function(){return n.extractWPSViolatorsReport()}),e._uU(8," Extract WPS violators report "),e.qZA()()()()),2&t&&(e.xp6(1),e.Q6J("formGroup",n.form),e.xp6(2),e.Q6J("dropzoneConfig",n.dropzoneConfig)("required",!0),e.xp6(2),e.Q6J("disabled",!n.form.valid),e.xp6(2),e.Q6J("disabled",n.isWpsReportProcessed))},directives:[l._Y,l.JL,l.sg,J.U2,l.JJ,l.u,l.Q7,g.uu],styles:[""],changeDetection:0}),s})();var U=r(63900),A=r(62764);const ne=[{field:"payrollMonthLabel",header:"Payroll Month"},{field:"contractSalary",header:"MOHRE Salary"},{field:"paidSalary",header:"Paid Salary"},{field:"wpsStatus",header:"WPS Status"},{field:"reportDate",header:"Report date"},{field:"uploadedDate",header:"Upload date"}];let y=class{constructor(o,t,n){this.apiService=o,this.cdRef=t,this.data=n,this.gridCols=ne,this.records=[]}ngOnInit(){this.apiService.getWpsEmployeeRecords(this.data.employeeUniqueId).subscribe(o=>{this.records=o,this.cdRef.detectChanges()})}};y.\u0275fac=function(o){return new(o||y)(e.Y36(h),e.Y36(e.sBO),e.Y36(d.WI))},y.\u0275cmp=e.Xpm({type:y,selectors:[["app-wps-records-history"]],decls:34,vars:9,consts:[["cc-std-dialog",""],["cc-dialog-title","",3,"align"],["cc-dialog-close","","cc-dialog-close-button",""],[1,"mb-4","px-4"],[1,"d-flex","gap-2"],[2,"color","red"],[3,"columns","data","showPaginator","loading"],["cc-raised-button","","cc-dialog-close","","color","primary",1,"px-4"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"cc-dialog-header")(2,"h1",1),e._uU(3,"WPS Records"),e.qZA(),e._UZ(4,"a",2),e.qZA(),e.TgZ(5,"cc-dialog-content")(6,"div",3)(7,"span"),e._uU(8,"The below are the WPS records for :"),e.qZA(),e.TgZ(9,"div",4)(10,"span",5),e._uU(11,"Name:"),e.qZA(),e.TgZ(12,"span"),e._uU(13),e.qZA()(),e.TgZ(14,"div",4)(15,"span",5),e._uU(16,"Name (Arabic):"),e.qZA(),e.TgZ(17,"span"),e._uU(18),e.qZA()(),e.TgZ(19,"div",4)(20,"span",5),e._uU(21,"EUID:"),e.qZA(),e.TgZ(22,"span"),e._uU(23),e.qZA()(),e.TgZ(24,"div",4)(25,"span",5),e._uU(26,"Violator"),e.qZA(),e.TgZ(27,"span"),e._uU(28),e.qZA()()(),e.TgZ(29,"div"),e._UZ(30,"cc-datagrid",6),e.qZA()(),e.TgZ(31,"cc-dialog-actions")(32,"button",7),e._uU(33," Close "),e.qZA()()()),2&o&&(e.xp6(2),e.Q6J("align","left"),e.xp6(11),e.Oqu(t.data.enName),e.xp6(5),e.Oqu(t.data.arName),e.xp6(5),e.Oqu(t.data.employeeUniqueId),e.xp6(5),e.Oqu(t.data.violate),e.xp6(2),e.Q6J("columns",t.gridCols)("data",t.records)("showPaginator",!1)("loading",!1))},directives:[c.iK,c.Cj,c.Zb,c.zn,c.fX,c.kL,A.Ge,c.Zu,g.uu],encapsulation:2,changeDetection:0}),y=(0,p.gn)([R.kG],y);var W=r(34378),N=r(45834),Q=r(4882);const ae=function(){return{standalone:!0}};function re(s,o){if(1&s){const t=e.EpF();e.ynx(0),e.TgZ(1,"div",6)(2,"cc-select",14),e.NdJ("ngModelChange",function(a){const f=e.CHM(t).$implicit;return e.oxw().monthFulfillmentsModel[f.id]=a}),e.qZA()(),e.BQk()}if(2&s){const t=o.$implicit,n=e.oxw();e.xp6(2),e.Q6J("label","WPS Status for "+t.text)("data",n.fulfillmentOptions)("ngModel",n.monthFulfillmentsModel[t.id])("ngModelOptions",e.DdM(6,ae))("multiple",!0)("emitFullSelectOption",!0)}}let C=class{constructor(o,t,n,a){this.fb=o,this.dataService=t,this.cdr=n,this.stateService=a,this.monthOptions=[],this.fulfillmentOptions=[],this.selectedMonths=[],this.monthFulfillmentsModel={},this.filterForm=this.fb.group({months:[[]],trashed:[!1]})}ngOnInit(){var o;this.monthOptions=this.dataService.getPreviousMonthsOptions(),this.dataService.wpsFulfillmentOptions().subscribe(t=>{this.fulfillmentOptions=t,this.cdr.detectChanges()}),null===(o=this.filterForm.get("months"))||void 0===o||o.valueChanges.subscribe(t=>{this.selectedMonths=t||[];const n=new Set(this.selectedMonths.map(a=>a.id));Object.keys(this.monthFulfillmentsModel).filter(a=>!n.has(a)).forEach(a=>delete this.monthFulfillmentsModel[a]),this.selectedMonths.forEach(a=>{void 0===this.monthFulfillmentsModel[a.id]&&(this.monthFulfillmentsModel[a.id]=[])})})}onSearch(){const o=Object.entries(this.monthFulfillmentsModel).map(([t,n])=>({payrollDate:t,wpsStatus:0!==n.length?n.map(a=>a.id):null}));this.stateService.updateSearchState({search:{wpsSearchCriteria:o,trashed:this.filterForm.value.trashed}})}};function ie(s,o){if(1&s){const t=e.EpF();e.TgZ(0,"button",8),e.NdJ("click",function(){e.CHM(t);const a=e.oxw().$implicit;return e.oxw(2).addToTrash(a)}),e.TgZ(1,"cc-icon"),e._uU(2,"delete"),e.qZA()()}}function ce(s,o){1&s&&e.YNc(0,ie,3,0,"button",7),2&s&&e.Q6J("ngIf",!o.$implicit.trashed)}function le(s,o){if(1&s){const t=e.EpF();e.TgZ(0,"a",9),e.NdJ("click",function(){const i=e.CHM(t).$implicit;return e.oxw(2).openWpsRecordsDialog(i)}),e._uU(1),e.qZA()}if(2&s){const t=o.$implicit;e.xp6(1),e.Oqu(t.employeeUniqueId)}}C.\u0275fac=function(o){return new(o||C)(e.Y36(l.qu),e.Y36(F),e.Y36(e.sBO),e.Y36(S))},C.\u0275cmp=e.Xpm({type:C,selectors:[["app-search-filter"]],decls:23,vars:7,consts:[[1,"my-4"],["expanded","true"],[1,"d-flex","justify-content-center","align-items-center","gap-1"],[2,"margin-right","2px"],[3,"formGroup"],[1,"d-flex","col-md-12","flex-column","flex-md-row","flex-wrap"],[1,"col-md-6"],["label","Payroll Month","formControlName","months",3,"data","multiple","emitFullSelectOption"],[4,"ngFor","ngForOf"],[1,"col-md-12"],["formControlName","trashed",3,"labelPosition"],[2,"font-weight","500"],[1,"d-flex","col-md-12","justify-content-center","gap-2","py-1"],["type","button","cc-raised-button","","color","accent",1,"px-4",3,"click"],[3,"label","data","ngModel","ngModelOptions","multiple","emitFullSelectOption","ngModelChange"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"cc-accordion")(2,"cc-panel",1)(3,"cc-panel-title",2)(4,"cc-icon",3),e._uU(5,"filter_alt"),e.qZA(),e.TgZ(6,"span"),e._uU(7,"Filter"),e.qZA()(),e.TgZ(8,"cc-panel-body")(9,"form",4)(10,"div",5)(11,"div",6),e._UZ(12,"cc-select",7),e.qZA(),e.YNc(13,re,3,7,"ng-container",8),e.TgZ(14,"div",9)(15,"cc-checkbox",10)(16,"span",11),e._uU(17,"Show trashed records only"),e.qZA(),e.TgZ(18,"span"),e._uU(19),e.qZA()()()(),e.TgZ(20,"div",12)(21,"button",13),e.NdJ("click",function(){return t.onSearch()}),e._uU(22," Search "),e.qZA()()()()()()()),2&o&&(e.xp6(9),e.Q6J("formGroup",t.filterForm),e.xp6(3),e.Q6J("data",t.monthOptions)("multiple",!0)("emitFullSelectOption",!0),e.xp6(1),e.Q6J("ngForOf",t.selectedMonths),e.xp6(2),e.Q6J("labelPosition","after"),e.xp6(4),e.Oqu(" (Trashed records will be highlighted in yellow)"))},directives:[W.I,W.CW,W.LL,N.Q9,W.G9,l._Y,l.JL,l.sg,P.jB,l.JJ,l.u,m.sg,l.On,Q.E,g.uu],encapsulation:2,changeDetection:0}),C=(0,p.gn)([R.kG],C);const Y=function(){return[]},pe=function(s,o){return{operations:s,employeeUniqueId:o}};function de(s,o){if(1&s){const t=e.EpF();e.ynx(0),e.TgZ(1,"cc-datagrid",3),e.NdJ("page",function(a){return e.CHM(t),e.oxw().handleNextPage(a)})("sortChange",function(a){return e.CHM(t),e.oxw().onSortChange(a)}),e.qZA(),e.YNc(2,ce,1,1,"ng-template",4,5,e.W1O),e.YNc(4,le,2,1,"ng-template",4,6,e.W1O),e.BQk()}if(2&s){const t=o.ngIf,n=e.MAs(3),a=e.MAs(5),i=e.oxw();e.xp6(1),e.Q6J("columns",i.gridCols)("data",t.content||e.DdM(10,Y))("length",t.totalElements)("pageOnFront",!1)("pageIndex",t.number)("pageSize",t.size)("rowStyleFormatter",i.rowStyleFormatter)("cellTemplate",e.WLB(11,pe,n,a)),e.xp6(1),e.Q6J("ccGridCell",t.content||e.DdM(14,Y)),e.xp6(2),e.Q6J("ccGridCell",t.content||e.DdM(15,Y))}}const me=[{field:"operations",header:"Actions"},{field:"enName",header:"Name",sortable:!0,sortProp:{arrowPosition:"after",id:"enName"}},{field:"arName",header:"Name (Arabic)",sortable:!0,sortProp:{arrowPosition:"after",id:"arName"}},{field:"visaStep",header:"Visa step (matching using name)"},{field:"employeeUniqueId",header:"EUID",sortable:!0,sortProp:{arrowPosition:"after",id:"employeeUniqueId"}}];let x=class{constructor(o,t,n){this.apiService=o,this.stateService=t,this.dialog=n,this.gridCols=me,this.rowStyleFormatter={"background-color":(a,i)=>({condition:a.trashed,value:"rgb(255, 224, 110)"}),color:(a,i)=>({condition:a.appointmentPassed,value:"#000"})}}ngOnInit(){this.vm$=this.stateService.refresh$.pipe((0,U.w)(()=>this.stateService.search$.pipe((0,U.w)(o=>this.apiService.getUnknownEmployees(o)))))}onSortChange(o){this.stateService.updateSearchState({params:{sort:`${o.active},${o.direction}`}})}handleNextPage(o){this.stateService.updateSearchState({params:{page:o.pageIndex,size:o.pageSize}})}openWpsRecordsDialog(o){this.dialog.originalOpen(y,{panelClass:["col-md-8"],data:o})}addToTrash(o){this.dialog.confirm("Warning","Are you sure you want to delete this employee?",()=>{this.deleteEmployee(o)},()=>{})}deleteEmployee(o){this.apiService.deleteWpsEmployee(o.employeeUniqueId).subscribe(()=>{this.stateService.refresh()})}ngOnDestroy(){this.stateService.resetSearchState()}};function ue(s,o){if(1&s){const t=e.EpF();e.TgZ(0,"a",6),e.NdJ("click",function(){const i=e.CHM(t).$implicit;return e.oxw(2).openWpsRecordsDialog(i)}),e._uU(1),e.qZA()}if(2&s){const t=o.$implicit;e.xp6(1),e.Oqu(t.employeeUniqueId)}}x.\u0275fac=function(o){return new(o||x)(e.Y36(h),e.Y36(S),e.Y36(c.uY))},x.\u0275cmp=e.Xpm({type:x,selectors:[["not-found-wps-employees"]],decls:7,vars:3,consts:[[1,"px-2","my-4"],[2,"color","red"],[4,"ngIf"],[1,"my-2",3,"columns","data","length","pageOnFront","pageIndex","pageSize","rowStyleFormatter","cellTemplate","page","sortChange"],[3,"ccGridCell"],["actionsTpl",""],["euidTpl",""],["cc-icon-button","","color","warn",3,"click",4,"ngIf"],["cc-icon-button","","color","warn",3,"click"],[1,"px-2",2,"cursor","pointer","color","red","font-weight","500",3,"click"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"div")(2,"p",1),e._uU(3," The below is a list of all employees that were found on the WPS report and do not have a Housemaid or Office Staff profile. "),e.qZA(),e._UZ(4,"app-search-filter"),e.qZA(),e.YNc(5,de,6,16,"ng-container",2),e.ALo(6,"async"),e.qZA()),2&o&&(e.xp6(5),e.Q6J("ngIf",e.lcZ(6,1,t.vm$)))},directives:[C,m.O5,A.Ge,A.VC,g.uu,N.Q9],pipes:[m.Ov],styles:[""],changeDetection:0}),x=(0,p.gn)([R.kG],x);const $=function(){return[]},he=function(s){return{employeeUniqueId:s}};function ge(s,o){if(1&s){const t=e.EpF();e.ynx(0),e.TgZ(1,"cc-datagrid",3),e.NdJ("page",function(a){return e.CHM(t),e.oxw().handleNextPage(a)})("sortChange",function(a){return e.CHM(t),e.oxw().onSortChange(a)}),e.qZA(),e.YNc(2,ue,2,1,"ng-template",4,5,e.W1O),e.BQk()}if(2&s){const t=o.ngIf,n=e.MAs(3),a=e.oxw();e.xp6(1),e.Q6J("columns",a.gridCols)("data",t.content||e.DdM(8,$))("pageIndex",t.number)("length",t.totalElements)("pageSize",t.size)("pageOnFront",!1)("cellTemplate",e.VKq(9,he,n)),e.xp6(1),e.Q6J("ccGridCell",t.content||e.DdM(11,$))}}const fe=[{field:"enName",header:"Name",sortable:!0,sortProp:{arrowPosition:"after",id:"enName"}},{field:"arName",header:"Name (Arabic)",sortable:!0,sortProp:{arrowPosition:"after",id:"arName"}},{field:"employeeUniqueId",header:"EUID",sortable:!0,sortProp:{arrowPosition:"after",id:"employeeUniqueId"}}];let Z=class{constructor(o,t,n){this.apiService=o,this.stateService=t,this.dialog=n,this.gridCols=fe}ngOnInit(){this.vm$=this.stateService.search$.pipe((0,U.w)(o=>this.apiService.getAllEmployees(o)))}handleNextPage(o){this.stateService.updateSearchState({params:{page:o.pageIndex,size:o.pageSize}})}openWpsRecordsDialog(o){this.dialog.originalOpen(y,{panelClass:["col-md-8"],data:o})}onSortChange(o){this.stateService.updateSearchState({params:{sort:`${o.active},${o.direction}`}})}ngOnDestroy(){this.stateService.resetSearchState()}};function _e(s,o){1&s&&e._UZ(0,"upload-and-manage-wps-report")}function ye(s,o){1&s&&e._UZ(0,"not-found-wps-employees")}function ve(s,o){1&s&&e._UZ(0,"wps-records")}Z.\u0275fac=function(o){return new(o||Z)(e.Y36(h),e.Y36(S),e.Y36(c.uY))},Z.\u0275cmp=e.Xpm({type:Z,selectors:[["wps-records"]],decls:6,vars:3,consts:[[1,"px-2","my-4"],[2,"color","red"],[4,"ngIf"],[1,"my-2",3,"columns","data","pageIndex","length","pageSize","pageOnFront","cellTemplate","page","sortChange"],[3,"ccGridCell"],["euidTpl",""],[1,"px-2",2,"cursor","pointer","color","red","font-weight","500",3,"click"]],template:function(o,t){1&o&&(e.TgZ(0,"div",0)(1,"div")(2,"p",1),e._uU(3," The below is a list of all employees that ever existed in a WPS report, grouped by EUID "),e.qZA()(),e.YNc(4,ge,4,12,"ng-container",2),e.ALo(5,"async"),e.qZA()),2&o&&(e.xp6(4),e.Q6J("ngIf",e.lcZ(5,1,t.vm$)))},directives:[m.O5,A.Ge,A.VC],pipes:[m.Ov],styles:[""],changeDetection:0}),Z=(0,p.gn)([R.kG],Z);let Se=(()=>{class s{constructor(){}ngOnInit(){}}return s.\u0275fac=function(t){return new(t||s)},s.\u0275cmp=e.Xpm({type:s,selectors:[["app-shell"]],decls:9,vars:0,consts:[[1,"px-4","my-4"],["animationDuration","0ms"],[1,"d-flex","flex-column","flex-md-row","justify-content-between","align-items-center"],["label","Upload and Manage WPS Report"],["ccTabContent",""],["label","Not Found WPS Employees"],["label","WPS Records"]],template:function(t,n){1&t&&(e.TgZ(0,"div",0)(1,"cc-tab-group",1)(2,"div",2)(3,"cc-tab",3),e.YNc(4,_e,1,0,"ng-template",4),e.qZA(),e.TgZ(5,"cc-tab",5),e.YNc(6,ye,1,0,"ng-template",4),e.qZA(),e.TgZ(7,"cc-tab",6),e.YNc(8,ve,1,0,"ng-template",4),e.qZA()()()())},directives:[v.e6,v.eF,v.ST,se,x,Z],styles:[""],changeDetection:0}),s})();var be=r(1402),je=r(43687);const Ce=[{path:"",component:Se,data:{label:"WPS Report Processing"}}];let xe=(()=>{class s{}return s.\u0275fac=function(t){return new(t||s)},s.\u0275mod=e.oAB({type:s}),s.\u0275inj=e.cJS({providers:[h,S,F],imports:[[m.ez,l.u5,l.UX,be.Bz.forChild(Ce),g.S6,N.L,je.f,q.Bp,Q.$,P.lK,c.I8,v.wA,A.Gz,W.yU,J.sJ.forChild({})]]}),s})()},46700:(k,w,r)=>{var m={"./af":27088,"./af.js":27088,"./ar":17038,"./ar-dz":52502,"./ar-dz.js":52502,"./ar-kw":30128,"./ar-kw.js":30128,"./ar-ly":84519,"./ar-ly.js":84519,"./ar-ma":65443,"./ar-ma.js":65443,"./ar-ps":14523,"./ar-ps.js":14523,"./ar-sa":17642,"./ar-sa.js":17642,"./ar-tn":68592,"./ar-tn.js":68592,"./ar.js":17038,"./az":51213,"./az.js":51213,"./be":69191,"./be.js":69191,"./bg":90322,"./bg.js":90322,"./bm":28042,"./bm.js":28042,"./bn":59620,"./bn-bd":65903,"./bn-bd.js":65903,"./bn.js":59620,"./bo":69645,"./bo.js":69645,"./br":45020,"./br.js":45020,"./bs":64792,"./bs.js":64792,"./ca":47980,"./ca.js":47980,"./cs":47322,"./cs.js":47322,"./cv":90365,"./cv.js":90365,"./cy":32092,"./cy.js":32092,"./da":77387,"./da.js":77387,"./de":54307,"./de-at":29459,"./de-at.js":29459,"./de-ch":73694,"./de-ch.js":73694,"./de.js":54307,"./dv":39659,"./dv.js":39659,"./el":3460,"./el.js":3460,"./en-au":94369,"./en-au.js":94369,"./en-ca":60530,"./en-ca.js":60530,"./en-gb":9998,"./en-gb.js":9998,"./en-ie":13391,"./en-ie.js":13391,"./en-il":75414,"./en-il.js":75414,"./en-in":19615,"./en-in.js":19615,"./en-nz":21248,"./en-nz.js":21248,"./en-sg":13767,"./en-sg.js":13767,"./eo":84530,"./eo.js":84530,"./es":86866,"./es-do":18944,"./es-do.js":18944,"./es-mx":29116,"./es-mx.js":29116,"./es-us":83609,"./es-us.js":83609,"./es.js":86866,"./et":96725,"./et.js":96725,"./eu":67931,"./eu.js":67931,"./fa":56417,"./fa.js":56417,"./fi":20944,"./fi.js":20944,"./fil":61766,"./fil.js":61766,"./fo":95867,"./fo.js":95867,"./fr":1636,"./fr-ca":16848,"./fr-ca.js":16848,"./fr-ch":77773,"./fr-ch.js":77773,"./fr.js":1636,"./fy":14940,"./fy.js":14940,"./ga":91402,"./ga.js":91402,"./gd":46924,"./gd.js":46924,"./gl":16398,"./gl.js":16398,"./gom-deva":72457,"./gom-deva.js":72457,"./gom-latn":52545,"./gom-latn.js":52545,"./gu":42641,"./gu.js":42641,"./he":7536,"./he.js":7536,"./hi":96335,"./hi.js":96335,"./hr":7458,"./hr.js":7458,"./hu":56540,"./hu.js":56540,"./hy-am":65283,"./hy-am.js":65283,"./id":98780,"./id.js":98780,"./is":14205,"./is.js":14205,"./it":34211,"./it-ch":29985,"./it-ch.js":29985,"./it.js":34211,"./ja":31003,"./ja.js":31003,"./jv":60420,"./jv.js":60420,"./ka":40851,"./ka.js":40851,"./kk":16074,"./kk.js":16074,"./km":53343,"./km.js":53343,"./kn":44799,"./kn.js":44799,"./ko":13549,"./ko.js":13549,"./ku":91037,"./ku-kmr":63775,"./ku-kmr.js":63775,"./ku.js":91037,"./ky":93125,"./ky.js":93125,"./lb":69586,"./lb.js":69586,"./lo":32349,"./lo.js":32349,"./lt":92400,"./lt.js":92400,"./lv":39991,"./lv.js":39991,"./me":28477,"./me.js":28477,"./mi":55118,"./mi.js":55118,"./mk":15943,"./mk.js":15943,"./ml":13849,"./ml.js":13849,"./mn":31977,"./mn.js":31977,"./mr":66184,"./mr.js":66184,"./ms":70485,"./ms-my":64524,"./ms-my.js":64524,"./ms.js":70485,"./mt":36681,"./mt.js":36681,"./my":52024,"./my.js":52024,"./nb":42688,"./nb.js":42688,"./ne":68914,"./ne.js":68914,"./nl":11758,"./nl-be":52272,"./nl-be.js":52272,"./nl.js":11758,"./nn":41510,"./nn.js":41510,"./oc-lnc":52797,"./oc-lnc.js":52797,"./pa-in":37944,"./pa-in.js":37944,"./pl":1605,"./pl.js":1605,"./pt":54225,"./pt-br":73840,"./pt-br.js":73840,"./pt.js":54225,"./ro":45128,"./ro.js":45128,"./ru":35127,"./ru.js":35127,"./sd":32525,"./sd.js":32525,"./se":59893,"./se.js":59893,"./si":33123,"./si.js":33123,"./sk":59635,"./sk.js":59635,"./sl":78106,"./sl.js":78106,"./sq":88799,"./sq.js":88799,"./sr":97949,"./sr-cyrl":52872,"./sr-cyrl.js":52872,"./sr.js":97949,"./ss":86167,"./ss.js":86167,"./sv":39713,"./sv.js":39713,"./sw":41982,"./sw.js":41982,"./ta":22732,"./ta.js":22732,"./te":43636,"./te.js":43636,"./tet":2115,"./tet.js":2115,"./tg":69801,"./tg.js":69801,"./th":2868,"./th.js":2868,"./tk":31310,"./tk.js":31310,"./tl-ph":22360,"./tl-ph.js":22360,"./tlh":66645,"./tlh.js":66645,"./tr":98374,"./tr.js":98374,"./tzl":256,"./tzl.js":256,"./tzm":61595,"./tzm-latn":61631,"./tzm-latn.js":61631,"./tzm.js":61595,"./ug-cn":6050,"./ug-cn.js":6050,"./uk":65610,"./uk.js":65610,"./ur":86077,"./ur.js":86077,"./uz":22862,"./uz-latn":12207,"./uz-latn.js":12207,"./uz.js":22862,"./vi":48093,"./vi.js":48093,"./x-pseudo":25590,"./x-pseudo.js":25590,"./yo":9058,"./yo.js":9058,"./zh-cn":77908,"./zh-cn.js":77908,"./zh-hk":8867,"./zh-hk.js":8867,"./zh-mo":31133,"./zh-mo.js":31133,"./zh-tw":83291,"./zh-tw.js":83291};function e(p){var d=v(p);return r(d)}function v(p){if(!r.o(m,p)){var d=new Error("Cannot find module '"+p+"'");throw d.code="MODULE_NOT_FOUND",d}return m[p]}e.keys=function(){return Object.keys(m)},e.resolve=v,k.exports=e,e.id=46700}}]);
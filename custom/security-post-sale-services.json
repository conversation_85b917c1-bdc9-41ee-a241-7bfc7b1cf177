{"NewComplaint": ["GET /complaints/complaint/userTeams", "GET /complaints/housemaid/", "GET /staffmgmt/housemaid/activecontract/", "GET /complaints/client/", "GET /staffmgmt/client/status/client", "GET /complaints/housemaid/clients/", "GET /complaints/housemaid/active/page", "GET /complaints/client/housemaids/", "GET /staffmgmt/matchingTypeNew/getMatchingTypesItems", "GET /complaints/category/list/withTypes", "GET /complaints/team/list", "GET /complaints/complaint/subComplaints/", "GET /complaints/complaint/page/client/", "GET /complaints/voice-resolver/has-to-dos/", "POST /complaints/complaint/create", "POST /staffmgmt/resolverToDo/complete/*/HUMAN_SMS_TODO_FOR_GOOGLE_REVIEW", "GET /complaints/client/getClientFilters/", "GET /complaints/complaint/contracts/", "GET /public/parameter", "GET /complaints/contract/getScheduledForTerminationContractsInfo/*", "GET /complaints/complaint/extractTextFromRecord"], "MaidToDiscipline": ["GET /complaints/complaint/page/maidsToDiscipline", "GET /complaints/complaint/page/clientsReasonability", "GET /complaints/category/list/withTypes", "POST /complaints/complaint/decision/"], "ClientReasonability": ["GET /complaints/complaint/page/maidsToDiscipline", "GET /complaints/complaint/page/clientsReasonability", "GET /complaints/category/list/withTypes", "POST /complaints/complaint/decision/"], "CategoryManagement": ["GET /complaints/category/list", "POST /complaints/category/create", "POST /complaints/category/update", "DELETE /complaints/category/delete/"], "TypeManagement": ["GET /complaints/type/page*", "GET /complaints/category/list", "POST /complaints/type/create", "POST /complaints/type/update", "DELETE /complaints/type/delete/", "GET /admin/tag/list", "GET /complaints/type/getTags/", "POST /complaints/type/deleteTags/", "POST /complaints/type/addTags/", "GET /admin/tag/page.*", "GET /complaints/type/addTag/", "POST /complaints/type/addTag/", "GET /public/picklist/items/TypeOfComplaintType"], "ComplaintFollowup": ["GET /complaints/complaint/", "POST /complaints/complaint/update", "GET /complaints/complaint/page/client/", "GET /complaints/complaint/page/housemaid/", "GET /complaints/team/list", "GET /complaints/category/list/withTypes", "POST /clientmgmt/contract/complaintPaymentsInfo/", "POST /complaints/complaint/addNote/", "POST /complaints/complaint/update"], "ClientServicesDashboard": ["GET /complaints/team/list", "GET /complaints/complaint/page/createdByMyTeam", "GET /complaints/complaint/page/createdByOtherTeam", "POST /complaints/complaint/update/"], "AddReplacement": ["GET /complaints/replacement/availableMaids", "GET /complaints/type/getenabledisreplacementtype", "GET /accounting/contractpaymentterm/getswitchmaidinfo", "GET /complaints/replacement/", "GET /sales/interviewvisit/", "GET /clientmgmt/contract/", "GET /complaints/housemaid/", "POST /complaints/replacement/update", "POST /complaints/replacement/create", "POST /complaints/replacement/transferToDelighters", "POST /clientmgmt/contract/fixAdjenddateForBulk", "GET /complaints/complaint/page/clientAndHousemaid/"], "EditReplacement": ["GET /complaints/replacement/availableMaids", "GET /complaints/type/getenabledisreplacementtype", "GET /accounting/contractpaymentterm/getswitchmaidinfo*", "GET /complaints/replacement/", "GET /sales/interviewvisit/", "GET /clientmgmt/contract/", "GET /complaints/housemaid/", "POST /complaints/replacement/update", "POST /complaints/replacement/create", "POST /complaints/replacement/transferToDelighters", "POST /clientmgmt/contract/fixAdjenddateForBulk", "GET /complaints/complaint/page/clientAndHousemaid/"], "ViewReplacement": ["GET /complaints/replacement/"], "ClientsNeedReplacement": ["GET /complaints/client/needReplacement", "GET /sales/client/getActiveContracts/", "POST /sales/clientagepreference/addclientagepreferences*", "POST /sales/matchingtype/addclientmatchingtypes*", "DELETE /sales/matchingtype/delete/", "DELETE /sales/clientagepreference/delete/", "POST /complaints/complaint/dismissPrepareForReplacement/", "POST /sales/clientremark/create", "GET /sales/clientremark/getclientremarks/", "POST /sales/client/vediourlissent/", "GET /public/picklist/items/"], "ReservationList": ["GET /complaints/reservation/allReservations", "POST /complaints/reservation/cancel/"], "ComplaintsArchive": ["GET /complaints/team/page/", "GET /complaints/team/page", "POST /complaints/complaint/archived", "POST /complaints/complaint/archived/", "POST /complaints/reservation/cancel/", "POST /complaints/reservation/cancel"], "teams-mgmt": ["POST /complaints/team/create", "POST /complaints/team/update", "DELETE /complaints/team/delete/", "GET /complaints/team/page/", "GET /complaints/team/members/", "POST /complaints/team/addMembers/", "DELETE /complaints/team/deleteMember/", "GET /public/user/page", "GET /complaints/team/page", "POST /complaints/team/create", "POST /complaints/team/update", "DELETE /complaints/team/delete/", "GET /complaints/team/getTags", "POST /complaints/team/deleteTags/", "GET /admin/team/page", "POST /complaints/team/addTag/", "GET /complaints/team/addTag/", "POST /complaints/team/addTags/", "GET /admin/tag/page.*"], "ReplacementHostComplaint": ["GET /complaints/housemaid/", "GET /complaints/complaint/page/clientAndHousemaid/", "GET /complaints/type/getenabledisreplacementtype", "POST /complaints/complaint/create"], "post-sale-services_ComplaintsDashboard": ["POST /complaints/complaint/complaintsScreen", "GET /complaints/team/getAllManagedByMe/", "GET /complaints/category/list/withTypes", "GET /complaints/complaint/userTeams", "POST /complaints/complaint/urgent/", "POST /complaints/complaint/unAssign/", "POST /complaints/complaint/assignToUserPrivateMethod.*", "GET /complaints/teamComplaintUpdate/historyOfComplaint/", "#staffmgmt#StaffMgmtHousemaidMobileNumberPopup", "#sales#salesClientMobileNumberPopup", "GET /staffmgmt/housemaid/getHousemaidNumbers/", "GET /sales/mobilenumber/getclientnumbers/"], "post-sale-services_Open_Complaint": ["GET /clientmgmt/voiceResolverToDo/", "GET /complaints/complaint/reopen/", "POST /complaints/complaint/transferTo/", "GET /complaints/team/list", "GET /complaints/complaint/", "GET /complaints/housemaid/active/page", "GET /complaints/client/housemaids/", "POST /complaints/complaint/update", "GET /complaints/teamComplaintUpdate/historyOfComplaint/", "POST /complaints/teamComplaintUpdate/create", "GET /complaints/complaint/userTeams", "POST /clientmgmt/contractTerminationNew/fireClientAndTerminateContract", "POST /clientmgmt/client/oneMoreChance/", "GET /clientmgmt/ccapp/sendTerminationNotifications/", "GET /complaints/complaint/relatedComplaints/", "GET /complaints/complaint/getClientInfo/", "GET /complaints/replacement/faultyReplacments/", "GET /complaints/replacement/page/contract/", "GET /complaints/category/list/withTypes", "POST /clientmgmt/voiceResolverToDo/system-generated-follow-update/", "POST /clientmgmt/voiceResolverToDo/manual-follow-update/", "POST /clientmgmt/voiceResolverToDo/client-did-not-answer/", "POST /complaints/complaint/proceed-with-cancellation/", "GET /clientmgmt/ccapp/unblockClient/", "GET /complaints/voice-resolver/has-to-dos/", "POST /clientmgmt/voiceResolverToDo/assigneeInteracted/", "POST /complaints/complaint/refreshAssignmentDate/", "GET /clientmgmt/client/", "GET /clientmgmt/voiceResolverToDo/getteammembers/", "GET /clientmgmt/voiceResolverToDo/getteamid", "GET /public/parameter", "POST /clientmgmt/voiceResolverToDo/log-action/", "POST /clientmgmt/client/get-client-details/", "GET /payroll/housemaid/getSalaryAndLoanAmount/", "GET /public/parameter", "GET /complaints/contract/getScheduledForTerminationContractsInfo/*", "GET /clientmgmt/voiceResolverToDo/get-follow-update/", "GET /complaints/complaint/extractTextFromCall/*", "GET /staffmgmt/mediatorSession/getBookedSessionsByClient/.*", "POST /staffmgmt/mediatorSession/cancelBookedSessionsByClient", "GET /staffmgmt/housemaid/getHousemaidNumbers/", "GET /sales/mobilenumber/getclientnumbers/", "GET /complaints/complaint/extractTextFromRecord", "GET /staffmgmt/mediatorSession/getMediatorVisitDetails/.*", "GET /complaints/checkSavedListenerSessions/", "GET /complaints/type/getTags/", "GET /complaints/complaint/getComplaintSnoozingHistory/", "GET /complaints/complaint/getComplaintTransferringHistory/"], "post-sale-services_HumanSMS": ["GET /staffmgmt/workflowTasks/getSearchbleFields*", "GET /staffmgmt/resolverToDo/nextStage/", "POST /staffmgmt/resolverToDo/complete/*/postpone_mv_agreement", "GET /sales/contract/processmaidvisapostponedcontract", "GET /staffmgmt/resolverToDo/messagesHistory/", "GET /staffmgmt/resolverToDo/resendMsg", "GET /staffmgmt/resolverToDo/resendEmail", "POST /staffmgmt/client/update", "GET /staffmgmt/workflowTasks/lazyLoad", "POST /staffmgmt/workflowTasks/lazyLoad", "GET /staffmgmt/workflowTasks/getSearchbleFields", "GET /complaints/workflowTasks/all", "POST /complaints/workflowTasks/all", "GET /staffmgmt/workflowTasks/all", "POST /staffmgmt/workflowTasks/all", "#staffmgmt#StaffMgmtHousemaidMobileNumberPopup", "#sales#salesClientMobileNumberPopup"], "post-sale-services_human-sms-details": ["GET /staffmgmt/resolverToDo/actionLog/"], "post-sale-services_ExpertDetails": ["GET /complaints/survey/getExperts", "GET /complaints/survey/getExpertDetailedRatings"], "post-sale-services_ExpertsRating": ["GET /complaints/survey/getExpertRatings"], "post-sale-services_predictReplacements": ["GET /complaints/predictedReplacement/filterPredictedReplacements", "GET /complaints/predictedReplacement/filterFinishedPrediction", "GET /complaints/predictedReplacement/predictOne/*", "GET /complaints/client/housemaids/", "GET /complaints/housemaid/clients/", "GET /complaints/client/hasActiveContract/page", "GET /complaints/housemaid/hasActiveContract/page", "GET /complaints/client/housemaids/activeContract/", "GET /complaints/predictedReplacement/getAccuracy"], "post-sale-services_vacationFlow": ["GET /complaints/vacationFlowManagement/list", "GET /complaints/vacationFlowManagement/picklist/items/nationalities", "POST /complaints/vacationFlowManagement/create", "POST /complaints/vacationFlowManagement/update", "DELETE /complaints/vacationFlowManagement/delete/"], "post_sale-service_maid_profile_todos": ["GET /complaints/complaint/getFixingMaidProfileTodo/*", "POST /complaints/complaint/resolveHousemaidFixingProfileTodos"], "n8nIntegration": ["POST /complaints/complaint/createHousemaidComplaintForGPT"]}
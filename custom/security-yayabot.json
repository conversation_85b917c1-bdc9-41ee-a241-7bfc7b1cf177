{"yayabot_FAQs": ["GET /yayabot/faq/searchquestions", "post /yayabot/faq/update", "post /yayabot/faq/create", "Delete /yayabot/faq/delete/"], "yayabotClinicForm": ["GET /yayabot/clinic/", "POST /yayabot/clinic/update", "POST /yayabot/clinic/create"], "yayabotClinics": ["GET /yayabot/clinic/page/", "Delete /yayabot/clinic/delete/"], "yayabotPsidMgmt": ["GET /yayabot/housemaid/page/", "GET /yayabot/psid/getpsids", "GET /yayabot/psid/changemaid/"], "YayaBotBroadcast": ["GET /yayabot/broadcaster/getAllBroadcast", "Delete /yayabot/broadcaster/delete/", "GET /yayabot/broadcaster/stopBroadcast/", "GET /yayabot/broadcaster/startBroadcast/"], "YayaBotBroadcastDetails": ["GET /yayabot/broadcaster/getBroadcastData/", "GET /yayabot/broadcaster/getBroadcastSendingDetails/", "GET /yayabot/broadcaster/enableUpdateDeliveryStats/", "GET /yayabot/broadcaster/updateDeliveryStats/"], "YayaBotBroadcastNewBroadcast": ["GET /staffmgmt/Room/findByRoomNumber", "GET /yayabot/audienceGroup/getAllAudienceGroup", "GET /yayabot/broadcaster/getBroadcastData/", "POST /yayabot/broadcaster/update", "POST /yayabot/broadcaster/createBroadcast", "GET /yayabot/broadcaster/getMessageParameters", "GET /yayabot/broadcaster/getMaidsWithPhoto", "GET /public/parameter.*", "GET /public/picklist/items/.*", "GET /public/download/.*", "GET /admin/metaBusinessAccount/templates/", "GET /admin/metaBusinessAccount/templates/numbers/", "POST /yayabot/broadcaster/getCorrectedText", "POST /yayabot/broadcaster/recipientsCount", "GET /yayabot/templates/*"], "YayaBotBroadcastEditBroadcast": ["GET /staffmgmt/Room/findByRoomNumber", "GET /yayabot/audienceGroup/getAllAudienceGroup", "GET /yayabot/broadcaster/getBroadcastData/", "POST /yayabot/broadcaster/update", "POST /yayabot/broadcaster/createBroadcast", "GET /yayabot/broadcaster/getMessageParameters", "GET /yayabot/broadcaster/getMaidsWithPhoto", "GET /public/parameter.*", "GET /public/picklist/items/.*", "GET /public/download/.*", "GET /admin/metaBusinessAccount/templates/", "GET /admin/metaBusinessAccount/templates/numbers/"], "YayaBotAudienceGroups": ["GET /yayabot/audienceGroup/getAllAudienceGroup", "POST /yayabot/audienceGroup/update"], "YayaBotAudienceGroupsForm": ["GET /staffmgmt/Room/findByRoomNumber", "GET /yayabot/audienceGroup/", "POST /yayabot/audienceGroup/update", "POST /yayabot/audienceGroup/create"]}
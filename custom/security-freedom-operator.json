{"freedomop_FreedomOperatorForm": ["GET /accounting/expenses/page/searchExpenses", "POST /freedomop/freedomOperatorPolicy/create", "GET /freedomop/freedomOperatorPolicy/changedisablestatus", "GET /freedomop/freedomOperatorNew/", "POST /freedomop/freedomOperatorNew/update", "POST /freedomop/freedomOperatorNew/create", "GET /public/picklist/items/.*", "GET /admin/picklist/items/.*"], "freedomop_ManageFreedomOperators": ["POST /freedomop/freedomOperator/search", "Delete /freedomop/freedomOperator/delete/", "POST /freedomop/freedomOrder/holdVisa", "POST /freedomop/freedomOrder/releaseVisa", "GET /public/picklist/items/nationalities", "GET /admin/picklist/items/nationalities", "GET /freedomop/maidat-operators/getAll", "Delete /freedomop/maidat-operators/delete/", "POST /freedomop/maidat-operators/update", "POST /freedomop/maidat-operators/create", "GET /freedomop/assign-travel-assist-rules/getAllByOrder", "POST /freedomop/assign-travel-assist-rules/create", "POST /freedomop/assign-travel-assist-rules/update", "POST /freedomop/assign-travel-assist-rules/saveRulesOrders", "DELETE /freedomop/assign-travel-assist-rules/delete"], "freedomop_GenerateOperatorInvoice": ["GET /freedomop/invoice/generateInvoice/", "GET /freedomop/freedomOperator/searchName", "GET /freedomop/invoice/getSOA/", "GET /public/download/.*"], "freedomop_StatementOfAccount": ["GET /freedomop/freedomOperator/.*", "GET /freedomop/freedomOperator/searchName", "GET /freedomop/freedomOperatorNew/getSummarySOA/", "GET /accounting/expenses/", "GET /accounting/expenses/.*", "POST /freedomop/soaManualRecord/create", "GET /freedomop/freedomOperatorNew/sendToOperator/", "GET /public/parameter.*", "GET /public/download/.*", "GET /freedomop/freedomop/freedomOperator/get-freedom-uuid"], "freedomop_FreedomOperatorNotes": ["GET /freedomop/freedomOperator/searchName", "POST /freedomop/freedomOperatorNote/searchOperatorNote", "Delete /freedomop/freedomOperatorNote/delete/"], "freedomop_FreedomOperatorUsers": ["GET /freedomop/freedomOperator/searchName", "GET /freedomop/freedomOperator/", "POST /freedomop/freedomOperatorUser/searchUsers", "GET /freedomop/freedomOperatorUser/enableUser/", "GET /freedomop/freedomOperatorUser/disableUser/", "POST /freedomop/freedomOperatorUser/create", "POST /freedomop/freedomOperatorUser/update", "DELETE /freedomop/freedomOperatorUser/delete/"], "freedomop_OperatorNoteForm": ["GET /freedomop/freedomOperator/searchName", "GET /freedomop/freedomOperatorNote/", "POST /freedomop/freedomOperatorNote/update", "POST /freedomop/freedomOperatorNote/create", "POST /freedomop/freedomOperator/getFOPayments/", "GET /freedomop/freedomOperator/exportFOPaymentsToExcel/", "GET /freedomop/freedomOperator/.*"], "freedomop_ApplicantForm": ["GET /freedomop/applicant/getOperatorRemainingOrdersCount", "GET /freedomop/freedomOperatorUser/getUserByGmail", "GET /freedomop/applicantToDo/.*", "GET /freedomop/applicant/logForApplicant/.*", "GET /freedomop/applicant/.*", "POST /freedomop/applicant/editApplicant/.*", "POST /freedomop/applicant/create/.*", "POST /freedomop/applicant/uploadSignedOfferLetter/.*", "POST /freedomop/applicant/addOtherDocumentsForApplicant/.*", "GET /freedomop/applicant/setTicketdate/.*", "GET /freedomop/applicant/completeSetTicketDateToDo/", "GET /public/picklist/items/locations", "GET /public/download/.*", "GET /admin/picklistItem/getTags/.*"], "freedomop_ApplicantsLogs": [], "freedomop_ManageOperatorGroups": ["GET /freedomop/freedomOperatorGroup/searchOperatorGroup", "GET /freedomop/freedomOperator/searchName", "GET /freedomop/freedomOperatorGroup/searchNameFreedom", "POST /freedomop/freedomOperatorGroup/editFreedomOperatorGroup", "POST /freedomop/freedomOperatorGroup/create"], "freedomop_reservoirs": ["GET /freedomop/reservoirs/getReservoirList", "POST /freedomop/reservoirs/editReservoir", "POST /freedomop/reservoirs/create", "Delete /freedomop/reservoirs/cancelReservoir/", "GET /public/picklist/items/nationalities", "GET /admin/picklist/items/nationalities"], "freedomop_operatorsPercentage": ["GET /freedomop/freedomOperator/getOperatorsPercentage", "POST /freedomop/freedomOperator/updateOperatorsPercentage"], "freedomop_ManagerDailyWorkOrder": ["GET /freedomop/freedomOperator/getDailyWorkList", "GET /freedomop/operatorRequest/getOperatorRequestAddingDetail", "GET /freedomop/operatorWarning/remindMeLater/", "GET /freedomop/applicant/cancelApplicant/", "GET /freedomop/freedomOperator/proceedOfferLetter/", "POST /freedomop/operatorRequest/createBulkOperatorRequest/", "GET /freedomop/freedomOperator/getOperatorsByNationality/", "GET /freedomop/maidVisaRequest/assignToOperator/", "GET /freedomop/freedomOperator/searchName", "GET /freedomop/applicant/getApplicantsForCancellation", "POST /freedomop/applicant/getExpenseAmountForCancelledApplicants", "POST /freedomop/applicant/cancelApplicants", "GET /public/picklist/items/", "GET /admin/picklist/items/nationalities/"], "freedomop_FOP_Dashboard": ["GET /freedomop/freedomOperatorUser/getUserByGmail", "GET /freedomop/freedomOperator/getFopDashboard", "GET /freedomop/portalUserToDo/getPortalUserToDos", "GET /freedomop/portalUserToDo/uploadSignedOfferLetter/", "GET /visa/customToDo/documentsOfOEC/", "GET /staffmgmt/extradocs/certificateofemployment/", "POST /freedomop/portalUserToDo/oldVisaCancelled/", "POST /freedomop/portalUserToDo/completeInformMaidTodos", "GET /freedomop/portalUserToDo/OECRequestReceived/", "GET /freedomop/freedomOperator/getMaidsWithoutCompleteDocuments/", "GET /freedomop/freedomOperator/getMaidsDetails/.*", "GET /freedomop/freedomOperator/getTerminatedMaids/", "GET /freedomop/freedomOperator/getPaymentsHistory", "GET /freedomop/freedomOperator/getFopMaidsForCancellation", "POST /freedomop/freedomOperator/getFopMaidsExpensesAmount", "POST /freedomop/freedomOperator/cancelSelectedRequest/", "GET /freedomop/portalUserToDo/updateTicketDateDetails", "POST /freedomop/portalUserToDo/updateTicketDate", "GET /freedomop/freedomOperator/getBalanceDetails", "GET /public/download/", "GET /freedomop/freedomOperator/get-freedom-uuid", "GET /freedomop/freedomOperator/searchName", "GET /freedomop/freedomOperator/.*", "POST /freedomop/actions/perform-action", "GET /freedomop/steps/get-maids-by-step", "GET /freedomop/steps/get-steps-by-operator-filters", "GET /freedomop/steps/fetch-all-steps-in-flow", "GET /freedomop/steps/get-steps-by-operator", "GET /admin/picklist/items/.*", "GET /visa/newRequest/tasks/.*", "GET /freedomop/freedomOperator/getOperatorsByNationalityMaid/.*", "POST /freedomop/actions/validate-action"], "freedomop_BringEmployeeToDubai": ["GET /freedomop/maidVisaRequest/getEmployeeDetail/", "GET /freedomop/maidVisaRequest/bringEmployeeToDubai/", "GET /freedomop/portalUserToDo/getFailedMedicalToDoDetails/", "POST /freedomop/portalUserToDo/completeFailedMedicalToDo", "GET /public/download/.*"], "freedomop_setMaidInfoTicketDate": ["GET /freedomop/portalUserToDo/getFailedMedicalToDoDetails/", "POST /freedomop/portalUserToDo/completeFailedMedicalToDo", "GET /public/download/.*"], "freedomop_SetMaidsTicketDate": ["POST /freedomop/portalUserToDo/setTicketDateDetails/", "POST /freedomop/portalUserToDo/setTicketDateForMaids/", "GET /public/download/.*"], "freedomop_DocumentsNeeded": ["GET /freedomop/freedomOperatorUser/getUserByGmail", "GET /freedomop/freedomOperator/searchName", "GET /freedomop/portalUserToDo/getNeededDocuments/", "GET /freedomop/portalUserToDo/uploadNeededDocument/"], "freedomop_agency-fees-setup": ["GET /freedomop/agencyFee/page", "GET /freedomop/agencyFee/countries", "POST /freedomop/'agencyFee/updateAgencyFee", "POST /freedomop/'agencyFee/create", "DELETE /freedomop/agencyFee/delete/", "GET /freedomop/agencyFee/getOtherCountriesFees", "POST /freedomop/agencyFee/setOtherCountriesFees/"], "freedomop__public_soa_page": ["GET /freedomop/public/meta.*", "GET /public/download", "GET /public/parameter", "GET /freedomop/freedomOperatorNew/getSummarySOA/.*", "GET /accounting/expenses/.*"], "freedomop_OperatorCollectE-signature": ["GET /public/parameter", "GET /public/download", "POST /public/upload", "GET /freedomop/applicant/.*", "GET /visa/maidESignature/updateHousemaidSignature/.*", "GET /visa/maidESignature/updateHousemaidThumbprint/.*", "GET /visa/maidESignature/updateHousemaidSignatureAndThumbprint/.*", "GET /visa/housemaidfilestemplates.*", "GET freedomop/freedomOperator/get-freedom-uuid", "GET /freedomop/freedomOperator/get-freedom-uuid"], "freedomop_OperatorFlowsSetup": ["GET /freedomop/flows/search-by-name/.*", "GET /freedomop/flows/set-as-default/.*", "POST /freedomop/flows/create", "POST /freedomop/flows/update", "DELETE /freedomop/flows/delete/", "POST /freedomop/flows/assign-to-operator-normally", "POST /freedomop/steps/create-step", "POST /freedomop/steps/update", "DELETE /freedomop/steps/delete/", "POST /freedomop/steps/reorder-steps", "POST /freedomop/steps/migrate-maids-between", "POST /freedomop/steps/mapping-steps-and-assign-operator", "GET /freedomop/steps/fetch-all-steps-in-flow/.*", "GET /freedomop/steps/change-visible-to-operator/.*", "GET /freedomop/steps/get-possible-configurations/.*", "GET /freedomop/steps/get-checked-configurations", "GET /freedomop/steps/get-steps-by-operator/.*", "GET /freedomop/steps/get-maids-by-step/.*", "GET /freedomop/steps/get-steps-by-operator-filters/.*", "GET /freedomop/actions/remove/.*", "GET /freedomop/actions/change-visible-to-operator/.*", "GET /freedomop/actions/change-move-to-next-step/.*", "GET /freedomop/maidInfos/remove/.*", "GET /freedomop/maidInfos/change-visible-to-operator/.*", "GET /freedomop/freedomOperator/searchName/.*", "GET /public/parameter/.*"]}
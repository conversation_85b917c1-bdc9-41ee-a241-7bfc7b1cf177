<html>

<head>
    <script>
        function getQueryStringArray() {
            var assoc = [];
            var items = window.location.search.substring(1).split('&');
            for (var j = 0; j < items.length; j++) {
                var a = items[j].split('='); assoc[a[0]] = a[1];
            }
            return assoc;
        }

        //point at which you want to determine redirection
        var qs = getQueryStringArray();
        var url = '';
        if (qs.t !== 'undefined' && qs.t) {
            window.location.href = '../modules/sales/videos_page/videos_page.html#!/' + qs.t; //reroute
        }
    </script>
</head>

<body></body>

</html>
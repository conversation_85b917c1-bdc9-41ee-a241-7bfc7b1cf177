mainApp.service("magnaMainService", function ($interval, $timeout) {

    /**
     * init GUI
     */
    this.initGUI = function () {
        $.material.init();
    }
    let _this = this;
    /**
     * adds attributes to a specified element
     * @param {Object} attributes - the attributes that should be added where key is an attribute name and value is the attribute value
     * @param {jqueryObj | string} - html element
     */
    this.addAttributesToElement = function (attributes, element) {
        var elm = $(element);
        for (var key in attributes) {
            var val = attributes[key];
            if (key == 'class')
                elm.addClass(val);
            else if (key == "style" && typeof val == 'object')
                elm.css(val); // object {key: value}
            else
                elm.attr(key, val);
        }
    }

    /**
     * to show or hide the loading icon
     * can call its functions as the following (LoadingIcon.show() or LoadingIcon.hide())
     */
    this.LoadingIcon = new (function () {

        function getLoadingElement() {
            var loadingElm = $("#loading-icon");
            if (loadingElm.length == 0) {
                loadingElm = $(`
                    <div id="loading-icon" class="load-wrap collapse">
                        <div class="load-3">
                            <div class="line"></div>
                            <div class="line"></div>
                            <div class="line"></div>
                        </div>
                    </div>`).appendTo('body');
            }
            return loadingElm;
        }

        this.show = function () {
            getLoadingElement().show()
        }

        this.hide = function () {
            getLoadingElement().hide();
        }
    })();

    this.DialogBox = new (function () {

        var modalData_prevModal = 'magna-prev-modal';
        var modalData_nextModal = 'magna-next-modal';

        this.showErrorMsg = function (msg, options) {
            var opts = $.extend({
                type: 'danger',
                placement: {from: "top", align: "center"},
                animate: {
                    enter: 'animated fadeInDown',
                    exit: 'animated fadeOutUp'
                },
                newest_on_top: true,
                allow_dismiss: true,
                offset: 5,
                z_index: 1000000
            }, options ? options : {});
            return $.notify({
                icon: 'glyphicon glyphicon-warning-sign',
                title: '<strong>Error</strong>',
                message: msg
            }, opts);
        }

        this.showSuccessMsg = function (msg, options) {
            var opts = $.extend({
                type: 'success',
                placement: {from: "top", align: "center"},
                animate: {
                    enter: 'animated fadeInDown',
                    exit: 'animated fadeOutUp'
                },
                newest_on_top: true,
                allow_dismiss: true,
                offset: 5,
                delay: 1000,
                z_index: 1000000
            }, options ? options : {});
            return $.notify({
                icon: 'glyphicon glyphicon-ok',
                //title: '<strong>Error</strong>',
                message: msg
            }, opts);
        }

        this.showInfoMsg = function (msg, options) {
            var opts = $.extend({
                type: 'info',
                placement: {from: "top", align: "center"},
                animate: {
                    enter: 'animated fadeInDown',
                    exit: 'animated fadeOutUp'
                },
                newest_on_top: true,
                allow_dismiss: true,
                offset: 5,
                delay: 2500,
                z_index: 1000000
            }, options ? options : {});
            return $.notify({
                icon: 'glyphicon glyphicon-info-sign',
                //title: '<strong>Error</strong>',
                message: msg
            }, opts);
        }

        /**
         * displays warning dialog box
         * @param {string} msg - warning message
         * @param {okayCallback} onOK - the callback function that will be called when the warning is accepted
         * @param {cancelCallback} onCancel - the callback function that will be called when the warning is cancelled
         */
        this.showWarningMsg = function (msg, onOK, onCancel, options) {
            var opt = $.extend({
                okBtnLabel: "Ok",
                cancelBtnLabel: "Cancel",
                isOnlyOK: false
            }, options ? options : {});
            var warning_element = $("#warning-box");
            if (warning_element.length == 0) {
                warning_element = $(`<div id="warning-box" class="notif red" style="display: none;">
                        <div class="dark-back">
                            <div class="win8-notif-body">
                                <div class="mid">
                                    <h3>Warning</h3>
                                    <p></p>
                                    <button class="win8-notif-button confirmation-ok-btn">OK</button>
                                    <button class="win8-notif-button confirmation-cancel-btn">CANCEL</button>
                                </div>
                            </div>
                        </div>
                    </div>`).appendTo('body');
            }
            warning_element.find('.mid p').html(msg);
            let okBtnElm = warning_element.find('.confirmation-ok-btn');
            let cancelBtnElm = warning_element.find('.confirmation-cancel-btn');
            okBtnElm.text(opt.okBtnLabel).unbind("click");
            okBtnElm.click(function () {
                onOK();
                $(".notif").fadeOut(50);
            });
            if(opt.isOnlyOK)
                cancelBtnElm.fadeOut(0);
            else{
                cancelBtnElm.text(opt.cancelBtnLabel).unbind("click");
                cancelBtnElm.click(function () {
                    if (onCancel) {
                        onCancel();
                    }
                    $(".notif").fadeOut(50);
                });
            }
            warning_element.removeClass('red').addClass('red').fadeIn(50); // show the confirmation
        }

        /**
         * displays confirm dialog box
         * @param {string} msg -  message
         * @param {okayCallback} onOK - the callback function that will be called when the warning is accepted
         * @param okText
         * @param cancelText
         */
        this.showConfirmDialog = function (msg, onOK, okText, cancelText) {
            var warning_element = $("#warning-box");
            if (warning_element.length == 0) {
                warning_element = $(`<div id="warning-box" class="notif red" style="display: none;">
                    <div class="dark-back">
                    <div class="win8-notif-body">
                    <div class="mid">
                    <h3>Warning</h3>
                    <p></p>
                    <button class="win8-notif-button confirmation-ok-btn">${okText ? okText : 'Yes'}</button>
                    <button class="win8-notif-button">${cancelText ? cancelText : 'No'}</button>
                    </div>
                    </div>
                    </div>
                    </div>`).appendTo('body');
                warning_element.find('.win8-notif-button').click(function () {
                    $(".notif").fadeOut(50);
                });
            }
            warning_element.find('.mid p').html(msg);
            $('.confirmation-ok-btn').unbind("click");
            warning_element.find('.confirmation-ok-btn').click(function () {
                onOK();
                $(".notif").fadeOut(50);
            });
            warning_element.removeClass('red').addClass('red').fadeIn(50); // show the confirmation
        }

        /**
         * displays a specified modal
         * @param {jqueryObj | string} modalElm - the modal that should be displayed
         */
        this.showModal = function (modalElm,options=null) {
            var modal = $(modalElm);
            if (modal.data(modalData_prevModal) === undefined) {
                modal.on('hidden.bs.modal', function (e) {
                    var $this = $(this);
                    var nextModal = $this.data(modalData_nextModal);
                    if (nextModal) {
                        options ? nextModal.modal(options,'show') :nextModal.modal('show');
                        $this.data(modalData_nextModal, null);
                    } else if ($this.data(modalData_prevModal) && $.contains(window.document, document.body, $this.data(modalData_prevModal)[0])) {
                        options ?$this.data(modalData_prevModal).modal(options,'show') :  $this.data(modalData_prevModal).modal('show');
                        $this.data(modalData_prevModal, null);
                    }
                });
            }
            var preVisibleModal = $('.modal:visible:first');
            modal.data(modalData_prevModal, preVisibleModal.length > 0 ? preVisibleModal : null);
            if (preVisibleModal.length > 0) {
                preVisibleModal.data(modalData_nextModal, modal);
                preVisibleModal.modal('hide');
            } else
             options ? modal.modal(options,"show") :   modal.modal("show");
        }
    })();

    /**
     * manipulates main progress bar
     */
    this.MainProgressBar = new (function () {
        var mainProgressElm = $('#main_progress_bar .progress-bar');
        var mainProgressComplete = null;
        if (mainProgressElm.length > 0)
            mainProgressElm.get(0).addEventListener('webkitTransitionEnd', function (event) {
                if (mainProgressElm[0].style.width == "100%")
                    mainProgressElm.addClass('notransition').css('width', '0%');
                else {
                    $.when(mainProgressComplete.promise()).then(function () {
                        mainProgressElm.css('width', '100%');
                    });
                }
            }, false);

        /**
         * starts the main progress bar
         */
        this.start = function () {
            if (mainProgressElm.length == 0)
                return;
            mainProgressComplete = $.Deferred();
            mainProgressElm.hide().addClass('notransition').css('width', '0%');
            setTimeout(function () {
                mainProgressElm.show().removeClass('notransition').css('width', '30%');
            }, 5);
        }

        /**
         * completes the main progress bar
         */
        this.complete = function () {
            if (mainProgressElm.length == 0)
                return;
            mainProgressComplete.resolve();
        }
    })();

    /**
     * to register interval or timeout
     */
    this.Timing = new (function () {
        var timing = {};

        function getId(promise) {
            return promise.hasOwnProperty('$$intervalId') ? promise.$$intervalId : promise.$$timeoutId;
        }

        function cancelPromise(promise) {
            if (promise.hasOwnProperty('$$intervalId'))
                $interval.cancel(promise);
            else
                $timeout.cancel(promise);
            delete timing[getId(promise)];
        }

        function register(scope, promise) {
            if (scope.hasOwnProperty("__is_distroyed")) {
                cancelPromise(promise);
                return null;
            }
            timing[getId(promise)] = promise;
            if (!scope.hasOwnProperty("__clear_timing_func_on_destroy")) {
                scope.$on("$destroy", function () {
                    for (var key in timing) {
                        cancelPromise(timing[key]);
                    }
                    scope["__is_distroyed"] = true;
                });
                scope["__clear_timing_func_on_destroy"] = true;
            }
            return promise;
        }

        /**
         * cancel interval or timeout
         * @param {object} promise
         */
        this.cancel = function (promise) {
            cancelPromise(promise);
        }

        /**
         * create an interval
         * @param {scope} scope a scope object
         * @param {func} func a function that should be called repeatedly
         * @param {number} delay Number of milliseconds between each function call
         */
        this.interval = function (scope, func, delay) {
            return register(scope, $interval(func, delay));
        }

        /**
         * create a timeout
         * @param {scope} scope a scope object
         * @param {func} func a function that should be called repeatedly
         * @param {number} delay delay in milliseconds
         *
         */
        this.timeout = function (scope, func, delay) {
            var promise = register(scope, $timeout(function () {
                cancelPromise(promise);
                func();
            }, delay));
            return promise;
        }

    })();

    /**
     * stores data batween routes (prev and current)
     */
    this.RouteData = new (function () {

        var currentRouteData = {};
        var prevRouteData = {};

        /**
         * this is a technical function to copy data from the prev route to the current route
         */
        this.updateRoute = function () {
            prevRouteData = {};
            angular.copy(currentRouteData, prevRouteData);
            currentRouteData = {};
        }

        /**
         * stores data in the current route
         */
        this.storeData = function (key, value) {
            currentRouteData[key] = value;
            return value;
        }

        /**
         * deletes data from the current route
         */
        this.deleteData = function (key) {
            if (currentRouteData.hasOwnProperty(key)) {
                var value = currentRouteData[key];
                delete currentRouteData[key];
                return value;
            }
            return null;
        }

        /**
         * returns a stored object from the prev route
         */
        this.getStoredData = function (key) {
            if (prevRouteData.hasOwnProperty(key))
                return prevRouteData[key];
            return null;
        }


    })();

    this.previewFile = function (url, fileType, fileName) {
        if (fileType != "application/pdf" && !fileType.startsWith("image/"))
            return;
        var download_link = $('#generic-download-link');
        download_link.attr("href", url);
        download_link.attr("download", fileName);

        var file_viewer = $("#file_viewer_modal");
        if (typeof $(file_viewer).data("download_link") == 'undefined') {
            $(file_viewer).on('hidden.bs.modal', function () {
                var $this = $(this);
                var downloadLink = $this.data("download_link");
                if (downloadLink && downloadLink.startsWith("blob")) {
                    URL.revokeObjectURL(downloadLink);
                    $this.data("download_link", null);
                }
            });
        }
        file_viewer.data("download_link", url);
        file_viewer.find(".modal-title").text(fileName);
        if (fileType == "application/pdf") {
            file_viewer.find(".modal-body").html(`<iframe src="` + __env.baseUrl + `/plugins/pdfjs/web/viewer.html?file=` + url + `" width="100%" height="375" style='border: none;'></iframe>`);
        } else {
            file_viewer.find(".modal-body").html(`<img src="` + url + `" height="auto" style="max-width: 100%;"></img>`);
        }
        this.DialogBox.showModal(file_viewer);
    }

    this.html2pdf = function (element, opt = {}) {
        var _this = this;
        var options = $.extend({
            margin: 1,
            filename: 'PDF File',
            image: {type: 'jpeg', quality: 0.95},
            html2canvas: {dpi: 200, letterRendering: true},
            jsPDF: {unit: 'mm', format: 'a4', orientation: 'p'}
        }, opt);
        html2pdf().set(options).from(element).toPdf().output('blob').then(function (blob) {
            var url = URL.createObjectURL(blob);
            _this.previewFile(url, "application/pdf", options.filename);
        });
    }

    this.cropImage = function (imageEL, callBack, extraOptions = {}, cropperOptions = {}) {
        function rotateImage(deg) {
            cropObject.rotate(deg);
            var old_canv = cropObject.getCanvasData();
            cropObject.setCropBoxData({left: 0, top: 0, width: old_canv.width, height: old_canv.height});
        }

        function validateBrightnessAndBlur(eParams) {
            let valid = true;
            if ((!eParams.brightness && eParams.blur) || (eParams.brightness < 0 || eParams.brightness > 1000)) {
                $('#crop-brightness-errors').text((!eParams.brightness && eParams.blur) ? 'Brightness is required' : 'Brightness should be between 0 and 1000')
                $('#crop-blur-errors').text('');
                valid = false;
            }
            if ((!eParams.blur && eParams.brightness) || (eParams.blur < 0 || eParams.blur > 1000)) {
                $('#crop-blur-errors').text((!!eParams.blur && eParams.brightness) ? 'Blur is required' : 'Blur should be between 0 and 1000');
                $('#crop-brightness-errors').text('');
                valid = false;
            }
            return valid;
        }

        function flipImage(horizontal) {
            var old = cropObject.getData();
            if (horizontal) {
                cropObject.scaleX(-1 * old.scaleX);
            } else {
                cropObject.scaleY(-1 * old.scaleY);
            }
        }

        var ths = this;
        var cropObject = null;
        var cropModal = $('#crop-image-modal');
        var actionsContainer = cropModal.find('.buttons-container');
        var imageContainer = cropModal.find('.imageContainer');
        var cropBtn = cropModal.find('.crop-btn');
        var cOptions = $.extend({
            viewMode: 2,
            autoCropArea: 1,
            responsive: true,
            minContainerWidth: '500',
            minContainerHeight: '500',
        }, cropperOptions);
        var eOptions = $.extend({
            showRotate: false,
            showBrightnessAndBlur: false,
        }, extraOptions);
        actionsContainer.html('');
        cropBtn.get(0).removeEventListener('click', callBack, false);
        $("<img/>").on('load', function () {
            $(imageContainer).html(this);
            cropObject = new Cropper(this, cOptions);
            ths.DialogBox.showModal(cropModal);
        }).on('error', function () {
            console.error("error loading image");
        }).attr("src", $(imageEL).attr('src'));
        if (eOptions.showRotate) {
            actionsContainer.append($('<button><i class="material-icons">rotate_right</i> </button>').click(function () {
                rotateImage(90)
            }));
            actionsContainer.append($('<button><i class="material-icons">rotate_left</i> </button>').click(function () {
                rotateImage(-90)
            }));
            actionsContainer.append($('<button><i class="material-icons">swap_horiz</i> </button>').click(function () {
                flipImage(true)
            }));
            actionsContainer.append($('<button><i class="material-icons">swap_vert</i> </button>').click(function () {
                flipImage(false)
            }));
        }
        if (eOptions.showBrightnessAndBlur) {
            actionsContainer.append(`
            <form class="form-horizontal w3-padding-32-h">
            <div class="row">
                <div class="form-group">
                    <label class="col-md-2 control-label">Brightness:</label>
                    <div class="col-md-10">
                        <input id="crop-brightness" type="number" min="0" max="1000" class="form-control" onkeypress="return event.charCode >= 48 && event.charCode <= 57" >
                    </div>
                    <p class="text-danger" id="crop-brightness-errors" ></p>
                </div>
            </div>
            <div class="row w3-margin-bottom">
                <div class="form-group">
                    <label class="col-md-2 control-label">Blur:</label>
                    <div class="col-md-10">
                        <input id="crop-blur" type="number" min="0" max="1000" class="form-control" onkeypress="return event.charCode >= 48 && event.charCode <= 57" >
                    </div>
                    <p class="text-danger" id="crop-blur-errors" ></p>
                </div>
            </div>
            </form>
            `);
        }
        cropModal.on('hidden.bs.modal', function () {
            cropObject.destroy();
        });
        if (typeof callBack == "function") {
            cropBtn.get(0).addEventListener('click', function () {
                let eParams = {
                    brightness: parseInt($('#crop-brightness').val()),
                    blur: parseInt($('#crop-blur').val()),
                }
                if (eOptions.showBrightnessAndBlur && validateBrightnessAndBlur(eParams)) {
                    $('#crop-image-modal').modal('hide');
                    callBack(cropObject, eParams)
                } else if (!eOptions.showBrightnessAndBlur) {
                    $('#crop-image-modal').modal('hide');
                    callBack(cropObject, eParams)
                }
            }, false);
        }
    }

      /** edit image with filters and cropping tool 
     * @param fileObj the attachement file we want to modify
     * @param imageURL url for fetching the image to edit
     * @param uploadURL optional : used in case for uploading the changes after saving directly to backend
    */
       this.editImage = function(fileObj,imageURL,onSaveCallback){
        this.DialogBox.showModal($("#edit-image-modal"));
        const { TABS, TOOLS } = window.FilerobotImageEditor;
        let attachment='';
        const config = {
            source:imageURL,
            onSave: (editedImageObject, designState) =>{
                return onSaveCallback(editedImageObject,designState);
            },
            onBeforeSave:(imageFileInfo)=>{
                //this is used to prevent triggering default save modal 
                return false;
            },
            translations: {
                profile: 'Profile',
                coverPhoto: 'Cover photo',
                facebook: 'Facebook',
                socialMedia: 'Social Media',
                fbProfileSize: '180x180px',
                fbCoverPhotoSize: '820x312px',
            },
            Crop:{
                presetsItems:[
                    {
                        titleKey: 'classicTv',
                        descriptionKey: '4:3',
                        ratio: 4 / 3,
                      },
                      {
                        titleKey: 'cinemascope',
                        descriptionKey: '21:9',
                        ratio: 21 / 9,
                      },
                ],
                presetsFolders:[
                    {
                        titleKey: 'socialMedia', // will be translated into Social Media as backend contains this translation key
                        groups: [
                          {
                            titleKey: 'facebook',
                            items: [
                              {
                                titleKey: 'profile',
                                width: 180,
                                height: 180,
                                descriptionKey: 'fbProfileSize',
                              },
                              {
                                titleKey: 'coverPhoto',
                                width: 820,
                                height: 312,
                                descriptionKey: 'fbCoverPhotoSize',
                              },
                            ],
                          },
                        ],
                      },
                ],

            },
            tabsIds: [TABS.ADJUST, TABS.FINETUNE,TABS.FILTERS,TABS.RESIZE], // or ['Adjust', 'Annotate', 'Watermark']
            defaultTabId: TABS.ADJUST, 
            defaultToolId: TOOLS.CROP, 
            closeAfterSave:false
            
        };
        const filerobotImageEditor = new window.FilerobotImageEditor(
            document.querySelector('#imageContainer'),
            config,
        );
        filerobotImageEditor.render({
            onClose: (closingReason) => {
                $("#edit-image-modal").modal('hide');
              console.log('Closing reason', closingReason);
              filerobotImageEditor.terminate();
              return attachment;
            },
          });  
    }
     
});

mainApp.service('magnaSecurityService',function($http,magnaMainService){
    this.redirectToSecurityPage =  async function(devleoperMessage,customRedirectUrl=null){
        if(devleoperMessage=="UNAUTHENTICATED"){
            window.location.replace('login.html');
            return;
        }
        let devMessage = devleoperMessage;
        var req = {
            method: "GET",
             url: __env.PUBLIC + "parameter?code=SECURITY_PAGE_BASEURL"
        };
        magnaMainService.LoadingIcon.show();
       await $http(req,{
            headers:{
                "Access-Control-Allow-Origin":"*"
            }
        }).then( function ({data, status, headers, config}) {
             magnaMainService.LoadingIcon.hide();
             const baseUrl =data[0]?.value ?? '';
             const errorMap= {
                 "TOTP_NOT_ACTIVATED":"/register-totp",
                 "INVALID_TOTP":"/verify-totp",
               };
             const returnUrl = customRedirectUrl!=null ? customRedirectUrl : window.location.href
             if(errorMap[devleoperMessage]){
                 const encoded = baseUrl + "/auth"+errorMap[devMessage]+"?returnUrl="+encodeURIComponent(returnUrl);
                 const url = new URL(encoded);
                 window.location.replace(url)
             }
             
        }).catch(function (response) {
            magnaMainService.LoadingIcon.hide();
        });
    }
   
});

mainApp.service("magnaHttpService", function (magnaMainService, magnaAuthenticationService, magnaTestModeService, $http, $rootScope, $timeout, $route, $cacheFactory, __env,$q,magnaSecurityService) {
    let magnaHttpService = this; 
    let pausedRequestsPromises = {};

    var timeouts_count = 0;
    var requests_cache = $cacheFactory('REQUESTS_CACHE', {capacity: 500});

    this.normalizeURL = function (url) {
        return url.replace(/([^:]\/)\/+/g, "$1"); // remove duplicated slashes
    }

    /**
     * Examples:
     * getDataValue("5") returns 5
     * getDataValue("") returns null
     * getDataValue({id: 5}) returns {id: 5}
     * getDataValue({id: ""}) returns null
     * getDataValue({id: "10", code:""}, ["code"]) returns null
     * getDataValue([{id: ""}, {id: "10"}]) returns [{id: "10"}]
     * getDataValue([{id: "10", code:""}, {id: "13", code:""}], ["code"]) returns []
     * getDataValue({param1: {id: "10", code:"15"}, param2: {id: "13", code:""}}, ["code"]) returns {param1: {id: "10", code:"15"}, param2: null}
     */
    this.getDataValue = function (val, requiredAttrs = ["id"]) {
        if (val == null) return null;
        if (Array.isArray(val)) {
            var newArray = [];
            for (var i = 0; i < val.length; i++) {
                var elm = val[i];
                if (this.getDataValue(elm, requiredAttrs) !== null)
                    newArray.push(elm);
            }
            return newArray;
        } else if (typeof val == "object") {
            for (var i = 0; i < requiredAttrs.length; i++) {
                var elm = requiredAttrs[i];
                if (val.hasOwnProperty(elm) && this.getDataValue(val[elm], requiredAttrs) === null)
                    return null;
            }
            for (var key in val) // check all object's attributes
                val[key] = this.getDataValue(val[key], requiredAttrs);
            return val;
        } else {
            if (val && typeof val == "string")
                val = val.trim();
            if (val || val === false || val === 0)
                return val;
            return null;
        }

    }

    /**
     * wrap the http service
     * @param {Object} options - all the options for original http service
     * @param {Function} - callback function to be called when request success
     * @param {Object} another_options [OPTIONAL]
     * {
     *  needs_loading_icon:boolean => [OPTIONAL] default :false
     *  loading_show_delay: number in milliseconds  => [OPTIONAL] default :2000
     * }
     * EXAMPLE :
     *
     magnaHttpService.HttpWrapper(
     {
         method : "GET", // method (GET,POST,DELETE...)
         url : "https://reqres.in/api/users?page=2"
         ...
         Any other options you want to pass to original http service
     },
     function(response){
            alert('done'); // do your custom success callback
        },
     {
        needs_loading_icon:true,
        loading_show_delay:3000
     }
     );
     *
     */
     this.HttpWrapper = function (request_options, success_callback, other_options) {
        request_options.url = this.normalizeURL(request_options.url);
        var obj = $.extend({
            timeout: 4000000
        }, request_options);
        if (!obj.headers)
            obj.headers = {}
    
        //try to add Allow cross origin *
        obj.headers = {
            ...obj.headers,
            "Access-Control-Allow-Origin":"*"
        }
        //////////////////////
        var options = $.extend({
            needs_loading_icon: false,
            loading_show_delay: 0,
            ignore_error_messages: false,
            error_handler: null, //function (response) {} a callback function that will be invoked when the request is failed
            ignore_authorization: false,
            pauseRequestsWhenTabInactive:false
        }, typeof (other_options) == 'object' ? other_options : {});
    
      
        if (magnaTestModeService.getTestMode())
            magnaTestModeService.addRequestIdHeaders(obj.headers, request_options);
        // prevent send same requst simultaneously
        var request_cache_key = JSON.stringify(obj);
        if (requests_cache.get(request_cache_key))
            return;
        requests_cache.put(request_cache_key, true);
    
        // show loading icon
        var last_timeout = null;
        if (options.needs_loading_icon) {
            timeouts_count = timeouts_count + 1;
            last_timeout = $timeout(function () {
                magnaMainService.LoadingIcon.show();
            }, options.loading_show_delay);
        }
    
        // update data object if exist
        if (obj.hasOwnProperty("data") && typeof obj.data == 'object') {
            for (var key in obj.data) {
                obj.data[key] = this.getDataValue(obj.data[key]);
            }
        }
        let self = this;
        /**create a promise to be resolved by resumeRequests function
         * which is called when current tab is active again
         * @returns Promise
         */
        pauseOrCancelRequest = function(url) {
            let promise =  $q.defer();
            if(pausedRequestsPromises[url]){
               return null;
            }
            pausedRequestsPromises[url]=promise;
            return promise.promise;
        }

        function sendHttpRequest() {
            var config = angular.copy($http.defaults);
            if(options.ignore_authorization){
                config.withCredentials = false;
            }
            else{
                config.withCredentials = true;
            }
            $http(obj,config).then(
                function (response) {
                    success_callback(response.data, response);
                },
                (response) => {
                    if(response.data.status==401){
                        const devleoperMessage = response.headers('developerMessage');
                        if(devleoperMessage){
                            magnaSecurityService.redirectToSecurityPage(devleoperMessage);
                        }
                    }
                    if(response.data?.message=="Please use your own user"){ window.location.replace('login.html');}
                    if(response.data?.status==498){
                        if(!document.getElementById('refresh_token_modal').classList.contains('in')){
                            magnaMainService.DialogBox.showModal($('#refresh_token_modal'),{backdrop: 'static', keyboard: false});
                        }
                        //show dialog to refresh the token or logout the user
                    }else{
                        if (options.error_handler && typeof options.error_handler == "function")
                            options.error_handler(response);
                        if (!options.ignore_error_messages) {
                            self.showDefaultErrorMessage(response)
                        }
                    }
                }
            ).finally(function (response) {
                requests_cache.remove(request_cache_key);
                if (options.needs_loading_icon) {
                    $timeout.cancel(last_timeout);
                    timeouts_count--;
                    if (timeouts_count == 0) {
                        magnaMainService.LoadingIcon.hide();
                    }
                }
            });
        }
      // add autherization token
      if (!options.ignore_authorization){
        let self = this;
        let headers = magnaAuthenticationService.addAuthorizationHeaders(obj.headers);
        obj.headers ={
            ...obj.headers,
            ...headers,
        }

        // the browser should handle authorization header from now on , cause it's httpOnly cookie
       // magnaAuthenticationService.addAuthorizationHeaders($http,obj.headers);

      }
      function sendOrHaltRequest(){
        let _this = this;
        if(document.hidden){
            //pauseOrCancle will return a promise or null to indicate canceling the request
            let pausedRequest =  pauseOrCancelRequest(request_options.url);
            if(pausedRequest){
                pausedRequest.then(()=>{
                    sendHttpRequest();
                })
            }

        }
        else{
            sendHttpRequest();
        }
    }
    if(options.pauseRequestsWhenTabInactive){
        sendOrHaltRequest();
    }
    else{
        sendHttpRequest();
    }
   
    };


    this.showDefaultErrorMessage = function (response) {
        switch (response.status) {
            case -1:
                magnaMainService.DialogBox.showErrorMsg('Unknown error, check your connection and the server status');
                break;
            case 400:
                magnaMainService.DialogBox.showErrorMsg(response.config.responseType && response.config.responseType == "arraybuffer" ? String.fromCharCode.apply(String, new Uint8Array(response.data)) : (response.data && response.data.message ? response.data.message : response.data));
                break;
            case 401:
                magnaMainService.DialogBox.showErrorMsg('Unauthorized');
                break;
            case 403:
                magnaMainService.DialogBox.showErrorMsg('Forbidden');
                break;
            case 404:
                magnaMainService.DialogBox.showErrorMsg('The page not found');
                break;
            case 500:
                magnaMainService.DialogBox.showErrorMsg(response.config.responseType && response.config.responseType == "arraybuffer" ? String.fromCharCode.apply(String, new Uint8Array(response.data)) : response.data.message);
                break;
            default:
                magnaMainService.DialogBox.showErrorMsg('Unknown error');
                break;
        }
    }

    this.downloadFile = function (link, options = {}, other_options = {}) {
        var opt = $.extend({
            method: "GET",
            responseType: "arraybuffer",
            url: link,
            timeout: 300000
        }, options);
        // show notification
        var notify = $.notify({
            message: "Downloading the file...",
            icon: "glyphicon glyphicon-download-alt"
        }, {
            autoHide: false,
            placement: {from: "top", align: "center"},
            animate: {
                enter: 'animated fadeInDown',
                exit: 'animated fadeOutUp'
            },
            newest_on_top: true,
            offset: 5,
            z_index: 1000000,
            delay: 0,
            type: "info"
        });
        other_options.error_handler = function () {
            notify.close();
        };
        other_options.needs_loading_icon = false;
        let previewFile = true;
        if (other_options.hasOwnProperty("preview_file")) {
            previewFile = other_options.preview_file;
            delete other_options["preview_file"];
        }

        this.HttpWrapper(opt, function (response, responseObj) {
            var filename = "";
            var disposition = responseObj.headers('Content-Disposition');
            if (disposition && disposition.indexOf('attachment') !== -1) {
                var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                var matches = filenameRegex.exec(disposition);
                if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
            }
            var type = responseObj.headers('Content-Type');
            var blob = typeof File === 'function' && (!document.documentMode && !/Edge/.test(navigator.userAgent) /*not supported from Edge*/) ? new File([response], filename, {type: type}) : new Blob([response], {type: type});
            var URL = window.URL || window.webkitURL;
            var downloadUrl = URL.createObjectURL(blob);
            var download_link = null;
            if (filename) {
                download_link = $('#generic-download-link');
                download_link.attr("href", downloadUrl);
                download_link.attr("download", filename);
            }
            if (previewFile && type && (type == "application/pdf" || type.startsWith("image/"))) {
                magnaMainService.previewFile(downloadUrl, type, filename);
            } else if (download_link && typeof download_link[0].download !== 'undefined') {// safari doesn't support this yet
                download_link[0].click();
            } else {
                window.location = downloadUrl;
            }
            notify.close();
        }, other_options);
    }

    this.getImage = function (link, imgElm, callback = null) {
        this.HttpWrapper({
            method: "GET",
            responseType: "arraybuffer",
            url: link,
            timeout: 300000
        }, function (response, responseObj) {
            var filename = "";
            var disposition = responseObj.headers('Content-Disposition');
            if (disposition && disposition.indexOf('attachment') !== -1) {
                var filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
                var matches = filenameRegex.exec(disposition);
                if (matches != null && matches[1]) filename = matches[1].replace(/['"]/g, '');
            }
            if (filename) {
                var type = responseObj.headers('Content-Type');
                var blob = typeof File === 'function' ? new File([response], filename, {type: type}) : new Blob([response], {type: type});
                var URL = window.URL || window.webkitURL;
                var downloadUrl = URL.createObjectURL(blob);
                $(imgElm).attr('src', downloadUrl);
                $timeout(function () {
                    URL.revokeObjectURL(downloadUrl);
                }, 100); // cleanup
            }
            if (callback && typeof callback == "function") {
                callback();
            }
        });
    }


    this.resumeRequests = function() {
        for(let url of Object.keys(pausedRequestsPromises) ){
            pausedRequestsPromises[url].resolve();
        }

        pausedRequestsPromises = {};
    }

});

// async function registerFirebaseSW() {
//     const messaging = firebase.messaging();

//     // Check if service workers are supported
//     if ('serviceWorker' in navigator) {
//         try {
//             const registrations = await navigator.serviceWorker.getRegistrations();

//             const olderServiceWorker = registrations.find((registration) => {
//                 return registration.active && registration.active.scriptURL == "https://www.teljoy.io/firebase-messaging-sw.js";
//             });

//             if (olderServiceWorker) {
//                 olderServiceWorker.unregister();
//             }

//             // Find the registration that matches the firebase-messaging-sw.js script
//             const firebaseServiceWorker = registrations.find((registration) => {
//                 return registration.active && registration.active.scriptURL.includes((new URL(__env.baseUrl)).pathname +'firebase-messaging-sw.js');
//             });
//             // Register the firebase-messaging-sw.js service worker if not already registered
//             if (!firebaseServiceWorker) {
//                 let SWscope =  new URL('.', window.location.href).pathname;
//                 let scriptUrl = './firebase-messaging-sw.js';
//                // Create a promise for service worker registration
//                const registrationPromise = new Promise((resolve, reject) => {
//                 navigator.serviceWorker.register(scriptUrl, { scope: SWscope })
//                     .then((registration) => {
//                         resolve(registration);
//                      })
//                     .catch((error) => {
//                         reject(error);
//                     });
//                 });

//                 // Wait for the service worker registration promise to resolve
//                 const registration = await registrationPromise;
//                 // Use the service worker for messaging
//                 messaging.useServiceWorker(registration);
//             }
//             else {
//               await  firebaseServiceWorker.update();
//             }
//         } catch (error) {
//             console.error(error);
//         }
//     }
// }
const vapid_key = "BHf8EtysrwY3hbIPHeuHZB0egvGuOGmN7GCZhY3USjM8gH-Utr1pgJN9-HCeHLv5XdmSJD-MtFJr-K0_eNdDz9M"
mainApp.service(
  "magnaFirebaseService",
  function (
    $q,
    magnaHttpService,
    magnaAuthenticationService,
    magnaMainService,
    $rootScope,
    $interval
  ) {
    const messaging = window.messaging;
    const getToken = window.getToken;
    const onMessage = window.onMessage;

    const initDeferred = $q.defer();
    const broadcastChannel = new BroadcastChannel("fcm-channel");

    async function requestPermission() {
      try {
        const currentToken = await getToken(messaging, {
          vapidKey:
            vapid_key,
        });
        if (currentToken) {
          sendTokenToServer(currentToken);
          console.log("Current Token:", currentToken);
        } else {
          console.log(
            "No registration token available. Request permission to generate one."
          );
        }
      } catch (err) {
        console.log("An error occurred while retrieving token. ", err);
      }
    }

    onMessage(messaging, (payload) => {
      console.log("Message received. ", payload);
      const data = JSON.parse(payload.data.data);
      if (!data || (data && !data.isSilent)) {
        appendMessage(payload);
      } else {
        handleSilentNotification(data);
      }
    });

    const tokenRefreshInterval = 60 * 60 * 1000; // 1 hour
    $interval(async () => {
      try {
        const currentToken = await getToken(messaging, {
          vapidKey:
            vapid_key,
        });
        if (currentToken) {
          sendTokenToServer(currentToken);
          console.log("Token refreshed:", currentToken);
        }
      } catch (err) {
        console.log("An error occurred while refreshing token. ", err);
      }
    }, tokenRefreshInterval);

    function showToken(currentToken) {
      console.log("showToken:", currentToken);
    }

    function sendTokenToServer(currentToken) {
      if (!isTokenSentToServer()) {
        let deviceId = magnaAuthenticationService.getDeviceId();
        magnaHttpService.HttpWrapper(
          {
            method: "GET",
            url:
              __env.PUBLIC +
              "notification/push/device/register?providerType=FireBase&recepientType=User&recepientId=" +
              $rootScope.user.id +
              "&token=" +
              currentToken +
              "&device=" +
              deviceId,
          },
          function (response) {
            setTokenSentToServer(true);
          },
          {
            error_handler: (error) => {
              console.log("error", error);
            },
          }
        );
      } else {
        console.log(
          "Token already sent to server so won't send it again unless it changes"
        );
      }
    }

    function isTokenSentToServer() {
      let path = new URL(".", window.location.href).pathname;
      path = path.replace(/\//g, "_");
      return window.localStorage.getItem(`sentToServer${path}`) == 1;
    }

    function setTokenSentToServer(sent) {
      let path = new URL(".", window.location.href).pathname;
      path = path.replace(/\//g, "_");
      window.localStorage.setItem(`sentToServer${path}`, sent ? 1 : 0);
    }

    function appendMessage(payload) {
      const data = JSON.parse(payload.data.data);
      const title = data.title;
      const tag = data.id;
      const seen = 0;

      if (Notification.permission === "granted") {
        const notification = new Notification(title, {
          tag: tag,
          icon: __env.baseUrl + "/images/magna_notification_icon.png",
        });

        notification.onclick = function () {
          $rootScope.$apply(function () {
            $location.path("/notification-details/" + tag + "/" + seen);
          });
          parent.focus();
          window.focus();
          this.close();
        };

        $.notify(
          {
            icon: "glyphicon glyphicon-bell",
            message: `<div style="margin-top: 10px">${title}</div>`,
            url: "#!/notification-details/" + tag + "/" + seen,
            target: "_self",
          },
          {
            className: "text",
            autoHide: false,
            placement: { from: "bottom", align: "right" },
            animate: {
              enter: "animated fadeInDown",
              exit: "animated fadeOutUp",
            },
            offset: 5,
            z_index: 1000000,
            delay: 10000,
            type: "notification-popup",
          }
        );
      }

      $rootScope.notification.notSeenCount++;
      console.log($rootScope.notification.notSeenCount);
    }

    function handleSilentNotification(payload) {
      dispatchCustomEvent(payload.action, payload.params);
    }

    broadcastChannel.addEventListener("message", (event) => {
      const data = JSON.parse(event.data.data);
      handleSilentNotification(data);
    });

    function dispatchCustomEvent(eventName, payload) {
      const event = new CustomEvent(eventName, {
        detail: payload,
      });
      document.dispatchEvent(event);
    }

    this.resetUI = function () {
      requestPermission().then(() => {
        showToken("loading...");
        getToken(messaging, {
          vapidKey: vapid_key,
        })
          .then((currentToken) => {
            if (currentToken) {
              sendTokenToServer(currentToken);
              console.log("Current Token:", currentToken);
            } else {
              setTokenSentToServer(false);
            }
          })
          .catch((err) => {
            console.log("An error occurred while retrieving token.", err);
            showToken("Error retrieving Instance ID token.", err);
            setTokenSentToServer(false);
          });
      });
    };

    this.initialized = initDeferred.promise;
    initDeferred.resolve(); // Ensure this is called to resolve the promise
  }
);

//   .config(['$provide', function ($provide) {
//     $provide.decorator('magnaFirebaseService', ['$delegate', function ($delegate, $injector) {

//       return new Promise((resolve, reject) => {
//         registerFirebaseSW().then(() => {
//           resolve($delegate);
//         }).catch((error) => {
//           console.log('Error:', error);
//           reject(error);
//         });
//       });
//     }]);
//   }]);


mainApp.service('magnaAuthenticationService', function ($http, $rootScope, __env, $cookies, $route, $injector,magnaMainService ,magnaSecurityService) {
    var cookiesPath = (new URL(__env.baseUrl)).pathname;
    var cookiesOptions = {
        path: cookiesPath,
        secure: true,
        sameSite: 'Strict'

    }
    function sendLoggedInUserToParent(){
        const globals = $cookies.getObject('globals') || {};
        const username = globals.currentUser?.username ?? JSON.parse(window.sessionStorage.getItem('globals'))?.currentUser?.username;

        // tells parent window that a user is logged in
        if (window.parent && username) {
            window.parent.postMessage({ isERPAuth: username }, "*");
        }
    }
    //this is because angular v 1.6 doesn't have option for setting  samesite 
    function setAuthCookie(name, value,domain=null) {
        var cookieString = name + '=' + encodeURIComponent(value) + ';';
      
        if (cookiesOptions.sameSite) {
          cookieString += ' SameSite=' + cookiesOptions.sameSite + ';';
        }
      
        if (cookiesOptions.secure) {
          cookieString += ' Secure;';
        }
       
        cookieString +=` path=${cookiesPath};`;

        if(domain){
            cookieString+=`domain=${domain}`
        }

        document.cookie = cookieString;
    }

    function isLivePerson(){
        if(window.sessionStorage.getItem('livePerson')) return true;

         //for firefox and chrome
        //  let decodedReferrer = decodeURIComponent(document.referrer);
        //  let ancestor = decodedReferrer.includes("https://z2.le.liveperson.net") 
        //  //
        //  if(ancestor) window.sessionStorage.setItem('livePerson',true);
        //  return ancestor;

        try {
            // Get the ancestor origins
            var ancestorOrigins = window.location.ancestorOrigins;
        
            // Iterate through the ancestor origins
            for (var i = 0; i < ancestorOrigins?.length; i++) {
              // Check if the ancestor host contains "liveperson"
              if (ancestorOrigins[i].indexOf("liveperson") !== -1) {
                window.sessionStorage.setItem("liveperson",1);
                return true;
              }
            }
          } catch (error) {
            // Error occurred while accessing ancestor origins (due to cross-origin restrictions)
            console.log("Error:", error);
          }
          window.sessionStorage.removeItem("liveperson");
          return false;
    }

    this.getDeviceId =function(){
        let path = new URL('.', window.location.href).pathname;
        path = path.replace(/\//g, '_');
        let deviceId = window.localStorage.getItem(`deviceId${path}`);
        if(deviceId){
            return deviceId ;
        }
        else{
            return  generateDeviceId();
        }
     }
     function generateDeviceId(){
        let path = new URL('.', window.location.href).pathname;
        path = path.replace(/\//g, '_');
        const timestamp = Date.now();
        window.localStorage.setItem(`deviceId${path}`,timestamp);

         return timestamp ;
      }
      function getCurrentDomain() {
        var hostname = window.location.hostname;
        var parts = hostname.split('.');
    
        // If the hostname contains more than two parts (subdomain + domain + tld)
        if (parts.length > 2) {
            // Get the last two parts which represent the domain and tld
            return '.' +  parts.slice(-2).join('.');
        } else {
            // Otherwise, return the entire hostname
            return hostname;
        }
    }
      function setCredentials(username,token,saveUserCookie=true) {
        $rootScope.globals = {
            currentUser: {
                username: username,
                isAuthenticated:true,
                token:token
            },
            domain:window.location.hostname
        };
        // store user details in globals cookie that keeps user logged till session ends
        // $cookies.putObject('globals', $rootScope.globals, cookiesOptions);
        setAuthCookie('globals',JSON.stringify($rootScope.globals));
        setAuthCookie('user',JSON.stringify({loginName:username}),getCurrentDomain());

        //if site is inside the livePerson 
        // we need to save the globals object in session storage too 

        if(isLivePerson()){
            window.sessionStorage.setItem('globals',JSON.stringify($rootScope.globals));
            if(__env.PRODUCTION_MODE){
                //send message to parent iframe 
                window.sessionStorage.setItem('isERPAuth',username, {'path': cookiesPath,'domain':'.maids.cc'});
                if(window.parent) window.parent.postMessage({isERPAuth:username},"*")
            }
        }
    
        if(__env.PRODUCTION_MODE)
            $cookies.put('isERPAuth',username, {'path': cookiesPath,'domain':'.maids.cc'});
    }
    function setRefreshTokens(expiration_date){
        const isoDateString = expiration_date.replace(" ", "").replace("+", "+").replace(/(\d{2})$/, ":$1");
        const dateFromServer = new Date(isoDateString);
        $cookies.put('expiration_date', new Date(dateFromServer.getTime() - (window.__env.EXPIRES_BEFORE*60000)), {'path': cookiesPath});
        
        if(isLivePerson()){
            window.sessionStorage.setItem('expiration_date', new Date(dateFromServer.getTime() - (window.__env.EXPIRES_BEFORE*60000)), {'path': cookiesPath,'domain':'.maids.cc'});
        }
    
    }

    this.login = function (username, password, callback) {  
        let deviceId = this.getDeviceId();
        if(isLivePerson()) deviceId+='LivePerson';
        var req = {
            method: 'POST',
            url: __env.PUBLIC + `login/jwt`,
            data:`"${btoa(username+":"+password+":"+deviceId)}"`
        };

        $http(req,{
            headers:{
                "Access-Control-Allow-Origin":"*"
            }
        }).then( async function ({data, status, headers, config}) {
            let speedTestRequired =false;
            if ('200' == status) {
                let token = headers('token');
                setCredentials(username,token ?? '');
                setTokenSentToServer(false);
                let exp = data.tokenExpirationDate;
                speedTestRequired = headers('Speed_test_required') ?? false;
                setRefreshTokens(exp);
             } 
             const developerMessage = headers('developerMessage');
             if(developerMessage){
              await  magnaSecurityService.redirectToSecurityPage(developerMessage,window.__env.baseUrl+'/main.html')
             }
             else{
                callback({data, status, headers, config,speedTestRequired});
             }
             
             //register public module then execute callback
             
        })
            .catch(function onError(response) {
                // if(response.status==500 && response.data.message=='SPEED_TEST_REQUIRED'){
                //     let token = headers('token');
                //      setCredentials(username, token);
                //     setTokenSentToServer(false);
                // }
                callback(response);
            });
    };

    // Google OAuth2 authentication method
    this.googleLogin = function (credential, callback) {
        let deviceId = this.getDeviceId();
        if(isLivePerson()) deviceId+='LivePerson';
        
        var req = {
            method: 'POST',
            url: __env.PUBLIC + 'auth/google',
            data: {
                credential: credential,
                deviceId: deviceId
            }
        };

        $http(req, {
            headers: {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": "application/json"
            }
        }).then(async function ({data, status, headers, config}) {
            if ('200' == status && data.success) {
                let token = data.token || headers('token');
                let username = data.user.email;
                setCredentials(username, token);
                setTokenSentToServer(false);
                
                if (data.tokenExpirationDate) {
                    setRefreshTokens(data.tokenExpirationDate);
                }
            }
            
            const developerMessage = headers('developerMessage');
            if(developerMessage){
                await magnaSecurityService.redirectToSecurityPage(developerMessage, window.__env.baseUrl+'/main.html');
            } else {
                callback({data, status, headers, config});
            }
        })
        .catch(function onError(response) {
            callback(response);
        });
    };

    // Google OAuth2 authorization code flow authentication
    this.googleAuthWithCode = function (code, redirectUrl, callback) {
        let deviceId = this.getDeviceId();
        if(isLivePerson()) deviceId+='LivePerson'; 
        var req = {
          method: "POST",
          url: __env.PUBLIC + `login/oAuth2`,
          data: {
            authorizationCode: code,
            redirectUrl: redirectUrl,
            deviceId: deviceId,
          },
        };

        $http(req, {
            headers: {
                "Access-Control-Allow-Origin": "*",
                "Content-Type": "application/json"
            }
        }).then(async function ({data, status, headers, config}) {
            if ('200' == status) {
                let token = data.token || headers('token');
                let username = data.user.loginName;
                setCredentials(username, token);
                setTokenSentToServer(false);
                
                if (data.tokenExpirationDate) {
                    setRefreshTokens(data.tokenExpirationDate);
                }
            }
            
            const developerMessage = headers('developerMessage');
            if(developerMessage){
                await magnaSecurityService.redirectToSecurityPage(developerMessage, window.__env.baseUrl+'/main.html');
            } else {
                callback({data, status, headers, config});
            }
        })
        .catch(function onError(response) {
            callback(response);
        });
    };

    this.refreshToken = function(error_callback=undefined,username,secureToken=""){
        let def  = $.Deferred();
        if($rootScope.globals==null){
            def.resolve();
            return def;
        }
        else{
            let deviceId =   this.getDeviceId();
            if(isLivePerson()) deviceId+='LivePerson';
            var req = {
                method:"POST",
                url: `${__env.PUBLIC}login/jwt/refreshtoken?username=${username}&device=${deviceId}`,
                data:{token:secureToken}
            };
            $http(req).then(function ({data, status, headers, config}) {
               
                if ('200' == status) {
                    if(data){
                        const userName = $rootScope.globals.currentUser.username;
                        let token = headers('token');
                        setCredentials(userName,token);
                        setTokenSentToServer(false);
                        magnaMainService.LoadingIcon.hide();
                        let exp =data.tokenExpirationDate;
                        setRefreshTokens(exp);
                        def.resolve(data.token);
                    }
                    else{
                        let token = $rootScope.globals.currentUser.token;
                        def.resolve(token);
                    }

                }
            })
            .catch(function onError(response) {
                magnaMainService.LoadingIcon.hide();
                if(error_callback){
                    error_callback();
                }
                else{
                // if(response.status==498){
                //     if(!document.getElementById('refresh_token_modal').classList.contains('in')){
                //         magnaMainService.DialogBox.showModal($('#refresh_token_modal'),{backdrop: 'static', keyboard: false});
                //     }
                // }
                if(!document.getElementById('refresh_token_modal').classList.contains('in')){
                    DialogBox.showModal($('#refresh_token_modal'),{backdrop: 'static', keyboard: false});
                }
                
                }
                
                def.reject();
    
            });
        }
        
    return def;
    }

    function deleteTokenFromServer(currentToken) {
        console.log('Sending token to server...');
        var req = {
            method: 'GET',
            url: __env.PUBLIC + "notification/push/device/unregister?token=" + currentToken,
        };
        $http(req).then(function (response) {
            if (response.data != true)
                callback(response);
        })
            .catch(function onError(response) {
                callback(response);
            });
    }

    this.deleteToken = function () {
        const messaging = window.messaging;
        // Delete Instance ID token.
        // [START delete_token]
       getToken(messaging, {
         vapidKey: vapid_key,
       })
         .then(function (currentToken) {
           messaging
             .deleteToken(currentToken)
             .then(function () {
               console.log("Token deleted.");
               setTokenSentToServer(false);
               deleteTokenFromServer(currentToken);
             })
             .catch(function (err) {
               console.log("Unable to delete token. ", err);
             });
           // [END delete_token]
         })
         .catch(function (err) {
           console.log("Error retrieving Instance ID token. ", err);
         });
    }

    function setTokenSentToServer(sent) {
        let path = new URL('.', window.location.href).pathname;
        path = path.replace(/\//g, '_');
        window.localStorage.setItem(`sentToServer${path}`, sent ? 1 : 0);
    }
  
    this.clearAuthdata =function(){
        this.deleteToken();
        window.sessionStorage.clear();
        $rootScope.globals = {};
        var cookies = $cookies.getAll();
        console.log("cookeies are ")
        angular.forEach(cookies, function (v, k) {
           
            $cookies.remove(k, {'path': cookiesPath});
            $cookies.remove(k, {'path': cookiesPath,domain:getCurrentDomain()});
        });
        //remove user cookie
        // document.cookie = "user" + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain=.teljoy.io; path=/;";
        // $cookies.remove("user",{'path': cookiesPath,domain:getCurrentDomain()});
        let path = new URL('.', window.location.href).pathname;
        path = path.replace(/\//g, '_');
        let deviceId = this.getDeviceId();
        // clear all storage items except for the keys that has deviceId
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key) && !key.includes('deviceId')) {
              localStorage.removeItem(key);
            }
          }
        //

        window.localStorage.setItem(`deviceId${path}`,deviceId);
    }
    this.logout = function () {
     
        let _this = this;
        magnaMainService.LoadingIcon.show();
        var req = {
            method:"GET",
            url: `${__env.PUBLIC}login/jwt/logout`,
        };
       
        if(__env.PRODUCTION_MODE){
            $cookies.remove('isERPAuth', {'path': cookiesPath,'domain':'.maids.cc'});
        }

            $injector.get('magnaHttpService').HttpWrapper(req, function ({data, status, headers, config}) {
                _this.clearAuthdata();
                setTimeout(() => {
                    magnaMainService.LoadingIcon.hide();
                    window.location.replace('login.html');
                }, 500);
            }, {
                needs_loading_icon:true,
                ignore_error_messages: true,
                error_handler: function(respons){
                    _this.clearAuthdata();
                    setTimeout(() => {
                        magnaMainService.LoadingIcon.hide();
                        window.location.replace('login.html');
                    }, 500);
                }
            }
        );
        
    };

    this.changeUserStatus=function(userId,status,callback){
        $injector.get('magnaHttpService').HttpWrapper({
            method: "POST",
            url: `${__env.ADMIN}/user/setUserStatus/${userId}?status=${status.id}`,
        }, function (response) {
            callback(response);
        }, {
            ignore_error_messages: true,
            needs_loading_icon:true,
            error_handler: function(response){
                callback(response);
            }
        });
    }


    this.isAuthenticated = function () {
        if(isLivePerson()){
            $rootScope.globals = JSON.parse(window.sessionStorage.getItem('globals')) || {};
            return $rootScope.globals.currentUser?.isAuthenticated ? true : false;
        }
       
        if(!this.checkSameCookieDomain('globals')) return false;

        $rootScope.globals = $cookies.getObject('globals') || {};
        let result =  $rootScope.globals?.currentUser?.isAuthenticated ? true:false;
        
        sendLoggedInUserToParent();
       
        return result;
    }

    this.checkSameCookieDomain = function(cookieName){
        const cookie = $cookies.getObject(cookieName) || {};
        return cookie && cookie.domain && cookie.domain == window.location.hostname
    }

    function getAuthorizationToken(){
        $rootScope.globals = $cookies.getObject('globals') || {};
       
        let token = $rootScope.globals?.currentUser?.token ?? '';
        if(isLivePerson() && !token){
            $rootScope.globals = JSON.parse(window.sessionStorage.getItem('globals')) || {};
            return $rootScope.globals.currentUser?.token ?? '';
        }
        return token;
    }


   
    this.addAuthorizationHeaders = function (headers) {
      if (this.isAuthenticated())
        headers.Authorization = "" + getAuthorizationToken();
      // add pageCode
      if(headers.pageCode) return;
      var pageCodeFromQuery = null;

      // Check hash fragment for query parameters (for hash-based routing)
      var hash = window.location.hash;
      if (hash && hash.includes("?")) {
        var hashQueryString = hash.split("?")[1];
        pageCodeFromQuery = new URLSearchParams(hashQueryString).get(
          "pageCode"
        );
      }

      // Fallback to main URL search parameters
      if (!pageCodeFromQuery) {
        pageCodeFromQuery = new URLSearchParams(window.location.search).get(
          "pageCode"
        );
      }

      if (pageCodeFromQuery) {
        headers.pageCode = pageCodeFromQuery;
      } else {
        var pageInfo = "";
        if (
          $route &&
          $route.current &&
          (pageInfo = this.getPageInfo($route.current.$$route))
        ) {
          headers.pageCode = pageInfo.code;
        }
      }
      return headers;
    };

    this.tokenPromise = function(headers){
        let def = $.Deferred();
        if(isTokenExpired() && !window.__env.refreshTokenRequest){
            window.__env.refreshTokenRequest = this.refreshToken();

            window.__env.refreshTokenRequest?.then(data=>{
                window.__env.refreshToken=null;
                // add authorization
                if (this.isAuthenticated())
                    headers.Authorization = '' + data;
                   
                    def.resolve(data);
            
            }).
            catch((error)=>{
                window.__env.refreshToken=null;
                def.reject();
            });      
        }
        else  if( window.__env.refreshTokenRequest){
            window.__env.refreshTokenRequest.then(data=>{
                   
                if (this.isAuthenticated()) headers.Authorization = '' +data;

                def.resolve(data);
            }).catch((error)=>{
                def.reject();
            });  
        }
        else def.resolve('');

        return def;
    }

    this.checkOrRefreshToken = function(error_callback=()=>{}){
        if(isTokenExpired()){
           let username =  $rootScope.globals.currentUser.username ?? "";
            this.triggerRefreshToken(error_callback,username);
        }
    }
    /* sends refresh token resquest and send event for other tabs*/
    this.triggerRefreshToken=function(error_callback=()=>{},username,secureToken=''){
        window.__env.refreshTokenRequest = this.refreshToken(error_callback,username,secureToken);
       
        window.__env.refreshTokenRequest.then(response=>{
            window.__env.refreshTokenRequest=null;
        }) ;
    }
    function isTokenExpired() {
        let now = new Date(new Date().getTime());
       
        let expiration_date = new Date($cookies.get('expiration_date')) ;

        if(isLivePerson() && !expiration_date){
            expiration_date = new Date(window.sessionStorage.getItem('expiration_date'));
        }

        return now>=expiration_date;
    }

    this.getPageInfo = function (route) {
        if (!route || typeof route != 'object' || !route.hasOwnProperty('page_info'))
            return null;
        var pageInfo = route.page_info;
        if (!pageInfo.hasOwnProperty("code")) {
            var path = route.originalPath.split('/');
            pageInfo.code = path[1] + "_" + (path.length > 2 ? path[2] : '');
        }
        if (!pageInfo.hasOwnProperty("module")) {
            var path = route.originalPath.split('/');
            if (path.length > 1 && path[1])
                pageInfo.module = path[1];
        }
        return pageInfo;
    }

    this.registerPages = function (selectedModule) {
        var routeCodes = [];
        for (var key in $route.routes) {
            var route = $route.routes[key];
            var pageInfo = this.getPageInfo(route);
            if (!pageInfo)
                continue;
            if (!pageInfo.hasOwnProperty('name')) {
                alert('route: ' + route.originalPath + " doesn't have the name attribute");
                return;
            }
            if (!pageInfo.hasOwnProperty('module')) {
                alert('route: ' + route.originalPath + " doesn't have the module attribute");
                return;
            }
            routeCodes.push({
                name: pageInfo.name,
                code: pageInfo.code,
                module: {code: pageInfo.module}
            });
        }
        let url = __env.ADMIN + 'module/register' + (selectedModule? ("?targetModuleCodes=" + selectedModule) : "");
        $injector.get('magnaHttpService').HttpWrapper({
                method: "POST",
                timeout: 0,
                url,
                headers: {
                    'Content-Type': "application/json"
                },
                data: routeCodes
            }, function (response) {
                $injector.get('magnaMainService').DialogBox.showSuccessMsg("Pages has been registered successfully");
            }, {needs_loading_icon: true}
        );

    }
    var allowdPages = {}, pagesPolicies = {};

    this.getUserAllowedPages = function (callback) {
        $injector.get('magnaHttpService').HttpWrapper({
                method: "GET",
                url: __env.PUBLIC + 'module/myPermissions',
                headers: {
                    'Content-Type': "application/json",

                }
            }, function (response) {
                myPermissions = response;
                myPermissions.forEach(page => {
                    if (page.permission !== 'NO') {
                        allowdPages[page.pageCode] = page.permission;
                    }
                    if (page.policyId)
                        pagesPolicies[page.pageCode] = {label: page.policyLabel};
                });
                callback();
            }, {needs_loading_icon: true,error_handler:(error)=>{
                if(error.status==500){
                    var cookies = $cookies.getAll();
                    angular.forEach(cookies, function (v, k) {
                        $cookies.remove(k, {'path': cookiesPath});
                    });
                }


             }}
        );
    }

    this.hasPagePermission = function (pageCode) {
        if ($rootScope.user && $rootScope.user.admin)
            return true;
        return (allowdPages[pageCode]) ? allowdPages[pageCode] : false;
    }

    this.hasRoutePermission = function (route) {
        var page_info = this.getPageInfo(route);
        if (!page_info || ($rootScope.user && $rootScope.user.admin && page_info.allowAdmin))
            return true;
        return this.hasPagePermission(page_info.code);
    }

    this.getPagePolicy = function (pageCode) {
        return pagesPolicies[pageCode];
    }

    this.verifyOtp = function(username,code,success_callback){
        let device = this.getDeviceId();
        if(isLivePerson()) device+='LivePerson';
        let data = {otp:code};
        
        var req = {
            method: 'POST',
            url: __env.PUBLIC + `login/verifyOtp?username=${username}&device=${device}`,
            data:data,
            headers:{
                "Access-Control-Allow-Origin":"*"
            }
        };

        $http(req).then(function ({data, status, headers, config}) {
            if(headers('token')){
                let token = headers('token');
                setCredentials(username, token);
                setTokenSentToServer(false);
                let exp = data.tokenExpirationDate;
                setRefreshTokens(exp);
            }

          success_callback(data);
        }, {
            needs_loading_icon:true,
            ignore_authorization: true,

        }
    );

    }
    this.reSendOTP = function(username,code,success_callback){
        let device = this.getDeviceId();
        if(isLivePerson()) device+='LivePerson';
        var req = {
            method: 'POST',
            url: __env.PUBLIC + `login/generateOTP?username=${username}&device=${device}`,
        };

        $http(req).then(function ({data, status, headers, config}) {
          success_callback(data);
        }, {
            needs_loading_icon:true,
            ignore_authorization: true,

        }
    );

    }
    this.continueToErp = function(){
        if (typeof (Storage) !== "undefined" && sessionStorage.destinationPage) {
            var destPage = sessionStorage.destinationPage;
            sessionStorage.destinationPage = '';
           window.location.replace(destPage);
        } else
           window.location.replace('main.html');
    }
});

mainApp.service("magnaValidationService", function () {

    var errors = {};// where key is the attribute name and its value is the error message

    function findElementByModel(modelName) {
        return $("[ng-model='" + modelName.replace(/'/g, "\\'") + "']");
    }

    var validators = {
        required: function (attr, val) {
            if (val || val === 0 || val === false)
                return true;
            return "This field is required";
        },
        email: function (attr, val) {
            if (!val || (typeof val == 'string' && /\S+@\S+\.\S+/.test(val)))
                return true;
            return "Invalid email address";
        },
        number: function (attr, val) {
            if ((!val && val !== false) || (val !== true && !isNaN(val)))
                return true;
            return "Invalid number";
        },
        phone: function (attr, val) {
            if (!val)
                return true;
            if (typeof val == "string") {
                let number = val.replace(/-/g, "");
                if (!number.startsWith('+')) {
                    number = '+' + number;
                }
                if (window.intlTelInputUtils && intlTelInputUtils.isValidNumber(number))
                    return true;
            }
            return "Invalid phone number";
        }
    }

    function getScopeAttrValue(scope, attr) {
        var $__scope = scope;
        return eval("$__scope." + attr);
    }

    function showErrors() {
        for (var key in errors) {
            var errMsg = errors[key];
            var element = findElementByModel(key);
            if (element.length > 0) {
                var errorElement = '<span class="help-block not-valid-err" style=" display: block; color:red">' + errMsg + '</span>';
                element.parents('.form-group').addClass("has-error");
                var colElement = element.parents('[class*="col-"]:first');
                if (colElement.length > 0)
                    colElement.append(errorElement);
                else
                    element.after(errorElement);
            }
        }
    }

    this.clearErrors = function () {
        errors = {};
        $(".has-error").removeClass("has-error");
        $(".not-valid-err").remove();
    }

    /**
     * validate scope's attributes
     * @param {Object} scope - model
     * @param {Object} attrs - scope's attributes and their validaters (Note: the validater can be string (pre defined validater) or function (custom validater))
     * @returns {boolean} - true if valid, false otherwise
     */
    this.validate = function (scope, attrs) {
        this.clearErrors();
        var isValid = true;
        for (var key in attrs) {
            var attrVal = null;
            try {
                attrVal = getScopeAttrValue(scope, key);
            } catch (exception) {
                alert('an error occurred when trying to get the value of (' + key + ')');
                return false;
            }
            var attrValidators = attrs[key];
            var isValidKey = true;
            for (var i = 0; i < attrValidators.length && isValidKey; i++) {
                var attrValidator = attrValidators[i];
                var validatorFunc = null;
                if (typeof attrValidator == "function")
                    validatorFunc = attrValidator;
                else {
                    if (!validators.hasOwnProperty(attrValidator)) {
                        alert('(' + attrValidator + ') validator not supported');
                        return false;
                    }
                    validatorFunc = validators[attrValidator];
                }
                var result = validatorFunc(key, attrVal);
                if (result === true)
                    isValidKey = true;
                else {
                    errors[key] = result;
                    isValidKey = false;
                }
            }
            isValid = isValid && isValidKey;
        }
        showErrors();
        return isValid;
    }

    /*** validate canvas signature element
     * @param {Object} elm - canvas htmlElm
     * @param {String} bgcolor - [Optional] canvas background color in Hex
     * @returns {boolean} - true if valid, false otherwise
     */
    this.validateSignatureCanvas = function (elm, bgcolor) {
        let isValid = true;
        let canvasJQueryElm = null;
        let ctx = null;

        function rgbToHex(r, g, b) {
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        }

        function isPixelColored(ctx, x, y) {
            const data = ctx.getImageData(x, y, 1, 1).data;
            return bgcolor ? ((bgcolor.r !== data[0] && 0 !== data[0]) || (bgcolor.g !== data[1] && 0 !== data[1]) || (bgcolor.b !== data[2] && 0 !== data[2])) : data[3] !== 0;
        }

        try {
            canvasJQueryElm = $(elm);
            ctx = canvasJQueryElm.get(0).getContext('2d');
        } catch (exception) {
            alert('an error occurred when trying to get the canvas element');
            return false;
        }
        let canvasEdgePoints = {
            topY: 0,
            bottomY: canvasJQueryElm.height() - 1,
            leftX: 0,
            rightX: canvasJQueryElm.width() - 1,
        }

        for (let x = canvasEdgePoints.leftX; x <= canvasEdgePoints.rightX; x++) {
            if (isPixelColored(ctx, x, canvasEdgePoints.topY) || isPixelColored(ctx, x, canvasEdgePoints.bottomY)) {
                isValid = false;
                break;
            }
        }
        if (isValid) {
            for (let y = canvasEdgePoints.topY; y <= canvasEdgePoints.bottomY; y++) {
                if (isPixelColored(ctx, canvasEdgePoints.leftX, y) || isPixelColored(ctx, canvasEdgePoints.rightX, y)) {
                    isValid = false;
                    break;
                }
            }
        }
        return isValid;
    }
});

mainApp.service('magnaTestModeService', function ($http, $rootScope, __env, $cookies, $window, $route, $injector) {

    var cookiesPath = (new URL(__env.baseUrl)).pathname;

    this.setTestMode = function (testMode) {
        $rootScope.isTestMode = testMode;
        if (!testMode) $cookies.remove('isTestMode', {'path': cookiesPath});
        else $cookies.put('isTestMode', $rootScope.isTestMode, {'path': cookiesPath});
    }

    this.getTestMode = function () {
        return ($cookies.get('isTestMode')) ? true : false;
    }

    this.setRequestIds = function (requestIds = []) {
        let stringifiedRequestIds = JSON.stringify(requestIds);
        $window.localStorage.setItem('requestIds', stringifiedRequestIds);
    }

    this.getRequestIds = function () {
        return ($window.localStorage.getItem('requestIds') && this.getTestMode()) ? JSON.parse($window.localStorage.getItem('requestIds')) : [];
    }

    this.addRequestIdHeaders = function (headers, requestOptions) {
        if (requestOptions.url.includes('/public/') || !requestOptions) return headers;
        let time = new Date().getTime();
        let requestID = (time + Math.random()).toString().replace('.', '');
        let requestIds = this.getRequestIds();

        requestIds.unshift({
            requestID,
            api: requestOptions.url,
            method: (requestOptions.method) ? requestOptions.method : 'GET',
            time
        });
        this.setRequestIds(requestIds.slice(0, 10));

        headers.requestId = requestID;

        return headers;
    }
    if(window.singleSpa){
        window.singleSpa.setBootstrapMaxTime(10000, true, 15000);
        window.singleSpa.start()
    }
})
